"use strict";
const path = require("path");
const defaultSettings = require("./src/settings.js");
const BundleAnalyzerPlugin=require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
// 引入等比适配插件
const px2rem = require("postcss-px2rem");

// 配置基本大小
const postcss = px2rem({
    // 基准大小 baseSize，需要和rem.js中相同
    remUnit: 16,
});
//版本：
const Version = new Date().getTime();

function resolve(dir) {
    return path.join(__dirname, dir);
}

// page title
const name = defaultSettings.title || "saas";

// If your port is set to 80,use administrator privileges to execute the command line.
// For example, Mac: sudo npm run You can change the port by the following method:
// port = 9527 npm run dev OR npm run dev --port = 9527
const port = process.env.port || process.env.npm_config_port || 9527; // dev port

const isProd = process.env.NODE_ENV === "production";
const CompressionPlugin = require('compression-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

// 是否开启打包分析
const shouldAnalyze =  process.env.VUE_APP_ANALYZE==='TRUE';

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
    publicPath: process.env.VUE_APP_BASE_URL ? '/' : '', //process.env==='test'?'/':'./'
    outputDir: process.env.VUE_APP_OUTPUT_DIR || "dist", //'dist',
    assetsDir: "static",
    lintOnSave: false, // 代码规范实时验证
    // lintOnSave: process.env.NODE_ENV !== 'production',
    productionSourceMap: false, // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建
    devServer: {
        hot: true, // 启用热重载
        open: true, // 自动打开浏览器
        // host: "free.iule.net",  //指定要使用的 host
        port: 9999, //指定端口号以侦听
        hotOnly: false, //启用热模块替换，而无需页面刷新作为构建失败时的回退。
        overlay: {
            warnings: false,
            errors: true,
        },
        https: true,
        headers: {
            "Access-Control-Allow-Origin": "*",
        },
        //代理
        //proxy: 'http://************:8088',
        proxy: "https://pangu.idicc.cn", //预发环境
        // proxy: 'https://pangustg.airuicloud.com',//开发环境
        //proxy:  'https://pangu.idicc.cn',//正式环境
        //proxy: 'http://************:8088', //本地 - 丹
        //  proxy: {
        //   '/admin': {
        //     target:'http://pangudev.idicc.cn', // 开发
        //     //target:'http://pangu.idicc.cn', // 线上
        //     // target:'http://pangutest.idicc.cn', // 测试
        //     // target:'http://pangustg.idicc.cn', // 预发
        //     //target:'http://**************:8088',//本地
        //     //target:'http://192.168.68.68:8088',//本地 - 丹
        //     changeOrigin: true,
        //     // ws: true,
        //     // secure: true,
        //     // pathRewrite: {
        //     //   '^/v1': ''
        //     // }
        //   },
        //   // 城市json 获取
        //   '/areas_v3': {
        //     target: 'https://geo.datav.aliyun.com',
        //     changeOrigin: true,
        //   },
        //    // 城市json 获取
        //    '/stock': {
        //     target: 'https://s.askci.com',
        //     changeOrigin: true,
        //   },
        //   '/ws': {
        //     target: 'https://localhost:9999',
        //     secure: false,
        //   }
        // },
        // before: require('./mock/mock-server.js')
    },
    // 样式
    css: {
        loaderOptions: {
            postcss: {
                plugins: [postcss]
            }
        },
        //是否使用css分离插件 ExtractTextPlugin
        extract: {
            // 修改打包后css文件名   // css打包文件，添加时间戳
            filename: `static/css/[name].css?${Version}`, // 此处static/css/xxx 目录根据自己打包情况来定,我使用的就没有static一层,所以直接去掉static即可。根据自己项目决定
            chunkFilename: `static/css/[name].css?${Version}`,
        },
    },

    configureWebpack: {
        devtool: process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'dev' ? 'source-map' : 'none',
        // provide the app's title in webpack's name field, so that
        // it can be accessed in index.html to inject the correct title.
        module: {
            rules: [
                {
                    test: /\.mjs$/,
                    include: /node_modules/,
                    type: "javascript/auto"
                },
            ]
        },
        performance: {
            hints: "warning",
            //入口起点的最大体积 整数类型（以字节为单位）
            maxEntrypointSize: 50000000,
            //生成文件的最大体积 整数类型（以字节为单位 300k）
            maxAssetSize: 30000000,
            //只给出 js 文件的性能提示
            assetFilter: function (assetFilename) {
                return assetFilename.endsWith(".js");
            },
        },
        name: name,
        // 只在生产环境使用外部化依赖
        externals: isProd ? {
            'vue': 'Vue',
            'element-ui': 'ELEMENT',
            'echarts': 'echarts'
        } : {},
        resolve: {
            alias: {
                "@": resolve("src"),
            },
        },
        plugins: [
            // 压缩
            new CompressionPlugin({
                //     //[file] 会被替换成原始资源。[path] 会被替换成原始资源的路径， [query] 会被替换成查询字符串。默认值是 "[path].gz[query]"。
                filename: '[path].gz[query]',// 提示compression-webpack-plugin@3.0.0的话asset改为filename
                //     //可以是 function(buf, callback) 或者字符串。对于字符串来说依照 zlib 的算法(或者 zopfli 的算法)。默认值是 "gzip"。
                algorithm: 'gzip',
                //     //所有匹配该正则的资源都会被处理。默认值是全部资源。
                test: /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i,
                // test: /\.(js|css|html|svg)$/,
                //     //只有大小大于该值的资源会被处理。单位是 bytes。默认值是 0。
                threshold: 10240,
                //     //只有压缩率小于这个值的资源才会被处理。默认值是 0.8。
                minRatio: 0.8,
                //     //删除原文件
                deleteOriginalAssets: false
            }),
            new MiniCssExtractPlugin({
                filename: "[name].css",
            }),
            // 添加打包分析插件，仅在本地分析时启用
            ...(shouldAnalyze ? [
                new BundleAnalyzerPlugin({
                    analyzerMode: 'server',
                    analyzerHost: '127.0.0.1',
                    analyzerPort: 8888,
                    reportFilename: 'report.html',
                    defaultSizes: 'parsed',
                    openAnalyzer: true,
                    generateStatsFile: false,
                    statsFilename: 'stats.json',
                    statsOptions: null,
                    logLevel: 'info'
                })
            ] : []),
            // 只在生产环境优化moment和echarts
            ...(isProd ? [
                // 优化时刻库 - 只保留中文语言包
                new (require('webpack')).ContextReplacementPlugin(
                    /moment[/\\]locale$/,
                    /zh-cn/
                ),
                // 忽略echarts不需要的组件
                new (require('webpack')).IgnorePlugin({
                    resourceRegExp: /^\.\/locale$/,
                    contextRegExp: /echarts[/\\]lib$/
                })
            ] : [])
        ],

        // 输出重构  打包编译后的 文件名称  【模块名称.版本号.时间戳】
        output: {
            // filename: utils.assetsPath('js/[name].[chunkhash].'+Version+'js'),
            // chunkFilename: utils.assetsPath('js/[id].[chunkhash].'+Version+'js')
            filename: `static/js/[name].js?${Version}`, // js打包文件，添加时间戳
            chunkFilename: `static/js/[name].js?${Version}`,
        },
    },

    chainWebpack(config) {
        config.plugin("preload").tap(() => [
            {
                rel: "preload",
                // to ignore runtime.js
                // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
                fileBlacklist: [
                    /\.map$/,
                    /hot-update\.js$/,
                    /runtime\..*\.js$/,
                ],
                include: "initial",
            },
        ]);

        // when there are many pages, it will cause too many meaningless requests
        config.plugins.delete("prefetch");
        // set svg-sprite-loader
        config.module.rule("svg").exclude.add(resolve("src/icons")).end();
        config.module
            .rule("icons")
            .test(/\.svg$/)
            .include.add(resolve("src/icons"))
            .end()
            .use("svg-sprite-loader")
            .loader("svg-sprite-loader")
            .options({
                symbolId: "icon-[name]",
            })
            .end();

        // videoPlayer
        config.module
            .rule("swf")
            .test(/\.swf$/)
            .use("url-loader")
            .loader("url-loader")
            .tap((options) => {
                options = {
                    name: `static/img/[name].[ext]?${Version}`,
                    fallback: {
                        loader: "file-loader",
                        options: {
                            name: `static/img/[name].[ext]?${Version}`,
                        },
                    },
                };
                return options;
            })
            .options({
                limit: 10000,
            })
            .end();

        //config.when(process.env.NODE_ENV !== 'development', config => {})
        //console.log(process.env.NODE_ENV !== 'development')

        config.when(process.env.NODE_ENV !== "development", (config) => {
            config
                .plugin("ScriptExtHtmlWebpackPlugin")
                .after("html")
                .use("script-ext-html-webpack-plugin", [
                    {
                        // `runtime` must same as runtimeChunk name. default is `runtime`
                        inline: /runtime\..*\.js$/,
                    },
                ])
                .end();

            //代码分割及性能优化 optimizations
            config.optimization.splitChunks({
                chunks: "all",
                maxInitialRequests: 6,
                maxAsyncRequests: 6,
                cacheGroups: {
                    // 基础库 - 变化较少的第三方库
                    libs: {
                        name: "chunk-libs",
                        test: /[\\/]node_modules[\\/]/,
                        priority: 10,
                        chunks: "initial",
                        enforce: true
                    },
                    // Element UI 单独打包
                    elementUI: {
                        name: "chunk-elementUI",
                        priority: 20,
                        test: /[\\/]node_modules[\\/]_?element-ui(.*)/,
                        chunks: "all"
                    },
                    // ECharts 单独打包
                    echarts: {
                        name: "chunk-echarts",
                        priority: 15,
                        test: /[\\/]node_modules[\\/]echarts/,
                        chunks: "all"
                    },
                    // 工具库
                    utils: {
                        name: "chunk-utils",
                        priority: 12,
                        test: /[\\/]node_modules[\\/](lodash|moment|axios)/,
                        chunks: "all"
                    },
                    // 公共组件
                    commons: {
                        name: "chunk-commons",
                        test: resolve("src/components"),
                        minChunks: 2,
                        priority: 5,
                        reuseExistingChunk: true,
                        chunks: "all"
                    },
                    // 默认分组
                    default: {
                        minChunks: 2,
                        priority: 1,
                        reuseExistingChunk: true
                    }
                },
            });
            // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
            config.optimization.runtimeChunk("single");
        });
    },
};
