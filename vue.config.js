"use strict";
const path = require("path");
const defaultSettings = require("./src/settings.js");
// 引入等比适配插件
const px2rem = require("postcss-px2rem");
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const LodashModuleReplacementPlugin = require('lodash-webpack-plugin');

// 配置基本大小
const postcss = px2rem({
    // 基准大小 baseSize，需要和rem.js中相同
    remUnit: 16,
});
//版本：
const Version = new Date().getTime();

function resolve(dir) {
    return path.join(__dirname, dir);
}

// page title
const name = defaultSettings.title || "saas";

// If your port is set to 80,use administrator privileges to execute the command line.
// For example, Mac: sudo npm run You can change the port by the following method:
// port = 9527 npm run dev OR npm run dev --port = 9527
const port = process.env.port || process.env.npm_config_port || 9527; // dev port

const isProd = process.env.NODE_ENV === "production";
const CompressionPlugin = require('compression-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
 // 是否开启打包分析
const shouldAnalyze =true
    // process.env.VUE_APP_ANALYZE === 'true';

//添加压缩 npm install compression-webpack-plugin -D  这里需要vue 4.4.4对应5.0
//const CompressionPlugin = require('compression-webpack-plugin');
// /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i;
// const productionGzipExtensions = /\.(js|css|)(\?.*)?$/i

// 去掉 console.log 打印
const UglifyJsPlugin = require('uglifyjs-webpack-plugin')

// 去掉无用资源
const UselessFile = require('useless-files-webpack-plugin')

const isProduction = process.env.NODE_ENV === 'production';


// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
    runtimeCompiler: true,
    publicPath: process.env.VUE_APP_BASE_URL ? '/' : '', //process.env==='test'?'/':'./'
    outputDir: process.env.VUE_APP_OUTPUT_DIR || "dist", //'dist',
    assetsDir: "static",
    lintOnSave: false, // 代码规范实时验证
    // lintOnSave: process.env.NODE_ENV !== 'production',
    productionSourceMap: false, // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建
    devServer: {
        hot: true, // 启用热重载
        open: true, // 自动打开浏览器
        // host: "free.iule.net",  //指定要使用的 host
        port: 9999, //指定端口号以侦听
        hotOnly: false, //启用热模块替换，而无需页面刷新作为构建失败时的回退。
        overlay: {
            warnings: false,
            errors: true,
        },
        https: true,
        headers: {
            "Access-Control-Allow-Origin": "*",
        },
        //代理
        //proxy: 'http://************:8088',
        proxy: "https://pangustg.idicc.cn", //预发环境
        // proxy: 'https://pangustg.airuicloud.com',//开发环境
        //proxy:  'https://pangu.idicc.cn',//正式环境
        //proxy: 'http://************:8088', //本地 - 丹
        //  proxy: {
        //   '/admin': {
        //     target:'http://pangudev.idicc.cn', // 开发
        //     //target:'http://pangu.idicc.cn', // 线上
        //     // target:'http://pangutest.idicc.cn', // 测试
        //     // target:'http://pangustg.idicc.cn', // 预发
        //     //target:'http://**************:8088',//本地
        //     //target:'http://192.168.68.68:8088',//本地 - 丹
        //     changeOrigin: true,
        //     // ws: true,
        //     // secure: true,
        //     // pathRewrite: {
        //     //   '^/v1': ''
        //     // }
        //   },
        //   // 城市json 获取
        //   '/areas_v3': {
        //     target: 'https://geo.datav.aliyun.com',
        //     changeOrigin: true,
        //   },
        //    // 城市json 获取
        //    '/stock': {
        //     target: 'https://s.askci.com',
        //     changeOrigin: true,
        //   },
        //   '/ws': {
        //     target: 'https://localhost:9999',
        //     secure: false,
        //   }
        // },
        // before: require('./mock/mock-server.js')
    },
    // 样式
    css: {
        loaderOptions: {
            postcss: {
                plugins: [postcss]
            }
        },
        //是否使用css分离插件 ExtractTextPlugin
        extract: {
            // 修改打包后css文件名   // css打包文件，添加时间戳
            filename: `static/css/[name].css?${Version}`, // 此处static/css/xxx 目录根据自己打包情况来定,我使用的就没有static一层,所以直接去掉static即可。根据自己项目决定
            chunkFilename: `static/css/[name].css?${Version}`,
        },
    },

    configureWebpack: {
        devtool: process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'dev' ? 'source-map' : 'none',
        // provide the app's title in webpack's name field, so that
        // it can be accessed in index.html to inject the correct title.
        module: {
            rules: [
                {
                    test: /\.mjs$/,
                    include: /node_modules/,
                    type: "javascript/auto"
                },
            ]
        },
        performance: {
            hints: "warning",
            //入口起点的最大体积 整数类型（以字节为单位）
            maxEntrypointSize: 50000000,
            //生成文件的最大体积 整数类型（以字节为单位 300k）
            maxAssetSize: 30000000,
            //只给出 js 文件的性能提示
            assetFilter: function (assetFilename) {
                return assetFilename.endsWith(".js");
            },
        },
        name: name,
        resolve: {
            alias: {
                "@": resolve("src"),
            },
        },
        plugins: [
            // 压缩
            new CompressionPlugin({
                //     //[file] 会被替换成原始资源。[path] 会被替换成原始资源的路径， [query] 会被替换成查询字符串。默认值是 "[path].gz[query]"。
                filename: '[path].gz[query]',// 提示compression-webpack-plugin@3.0.0的话asset改为filename
                //     //可以是 function(buf, callback) 或者字符串。对于字符串来说依照 zlib 的算法(或者 zopfli 的算法)。默认值是 "gzip"。
                algorithm: 'gzip',
                //     //所有匹配该正则的资源都会被处理。默认值是全部资源。
                test: /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i,
                // test: /\.(js|css|html|svg)$/,
                //     //只有大小大于该值的资源会被处理。单位是 bytes。默认值是 0。
                threshold: 10240,
                //     //只有压缩率小于这个值的资源才会被处理。默认值是 0.8。
                minRatio: 0.8,
                //     //删除原文件
                deleteOriginalAssets: false
            }),
            new MiniCssExtractPlugin({
                filename: "[name].css",
            }),
                  // 添加打包分析插件，仅在本地分析时启用
            ...(shouldAnalyze ? [
                new BundleAnalyzerPlugin({
                    analyzerMode: 'server',
                    analyzerHost: '127.0.0.1',
                    analyzerPort: 8888,
                    reportFilename: 'report.html',
                    defaultSizes: 'parsed',
                    openAnalyzer: true,
                    generateStatsFile: false,
                    statsFilename: 'stats.json',
                    statsOptions: null,
                    logLevel: 'info'
                })
            ] : []),
            // Lodash 按需引入优化
            new LodashModuleReplacementPlugin({
                // 保留项目中实际使用的功能
                'collections': true,    // 用于 findKey 等集合操作
                'paths': true,          // 用于 set 等路径操作
                'cloning': true,        // 用于 cloneDeep
                'currying': true,       // 用于 debounce
                'deburring': false,     // 不使用
                'unicode': false,       // 不使用
                'chaining': false,      // 不使用链式调用
                'memoizing': false,     // 不使用缓存
                'coercions': false,     // 不使用类型转换
                'flattening': false,    // 不使用扁平化
                'guards': false,        // 不使用守卫
                'metadata': false,      // 不使用元数据
                'placeholders': false,  // 不使用占位符
                'shorthands': false     // 不使用简写
            }),
        ],

        // 输出重构  打包编译后的 文件名称  【模块名称.版本号.时间戳】
        output: {
            // filename: utils.assetsPath('js/[name].[chunkhash].'+Version+'js'),
            // chunkFilename: utils.assetsPath('js/[id].[chunkhash].'+Version+'js')
            filename: `static/js/[name].js?${Version}`, // js打包文件，添加时间戳
            chunkFilename: `static/js/[name].js?${Version}`,
        },
        // 外部化地图资源，减少打包体积
        externals: isProd ? {
          // ...existing externals...
        } : {},
    },

    chainWebpack(config) {
        config.plugin("preload").tap(() => [
            {
                rel: "preload",
                // to ignore runtime.js
                // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
                fileBlacklist: [
                    /\.map$/,
                    /hot-update\.js$/,
                    /runtime\..*\.js$/,
                ],
                include: "initial",
            },
        ]);

        config.plugin('uselessFile')
            .use(
                new UselessFile({
                    root: path.resolve(__dirname, './src'), // 项目目录
                    out: './fileList.json', // 输出文件列表
                    clean: false, // 是否删除文件,
                    exclude: /node_modules/ // 排除文件列表
                })
            )

        // when there are many pages, it will cause too many meaningless requests
        config.plugins.delete("prefetch");
        // set svg-sprite-loader
        config.module.rule("svg").exclude.add(resolve("src/icons")).end();
        config.module
            .rule("icons")
            .test(/\.svg$/)
            .include.add(resolve("src/icons"))
            .end()
            .use("svg-sprite-loader")
            .loader("svg-sprite-loader")
            .options({
                symbolId: "icon-[name]",
            })
            .end();

        // videoPlayer
        config.module
            .rule("swf")
            .test(/\.swf$/)
            .use("url-loader")
            .loader("url-loader")
            .tap((options) => {
                options = {
                    name: `static/img/[name].[ext]?${Version}`,
                    fallback: {
                        loader: "file-loader",
                        options: {
                            name: `static/img/[name].[ext]?${Version}`,
                        },
                    },
                };
                return options;
            })
            .options({
                limit: 10000,
            })
            .end();

        // 压缩图片
        // config.module
        //     .rule("images")
        //     .use("url-loader")
        //     .tap((options) => ({
        //         limit: 10240,
        //         fallback: {
        //             loader: require.resolve("file-loader"),
        //             options: {
        //                 name: "static/img/[name].[hash:8].[ext]",
        //                 esModule: false, //低版本默认为false，高版本默认为true
        //             },
        //         },
        //     }))
        //     .end()
        //     .use("image-webpack-loader")
        //     .loader("image-webpack-loader")
        //     .options({
        // mozjpeg: {
        //     progressive: true,
        //     quality: 50,
        // }, // 压缩JPEG图像
        // optipng: {
        //     enabled: true,
        // }, // 压缩PNG图像
        // pngquant: {
        //     quality: [0.5, 0.65],
        //     speed: 4,
        // }, // 压缩PNG图像
        // gifsicle: {
        //     interlaced: false,
        // }, // 压缩GIF图像
        // })
        // .end()
        // .enforce("post");

        config.module
            .rule('images')
            .use('image-webpack-loader')
            .loader('image-webpack-loader')
            .options({
                bypassOnDebug: true
            })
            .end()

        // 优化地图 JSON 文件的处理
        config.module
          .rule('json')
          .test(/\.json$/)
          .include.add(resolve('src/assets/mapJson'))
          .end()
          .type('javascript/auto')
          .use('file-loader')
          .loader('file-loader')
          .options({
            name: 'static/json/[name].[hash:8].[ext]'
          });

        config.when(process.env.NODE_ENV !== "development", (config) => {
            config
                .plugin("ScriptExtHtmlWebpackPlugin")
                .after("html")
                .use("script-ext-html-webpack-plugin", [
                    {
                        // `runtime` must same as runtimeChunk name. default is `runtime`
                        inline: /runtime\..*\.js$/,
                    },
                ])
                .end();
            config.optimization.minimizer = [
                //比较慢，开发环境关闭
                new UglifyJsPlugin({
                    uglifyOptions: {
                        compress: {
                            warnings: false,
                            drop_console: true, //console
                            drop_debugger: true,
                            pure_funcs: ["console.log"], //移除console
                        },
                        // output: {
                        //   comments: false, //去掉注释
                        //   },
                    },
                    sourceMap: false,
                    parallel: true, //使用多进程并行运行来提高构建速度
                }),
            ];
            //代码分割及性能优化 optimizations
            config.optimization.splitChunks({
                chunks: "all",
                minSize: 30000,
                maxSize: 0,
                minChunks: 1,
                maxAsyncRequests: 5,
                maxInitialRequests: 3,
                automaticNameDelimiter: '~',
                name: true,
                cacheGroups: {
                    libs: {
                        name: "chunk-libs",
                        test: /[\\/]node_modules[\\/]/,
                        priority: 10,
                        chunks: "initial", // only package third parties that are initially dependent
                    },
                    elementUI: {
                        name: "chunk-elementUI", // split elementUI into a single package
                        priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                        test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
                    },
                    lodash: {
                        name: "chunk-lodash", // 将 lodash 单独打包
                        priority: 15, // 优先级高于 libs
                        test: /[\\/]node_modules[\\/]_?lodash(.*)/, // 匹配 lodash 相关模块
                        chunks: "all",
                        enforce: true
                    },
                    commons: {
                        name: "chunk-commons",
                        test: resolve("src/components"), // can customize your rules
                        minChunks: 3, //  minimum common number
                        priority: 5,
                        reuseExistingChunk: true,
                    }
                },
            });
            // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
            config.optimization.runtimeChunk("single");
        });
    },
};
