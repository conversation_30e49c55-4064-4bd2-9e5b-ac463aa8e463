#!/usr/bin/env node

/**
 * Lodash 优化分析脚本
 * 用于分析项目中 lodash 的使用情况和优化效果
 */

const fs = require('fs');
const path = require('path');

// 分析 dist 目录中的 lodash 相关文件
function analyzeLodashBundle() {
  const distPath = path.join(__dirname, '../dist/static/js');
  
  if (!fs.existsSync(distPath)) {
    console.log('❌ dist 目录不存在，请先运行 npm run build');
    return;
  }

  const files = fs.readdirSync(distPath);
  const lodashFiles = files.filter(file => file.includes('lodash'));
  
  console.log('🔍 Lodash 打包分析结果:');
  console.log('='.repeat(50));
  
  if (lodashFiles.length === 0) {
    console.log('❌ 未找到 lodash 相关的打包文件');
    return;
  }

  lodashFiles.forEach(file => {
    const filePath = path.join(distPath, file);
    const stats = fs.statSync(filePath);
    const sizeKB = (stats.size / 1024).toFixed(2);
    
    console.log(`📦 ${file}`);
    console.log(`   大小: ${sizeKB} KB`);
    console.log(`   路径: ${filePath}`);
  });

  // 分析总的 JS 文件大小
  const allJsFiles = files.filter(file => file.endsWith('.js'));
  const totalSize = allJsFiles.reduce((total, file) => {
    const filePath = path.join(distPath, file);
    const stats = fs.statSync(filePath);
    return total + stats.size;
  }, 0);

  const lodashSize = lodashFiles.reduce((total, file) => {
    const filePath = path.join(distPath, file);
    const stats = fs.statSync(filePath);
    return total + stats.size;
  }, 0);

  const lodashPercentage = ((lodashSize / totalSize) * 100).toFixed(2);

  console.log('\n📊 统计信息:');
  console.log(`   总 JS 大小: ${(totalSize / 1024).toFixed(2)} KB`);
  console.log(`   Lodash 大小: ${(lodashSize / 1024).toFixed(2)} KB`);
  console.log(`   Lodash 占比: ${lodashPercentage}%`);
}

// 分析源代码中的 lodash 使用情况
function analyzeLodashUsage() {
  console.log('\n🔍 源代码中的 Lodash 使用分析:');
  console.log('='.repeat(50));

  const usageMap = {
    'cloneDeep': [],
    'debounce': [],
    'findKey': [],
    'set': [],
    'throttle': [],
    'get': [],
    'merge': [],
    'pick': [],
    'omit': []
  };

  // 扫描源代码目录
  function scanDirectory(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        scanDirectory(filePath);
      } else if (file.endsWith('.js') || file.endsWith('.vue')) {
        try {
          const content = fs.readFileSync(filePath, 'utf8');
          
          // 检查 lodash 导入
          const lodashImports = content.match(/import.*from\s+['"]lodash['"]/g) || [];
          const lodashSpecificImports = content.match(/import.*from\s+['"]lodash\/\w+['"]/g) || [];
          
          if (lodashImports.length > 0 || lodashSpecificImports.length > 0) {
            // 检查具体使用的方法
            Object.keys(usageMap).forEach(method => {
              const regex = new RegExp(`\\b${method}\\b`, 'g');
              if (regex.test(content)) {
                usageMap[method].push(filePath.replace(process.cwd(), '.'));
              }
            });
          }
        } catch (error) {
          // 忽略读取错误
        }
      }
    });
  }

  const srcPath = path.join(__dirname, '../src');
  if (fs.existsSync(srcPath)) {
    scanDirectory(srcPath);
  }

  // 输出使用情况
  Object.entries(usageMap).forEach(([method, files]) => {
    if (files.length > 0) {
      console.log(`✅ ${method}:`);
      files.forEach(file => {
        console.log(`   - ${file}`);
      });
    } else {
      console.log(`❌ ${method}: 未使用`);
    }
  });
}

// 提供优化建议
function provideOptimizationSuggestions() {
  console.log('\n💡 优化建议:');
  console.log('='.repeat(50));
  
  const suggestions = [
    '1. 继续使用按需导入: import { method } from "lodash"',
    '2. 考虑使用原生 JavaScript 替代简单的 lodash 方法',
    '3. 定期检查是否有未使用的 lodash 方法',
    '4. 使用 webpack-bundle-analyzer 监控打包体积变化',
    '5. 考虑使用 lodash-es 获得更好的 tree-shaking 效果'
  ];

  suggestions.forEach(suggestion => {
    console.log(`   ${suggestion}`);
  });
}

// 主函数
function main() {
  console.log('🚀 Lodash 优化分析工具');
  console.log('='.repeat(50));
  
  analyzeLodashBundle();
  analyzeLodashUsage();
  provideOptimizationSuggestions();
  
  console.log('\n✨ 分析完成！');
}

// 运行分析
if (require.main === module) {
  main();
}

module.exports = {
  analyzeLodashBundle,
  analyzeLodashUsage,
  provideOptimizationSuggestions
};
