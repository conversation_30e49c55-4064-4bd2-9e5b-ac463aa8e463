#!/usr/bin/env node

/**
 * Lodash 优化分析脚本
 * 用于分析项目中 lodash 的使用情况和优化效果
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class LodashAnalyzer {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.usedMethods = new Set();
    this.sourceExtensions = ['.js', '.vue', '.jsx', '.ts', '.tsx'];
  }

  // 获取所有源代码文件
  getAllSourceFiles() {
    const sourcePatterns = this.sourceExtensions.map(ext => `**/*${ext}`);
    let sourceFiles = [];
    
    sourcePatterns.forEach(pattern => {
      const files = glob.sync(pattern, {
        cwd: this.projectRoot,
        ignore: ['node_modules/**', 'dist/**', '.git/**'],
        absolute: true
      });
      sourceFiles = sourceFiles.concat(files);
    });

    return sourceFiles;
  }

  // 分析 lodash 使用情况
  analyzeLodashUsage() {
    console.log('🔍 分析 Lodash 使用情况...');
    
    const sourceFiles = this.getAllSourceFiles();
    const patterns = [
      // import _ from 'lodash'
      /import\s+_\s+from\s+['"`]lodash['"`]/g,
      // import { method } from 'lodash'
      /import\s+\{\s*([^}]+)\s*\}\s+from\s+['"`]lodash['"`]/g,
      // import method from 'lodash/method'
      /import\s+\w+\s+from\s+['"`]lodash\/(\w+)['"`]/g,
      // _.method()
      /_\.(\w+)/g,
      // require('lodash')
      /require\s*\(\s*['"`]lodash['"`]\s*\)/g,
      // require('lodash/method')
      /require\s*\(\s*['"`]lodash\/(\w+)['"`]\s*\)/g
    ];

    sourceFiles.forEach(filePath => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const relativePath = path.relative(this.projectRoot, filePath);
        
        patterns.forEach((pattern, index) => {
          let match;
          while ((match = pattern.exec(content)) !== null) {
            if (index === 1) { // import { methods } from 'lodash'
              const methods = match[1].split(',').map(m => m.trim());
              methods.forEach(method => this.usedMethods.add(method));
            } else if (index === 2 || index === 5) { // import from 'lodash/method'
              this.usedMethods.add(match[1]);
            } else if (index === 3) { // _.method()
              this.usedMethods.add(match[1]);
            }
          }
        });
      } catch (error) {
        console.warn(`⚠️  读取文件失败: ${filePath}`);
      }
    });

    this.generateLodashReport();
  }

  // 生成 Lodash 分析报告
  generateLodashReport() {
    const reportPath = path.join(this.projectRoot, 'lodash-usage-report.json');
    const report = {
      timestamp: new Date().toISOString(),
      usedMethods: Array.from(this.usedMethods).sort(),
      methodCount: this.usedMethods.size,
      recommendations: this.getRecommendations()
    };

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`📊 Lodash 使用报告:`);
    console.log(`   使用的方法数量: ${this.usedMethods.size}`);
    console.log(`   使用的方法: ${Array.from(this.usedMethods).join(', ')}`);
    console.log(`   详细报告: ${reportPath}`);
  }

  // 获取优化建议
  getRecommendations() {
    const recommendations = [];
    
    if (this.usedMethods.size < 10) {
      recommendations.push('建议使用按需引入: import method from "lodash/method"');
    }
    
    if (this.usedMethods.has('cloneDeep')) {
      recommendations.push('考虑使用原生 structuredClone() 替代 cloneDeep (现代浏览器支持)');
    }
    
    if (this.usedMethods.has('debounce')) {
      recommendations.push('可以考虑自实现 debounce 函数，减少依赖');
    }

    return recommendations;
  }

  run() {
    this.analyzeLodashUsage();
  }
}

// 命令行执行
if (require.main === module) {
  const analyzer = new LodashAnalyzer();
  analyzer.run();
}

module.exports = LodashAnalyzer;
