const fs = require('fs');
const path = require('path');
const glob = require('glob');

class UnusedImageCleaner {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.ico'];
    this.sourceExtensions = ['.js', '.vue', '.jsx', '.ts', '.tsx', '.scss', '.css', '.less', '.html'];
    this.excludeDirs = ['node_modules', 'dist', '.git', 'scripts'];
    
    this.usedImages = new Set();
    this.allImages = new Set();
    this.unusedImages = new Set();
  }

  // 获取所有图片文件
  getAllImages() {
    console.log('📸 扫描所有图片文件...');
    
    const imagePatterns = this.imageExtensions.map(ext => 
      `**/*${ext}`
    );
    
    imagePatterns.forEach(pattern => {
      const files = glob.sync(pattern, {
        cwd: this.projectRoot,
        ignore: this.excludeDirs.map(dir => `${dir}/**`),
        absolute: true
      });
      
      files.forEach(file => {
        const relativePath = path.relative(this.projectRoot, file);
        this.allImages.add(relativePath);
      });
    });

    console.log(`找到 ${this.allImages.size} 个图片文件`);
  }

  // 获取所有源代码文件
  getAllSourceFiles() {
    const sourcePatterns = this.sourceExtensions.map(ext => 
      `**/*${ext}`
    );
    
    let sourceFiles = [];
    sourcePatterns.forEach(pattern => {
      const files = glob.sync(pattern, {
        cwd: this.projectRoot,
        ignore: this.excludeDirs.map(dir => `${dir}/**`),
        absolute: true
      });
      sourceFiles = sourceFiles.concat(files);
    });

    return sourceFiles;
  }

  // 在文件内容中查找图片引用
  findImageReferences(content) {
    const references = new Set();
    
    // 各种引用模式
    const patterns = [
      // import 语句
      /import\s+.*?from\s+['"`]([^'"`]*\.(?:png|jpg|jpeg|gif|svg|webp|ico))['"`]/gi,
      // require 语句
      /require\s*\(\s*['"`]([^'"`]*\.(?:png|jpg|jpeg|gif|svg|webp|ico))['"`]\s*\)/gi,
      // CSS background
      /background[^:]*:\s*url\s*\(\s*['"`]?([^'"`\)]*\.(?:png|jpg|jpeg|gif|svg|webp|ico))['"`]?\s*\)/gi,
      // CSS background-image
      /background-image\s*:\s*url\s*\(\s*['"`]?([^'"`\)]*\.(?:png|jpg|jpeg|gif|svg|webp|ico))['"`]?\s*\)/gi,
      // HTML img src
      /<img[^>]+src\s*=\s*['"`]([^'"`]*\.(?:png|jpg|jpeg|gif|svg|webp|ico))['"`]/gi,
      // Vue template src
      /:src\s*=\s*['"`]([^'"`]*\.(?:png|jpg|jpeg|gif|svg|webp|ico))['"`]/gi,
      // 字符串中的路径
      /['"`]([^'"`]*\.(?:png|jpg|jpeg|gif|svg|webp|ico))['"`]/gi,
      // CDN 或 HTTP 链接
      /https?:\/\/[^'"`\s]*\.(?:png|jpg|jpeg|gif|svg|webp|ico)/gi
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        references.add(match[1]);
      }
    });

    return references;
  }

  // 标准化路径
  normalizePath(imagePath) {
    // 处理相对路径
    if (imagePath.startsWith('./') || imagePath.startsWith('../')) {
      return imagePath;
    }
    
    // 处理 @ 别名
    if (imagePath.startsWith('@/')) {
      return imagePath.replace('@/', 'src/');
    }
    
    // 处理 ~ 别名
    if (imagePath.startsWith('~/')) {
      return imagePath.replace('~/', '');
    }
    
    // 处理绝对路径
    if (imagePath.startsWith('/')) {
      return imagePath.substring(1);
    }
    
    return imagePath;
  }

  // 解析相对路径
  resolveRelativePath(basePath, imagePath) {
    const baseDir = path.dirname(basePath);
    const resolved = path.resolve(this.projectRoot, baseDir, imagePath);
    return path.relative(this.projectRoot, resolved);
  }

  // 扫描源代码文件
  scanSourceFiles() {
    console.log('🔍 扫描源代码文件中的图片引用...');
    
    const sourceFiles = this.getAllSourceFiles();
    let processedFiles = 0;

    sourceFiles.forEach(filePath => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const references = this.findImageReferences(content);
        const relativePath = path.relative(this.projectRoot, filePath);

        references.forEach(imagePath => {
          const normalizedPath = this.normalizePath(imagePath);
          
          // 如果是相对路径，需要解析
          if (normalizedPath.startsWith('./') || normalizedPath.startsWith('../')) {
            const resolvedPath = this.resolveRelativePath(relativePath, normalizedPath);
            this.usedImages.add(resolvedPath);
          } else {
            this.usedImages.add(normalizedPath);
          }
        });

        processedFiles++;
        if (processedFiles % 100 === 0) {
          console.log(`已处理 ${processedFiles}/${sourceFiles.length} 个文件`);
        }
      } catch (error) {
        console.warn(`⚠️  读取文件失败: ${filePath}`, error.message);
      }
    });

    console.log(`✅ 扫描完成，找到 ${this.usedImages.size} 个被引用的图片`);
  }

  // 查找未使用的图片
  findUnusedImages() {
    console.log('🗑️  查找未使用的图片...');
    
    this.allImages.forEach(imagePath => {
      const isUsed = Array.from(this.usedImages).some(usedPath => {
        // 标准化路径比较
        const normalizedUsed = path.normalize(usedPath);
        const normalizedImage = path.normalize(imagePath);
        
        return normalizedUsed === normalizedImage || 
               normalizedUsed.endsWith(normalizedImage) ||
               normalizedImage.endsWith(normalizedUsed);
      });

      if (!isUsed) {
        this.unusedImages.add(imagePath);
      }
    });

    console.log(`❌ 找到 ${this.unusedImages.size} 个未使用的图片`);
  }

  // 生成报告
  generateReport() {
    const reportPath = path.join(this.projectRoot, 'unused-images-report.json');
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalImages: this.allImages.size,
        usedImages: this.usedImages.size,
        unusedImages: this.unusedImages.size
      },
      unusedImages: Array.from(this.unusedImages).sort(),
      usedImages: Array.from(this.usedImages).sort()
    };

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`📊 报告已生成: ${reportPath}`);
    
    return report;
  }

  // 删除未使用的图片
  deleteUnusedImages(dryRun = true) {
    if (this.unusedImages.size === 0) {
      console.log('✨ 没有找到未使用的图片文件');
      return;
    }

    console.log(`${dryRun ? '🔍 预览' : '🗑️  删除'} ${this.unusedImages.size} 个未使用的图片:`);
    
    let deletedCount = 0;
    let deletedSize = 0;

    this.unusedImages.forEach(imagePath => {
      const fullPath = path.join(this.projectRoot, imagePath);
      
      try {
        const stats = fs.statSync(fullPath);
        deletedSize += stats.size;
        
        console.log(`${dryRun ? '  - ' : '  ✅ '}${imagePath} (${(stats.size / 1024).toFixed(2)} KB)`);
        
        if (!dryRun) {
          fs.unlinkSync(fullPath);
          deletedCount++;
        }
      } catch (error) {
        console.warn(`⚠️  处理文件失败: ${imagePath}`, error.message);
      }
    });

    const totalSizeMB = (deletedSize / 1024 / 1024).toFixed(2);
    
    if (dryRun) {
      console.log(`\n📊 预计可释放空间: ${totalSizeMB} MB`);
      console.log('💡 运行 npm run clean:images --delete 来实际删除文件');
    } else {
      console.log(`\n✅ 已删除 ${deletedCount} 个文件，释放空间: ${totalSizeMB} MB`);
    }
  }

  // 主执行方法
  async run(options = {}) {
    const { delete: shouldDelete = false } = options;
    
    console.log('🚀 开始扫描未使用的图片文件...\n');
    
    try {
      this.getAllImages();
      this.scanSourceFiles();
      this.findUnusedImages();
      
      const report = this.generateReport();
      
      if (this.unusedImages.size > 0) {
        this.deleteUnusedImages(!shouldDelete);
      }
      
      console.log('\n✨ 扫描完成!');
      
    } catch (error) {
      console.error('❌ 扫描过程中出现错误:', error);
      process.exit(1);
    }
  }
}

// 命令行执行
if (require.main === module) {
  const shouldDelete = process.argv.includes('--delete');
  const cleaner = new UnusedImageCleaner();
  cleaner.run({ delete: shouldDelete });
}

module.exports = UnusedImageCleaner;
