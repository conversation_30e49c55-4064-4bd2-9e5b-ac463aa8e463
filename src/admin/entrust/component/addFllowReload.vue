<template>
  <div class="addFllowReload">
    <el-form 
      ref="ruleForm" 
      :model="ruleForm" 
      :rules="rules" 
      label-width="105px" 
      label-position="left"
      size="small"
      class="demo-ruleForm"
    >
      <el-form-item
        label="跟进日期："
        prop="followUpDate"
        class="appendToBodyFalse"
      >
        <el-date-picker
        :append-to-body="false"
          v-model="ruleForm.followUpDate"
          type="date"
          placeholder="选择日期"
          :picker-options="pickerOptions"
        />
      </el-form-item>
      <el-form-item
        label="跟进人："
        prop="followUpPerson"
      >
        <el-input v-model="ruleForm.followUpPerson" />
      </el-form-item>
      <el-form-item
        label="企业对接人："
        prop="enterpriseContactPerson"
      >
        <el-input v-model="ruleForm.enterpriseContactPerson" />
      </el-form-item>
      <el-form-item
        label="联系方式："
        prop="enterpriseContactInformation"
      >
        <el-input v-model="ruleForm.enterpriseContactInformation" />
      </el-form-item>
      <el-form-item
        label="跟进概况："
        prop="followUpOverview"
      >
        <el-input
          v-model="ruleForm.followUpOverview"
          type="textarea"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="submitForm('ruleForm')"
        >
          保存
        </el-button>
        <el-button @click="resetForm('ruleForm')">
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { addRecord } from '../../apiUrl'
  export default {
    name: 'AddFllowReload',
    props: {
      id: {
        type: String,
        default: null
      },
      info: {
        type: Object,
        default: null
      }
    },
    data() {
      return {
        ruleForm: {
          investmentEntrustTaskId:'',
          followUpDate:'',
          followUpPerson:'',
          enterpriseContactPerson:'',
          enterpriseContactInformation:'',
          followUpOverview:'',
        },
        rules: {
          followUpDate: [
            { type: 'date', required: true, message: '请选择日期', trigger: 'change' }
          ],
          followUpPerson: [
            { required: true, message: '请输入跟进人名称', trigger: 'blur' },
          ],
          enterpriseContactPerson: [
            { required: true, message: '请输入企业对接人', trigger: 'blur' },
          ],
          enterpriseContactInformation: [
            { required: true, message: '请输入联系方式', trigger: 'blur' },
          ],
          followUpOverview: [
            { required: true, message: '请输入跟进概况', trigger: 'blur' },
          ]
        },
        pickerOptions:{
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
        }
      }
    },
    created () {
      // console.log('this.info.entrustTimeStamp', this.info.entrustTimeStamp)
      let entrustTimeStamp = this.info.entrustTimeStamp;
      let oneDay = 24*60*60*1000;
      this.pickerOptions={
        disabledDate(time) {
          return time.getTime() > Date.now() || time.getTime() < entrustTimeStamp - oneDay;
        },
      };
    },
    methods: {
      // 添加记录
      addRecord() {
        let data = this.ruleForm;
        data.investmentEntrustTaskId = this.id;
        data.followUpDate = +new Date(data.followUpDate);
        addRecord(data).then(()=>{
          this.$message.success('添加跟进记录成功！')
          this.$emit('init')
        });
      },
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.addRecord()
          } else {
            // console.log('error submit!!');
            return false;
          }
        });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      }
    },
  }
</script>

<style lang="scss" scoped>
  .addFllowReload{
    border: 1px solid #bbb;
    padding: 15px 25px;
    ::v-deep{
      .el-form-item__label{
        font-size: 12px;
      }
      .el-input--mini, .el-date-editor.el-input{
        width: 180px;
      }
    }
  }
</style>