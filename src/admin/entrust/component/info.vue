<template>
  <div class="info">
    <div class="close">
      <i
        class="el-icon-circle-close"
        @click="close"
      />
    </div>
    <div class="title">
      <div class="label">
        委托任务详情
      </div>
      <el-button
        type="primary"
        size="mini"
        style="margin-right:60px;"
        @click="followUp"
      >
        跟进
      </el-button>
    </div>
    <div class="info-con">
      <div class="info-con-list">
        <div class="label">
          委托企业名称：
        </div>
        <div class="txt">
          {{ infoData.enterpriseName }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          统一信用代码：
        </div>
        <div class="txt">
          {{ infoData.unifiedSocialCreditCode }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          产业链：
        </div>
        <div class="txt">
          {{ infoData.chainName }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          委托机构：
        </div>
        <div class="txt">
          {{ infoData.orgName }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          委托人：
        </div>
        <div class="txt">
          {{ infoData.entrustPerson }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          委托人所在部门：
        </div>
        <div class="txt">
          {{ infoData.deptName }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          委托人联系电话：
        </div>
        <div class="txt">
          {{ infoData.mobile }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          委托时间：
        </div>
        <div class="txt">
          {{ infoData.entrustTimeStamp | time }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          任务剩余时间：
        </div>
        <div class="txt">
          <span v-if="infoData.followUpStatus==2">{{ infoData.finishRemainTimeStamp | timeStamp }}</span>
          <span v-else>{{ infoData.surplusTimeStamp | timeStamp }}</span>
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          跟进状态：
        </div>
        <div class="txt">
          <span v-if="infoData.followUpStatus==0">未开始</span>
          <span v-if="infoData.followUpStatus==1">进行中</span>
          <span v-if="infoData.followUpStatus==2">已完成</span>
          <span v-if="infoData.followUpStatus==3">已延期</span>
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          委托备注：
        </div>
        <div class="txt">
          {{ infoData.remark }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { entrustDetail } from '../../apiUrl'
  import { formatDate } from '@/utils/utils'
  export default {
    name: 'InfoDrawer',
    filters: {
      time: function(value) {
        return formatDate('yyyy-MM-dd HH:mm:ss', new Date(+value));
      },
      timeStamp: function(val){
        // console.log(val)
        let date = val/(60*60*1000*24)
        let d = parseInt(val/(60*60*1000*24))
        let h = parseInt((date%1)*24)
        let s = parseInt((((date%1)*24)%1)*60);
        let str = d + '天' + Math.abs(h) + '时' + Math.abs(s) + '分'
        if(d<0 || h<0 || s<0){
          str = '-' + Math.abs(d) + '天' + Math.abs(h) + '时' + Math.abs(s) + '分'
        }
        return str;
      }
    },
    props: {
      id: {
        type: String,
        default: null
      },
    },
    data() {
      return {
        infoData: {}
      }
    },
    created () {
      this.getEntrustDetail();
    },
    methods: {
      getEntrustDetail() {
        entrustDetail({id: this.id}).then(res=>{
          this.infoData = res;
        })
      },
      followUp(){
        this.$emit('followUp', this.infoData)
      },
      close() {
        this.$emit('close');
      },
    },
  }
</script>

<style lang="scss" scoped>
  .info{
    padding: 0 12px;
    position: relative;
    .close{
      width: 30px;
      height: 30px;
      position: absolute;
      right: 10px;
      top: 7px;
      z-index: 999999;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .el-icon-circle-close{
        font-size: 24px;
      }
    }
    .title{
      position: relative;
      padding-left: 35px;
      border-bottom: 1px solid #cacaca;
      line-height: 45px;
      font-size: 14px;
      color: #101010;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &::before{
        content: '';
        width: 5px;
        height: 18px;
        background: rgba(20, 66, 119, 0.98);
        position: absolute;
        top: 15px;
        left: 15px;
      }
      .el-button--mini{

      }
    }
    &-con{
      padding-top: 15px;
      &-list{
        display: flex;
        font-size: 12px;
        line-height: 42px;
        .label{
          width: 100px;
          padding-left: 60px;
          box-sizing: content-box;
        }
        .txt{
          color: #212121;
        }
      }
    }
  }
</style>