<template>
  <div class="entrust">
    <div class="entrust-con">
      <table-layout :tab-name-list="['委托任务管理']">
        <el-form
          slot="elForm"
          :inline="true"
          :model="form"
          class="entrust-form"
          label-width="80px"
          size="mini"
        >
          <el-form-item label="企业名称">
            <el-input 
              v-model="form.enterpriseName"
              placeholder="企业名称"
            />
          </el-form-item>
          <el-form-item
            label="委托机构"
            class="appendToBodyFalse"
          >
            <el-select
              v-model="form.orgIds"
              :popper-append-to-body="false"
              multiple
              collapse-tags
              placeholder="委托机构"
            >
              <el-option
                v-for="(item,k) in institutionList"
                :key="k"
                :label="item.orgName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="委托人">
            <el-input
              v-model="form.entrustPerson"
              placeholder="委托人"
            />
          </el-form-item>
          <el-form-item label="产业链">
            <el-select
              v-model="form.chainIds"
              multiple
              collapse-tags
              placeholder="产业链"
            >
              <el-option
                v-for="(item,k) in chainList"
                :key="k"
                :label="item.chainName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="委托时间">
            <el-date-picker
              v-model="entrustTime"
              unlink-panels
              type="daterange"
              range-separator=""
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item label="跟进状态">
            <el-select
              v-model="form.followUpStatusList"
              multiple
              collapse-tags
              placeholder="跟进状态"
            >
              <el-option
                v-for="(item,i) in typeList"
                :key="i"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="onSubmit"
            >
              查询
            </el-button>
            <el-button @click="resetForm('form')">
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <div
          slot="selTable"
          class="entrust-table"
        >
          <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
            size="mini"
            @sort-change="sortChange"
          >
            <el-table-column
              prop="enterpriseName"
              label="委托企业名称"
              align="center"
            />
            <el-table-column
              prop="chainName"
              label="产业链"
              align="center"
            />
            <el-table-column
              prop="orgName"
              label="委托机构"
              align="center"
            />
            <el-table-column
              prop="entrustPerson"
              label="委托人"
              align="center"
            />
            <el-table-column
              prop="entrustTimeStamp"
              label="委托时间"
              align="center"
              sortable="custom"
            >
              <template slot-scope="{ row }">
                {{ row.entrustTimeStamp | time }}
              </template>
            </el-table-column>
            <el-table-column
              prop="surplusTimeStamp"
              label="剩余时间"
              align="center"
              sortable="custom"
            >
              <template slot-scope="{ row }">
                <span v-if="row.followUpStatus==2">                
                  {{ row.finishRemainTimeStamp | timeStamp }}
                </span>
                <span v-else>
                  {{ row.surplusTimeStamp | timeStamp }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="跟进状态"
            >
              <template slot-scope="{ row }">
                <span v-if="row.followUpStatus==0">未开始</span>
                <span v-if="row.followUpStatus==1">进行中</span>
                <span v-if="row.followUpStatus==2">已完成</span>
                <span v-if="row.followUpStatus==3">已延期</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              width="180"
              label="操作"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="detail(scope.row)"
                >
                  详情
                </el-button>
                <el-divider direction="vertical" />
                <el-button
                  type="text"
                  @click="followUp(scope.row)"
                >
                  跟进
                </el-button>
                <el-divider direction="vertical" />
                <el-button
                  v-if="scope.row.followUpStatus==2"
                  type="text"
                  disabled
                >
                  结束
                </el-button>
                <el-popconfirm
                  v-else
                  title="确定结束？"
                  @confirm="endTask(scope.row)"
                >
                  <el-button
                    slot="reference"
                    type="text"
                  >
                    结束
                  </el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </table-layout>
      <div class="ye">
        <el-pagination
          :current-page.sync="page.pageNum"
          :page-sizes="[5, 10, 20, 50]"
          :page-size.sync="page.pageSize"
          :total="+total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="getEntrustList"
          @current-change="getEntrustList"
        />
      </div>

      <!-- drawer start -->
      <el-drawer
        :visible.sync="drawer"
        :with-header="false"
        :wrapper-closable="false"
      >
        <info
          v-if="drawerStatus=='followIn'"
          :id="infoData.id"
          @followUp="followUp"
          @close="close"
        />
        <followUp
          v-if="drawerStatus=='followUp'"
          :id="infoData.id"
          :info="infoData"
          @close="close"
        />
      </el-drawer>
      <!-- drawer end -->
    </div>
  </div>
</template>

<script>
  import TableLayout from "@/common/components/table-layout";
  import { getEntrustList, entrustEndTask, industryChainList, institutionList } from './../apiUrl'
  import { formatDate, filterData } from '@/utils/utils'
  import info from './component/info.vue'
  import followUp from './component/followUp.vue'
  export default {
    // eslint-disable-next-line vue/multi-word-component-names
    name: 'Entrust',
    components: {
      info,
      followUp,
      TableLayout,
    },
    filters: {
      time: function(value) {
        return formatDate('yyyy-MM-dd', new Date(+value));
      },
      timeStamp: function(val){
        let date = val/(60*60*1000*24)
        let d = parseInt(val/(60*60*1000*24))
        let h = parseInt((date%1)*24)
        let s = parseInt((((date%1)*24)%1)*60)
        let str = d + '天' + Math.abs(h) + '时' + Math.abs(s) + '分'
        if(d<0 || h<0 || s<0){
          str = '-' + Math.abs(d) + '天' + Math.abs(h) + '时' + Math.abs(s) + '分'
        }
        return str;
      }
    },
    data() {
      return {
        drawer: false,
        drawerStatus: '',
        loading: false,
        entrustTime: [], // 委托时间 
        form: {
          enterpriseName: '', // 企业名称
          orgIds: [], // 委托机构
          chainIds: [], // 产业链
          entrustPerson: '', // 委托人
          followUpStatusList: '', // 跟进状态集合
          startTimeStamp: '', // 委托开始时间戳
          endTimeStamp: '', // 委托结束时间戳
          entrustTimeOrderAsc: '', // 是否以委托时间升序排序
          maturityTimeOrderAsc: '', // 是否以剩余时间升序排序
        },
        typeList: [
          {
            label: '全部',
            value: ''
          },
          {
            label: '未开始',
            value: '0'
          },{
            label: '进行中',
            value: '1'
          },{
            label: '已完成',
            value: '2'
          },{
            label: '延期',
            value: '3'
          }
        ],
        page: {
          pageNum: 1,
          pageSize: 10,
        },
        total: 0,
        tableData:[],
        infoData: {}, // 详情
        chainList: [], // 产业链列表
        institutionList: [], // 机构列表
      }
    },
    created () {
      this.getEntrustList();
      this.industryChainList();
      this.getInstitutionList();
    },
    methods: {
      // 机构列表
      getInstitutionList(){
        // let data = {
        //   orgName: '',
        //   primaryContactName: '',
        //   accountType: ''
        // }
        institutionList().then(res=>{
          this.institutionList = res;
        })
      },
      // 获取产业链列表列表
      industryChainList(){
        industryChainList().then(res=>{
          this.chainList = res;
        })
      },
      // 获取任务列表
      getEntrustList(){
        let data = {
          ...this.page,
          ...this.form,
        }
        if(this.entrustTime && this.entrustTime.length>0){
          data.startTimeStamp = +new Date(this.entrustTime[0])
          data.endTimeStamp = +new Date(this.entrustTime[1])
          if(data.startTimeStamp == data.endTimeStamp) {
            data.endTimeStamp = data.endTimeStamp + 24*60*60*1000
          }
        }
        if(data.followUpStatusList.includes('')){
          delete data.followUpStatusList
        }
        filterData(data);
        getEntrustList(data).then(res=>{
          this.tableData = res.records;
          this.total = res.total;
        })
      },
      // 详情
      detail(e){
        this.drawer = true;
        this.infoData = e;
        this.drawerStatus = 'followIn';
      },
      // 结束任务
      endTask(e){
        entrustEndTask({id: e.id}).then(()=>{
          this.$message.success('结束任务成功！');
          this.getEntrustList();
        });
      },
      // 跟进
      followUp(e){
        this.drawer = true;
        this.infoData = e;
        this.drawerStatus = 'followUp';
      },
      // 查询
      onSubmit() {
        this.getEntrustList();
      },
      // 重置
      resetForm() {
        // this.$refs[formName].resetFields();
        this.form= {
          enterpriseName: '', // 企业名称
          orgIds: [], // 委托机构
          chainIds: [], // 产业链
          entrustPerson: '', // 委托人
          followUpStatusList: '', // 跟进状态集合
          startTimeStamp: '', // 委托开始时间戳
          endTimeStamp: '', // 委托结束时间戳
          entrustTimeOrderAsc: '', // 是否以委托时间升序排序
          maturityTimeOrderAsc: '', // 是否以剩余时间升序排序
        }
        this.entrustTime = [];
        this.page= {
          pageNum: 1,
          pageSize: 10,
        },
        this.getEntrustList();
      },
      // 分页跳转
      SettledList(val) {
        this.page.pageNum = val;
        this.getEntrustList();
      },
      // 排序
      sortChange({  order, prop }) {
        if(order == "descending"){
          if(prop == 'entrustTimeStamp'){
            this.form.entrustTimeOrderAsc = false;
          }
          if(prop == 'surplusTimeStamp'){
            this.form.maturityTimeOrderAsc = false;
          }
        } else if(order == "ascending"){
          if(prop == 'entrustTimeStamp'){
            this.form.entrustTimeOrderAsc = true;
          }
          if(prop == 'surplusTimeStamp'){
            this.form.maturityTimeOrderAsc = true;
          }
        } else {
          if(prop == 'entrustTimeStamp'){
            this.form.entrustTimeOrderAsc = '';
          }
          if(prop == 'surplusTimeStamp'){
            this.form.maturityTimeOrderAsc = '';
          }
        }
        this.getEntrustList();
      },
      close() {
        this.drawer = false;
        this.drawerStatus = '';
      },
    },
  }
</script>

<style lang="scss" scoped>
  .entrust{
    // padding: 8px 20px;
    // background: #F7F8FA;
    min-height: calc(100vh - 84px);
    &-form{
      padding: 20px;
      background: #fff;
      box-shadow: 0px 4px 12px 0px #EEF1F8;
      border-radius: 10px;
      ::v-deep{
        .el-form-item{
          width: auto !important;
        }
        .el-date-editor--daterange.el-input__inner{
          width: 250px !important;
        }
        .el-select__tags{
          max-width: 165px !important;
        }
      }
    }
    &-table{
      margin: 20px 0;
    }
    .ye{
      position: relative;
      height: 50px;
      background: #fff;
      .el-pagination{
        padding-right: 20px;
        bottom: 15px !important;
      }
    }
  }
</style>