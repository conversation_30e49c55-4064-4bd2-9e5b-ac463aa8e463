import interfaceRequest from '@/utils/interfaceRequest'
import { 
  apiUrl, 
} from '@/api/user'
import axios from 'axios';
import store from '@/store'
import { getToken } from "@/utils/auth"; // get token from cookie

const apiDomain = apiUrl;
export const chartApi = {
  entrustList: apiDomain + '/admin/investment/entrust/task/pageList', // 委托任务列表
  entrustDetail: apiDomain + '/admin/investment/entrust/task/detail', // 委托任务详情
  entrustRecordList: apiDomain + '/admin/investment/entrust/task/recordList', // 获取指定委托任务的跟进记录列表
  entrustEndTask: apiDomain + '/admin/investment/entrust/task/endTask', // 结束委托任务
  addRecord: apiDomain + '/admin/investment/entrust/task/addRecord', // 新增指定委托任务的跟进记录

  industryChainList: apiDomain + '/admin/industryChain/getAll', // 产业链列表
  institutionList: apiDomain + '/admin/institution/getAll', // 机构列表

  linkPlaceList: apiDomain + '/admin/industryChain/linkPlaceList', // 产业环节下拉框
  listByType: apiDomain + '/admin/keyword/dictionary/listByType', // 推荐理由

  // 招商推荐
  'zstjList': apiDomain + '/admin/investment/recommendation/pageList',// 招商推荐列表
  'zstjDetail': apiDomain + '/admin/investment/recommendation/detail',// 招商推荐详情
  'zstjAdd': apiDomain + '/admin/investment/recommendation/add',// 新增招商推荐记录
  'zstjUpdate': apiDomain + '/admin/investment/recommendation/update',// 招商推荐编辑
  'getByName': apiDomain + '/admin/enterprise/getByName',// 获取一条指定名称的企业信息
  'offline': apiDomain + '/admin/investment/recommendation/offline',// 招商推荐下线
  'zstjAudit': apiDomain + '/admin/investment/recommendation/audit',// 下线招商资讯
  'zstjUpload':  process.env.VUE_APP_PORT_URL   + '/admin/investment/recommendation/upload',// 上传批量导入招商推荐excel文件
  'zstjDownloadExcelModel': apiDomain + '/admin/investment/recommendation/downloadExcelModel',// 下载上传招商推荐excel模板
  'getBeforeOrAfterById': apiDomain + '/admin/investment/recommendation/getBeforeOrAfterById',// 获取指定招商推荐的上一条或下一条
  'zstjAuditList': apiDomain + '/admin/investment/recommendation/auditList',// 获取指定推荐记录的审核记录列表
  


  // 招商资讯
  'zszxList': apiDomain + '/admin/investment/information/pageList',// 招商资讯列表
  'zszxAdd': apiDomain + '/admin/investment/information/add',// 新增招商资讯
  'zszxDetail': apiDomain + '/admin/investment/information/detail',// 招商资讯详情
  'zszxOffline': apiDomain + '/admin/investment/information/offline',// 下线招商资讯
  'zszxUpdate': apiDomain + '/admin/investment/information/update',// 下线招商资讯
  'zszxUpload':  process.env.VUE_APP_PORT_URL   + '/admin/investment/information/upload',// 上传招商资讯excel文件
  'downloadExcelModel': apiDomain + '/admin/investment/information/downloadExcelModel',// 下载招商资讯excel模板
  'zszxGetBeforeOrAfterById': apiDomain + '/admin/investment/information/getBeforeOrAfterById',// 获取指定招商资讯的上一条或下一条

  
}

// 获取指定招商资讯的上一条或下一条
export function zstjAuditList(data) {
  return interfaceRequest({
    url: chartApi.zstjAuditList,
    method: 'GET',
    params: { ...data }
  })
}
// 获取指定招商推荐的上一条或下一条
export function getBeforeOrAfterById(data) {
  return interfaceRequest({
    url: chartApi.getBeforeOrAfterById,
    method: 'GET',
    params: { ...data }
  })
}
// 获取指定招商资讯的上一条或下一条
export function zszxGetBeforeOrAfterById(data) {
  return interfaceRequest({
    url: chartApi.zszxGetBeforeOrAfterById,
    method: 'GET',
    params: { ...data }
  })
}
// 获取招商资讯详情
export function zszxDetail(data) {
  return interfaceRequest({
    url: chartApi.zszxDetail,
    method: 'GET',
    params: { ...data }
  })
}
// 获取一条指定名称的企业信息
export function getByName(data) {
  return interfaceRequest({
    url: chartApi.getByName,
    method: 'GET',
    params: { ...data }
  })
}
// 招商推荐详情
export function zstjDetail(data) {
  return interfaceRequest({
    url: chartApi.zstjDetail,
    method: 'GET',
    params: { ...data }
  })
}
// 推荐理由
export function listByType(data) {
  return interfaceRequest({
    url: chartApi.listByType,
    method: 'GET',
    params: { ...data }
  })
}
// 产业环节下拉框
export function linkPlaceList(data) {
  return interfaceRequest({
    url: chartApi.linkPlaceList,
    method: 'GET',
    params: { ...data }
  })
}
// 产业链列表
export function industryChainList(data) {
  return interfaceRequest({
    url: chartApi.industryChainList,
    method: 'GET',
    params: { ...data }
  })
}
// 获取指定委托任务的跟进记录列表
export function entrustRecordList(data) {
  return interfaceRequest({
    url: chartApi.entrustRecordList,
    method: 'GET',
    params: { ...data }
  })
}

// 委托任务详情
export function entrustDetail(data) {
  return interfaceRequest({
    url: chartApi.entrustDetail,
    method: 'GET',
    params: { ...data }
  })
}
//   委托任务列表
export function getEntrustList(data) {
  return interfaceRequest({
    url: chartApi.entrustList,
    method: 'POST',
    data
  })
}

//   结束委托任务
export function entrustEndTask(data) {
  return interfaceRequest({
    url: chartApi.entrustEndTask,
    method: 'POST',
    data
  })
}
//   新增指定委托任务的跟进记录
export function addRecord(data) {
  return interfaceRequest({
    url: chartApi.addRecord,
    method: 'POST',
    data
  })
}
//   招商推荐审核
export function zstjAudit(data) {
  return interfaceRequest({
    url: chartApi.zstjAudit,
    method: 'POST',
    data
  })
}








//   招商推荐列表
export function zstjList(data) {
  return interfaceRequest({
    url: chartApi.zstjList,
    method: 'POST',
    data
  })
}
//   机构列表
export function institutionList(data) {
  return interfaceRequest({
    url: chartApi.institutionList,
    method: 'GET',
    params: { ...data }
  })
}

//   新增招商推荐记录
export function zstjAdd(data) {
  return interfaceRequest({
    url: chartApi.zstjAdd,
    method: 'POST',
    data
  })
}
//   招商推荐下线
export function offline(data) {
  return interfaceRequest({
    url: chartApi.offline,
    method: 'POST',
    data
  })
}

//   下线招商资讯
export function zszxOffline(data) {
  return interfaceRequest({
    url: chartApi.zszxOffline,
    method: 'POST',
    data
  })
}

//  招商推荐编辑
export function zstjUpdate(data) {
  return interfaceRequest({
    url: chartApi.zstjUpdate,
    method: 'POST',
    data
  })
}

// 获取招商资讯列表
export function zszxList(data) {
  return interfaceRequest({
    url: chartApi.zszxList,
    method: 'POST',
    data
  })
}
// 新增招商资讯
export function zszxAdd(data) {
  return interfaceRequest({
    url: chartApi.zszxAdd,
    method: 'POST',
    data
  })
}

// 编辑招商资讯
export function zszxUpdate(data) {
  return interfaceRequest({
    url: chartApi.zszxUpdate,
    method: 'POST',
    data
  })
}

// 上传招商资讯excel文件
export function zszxUpload(data) {
  return interfaceRequest({
    url: chartApi.zszxUpload,
    method: 'POST',
    data
  })
}

// 下载上传招商推荐excel模板
export function zstjUpload(data) {
  return interfaceRequest({
    url: chartApi.zszxUpload,
    method: 'POST',
    data
  })
}

// 下载招商资讯excel模板
// export function downloadExcelModel(data) {
//   return interfaceRequest({
//     url: chartApi.downloadExcelModel,
//     method: 'POST',
//     data,
//     responseType: 'blob' || '',
//   })
// }

// 文件下载
export function downloadExcelModel(data) {
  let dUrl = chartApi.downloadExcelModel;
  return axios({
    method: 'post',
    url: dUrl,
    headers: {
      'Content-Type': 'application/json',
      'token': getToken()
    },
    data: data, // 参数
    responseType: 'blob' || '',
  })
}
export function zstjDownloadExcelModel(data) {
  let dUrl = chartApi.zstjDownloadExcelModel;
  return axios({
    method: 'post',
    url: dUrl,
    headers: {
      'Content-Type': 'application/json',
      'token': getToken()
      //  store.getters.user.token||JSON.parse(localStorage.getItem('TOKEN'))?.value
    },
    data: data, // 参数
    responseType: 'blob' || '',
  })
}
//通过亲商人姓名模糊匹配出对应的列表集合
export function listNameFuzzyMatchingAPI(data) {
  return interfaceRequest({
    url: '/admin/personal/information/listNameFuzzyMatching',
    method: 'GET',
    params: { ...data }
  })
}
// 批量复制推荐企业至新机构
export function batchReplicationAPI(data) {
  return interfaceRequest({
    url: '/admin/investment/recommendation/batchReplication',
    method: 'POST',
    data
  })
}
