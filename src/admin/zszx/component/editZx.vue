<template>
  <div class="edit">
    <div class="close">
      <i
        class="el-icon-circle-close"
        @click="close"
      />
    </div>
    <div class="title">
      <div class="label">
        招商资讯编辑
      </div>
      <div class="btn-con">
        <el-button
          type="primary"
          size="mini"
          class="prev"
          @click="eventClick('1')"
        >
          上一条
        </el-button>
        <el-button
          type="primary"
          size="mini"
          class="next"
          @click="eventClick('2')"
        >
          下一条
        </el-button>
      </div>
    </div>
    <div class="edit-con">
      <div class="left">
        <div class="top">
          <div class="txt1">
            {{ infoData.title }}
          </div>
          <div class="txt2">
            发布日期： {{ infoData.releaseDate }} 
            <span> 来源： {{ infoData.source }} </span>
          </div>
        </div>
      </div>
      <div class="right">
        <addFrom
          :id="id"
          :status="'edit'"
          :info-data="infoData"
          @close="close"
          @getList="getList" 
        />
      </div>
    </div>
  </div>
</template>

<script>
import { zszxDetail, zszxGetBeforeOrAfterById } from '../../apiUrl'
  // import { formatDate } from '@/utils/utils'
  import addFrom from './addFrom.vue'
  export default {
    name: 'EditZx',
    components: {
      addFrom,
    },
    props: {
        id: {
          type: String,
          default: null
        },
      },
    data() {
        return {
          newId: '',
          infoData: {}
        }
      },
      created () {
        this.getZszxDetail();
        this.newId = this.id;
      },
      methods: {
        getZszxDetail() {
          zszxDetail({id: this.id}).then(res=>{
            this.infoData = res;
          })
        },
        close(){
          this.$emit('getList')
          this.$emit('close')
        },
        getList(){
          this.$emit('getList')
        },
        // 上一条下一条点击
        eventClick(e){
          let data = {
            id: this.newId,
            type: e
          }
          zszxGetBeforeOrAfterById(data).then(res=>{
            // this.getZszxDetail(res.id)
            if(res){
              this.infoData = res;
              this.newId = res.id;
            }else{
              if(e==1){
                this.$message('已经是第一条了')
              }else{
                this.$message('已经是最后一条了')
              }
              
            }
          })
        }
      },
  }
</script>

<style lang="scss" scoped>
  .edit{
    background: #fff;
    box-sizing: border-box;
    padding: 15px;
    border-radius: 20px;
    min-height: calc(100vh - 90px);
    position: relative;
    .close{
      width: 30px;
      height: 30px;
      position: absolute;
      right: 10px;
      top: 10px;
      z-index: 999999;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .el-icon-circle-close{
        font-size: 24px;
      }
    }
    .title{
      position: relative;
      padding-left: 35px;
      border-bottom: 1px solid #cacaca;
      line-height: 45px;
      font-size: 14px;
      color: #101010;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &::before{
        content: '';
        width: 5px;
        height: 18px;
        background: rgba(20, 66, 119, 0.98);
        position: absolute;
        top: 15px;
        left: 15px;
      }
      .btn-con{
        display: flex;
        margin-right: 110px;
        .btn{
          margin-right: 20px;
        }
      }
      .el-button--mini{

      }
    }
    &-con{
      display: flex;
      justify-content: space-between;
      padding-top: 10PX;
      .left{
        width: 60%;
        border-right: 1px solid #bbb;
        box-sizing: border-box;
        padding: 0 20px;
        .top{
          .txt1{
            font-size: 16px;
            line-height: 38px;
            color: rgba(1,118,255,1);
          }
          .txt2{
            font-size: 14px;
            padding-top: 10px;
            color: #000;
            span{
              margin-left: 30px;
            }
          }
        }
      }
      .right{
        width: 40%;
      }
    }
  }
</style>