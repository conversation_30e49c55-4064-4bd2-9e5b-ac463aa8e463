<template>
  <div class="addForm">
    <el-form 
      ref="ruleForm" 
      :model="ruleForm" 
      :rules="rules" 
      label-width="105px" 
      label-position="left"
      size="mini"
      class="demo-ruleForm"
    >
      <el-form-item
        label="新闻标题："
        prop="title"
      >
        <el-input
          v-model="ruleForm.title"
          placeholder="输入新闻资讯标题"
        />
      </el-form-item>
      <el-form-item
        label="链接："
        prop="url"
      >
        <el-input
          v-model="ruleForm.url"
          placeholder="输入资讯链接"
        />
      </el-form-item>
      <el-form-item
        label="发布日期："
        prop="releaseDate"
      >
        <el-date-picker
          v-model="ruleForm.releaseDate"
          type="date"
          placeholder="选择日期"
          :picker-options="pickerOptions"
        />
      </el-form-item>
      <el-form-item
        label="来源："
        prop="source"
      >
        <el-input
          v-model="ruleForm.source"
          placeholder="输入资讯来源"
        />
      </el-form-item>
      <el-form-item
        label="关联产业："
        prop="correlationIndustryList"
      >
        <el-select
          v-model="ruleForm.correlationIndustryList"
          multiple
          collapse-tags
          placeholder="关联产业"
        >
          <el-option
            v-for="(item,k) in chainList"
            :key="k"
            :label="item.chainName"
            :value="item.chainName"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="关联企业："
        prop="correlationEnterpriseList"
      >
        <el-input
          v-model="ruleForm.correlationEnterpriseList"
          placeholder="输入关联企业全称，多个用“ ; ”隔开"
        />
      </el-form-item>
      <el-form-item
        label="新闻主题："
        prop="newsThemeIds"
      >
        <el-select
          v-model="ruleForm.newsThemeIds"
          multiple
          collapse-tags
          placeholder="新闻主题"
        >
          <el-option
            v-for="(item,k) in newsList"
            :key="k"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="备注："
        prop="remark"
      >
        <el-input
          v-model="ruleForm.remark"
          type="textarea"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="resetForm('ruleForm')">
          取消
        </el-button>
        <el-button
          v-if="status=='edit'"
          @click="submitForm('ruleForm',2)"
        >
          保存未通过
        </el-button>
        <el-button
          type="primary"
          @click="submitForm('ruleForm',1)"
        >
          保存并发布
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
  
<script>
  import { deepClone } from '@/utils/utils'
  import { zszxAdd, listByType, 
    industryChainList,
    zszxUpdate 
  } from '../../apiUrl'
  export default {
    name: 'AddForm',
    props: {
      id: {
        type: String,
        default: null
      },
      infoData: {
        type: Object,
        default: null
      },
      status: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        enterpriseName: '',
        ruleForm: {
          title:'',
          url:'',
          releaseDate:'',
          source:'',
          correlationIndustryList:'',
          correlationEnterpriseList:'',
          newsThemeIds:'',
          remark:'',
        },
        rules: {
          title: [
            { required: true, message: '请输入新闻标题', trigger: 'blur' },
          ],
          url: [
            { required: true, message: '请输入新闻链接', trigger: 'blur' },
          ],
          releaseDate: [
            { type: 'date', required: true, message: '请选择发布日期', trigger: 'change' }
          ],
          source: [
            { required: true, message: '请输入来源', trigger: 'blur' },
          ],
          correlationIndustryList: [
            { required: true, message: '请选择', trigger: 'blur' },
          ],
          // correlationEnterpriseList: [
          //   { required: true, message: '请选择', trigger: 'change' },
          // ],
          newsThemeIds: [
            { required: true, message: '请选择', trigger: 'blur' },
          ],
          // remark: [
          //   { required: true, message: '', trigger: 'blur' },
          // ],
        },

        newsList: [], // 新闻主题
        chainList: [], // 产业链列表

        pickerOptions:{
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
        }
      }
    },
    watch: {
      infoData(val) {
        if(val){
          this.ruleForm = {
            title: val.title || '',
            url: val.url || '',
            releaseDate: (val.releaseDate && new Date(val.releaseDate)) || '',
            source: val.source || '',
            correlationIndustryList: '',
            correlationEnterpriseList: val.correlationEnterprises || '',
            newsThemeIds: (val.newsThemeIds && val.newsThemeIds.split(';')) || '',
            remark: val.remark || '',
          }
          if(val.correlationIndustry){
            this.ruleForm.correlationIndustryList = val.correlationIndustry.split(';')
          }
          // console.log('val', val);
          // console.log('this.ruleForm', this.ruleForm);
        }
      }
    },
    created () {
      this.getListByType();
      // this.getInstitutionList();
      this.industryChainList();
    },
    destroyed () {
      // console.log('destroyed');
    },
    methods: {
      // 获取产业链列表列表
      industryChainList(){
        industryChainList().then(res=>{
          // console.log('industryChainList',res)
          this.chainList = res;
        })
      },
      // 招商资讯新闻主题
      getListByType(){
        listByType({
          keywordType: 1, // 0未知 1招商资讯新闻主题 2招商情报推荐理由
        }).then(res=>{
          // console.log('listByType',res)
          this.newsList = res;
        })
      },
      // 添加记录
      zszxAdd(releaseStatus) {
        this.ruleForm.releaseDate = +new Date(this.ruleForm.releaseDate);
        let data = deepClone(this.ruleForm);
        // data.releaseDate = +new Date(data.releaseDate);
        if(data.correlationEnterpriseList){
          data.correlationEnterpriseList = data.correlationEnterpriseList.replace(/；/g,';');
          data.correlationEnterpriseList = data.correlationEnterpriseList.split(';');
        }else{
          data.correlationEnterpriseList = [];
        }
        
        // 编辑
        if(this.id){
          data.id = this.id;
          data.releaseStatus = releaseStatus;
          // console.log('data.newsThemeIds', data.newsThemeIds);
          zszxUpdate(data).then(()=>{
            this.$message.success('编辑成功！')
            this.$emit('close')
          });
          return;
        }
        // 新增
        zszxAdd(data).then(()=>{
          this.$message.success('添加成功！')
          this.$emit('getList')
        });
        
      },
      submitForm(formName,releaseStatus) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.zszxAdd(releaseStatus)
          } else {
            // console.log('error submit!!');
            return false;
          }
        });
      },
      resetForm() {
        // this.$refs[formName].resetFields();
        this.$emit('close')
      }
    },
  }
</script>

<style lang="scss" scoped>
  .addForm{
    padding: 15px 25px;
    ::v-deep{
      .el-form-item__label{
        font-size: 12px;
      }
      .el-input--mini, .el-date-editor.el-input{
        width: 250px;
      }
    }
  }
</style>