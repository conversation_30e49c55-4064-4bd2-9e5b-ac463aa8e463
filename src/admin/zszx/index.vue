<template>
  <div class="zszx">
    <div class="zszx-con">
      <div
        v-if="drawerStatus == 'edit'"
        class="zszx-2"
      >
        <editZx
          :id="infoData.id"
          @close="close"
          @getList="getZszxList" 
        />
      </div>
      <div
        v-else
        class="zszx-1"
      >
        <table-layout :tab-name-list="['招商资讯管理']">
          <el-form
            slot="elForm"
            :inline="true"
            :model="form"
            class="zszx-form"
            label-width="80px"
            size="mini"
          >
            <el-form-item label="新闻标题">
              <el-input 
                v-model="form.title"
                placeholder="请输入关键词"
              />
            </el-form-item>
            <el-form-item label="发布日期">
              <el-date-picker
                v-model="sellTime"
                type="daterange"
                unlink-panels
                range-separator=""
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item label="产业链">
              <el-select
                v-model="form.correlationIndustryList"
                multiple
                collapse-tags
                placeholder="产业链"
              >
                <el-option
                  v-for="item in chainList"
                  :key="item.id"
                  :label="item.chainName"
                  :value="item.chainName"
                />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="onSubmit"
              >
                查询
              </el-button>
              <el-button @click="resetForm('form')">
                重置
              </el-button>
              <el-button 
                type="primary" 
                icon="el-icon-plus"
                @click="addEvent"
              >
                新增
              </el-button>
            </el-form-item>
          </el-form>
          <div
            slot="selTable"
            class="zszx-table"
          >
            <el-table
              v-loading="loading"
              :data="tableData"
              style="width: 100%"
              size="mini"
              :default-sort="{prop: 'recommendedDate', order: 'descending'}"
            >
              <el-table-column
                prop="title"
                label="新闻标题"
                align="center"
              />
              <el-table-column
                prop="releaseDate"
                label="发布时间"
                align="center"
              />
              <el-table-column
                prop="source"
                label="来源"
                align="center"
              />
              <el-table-column
                prop="correlationIndustry"
                label="关联产业"
                align="center"
              />
              <el-table-column
                prop="releaseStatus"
                label="发布状态"
                align="center"
              >
                <template slot-scope="{ row }">
                  <span v-if="row.releaseStatus==0">待处理</span>
                  <span v-if="row.releaseStatus==1">已发布</span>
                  <span v-if="row.releaseStatus==2">未通过</span>
                  <span v-if="row.releaseStatus==3">已下线</span>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                width="200"
                label="操作"
              >
                <template slot-scope="scope">
                  <el-button
                    v-show="scope.row.releaseStatus==1"
                    type="text"
                    disabled
                  >
                    编辑
                  </el-button>
                  <el-button
                    v-show="scope.row.releaseStatus!=1"
                    type="text"
                    size="mini"
                    @click="edit(scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-divider direction="vertical" />

                  <el-popconfirm
                    v-if="scope.row.releaseStatus==1"
                    title="确定下线？"
                    @confirm="offline(scope.row)"
                  >
                    <el-button
                      slot="reference"
                      type="text"
                      size="mini"
                    >
                      下线
                    </el-button>
                  </el-popconfirm>

                  <el-button
                    v-else
                    type="text"
                    disabled
                  >
                    下线
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </table-layout>
        <div class="ye">
          <el-pagination
            :current-page.sync="page.pageNum"
            :page-sizes="[5, 10, 20, 50]"
            :page-size.sync="page.pageSize"
            :total="+total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="getZszxList"
            @current-change="getZszxList"
          />
        </div>
      </div>
    </div>

    <!-- drawer start -->
    <el-drawer
      :visible.sync="drawer"
      :with-header="false"
      :wrapper-closable="false"
    >
      <addZx
        v-if="drawerStatus=='add'" 
        @close="close"
        @getList="getZszxList" 
      />
    </el-drawer>
    <!-- drawer end -->
  </div>
</template>

<script>
  import addZx from './component/addZx.vue'
  import editZx from './component/editZx.vue'
  import TableLayout from "@/common/components/table-layout";
  import { 
    // formatDate, 
    filterData 
  } from '@/utils/utils'
  import { 
    // zszxDetail, zszxAdd, 
    zszxList, 
    zszxOffline,
    industryChainList 
  } from '../apiUrl'
  export default {
    name: 'ZsZx',
    components: {
      addZx,
      editZx,
      TableLayout,
    },
    data() {
      return {
        form: {
          title: '',
          startDate: '',
          endDate: '',
          correlationIndustryList: []
        },
        page: {
          pageNum: 1,
          pageSize: 10,
        },
        total: 0,
        tableData:[],
        loading: false,
        sellTime: null,
        chainList: [],

        drawer: false,
        drawerStatus: 'add',
        infoData: {},
        linkPlaceList: [],
      }
    },
    watch: {
      sellTime(newValue) {
        if(newValue){
          this.form.startDate = +new Date(newValue[0])
          this.form.endDate = +new Date(newValue[1])
        }else{
          this.form.startDate = ''
          this.form.endDate = ''
        }
      },
      drawer(val) {
        !val && (this.drawerStatus = '')
      }
    },
    created () {
      this.getZszxList();
      this.industryChainList();
    },
    methods: {
      // 获取产业链列表列表
      industryChainList(){
        industryChainList().then(res=>{
          this.chainList = res;
        })
      },
      // 资讯列表
      getZszxList() {
        this.drawer = false;
        let data = {
          ...this.page,
          ...this.form,
        }
        if(this.entrustTime && this.entrustTime.length>0){
          data.startTimeStamp = +new Date(this.entrustTime[0])
          data.endTimeStamp = +new Date(this.entrustTime[1])
        }
        filterData(data);
        zszxList(data).then(res=>{
          res.records.map(e=>{
            e.releaseDate = e.releaseDate.slice(0,10);
            return e
          })
          this.total = res.total;
          this.tableData = res.records;
        })
      },
      onSubmit() {
        this.getZszxList();
      },
      resetForm() {
        this.form= {
          title: '',
          startDate: '',
          endDate: '',
          correlationIndustryList: []
        };
        this.sellTime = null;
        this.getZszxList();
      },
      // 新增
      addEvent() {
        this.drawer = true;
        this.drawerStatus = 'add';
      },
      // 下线资讯
      offline(e) {
        zszxOffline({id:e.id}).then(()=>{
          this.$message.success('下线资讯成功！');
          this.getZszxList();
        });
      },
      // 分页跳转
      SettledList(val) {
        this.page.pageNum = val;
        this.getZszxList();
      },
      // 编辑
      edit(e) {
        this.infoData = e;
        this.drawerStatus = 'edit';
      },
      close() {
        this.drawer = false;
        this.drawerStatus = '';
      },
    },
  }
</script>

<style lang="scss" scoped>
  .zszx{
    padding: 8px 20px;
    background: #F7F8FA;
    min-height: calc(100vh - 84px);
    &-form{
      padding: 20px;
      background: #fff;
      box-shadow: 0px 4px 14px 0px rgba(217,217,217,0.25);
      border-radius: 10px;
      box-shadow: 0px 4px 12px 0px #EEF1F8;
      ::v-deep{
        .el-form-item{
          width: auto !important;
          .el-select{
            width: 210px !important;
          }
        }
        .el-date-editor--daterange.el-input__inner{
          width: 250px !important;
        }
      }
    }
    &-table{
      margin: 20px 0;
    }
    .ye{
      position: relative;
      height: 50px;
      background: #fff;
      .el-pagination{
        padding-right: 20px;
        bottom: 15px !important;
      }
    }
  }
</style>