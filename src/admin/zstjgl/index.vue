<template>
  <div class="zstjgl">
    <div class="zstjgl-con">
      <div
        v-if="drawerStatus == 'process'"
        class="zstjgl-con-1"
      >
        <Process 
          :id="infoData.id"
          @close="close"
          @getList="getList" 
        />
      </div>
      <div
        v-else
        class="zstjgl-con-2"
      >
        <table-layout :tab-name-list="['招商情报管理']">
          <el-form
            slot="elForm"
            :inline="true"
            :model="form"
            class="zstjgl-form"
            label-width="80px"
            size="small"
          >
            <el-form-item label="推荐企业">
              <el-input 
                v-model="form.enterpriseName"
                placeholder="请输入推荐企业名称"
              />
            </el-form-item>
            <el-form-item label="推荐机构">
              <el-select
                v-model="form.orgId"
                placeholder="推荐机构"
              >
                <el-option
                  v-for="(item,k) in institutionList"
                  :key="k"
                  :label="item.orgName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="产业环节">
              <el-select
                v-model="form.chainNodeIds"
                multiple
                collapse-tags
                placeholder="产业环节"
              >
                <el-option-group
                  v-for="group in linkPlaceList"
                  :key="group.chainId"
                  :label="group.chainName"
                >
                  <el-option
                    v-for="item in group.nodes"
                    :key="item.nodeId"
                    :label="item.nodeName"
                    :value="item.nodeId"
                  />
                </el-option-group>
              </el-select>
            </el-form-item>
            <!--             <el-form-item label="推荐理由">
              <el-select
                v-model="form.recommendationReasonIds"
                multiple
                collapse-tags
                placeholder="推荐理由"
              >
                <el-option
                  v-for="(item,k) in listByType"
                  :key="k"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item> -->
            <el-form-item label="招商模式">
              <el-select
                v-model="form.types"
                multiple
                collapse-tags
                placeholder="招商模式"
              >
                <el-option
                  v-for="(item,k) in listByType"
                  :key="k"
                  :label="item.nodeName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="推荐日期">
              <el-date-picker
                v-model="sellTime"
                type="daterange"
                unlink-panels
                range-separator=""
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item label="审核状态">
              <el-select 
                v-model="form.auditStatus"
                placeholder="审核状态"
                multiple
                collapse-tags
              >
                <el-option
                  v-for="(item,i) in typeList"
                  :key="i"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="onSubmit"
              >
                查询
              </el-button>
              <el-button @click="resetForm('form')">
                重置
              </el-button>
              <el-button 
                type="primary" 
                icon="el-icon-plus"
                @click="addEnterprise"
              >
                新增推荐企业
              </el-button>
            </el-form-item>
          </el-form>
          <div
            v-show="multipleSelection.length>0"
            slot="selected"
            class="selectedss"
          > 
            <div>
              <i class="el-icon-info" />
              已选择<span class="num"> {{ num }} </span>项
            </div>
            <div>
              <span
                style="margin-right: 10px;"
                @click="copyList"
              >复制</span>
              <span @click="empty">清空</span>
            </div>
          </div>
          <div
            slot="selTable"
            class="zstjgl-table"
          >
            <el-table
              ref="multipleTable"
              v-loading="loading"
              :data="tableData"
              style="width: 100%"
              size="mini"
              :default-sort="{prop: 'recommendedDate', order: 'descending'}"
              :row-key="getRowKeys"
              @sort-change="sortChange"
              @selection-change="handleSelectStockChange"
            >
              <el-table-column
                :reserve-selection="true"
                type="selection"
                width="55"
                align="center"
              />
              <el-table-column
                prop="enterpriseName"
                label="推荐企业名称"
                width="150"
                align="center"
              />
              <el-table-column
                label="所在地区"
                width="100"
                align="center"
              >
                <template slot-scope="{ row }">
                  {{ row.province }} 
                  {{ row.city }} 
                  {{ row.area }}
                </template>
              </el-table-column>
              <el-table-column
                prop="linkPlace"
                label="产业环节"
                align="center"
              />
              <el-table-column
                prop="orgName"
                label="推荐机构"
                width="100"
                align="center"
              />
              <!--               <el-table-column
                prop="recommendationReason"
                label="推荐理由"
                align="center"
              /> -->
              <el-table-column
                label="招商模式"
                width="100"
                align="center"
              >
                <template slot-scope="{row}">
                  <span v-if="row.type==0">后台添加</span>
                  <span v-if="row.type==1">亲缘招商</span>
                  <span v-if="row.type==2">资源招商</span>
                  <span v-if="row.type==3">链主招商 </span>
                  <span v-if="row.type==4">政策招商</span>
                  <span v-if="row.type==5">舆情招商</span>
                  <span v-if="row.type==6">AI+招商</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="recommendedDate"
                label="推荐日期"
                width="100"
                align="center"
                sortable="custom"
              >
                <template slot-scope="{ row }">
                  {{ row.recommendedDate }}
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                width="90"
                label="审核状态"
              >
                <template slot-scope="{ row }">
                  <span v-if="row.auditStatus==0">待审核</span>
                  <span v-if="row.auditStatus==1">已发布</span>
                  <span v-if="row.auditStatus==2">未通过</span>
                  <span v-if="row.auditStatus==3">已下线</span>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                width="200"
                label="操作"
              >
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    @click="detail(scope.row)"
                  >
                    详情
                  </el-button>
                  <!-- 编辑 -->
                  <el-button
                    v-show="scope.row.auditStatus==1"
                    type="text"
                    class="disabled"
                    disabled
                  >
                    编辑 
                  </el-button>
                  <el-button
                    v-show="scope.row.auditStatus!=1"
                    type="text"
                    @click="edit(scope.row)"
                  >
                    编辑
                  </el-button>
                  <!-- 编辑 -->
                  <el-divider direction="vertical" />
                  <el-button
                    v-show="scope.row.auditStatus==1"
                    type="text"
                    disabled
                  >
                    审核
                  </el-button>
                  <el-button
                    v-show="scope.row.auditStatus!=1"
                    type="text"
                    @click="processEvent(scope.row)"
                  >
                    审核
                  </el-button>
                  <el-divider direction="vertical" />

                  <el-popconfirm
                    v-if="scope.row.auditStatus==1"
                    title="确定下线？"
                    @confirm="offline(scope.row)"
                  >
                    <el-button
                      slot="reference"
                      type="text"
                    >
                      下线
                    </el-button>
                  </el-popconfirm>

                  <el-button
                    v-else
                    type="text"
                    disabled
                  >
                    下线
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </table-layout>
        <div class="ye">
          <span
            style="text-align: center;margin-top: 16px;padding-bottom: 20px;font-size: 14px;min-width: 77px;"
          >共{{ informationtotals }}条</span>
          <el-pagination
            :current-page.sync="page.pageNum"
            :page-sizes="[5, 10, 20, 50]"
            :page-size.sync="page.pageSize"
            :total="+informationtotal"
            layout=" sizes, prev, pager, next, jumper"
            @size-change="getList"
            @current-change="getList"
          />
          <div
            v-if="informationtotal >= 10000"
            style="margin-top: 16px;padding-bottom: 20px;font-size: 14px;min-width: 150px;"
          >
            最多查看10000条数据
          </div>
        </div>
      </div>
    </div>


    <!-- drawer start -->
    <el-drawer
      :visible.sync="drawer"
      :with-header="false"
      :wrapper-closable="false"
    >
      <addEnterprise 
        v-if="drawerStatus=='add'" 
        @close="close"
        @getList="getList" 
      />
      <detail 
        v-if="drawerStatus=='detail'" 
        :id="infoData.id" 
        @close="close"
      />
      <edit 
        v-if="drawerStatus=='edit'" 
        :id="infoData.id" 
        :info="infoData"
        @close="close"
        @processEvent="processEvent"
        @getList="getList" 
      />
    </el-drawer>
    <!-- drawer end -->
    <sellTime
      v-if="copydig"
      :copydig="copydig"
      :multiple-selection="multipleSelection"
      @closeassignball="closeassignball"
      @getList="getList" 
      @updatList="updatList"
    />
  </div>
</template>

<script>
  import TableLayout from "@/common/components/table-layout";
  import { zstjList, linkPlaceList, institutionList, 
    //listByType, 
    offline } from '../apiUrl'
  import { formatDate, filterData } from '@/utils/utils'
  import detail from './component/detail.vue'
  import edit from './component/edit.vue'
  import addEnterprise from './component/addEnterprise.vue'
  import Process from './component/process.vue'
  import sellTime from  './component/sellTime.vue'
  export default {
    name: 'ZstJgl',
    components: {
      edit,
      detail,
      Process,
      addEnterprise,
      TableLayout,
      sellTime
    },
    filters: {
      time: function(value) {
        return formatDate('yyyy-MM-dd', new Date(+value));
      }
    },
    data() {
      return {
        drawer: false,
        copydig:false,
        drawerStatus: 'detail',
        form: {
          enterpriseName:'',
          orgId: [],
          chainNodeIds: [],
          recommendationReasonIds: [],
          startTime: '',
          endTime: '',
          auditStatus: '',
          recommendedDateOrderAsc: false,
          types:[]
        },
        page: {
          pageNum: 1,
          pageSize: 10,
        },
        total: 0,
        informationtotal: 0,
        informationtotals:0,
        tableData:[],
        loading: false,
        sellTime: null,
        typeList: [
          {
            label: '全部',
            value: ''
          },
          {
            label: '待审核',
            value: '0'
          },
          {
            label: '已发布 ',
            value: '1'
          },
          {
            label: '未通过 ',
            value: '2'
          },
          {
            label: '已下线 ',
            value: '3'
          }
        ],
        listByType: [
        {
          nodeName: "亲缘招商",
          id: 1,
        },
        // {
        //   nodeName: "资源招商",
        //   id: 2,
        // },
        {
          nodeName: "链主招商",
          id: 3,
        },
        // {
        //   nodeName: "政策招商",
        //   id: 4,
        // },
        {
          nodeName: "舆情招商",
          id: 5,
        },
        // {
        //   nodeName: "AI+招商",
        //   id: 6,
        // },
        ], // 推荐理由
        linkPlaceList: [], // 产业环节下拉框
        institutionList: [], // 机构列表

        infoData: {},
        multipleSelection:[]
      }
    },
    computed:{
      num:function(){
    return this.multipleSelection.length
  }
    },
    watch: {
      sellTime(newValue) {
        if(newValue){
          this.form.startTime = +new Date(newValue[0])
          this.form.endTime = +new Date(newValue[1])
        }else{
          this.form.startTime = ''
          this.form.endTime = ''
        }
      },
      drawer(val) {
        !val && (this.drawerStatus = '')
      }
    },
    created () {
      this.getList();
      this.getLinkPlaceList();
      this.getInstitutionList();
      //this.getListByType();
    },
    methods: {
      closeassignball(){
          this.copydig=false
      },
      copyList(){
        if(this.multipleSelection.length==0){
          return this.$message.error("您还未选择")
        }else{
          this.copydig=true
        }
      },
      empty(){
        this.$refs.multipleTable.clearSelection();
      },
      getRowKeys(row) {
        return row.id;
      },
      handleSelectStockChange(val) {
      let stockSelectlist = [];
      val.forEach((el) => {
        stockSelectlist.push(el.id);
      });
      this.multipleSelection = stockSelectlist;
    },
      close() {
        this.drawer = false;
        this.drawerStatus = '';
      },
      processEvent(e){
        this.drawer = false;
        this.infoData = e;
        this.$nextTick().then(()=>{
          this.drawerStatus = 'process';
        })
      },
      // 下线
      offline(e) {
        offline({id: e.id}).then(()=>{
          this.$message.success('下线成功！');
          this.getList();
        })
      },
      // 推荐机构列表
      getInstitutionList(){
        institutionList().then(res=>{
          this.institutionList = res;
        })
      },
      // 产业环节下拉框
      getLinkPlaceList(){
        linkPlaceList().then(res=>{
          this.linkPlaceList = res;
        })
      },
      // 推荐理由列表
/*       getListByType(){
        let data = {
          keywordType: 2, // 0未知 1招商资讯新闻主题 2招商情报推荐理由
        }
        listByType(data).then(res=>{
          this.listByType = res;
        })
      }, */
      updatList(){
         this.empty()
         this.page.pageNum= 1,
         this.getList()
      },
      // 获取推荐列表
      getList(){
        this.drawer = false;
        let data = {
          ...this.page,
          ...this.form,
        }
        if(this.entrustTime && this.entrustTime.length>0){
          data.startTimeStamp = +new Date(this.entrustTime[0])
          data.endTimeStamp = +new Date(this.entrustTime[1])
        }
        if(data.auditStatus.includes('')){
          delete data.auditStatus
        }
        filterData(data);
        zstjList(data).then(res=>{
          // this.tableData = res.records;
          this.$set(this, 'tableData', res.records);
          this.total = res.total;
          this.informationtotals = res.total;
          this.informationtotal = res.total;
          if (res.total > 10000) {
           this.informationtotal = 10000;
          }
        });
      },
      // 新增推荐企业
      addEnterprise() {
        this.drawer = true;
        this.drawerStatus = 'add';
      },
      // 查询
      onSubmit() {
        this.getList();
      },
      // 重置
      resetForm() {
        this.form = {
          enterpriseName:'',
          orgId: [],
          chainNodeIds: [],
          recommendationReasonIds: [],
          startTime: '',
          endTime: '',
          auditStatus: '',
          recommendedDateOrderAsc: false,
        }
        this.page= {
          pageNum: 1,
          pageSize: 10,
        }
        this.sellTime = ''
        this.getList();
      },
      // 分页跳转
      SettledList(val) {
        this.page.pageNum = val;
        this.getList();
      },
      // 排序
      sortChange({ order }) {
        if(order == "descending"){
          this.form.recommendedDateOrderAsc = false;
        }else{
          this.form.recommendedDateOrderAsc = true;
        }
        this.getList();
      },
      // 详情
      detail(e) {
        this.drawer = true;
        this.infoData = e;
        this.drawerStatus = 'detail';
      },
      edit(e) {
        this.drawer = true;
        this.infoData = e;
        this.drawerStatus = 'edit';
      }
    },
  }
</script>

<style lang="scss" scoped>
 
  .selectedss {
    width: auto;
    margin: 20px;
    margin-bottom: 0px;
    padding: 0 12px;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;
    background: #e6f7ff;
    opacity: 1;
    border: 1px solid #bae7ff;
    span{
      cursor: pointer;
      color: #63bfff;
    }
    .num{
      color: #63bfff;
    }
  }
  .zstjgl{
    // padding: 8px 20px 0px 20px;
    background: #F7F8FA;
    min-height: calc(100vh - 84px);
    &-form{
      padding: 20px;
      background: #fff;
      box-shadow: 0px 4px 12px 0px #EEF1F8;
      border-radius: 10px;
      ::v-deep{
        .el-form-item{
          width: auto !important;
        }
        .el-date-editor--daterange.el-input__inner{
          width: 250px !important;
        }
      }
    }
    &-table{
      margin: 20px 0;
    }
    .ye{
      width: 100%;
      background-color: #fff;
      display: flex;
      justify-content: flex-end;
      .el-pagination{
        padding-right: 20px;
        width: auto !important;
      }    
    }
  }
</style>