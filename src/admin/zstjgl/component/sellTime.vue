<template>
  <div>
    <el-dialog
      width="30%"
      title="复制推荐企业"
      :visible="copydig"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="form"
        label-position="top"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item
          label="推荐机构"
          prop="orgId"
        >
          <el-select
            v-model="form.orgId"
            multiple
            collapse-tags
            placeholder="请选择推荐机构"
            filterable
          >
            <el-option
              v-for="item in institutionList"
              :key="item.id"
              :label="item.orgName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="推荐日期"
          prop="time"
        >
          <el-date-picker
            v-model="form.time"
            type="date"
            format="yyyy-MM-dd"
            value-format="timestamp"
            placeholder="选择日期"
            :picker-options="pickerOptions"
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          style="background-color: #f5f5f5;color: rgba(0, 0, 0, 0.85);"
          @click="cancel"
        >
          取 消
        </el-button>
        <el-button
          type="primary"
          style="color: #fff"
          @click="preserve"
        >
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
        
<script>
import {  institutionList ,batchReplicationAPI  } from '../../apiUrl'
export default {
  name: "SellTime",
  props: {
    copydig: {
      type: Boolean,
      default: false,
    },
    multipleSelection:{
      type: Array,
      default: ()=>[],
    }
  },
  data() {
    return {
      institutionList: [],
      form: {
        orgId: [],
        time: "",
      },
      rules: {
        orgId: [{ required: true, message: "推荐机构不能为空" }],
        time: [{ required: true, message: "推荐日期不能为空" }],
      },
      pickerOptions:{
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
        }
    };
  },
  created() {
    this.getInstitutionList();
  },
  methods: {
    getInstitutionList(){
        institutionList().then(res=>{
          this.institutionList = res;
        })
      },
    cancel() {
      this.$emit("closeassignball");
    },
    async preserve() {
        await this.$refs.form.validate();
        let data = {
          recommendedDate: this.form.time,
          orgIds: this.form.orgId,
          ids: this.multipleSelection,
        };
       const res = await batchReplicationAPI(data);
        if(!res){
          this.$message.success("批量复制成功！");
          this.cancel();
          this.$emit('updatList')
        }else{
          let str =res.result.map(it=>{
            return it
          }).join(',<br>');
          this.$message({
            type: 'error',
          dangerouslyUseHTMLString: true,
          message: str,
          duration:5000
        });
        }
      }
  },
};
</script>
<style scoped lang="scss">
      ::v-deep {
  .el-form-item {
    margin-bottom: 5px !important;
  }
  .el-form--label-top .el-form-item__label{
    padding: 0px !important;
  }
}
.inline-input {
  width: 100%;
}
</style>