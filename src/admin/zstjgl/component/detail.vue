<template>
  <div class="info">
    <div class="close">
      <i
        class="el-icon-circle-close"
        @click="close"
      />
    </div>
    <div class="title">
      <div class="label">
        推荐企业详情
      </div>
    </div>
    <div class="info-con">
      <div class="info-con-list">
        <div class="label">
          推荐企业名称：
        </div>
        <div class="txt">
          {{ infoData.enterpriseName }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          统一信用代码：
        </div>
        <div class="txt">
          {{ infoData.unifiedSocialCreditCode }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          所在地区：
        </div>
        <div class="txt">
          {{ infoData.province }}
          {{ infoData.city }}
          {{ infoData.area }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          产业环节：
        </div>
        <div class="txt">
          {{ infoData.linkPlace }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          推荐机构：
        </div>
        <div class="txt">
          {{ infoData.orgName }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          推荐日期：
        </div>
        <div class="txt">
          {{ infoData.recommendedDate }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          推荐理由：
        </div>
        <div class="txt on">
          {{ infoData.recommendationReasonDetail }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          推荐类型：
        </div>
        <div class="txt">
          <span v-if=" infoData.type ==1">亲缘招商</span>
          <span v-else-if=" infoData.type ==2">资源招商</span>
          <span v-else-if=" infoData.type ==3">链主招商</span>
          <span v-else-if=" infoData.type ==4">政策招商</span>
          <span v-else-if=" infoData.type ==5">舆情招商</span>
          <span v-else-if=" infoData.type ==6">AI+招商</span>
          <span v-else-if=" infoData.type ==0">后台添加</span>
        </div>
      </div>
      <div
        v-if="infoData.type==1"
        class="info-con-list"
      >
        <div class="label">
          关联亲商姓名：
        </div>
        <div class="txt">
          {{ infoData.relationUserName }}
        </div>
      </div>
      <div
        v-if="infoData.type==1"
        class="info-con-list"
      >
        <div class="label">
          关联关系：
        </div>
        <div class="txt">
          {{ infoData.associationRelationship }}
        </div>
      </div>
      <div
        v-if="infoData.type==2"
        class="info-con-list"
      >
        <div class="label">
          资源需求：
        </div>
        <div class="txt">
          {{ infoData.resourceNeeds }}
        </div>
      </div>
      <div
        v-if="infoData.type==3"
        class="info-con-list"
      >
        <div class="label">
          关联本地企业社会统一信用代码：
        </div>
        <div class="txt">
          {{ infoData.associateLocalEnterpriseCode }}
        </div>
      </div>
      <div
        v-if="infoData.type==3"
        class="info-con-list"
      >
        <div class="label">
          供应关系：
        </div>
        <div class="txt">
          {{ infoData.supplyRelation }}
        </div>
      </div>
      <div
        v-if="infoData.type==3"
        class="info-con-list"
      >
        <div class="label">
          关联本地企业名称：
        </div>
        <div class="txt on">
          {{ infoData.recommendationReasonDetail }}
        </div>
      </div>
      <div
        v-if="infoData.type==4"
        class="info-con-list"
      >
        <div class="label">
          关联政策：
        </div>
        <div class="txt on">
          {{ infoData.associatePolicy }}
        </div>
      </div>
      <div
        v-if="infoData.type==5"
        class="info-con-list"
      >
        <div class="label">
          关联资讯url：
        </div>
        <div class="txt on">
          <a
            target="_blank"
            :href="infoData.associateInformationUrl"
          >{{ infoData.associateInformationUrl }}</a>
        </div>
      </div>
      <div
        v-if="infoData.type==6"
        class="info-con-list"
      >
        <div class="label">
          对外投资意愿：
        </div>
        <div class="txt on">
          {{ infoData.outsideInvestSatisfaction }}
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
    import { zstjDetail } from '../../apiUrl'
    import { formatDate } from '@/utils/utils'
    export default {
      name: 'InfoDrawer',
      filters: {
        time: function(value) {
          return formatDate('yyyy-MM-dd', new Date(+value));
        },
        timeStamp: function(val){
          // console.log(val)
          let date = val/(60*60*1000*24)
          let d = parseInt(val/(60*60*1000*24))
          let h = parseInt((date%1)*24)
          let s = parseInt((((date%1)*24)%1)*60)
          return d + '天' + h + '时' + s + '分'
        }
      },
      props: {
        id: {
          type: String,
          default: null
        },
      },
      data() {
        return {
          infoData: {},
          newId: '',
        }
      },
      watch: {
        id(newValue) {
          this.newId = newValue;
          this.getZstjDetail();
        }
      },
      created () {
        this.newId = this.id;
        this.getZstjDetail();
      },
      methods: {
        getZstjDetail() {
          zstjDetail({id: this.newId}).then(res=>{
            this.infoData = res;
          })
        },
        close() {
          this.$emit('close');
        },
      },
    }
  </script>
  
  <style lang="scss" scoped>
    .info{
      padding: 0 12px;
      position: relative;
      .close{
        width: 30px;
        height: 30px;
        position: absolute;
        right: 10px;
        top: 10px;
        z-index: 999999;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        .el-icon-circle-close{
          font-size: 24px;
        }
      }
      .title{
        position: relative;
        padding-left: 35px;
        border-bottom: 1px solid #cacaca;
        line-height: 45px;
        font-size: 14px;
        color: #101010;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &::before{
          content: '';
          width: 5px;
          height: 18px;
          background: rgba(20, 66, 119, 0.98);
          position: absolute;
          top: 15px;
          left: 15px;
        }
        .el-button--mini{
  
        }
      }
      &-con{
        padding-top: 15px;
        &-list{
          display: flex;
          font-size: 12px;
          line-height: 42px;
          .label{
            width: 100px;
            padding-left: 60px;
            box-sizing: content-box;
          }
          .txt{
            color: #212121;
            width: 210px;
            &.on{
              line-height: 20px;
              padding-top: 10px;
            }
          }
        }
      }
    }
  </style>