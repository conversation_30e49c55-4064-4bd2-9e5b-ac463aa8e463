<template>
  <div class="followUp">
    <div class="followUp-con">
      <div class="top">
        当前共{{ infoData.length }}条跟进记录
      </div>
      <div class="followUp-con-main">
        <el-collapse
          v-model="activeNames"
          @change="handleChange"
        >
          <el-collapse-item
            v-for="(item,k) in infoData"
            :key="k"
            :name="k"
          >
            <template slot="title">
              <div class="dis_flex">
                <span>
                  {{ item.gmtCreate }}  
                </span>
                <span
                  v-if="item.auditStatus==0"
                  class="ml_100"
                >
                  审核不通过 
                </span>
                <span
                  v-else-if="item.auditStatus==1"
                  class="ml_100"
                >
                  审核通过 
                </span>
              </div>
            </template>
            <div class="list">
              <div class="label">
                审核人：
              </div>
              <div class="txt">
                {{ item.createBy }}
              </div>
            </div>
            <div class="list">
              <div class="label">
                审核意见：
              </div>
              <div class="txt">
                {{ item.auditOpinion }}
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script>
  import { zstjAuditList } from '../../apiUrl'
  import { formatDate } from '@/utils/utils'
  export default {
    name: 'FollowUp',
    filters: {
      time: function(value) {
        return formatDate('yyyy MM-dd', new Date(+value));
      }
    },
    props: {
      id: {
        type: String,
        default: null
      },
      info: {
        type: Object,
        default: null
      }
    },
    data() {
      return {
        activeNames: ['1'],
        isShowAdd: false,
        infoData: []
      }
    },
    watch: {
      id() {
        this.getZstjAuditList();
      }
    },
    created () {
      this.getZstjAuditList();
    },
    methods: {
      init(){
        this.isShowAdd=false;
        this.getZstjAuditList();
      },
      // 添加记录按钮点击
      addReload(){
        this.isShowAdd=true;
      },
      getZstjAuditList() {
        zstjAuditList({id: this.id}).then(res=>{
          this.infoData = res;
        });
      },
      handleChange(val) {
        // console.log(val);
      },
      close() {
        this.$emit('close');
      },
    },
  }
</script>

<style lang="scss" scoped>
  .followUp{
    padding: 0 12px;
    position: relative;
    padding-top: 20px;
    .close{
      width: 30px;
      height: 30px;
      position: absolute;
      right: 10px;
      top: 10px;
      z-index: 999999;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .el-icon-circle-close{
        font-size: 24px;
      }
    }
    .title{
      position: relative;
      padding-left: 35px;
      line-height: 45px;
      font-size: 14px;
      color: #101010;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &::before{
        content: '';
        width: 5px;
        height: 18px;
        background: rgba(20, 66, 119, 0.98);
        position: absolute;
        top: 15px;
        left: 15px;
      }
    }
    &-con{
      padding-left: 40px;
      .top{
        line-height: 24px;
        padding-bottom: 10px;
      }
      &-main{
        .dis_flex{
          display: flex;
        }
        .ml_100{
          margin-left: 100px;
        }
      }
      .list{
        display: flex;
        font-size: 12px;
        .label{
          width: 120px;
          font-family: SourceHanSansSC;
          font-weight: 400;
          color: rgb(16, 16, 16);
          font-style: normal;
          line-height: 30px;
        }
        .txt{
          font-family: SourceHanSansSC;
          font-weight: 300;
          color: rgb(16, 16, 16);
          font-style: normal;
          line-height: 30px;
        }
      }
    }
  }
</style>