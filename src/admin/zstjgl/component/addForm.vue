<template>
  <div class="addForm">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="110px"
      label-position="left"
      size="small"
      class="demo-ruleForm"
    >
      <el-form-item
        v-if="id"
        label="推荐机构："
        prop="orgIds"
        class="appendToBodyFalse"
      >
        <el-select
          v-model="ruleForm.orgId"
          :popper-append-to-body="false"
          placeholder="推荐机构"
          disabled
          @change="$forceUpdate()"
        >
          <el-option
            v-for="(item, k) in institutionList"
            :key="k"
            :label="item.orgName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-else
        label="推荐机构："
        prop="orgIds"
        class="appendToBodyFalse"
      >
        <el-select
          v-model="ruleForm.orgIds"
          :popper-append-to-body="false"
          placeholder="推荐机构"
          multiple
          collapse-tags
          @change="$forceUpdate()"
        >
          <el-option
            v-for="(item, k) in institutionList"
            :key="k"
            :label="item.orgName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="企业名称："
        prop="enterpriseId"
      >
        <el-input
          v-show="false"
          v-model="ruleForm.enterpriseId"
        />
        <el-input v-model="enterpriseName" />
      </el-form-item>
      <el-form-item
        label="推荐日期："
        prop="recommendedDate"
        class="appendToBodyFalse"
      >
        <el-date-picker
          v-model="ruleForm.recommendedDate"
          :append-to-body="false"
          type="date"
          placeholder="选择日期"
          :picker-options="pickerOptions"
        />
      </el-form-item>
      <el-form-item
        prop="type"
        label="招商模式："
        class="appendToBodyFalse"
      >
        <el-select
          v-model="ruleForm.type"
          :popper-append-to-body="false"
          clearable
          @change="changetype"
        >
          <el-option
            v-for="(item, index) in linkList"
            :key="index"
            :label="item.nodeName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="ruleForm.type == 1"
        prop="relationUserNameOnlyLogo"
        label="关联亲商姓名："
        class="appendToBodyFalse"
      >
        <el-select
          v-model="ruleForm.relationUserNameOnlyLogo"
          :popper-append-to-body="false"
          filterable
          remote
          reserve-keyword
          placeholder="请输入关键词"
          :remote-method="remoteMethod"
          :loading="loading"
        >
          <!--           <el-option
            v-for="item in sourceList"
            :key="item.id"
            :label="item.name"
            :value="item.uniqId"
          /> -->
          <el-option
            v-for="item in sourceList"
            :key="item.uniqId"
            :label="item.name"
            :value="item.uniqId"
          >
            <el-popover
              placement="right"
              width="500"
              trigger="hover"
              :content="item.resume"
            >
              <el-button
                slot="reference"
                type="text"
                style="width:100%;color:black;"
              >
                <span style="float: left">{{ item.name }}</span>
              </el-button>
            </el-popover>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="ruleForm.type == 1"
        prop="associationRelationship"
        label="关联关系："
      >
        <el-input v-model="ruleForm.associationRelationship" />
      </el-form-item>
      <el-form-item
        v-if="ruleForm.type == 2"
        prop="resourceNeeds"
        label="资源需求："
      >
        <el-input v-model="ruleForm.resourceNeeds" />
      </el-form-item>
      <el-form-item
        v-if="ruleForm.type == 3"
        prop="associateLocalEnterpriseCode"
        label="关联本地企业社会统一信用代码："
      >
        <el-input v-model="ruleForm.associateLocalEnterpriseCode" />
      </el-form-item>
      <el-form-item
        v-if="ruleForm.type == 3"
        prop="supplyRelation"
        label="供应关系："
      >
        <el-input v-model="ruleForm.supplyRelation" />
      </el-form-item>
      <el-form-item
        v-if="ruleForm.type == 4"
        prop="associatePolicy"
        label="关联政策："
      >
        <el-input v-model="ruleForm.associatePolicy" />
      </el-form-item>
      <el-form-item
        v-if="ruleForm.type == 5"
        prop="associateInformationUrl"
        label="关联资讯url："
      >
        <el-input v-model="ruleForm.associateInformationUrl" />
      </el-form-item>
      <el-form-item
        v-if="ruleForm.type == 6"
        prop="outsideInvestSatisfaction"
        label="对外投资意愿："
      >
        <el-input v-model="ruleForm.outsideInvestSatisfaction" />
      </el-form-item>
      <!--       <el-form-item
        label="推荐理由："
        prop="recommendationReasonIds"
      >
        <el-select
          v-model="ruleForm.recommendationReasonIds"
          multiple
          collapse-tags
          placeholder="推荐理由"
        >
          <el-option
            v-for="(item,k) in listByType"
            :key="k"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item
        label="推荐理由："
        prop="recommendationReasonDetail"
      >
        <el-input
          v-model="ruleForm.recommendationReasonDetail"
          type="textarea"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="submitForm('ruleForm')"
        >
          保存
        </el-button>
        <el-button @click="resetForm('ruleForm')">
          取消
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
  
  <script>
import {
  zstjAdd,
  listByType,
  institutionList,
  getByName,
  zstjUpdate,
  listNameFuzzyMatchingAPI
} from "../../apiUrl";
export default {
  name: "AddForm",
  props: {
    id: {
      type: String,
      default: null,
    },
    infoData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      enterpriseName: "",
      ruleForm: {
        orgIds: [],
        enterpriseId: "",
        recommendedDate: "",
        recommendationReasonIds: "",
        recommendationReasonDetail: "",
        type: "", //推荐类型：1亲商招商 2资源招商 3链主招商 4政策招商 5舆情招商 6AI+招商
        relationUserNameOnlyLogo: "", //关联亲商姓名唯一标识，推荐类型为1必填
        associationRelationship: "", //亲商模式推荐关联关系
        resourceNeeds: "", //资源需求，推荐类型为2必填
        associateLocalEnterpriseCode: "", //关联本地企业社会统一信用代码，推荐类型为3必填
        supplyRelation: "", //供应关系，推荐类型为3必填
        associatePolicy: "", //关联政策，推荐类型为4必填
        associateInformationUrl: "", //关联资讯url，推荐类型为5必填
        outsideInvestSatisfaction: "", //对外投资意愿，推荐类型为6必填
      },
      linkList: [
        {
          nodeName: "亲缘招商",
          id: "1",
        },
        // {
        //   nodeName: "资源招商",
        //   id: "2",
        // },
        {
          nodeName: "链主招商",
          id: "3",
        },
        // {
        //   nodeName: "政策招商",
        //   id: "4",
        // },
        {
          nodeName: "舆情招商",
          id: "5",
        },
        // {
        //   nodeName: "AI+招商",
        //   id: "6",
        // },
      ],
      rules: {
        recommendedDate: [
          {
            type: "date",
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        orgIds: [{ required: true, message: "请选择", trigger: "change" }],
        type: [
          { required: true, message: "请选择招商模式", trigger: "change" },
        ],
        enterpriseId: [
          { required: true, message: "企业名称不存在", trigger: "blur" },
        ],
        recommendationReasonIds: [
          { required: true, message: "请选择推荐类型", trigger: "blur" },
        ],
        recommendationReasonDetail: [
          { required: true, message: "推荐理由详情不能为空", trigger: "blur" },
        ],
        relationUserNameOnlyLogo: [
          { required: true, message: "关联亲商姓名不能为空", trigger: "blur" },
        ],
        associationRelationship: [
          {
            required: true,
            message: "亲商模式推荐关联关系不能为空",
            trigger: "blur",
          },
        ],
        resourceNeeds: [
          { required: true, message: "资源需求不能为空", trigger: "blur" },
        ],
        associateLocalEnterpriseCode: [
          {
            required: true,
            message: "关联本地企业社会统一信用代码不能为空",
            trigger: "blur",
          },
        ],
        supplyRelation: [
          { required: true, message: "供应关系不能为空", trigger: "blur" },
        ],
        associatePolicy: [
          { required: true, message: "关联政策不能为空", trigger: "blur" },
        ],
        associateInformationUrl: [
          { required: true, message: "关联资讯url不能为空", trigger: "blur" },
        ],
        outsideInvestSatisfaction: [
          { required: true, message: "对外投资意愿不能为空", trigger: "blur" },
        ],
      },

      listByType: [], // 推荐理由
      loading: false,
      sourceList:[],//情商人
      institutionList: [], // 机构列表,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
    };
  },
  watch: {
    enterpriseName(newValue) {
      this.getByName(newValue);
    },
    infoData(val) {
      if(val.type==1){
        this.remoteMethod(val.relationUserName)
      }
      if(val.type==0){
        val.type='66'
        this.linkList.unshift({
          nodeName: "后台添加",
          id: "66",
        })
      }
      val.orgIds=[]
      val.orgIds[0]=val.orgId
      this.ruleForm = val;
      this.ruleForm.recommendedDate = new Date(val.recommendedDate);
      this.enterpriseName = val.enterpriseName;
      let arr = [];
      let data = {
        keywordType: 2, // 0未知 1招商资讯新闻主题 2招商情报推荐理由
      };
      listByType(data).then((res) => {
        this.listByType = res;

        this.listByType.map((e) => {
          if (val.recommendationReason.indexOf(e.name) > -1) {
            arr.push(e.id);
          }
        });
      });
      this.ruleForm.recommendationReasonIds = arr;
    },
  },
  created() {
    this.getListByType();
    this.getInstitutionList();
  },
  destroyed() {
  },
  methods: {
    async remoteMethod(keyword){
    try {
      this.loading=true
      const res =  await  listNameFuzzyMatchingAPI({
        count:10,
        keyword
      })
      this.sourceList=res
    } finally{
      this.loading=false
    }
    },
    changetype() {
      (this.ruleForm.relationUserNameOnlyLogo = ""), //关联亲商姓名唯一标识，推荐类型为1必填
        (this.ruleForm.associationRelationship = ""), //亲商模式推荐关联关系
        (this.ruleForm.resourceNeeds = ""), //资源需求，推荐类型为2必填
        (this.ruleForm.associateLocalEnterpriseCode = ""), //关联本地企业社会统一信用代码，推荐类型为3必填
        (this.ruleForm.supplyRelation = ""), //供应关系，推荐类型为3必填
        (this.ruleForm.associatePolicy = ""), //关联政策，推荐类型为4必填
        (this.ruleForm.associateInformationUrl = ""), //关联资讯url，推荐类型为5必填
        (this.ruleForm.outsideInvestSatisfaction = ""); //对外投资意愿，推荐类型为6必填
    },
    getByName(enterpriseName) {
      getByName({ enterpriseName }).then((res) => {
        this.ruleForm.enterpriseId = res.id;
        let arr= {
          enterpriseName:res.enterpriseName,
          enterpriseId:res.id
        }
        this.ruleForm.enterpriseRequest=arr
      });
    },
    // 推荐机构列表
    getInstitutionList() {
      institutionList().then((res) => {
        this.institutionList = res;
      });
    },
    // 推荐理由列表
    getListByType() {
      let data = {
        keywordType: 2, // 0未知 1招商资讯新闻主题 2招商情报推荐理由
      };
      listByType(data).then((res) => {
        this.listByType = res;
      });
    },
    // 添加记录
    zstjAdd() {
      let data = this.ruleForm;
      data.recommendedDate = +new Date(data.recommendedDate);
      // 编辑
      if (this.id) {
        zstjUpdate(data).then(() => {
          this.$message.success("编辑成功！");
          this.$emit("getList");
        });
        return;
      }
      data.orgList = data.orgIds.map(id => this.institutionList.find(obj => obj.id === id));
      data.orgList = data.orgList.map(it=>{
        return{
          orgName:it.orgName,
          orgId:it.id
        }
      })
      // 新增
       zstjAdd(data).then((res) => {
        if(!res){
          this.$message.success("添加成功！");
          this.$emit("getList");
        }else{
          let str =res.result.map(it=>{
            return it
          }).join(',<br>');
          this.$message({
            type: 'error',
          dangerouslyUseHTMLString: true,
          message: str,
          duration:5000
        });
        }
      
      });
    },
    submitForm(formName) {
      if(this.ruleForm.type==66){
        return this.$message.error("招商模式不能为后台添加")
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.zstjAdd();
        } else {
          return false;
        }
      });
    },
    resetForm() {
      // this.$refs[formName].resetFields();
      this.$emit("close");
    },
  },
};
</script>
  
  <style lang="scss" scoped>
.addForm {
  padding: 15px 25px;
  ::v-deep {
    .el-form-item__label {
      font-size: 12px;
    }
    .el-input--mini,
    .el-date-editor.el-input {
      width: 180px;
    }
  }
}
</style>