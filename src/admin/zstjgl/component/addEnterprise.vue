<template>
  <div class="addEnterprise">
    <div class="close">
      <i
        class="el-icon-circle-close"
        @click="close"
      />
    </div>
    <div class="title">
      <div class="label">
        新增推荐企业
      </div>
    </div>
    <div class="addEnterprise-con">
      <div class="tab">
        <div class="tab-top">
          <div
            class="tab-list"
            :class="tabCheck==1?'on':''"
            @click="tabCheck=1"
          >
            新增推荐
          </div>
          <div
            class="tab-list"
            :class="tabCheck==2?'on':''"
            @click="tabCheck=2"
          >
            批量新增
          </div>
        </div>
        <div class="tab-main">
          <div
            v-if="tabCheck==1" 
            class="tab-main-1"
          >
            <addFrom 
              @getList="getList"
              @close="close" 
            />
          </div>
          <div
            v-if="tabCheck==2"
            class="tab-main-2"
          >
            <div class="upload">
              <el-upload
                ref="upload"
                class="upload-demo"
                :action="chartApi.zstjUpload"
                :on-success="handleSuccess"
                :file-list="fileList"
                :headers="{
                  token
                }"
                :auto-upload="false"
              >
                <el-button
                  slot="trigger"
                  size="small"
                  type="primary"
                >
                  选取文件
                </el-button>
                <el-button
                  style="margin-left: 10px;"
                  size="small"
                  type="success"
                  @click="submitUpload"
                >
                  上传到服务器
                </el-button>
                <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
              </el-upload>
            </div>
            <div class="upload-txt">
              <div class="txt1">
                准备Excel文件 <span
                  class="a"
                  @click="lookDemo"
                >查看样例</span>
              </div>
              <div class="txt2">
                ①  支持最多100条信息
              </div>
              <div class="txt2">
                ②  需要输入完整新闻资讯信息且符合相对应格式后方可添加（新闻资讯数据需与系统中相对应已创建字段保持一致，否则无法成功添加）。
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import addFrom from './addForm.vue'
  import { 
    chartApi,
    zstjDownloadExcelModel
  } from '../../apiUrl'
import { getToken } from "@/utils/auth"; // get token from cookie

  export default {
    name: 'AddEnterprise',
    components: {
      addFrom,
    },
    data() {
      return {
        chartApi,
        tabCheck: 1, // 1新增，2批量新增

        token: '',
        fileList: [],
      }
    },
    created () {
      if(localStorage.user){
        this.token =  getToken()
        // JSON.parse(localStorage.user).user.user.token;
      }
    },
    methods: {
      getList() {
        // console.log(1)
        this.$emit('getList')
      },
      close() {
        this.$emit('close');
      },

      // 下载文件
      lookDemo(){
        zstjDownloadExcelModel().then(res=>{
         // console.log(res);
          let blob = new Blob([res.data], {
              type: "text/csv,charset=UTF-8",
            });
            let objectUrl = URL.createObjectURL(blob);
            window.location.href = objectUrl;
        })
      },

      submitUpload() {
        if(this.$refs.upload.uploadFiles.length==0){
          this.$message.error('请选择文件！')
          return
        }
        this.$refs.upload.submit();
      },
      handleSuccess(file) {
        // console.log(file);
        // console.log(file,'file??');
        if(file.msg=='请求成功'){
          this.$message.success('上传成功！')
        }else{
          this.$message.error(file.msg || '导入异常')
        }
      }

    },
  }
</script>

<style lang="scss" scoped>
  .addEnterprise{
    padding: 0 12px;
    position: relative;
    .close{
      width: 30px;
      height: 30px;
      position: absolute;
      right: 10px;
      top: 10px;
      z-index: 999999;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .el-icon-circle-close{
        font-size: 24px;
      }
    }
    .title{
      position: relative;
      padding-left: 35px;
      border-bottom: 1px solid #cacaca;
      line-height: 45px;
      font-size: 14px;
      color: #101010;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &::before{
        content: '';
        width: 5px;
        height: 18px;
        background: rgba(20, 66, 119, 0.98);
        position: absolute;
        top: 15px;
        left: 15px;
      }
      .el-button--mini{

      }
    }
    &-con{
      .tab{
        &-top{
          display: flex;
          padding-top: 15px;
          padding-bottom: 15px;
          border-bottom: 1px solid #bbb;
        }
        &-list{
          width: 50%;
          text-align: center;
          font-size: 13px;
          font-weight: normal;
          font-style: normal;
          text-decoration: none;
          cursor: pointer;
          &.on{
            color: rgb(22, 132, 252);
            position: relative;
            &::before{
              content: '';
              width: 62px;
              height: 2px;
              background: rgb(22, 132, 252);
              position: absolute;
              bottom: -16px;
              left: 50%;
              transform: translateX(-50%);
            }
          }
        }
        &-main-2{
          padding: 30px;
          text-align: center;
          .upload-txt{
            margin-top: 20px;
            padding: 20px 20px;
            background: rgb(238, 238, 238);
            text-align: left;
            color: rgb(16, 16, 16);
            line-height: 40px;
            .txt1{
              font-size: 15px;
              .a{
                color: rgba(1,118,255,1);
                font-size: 13px;
                margin-left: 20px;
                cursor: pointer;
              }
            }
            .txt2{
              font-size: 12px;
            }
          }
        }
      }
    }
  }
</style>