<template>
  <div class="edit">
    <div class="close">
      <i
        class="el-icon-circle-close"
        @click="close"
      />
    </div>
    <div class="title">
      <div class="label">
        推荐企业编辑
      </div>
      <el-button
        v-if="infoData.havaAuditOpinion"
        type="primary"
        size="mini"
        style="margin-right: 60px;"
        @click="autoEvent"
      >
        {{ showStatus=='auto' ? '返回编辑' : '查看审核意见' }}
      </el-button>
    </div>

    <addFrom 
      v-show="showStatus=='add'"
      :id="infoData.id" 
      :info-data="infoData" 
      @getList="getList" 
      @close="close"
    />
    <autoList
      v-show="showStatus=='auto'"
      :id="id" 
      :info-data="infoData" 
      @getList="getList" 
      @close="close"
    />
  </div>
</template>

<script>
import { zstjDetail } from '../../apiUrl'
import addFrom from './addForm.vue'
import autoList from './autoList.vue'
export default {
  name: 'EdiT',
  components: {
    addFrom,
    autoList
  },
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      infoData: {},
      showStatus: 'add', // add or auto
    }
  },
  created () {
    this.getZstjDetail();
  },
  methods: {
    getZstjDetail() {
      zstjDetail({id: this.id}).then(res=>{
        this.infoData = res;
      })
    },
    getList() {
      this.$emit('getList');
    },
    close() {
      this.$emit('close');
    },
    autoEvent(){
      // this.$emit('processEvent', this.info)
      if(this.showStatus == 'auto'){
        this.showStatus = 'add'
      }else{
        this.showStatus = 'auto';
      }
      
    }
  },
}
</script>

<style lang="scss" scoped>
  .edit{
    padding: 0 12px;
    position: relative;
    .close{
      width: 30px;
      height: 30px;
      position: absolute;
      right: 10px;
      top: 10px;
      z-index: 999999;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .el-icon-circle-close{
        font-size: 24px;
      }
    }
    .title{
      position: relative;
      padding-left: 35px;
      border-bottom: 1px solid #cacaca;
      line-height: 45px;
      font-size: 14px;
      color: #101010;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &::before{
        content: '';
        width: 5px;
        height: 18px;
        background: rgba(20, 66, 119, 0.98);
        position: absolute;
        top: 15px;
        left: 15px;
      }
      .el-button--mini{

      }
    }
  }
</style>