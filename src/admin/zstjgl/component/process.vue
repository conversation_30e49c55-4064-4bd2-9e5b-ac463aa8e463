<template>
  <div class="process">
    <div class="close">
      <i
        class="el-icon-circle-close"
        @click="close"
      />
    </div>
    <div class="title">
      <div class="label">
        招商情报审核
      </div>
      <div class="btn-con">
        <el-button
          type="primary"
          size="mini"
          class="prev"
          @click="eventClick('1')"
        >
          上一条
        </el-button>
        <el-button
          type="primary"
          size="mini"
          class="next"
          @click="eventClick('2')"
        >
          下一条
        </el-button>
      </div>
    </div>
    <div class="process-con">
      <div class="left">
        <Detail
          :id="newId"
          v-loading="delLoading"
        />
      </div>
      <div class="right">
        <div class="right-top">
          审核意见
        </div>
        <div class="right-list">
          <div class="label">
            审核结果：
          </div>
          <div class="other">
            <el-radio
              v-model="form.auditStatus"
              label="1"
            >
              通过
            </el-radio>
            <el-radio
              v-model="form.auditStatus"
              label="0"
            >
              不通过
            </el-radio>
          </div>
        </div>
        <div class="right-list">
          <div class="label">
            审核意见说明：
          </div>
        </div>
        <div class="right-list">
          <div class="other">
            <el-input
              v-model="form.auditOpinion"
              type="textarea"
              :rows="3"
              placeholder="请输入审核意见"
            />
          </div>
        </div>
        <div class="right-list cen">
          <el-button
            size="mini"
            @click="close"
          >
            取消
          </el-button>
          <el-button
            v-loading="delLoading"
            type="primary"
            size="mini"
            @click="yes"
          >
            确定
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { zstjDetail, zstjAudit, getBeforeOrAfterById } from '../../apiUrl'
  // import { formatDate } from '@/utils/utils'
  import Detail from './detail.vue'
  export default {
    name: 'ProcessZx',
    components: {
      Detail
    },
    props: {
        id: {
          type: String,
          default: null
        },
      },
    data() {
        return {
          newId: '',
          infoData: {},
          delLoading:false,
          form:{
            id: '',
            auditStatus: '1', // 审核状态 0审核不通过 1审核通过
            auditOpinion: '', // 审核意见
          }
        }
      },
      created () {
        //this.getZstjDetail();
        this.newId = this.id;
      },
      methods: {
        getZstjDetail() {
          zstjDetail({id: this.id}).then(res=>{
            this.infoData = res;
          })
        },
        close(){
          this.$emit('close')
          this.$emit('getList')
        },
        yes(){
          if(this.delLoading){
            return 
          }
          let data = this.form;
          data.id = this.newId;
          if(!data.auditOpinion && data.auditStatus==0){
            this.$message.error('请填写审核意见！')
            return 
          }
          this.delLoading = true
          zstjAudit(data).then(()=>{
            this.$message.success('审核已提交！')
            //this.$emit('close')
            //this.$emit('getList')
            this.eventClicks('1')
          }).catch(()=>{
            this.delLoading = false
          })
        },
                // 上一条下一条点击
         eventClicks(e){
          let data = {
            id: this.newId,
            type: e
          }
          this.delLoading=true
          getBeforeOrAfterById(data).then(res=>{
            if(res){
              this.infoData = res;
              this.newId = res.id;
            }else{
              if(e==1){
                this.$message('已经是第一条了')
                this.$emit('close')
                this.$emit('getList')
              }else{
                this.$message('已经是最后一条了')
              }
              
            }
          }).finally(() => {
             this.delLoading = false;
          });
        },
        // 上一条下一条点击
        eventClick(e){
          let data = {
            id: this.newId,
            type: e
          }
          this.delLoading=true
          getBeforeOrAfterById(data).then(res=>{
            if(res){
              this.infoData = res;
              this.newId = res.id;
            }else{
              if(e==1){
                this.$message('已经是第一条了')
              }else{
                this.$message('已经是最后一条了')
              }
              
            }
          }).finally(() => {
             this.delLoading = false;
          });
        }
      },
  }
</script>

<style lang="scss" scoped>
  .process{
    background: #fff;
    box-sizing: border-box;
    padding: 15px;
    border-radius: 20px;
    min-height: calc(100vh - 90px);
    position: relative;
    .close{
      width: 30px;
      height: 30px;
      position: absolute;
      right: 10px;
      top: 10px;
      z-index: 999999;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .el-icon-circle-close{
        font-size: 24px;
      }
    }
    .title{
      position: relative;
      padding-left: 35px;
      border-bottom: 1px solid #cacaca;
      line-height: 45px;
      font-size: 14px;
      color: #101010;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &::before{
        content: '';
        width: 5px;
        height: 18px;
        background: rgba(20, 66, 119, 0.98);
        position: absolute;
        top: 15px;
        left: 15px;
      }
      .btn-con{
        display: flex;
        margin-right: 110px;
        .btn{
          margin-right: 20px;
        }
      }
      .el-button--mini{

      }
    }
    &-con{
      display: flex;
      justify-content: space-between;
      padding-top: 10PX;
      .left{
        width: 60%;
        border-right: 1px solid #bbb;
        box-sizing: border-box;
        padding: 0 20px;
        .top{
          .txt1{
            font-size: 16px;
            line-height: 38px;
            color: rgba(1,118,255,1);
          }
          .txt2{
            font-size: 14px;
            padding-top: 10px;
            color: #000;
            span{
              margin-left: 30px;
            }
          }
        }
      }
      .right{
        width: 40%;
        padding: 30px;
        &-top{
          font-size: 16px;
          font-weight: bold;
          padding-bottom: 10px;
          border-bottom: 1px dashed #bbb;
          margin-bottom: 20px;
        }
        &-list{
          display: flex;
          padding-bottom: 20px;
          .label{
            width: 100px;
          }
          .other{
            width: 100%;
          }
          &.cen{
            justify-content: center;
          }
        }
      }
    }
  }
</style>