import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token-DaDa'

export function getToken() {
  return localStorage.getItem(TokenKey)
}

export function setToken(token) {

  return localStorage.setItem(Token<PERSON>ey, token)
}

export function removeToken() {

  localStorage.removeItem('userInfo')
  localStorage.removeItem('accountType')
  return localStorage.removeItem(TokenKey)
}

export function getOrg() {
  return localStorage.getItem('orgCode') || ""
}

