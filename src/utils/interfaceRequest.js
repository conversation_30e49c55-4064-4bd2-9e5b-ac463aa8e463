import axios from 'axios';
import resetMessage from './resetMessage';
// eslint-disable-next-line no-unused-vars
import { MessageBox, Message } from 'element-ui';
import store from '@/store';
import { getToken } from '@/utils/auth';
import { getPathId } from '@/utils/utils';
import router from '@/router';
//const baseURL ='https://pangustg.idicc.cn/'//预发
const baseURL = process.env.VUE_APP_PORT_URL;

// create an axios instance
const service = axios.create({
  baseURL,
  // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 30000, // request timeout
});

// request interceptor
service.interceptors.request.use(
  (config) => {
    config.headers['appId'] = 12;
    if (store.getters.token) {
      config.headers['Content-Type'] =
        'multipart/form-data; boundary=----WebKitFormBoundaryyBCdfJo8qTLX6LQF';
      if (config.url === '/dpar/industryReport/upload') {
        config.headers['Content-Type'] = 'multipart/form-data';
      } else {
        config.headers['Content-Type'] = 'application/json';
      }
      config.headers['token'] = getToken();
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
let lastExecutedTime = 0;
let isExecuting = false;
let isgo = true;
// response interceptor
service.interceptors.response.use(
  (response) => {
    /*  if(response.headers?.token){
       store.dispatch("user/logins", response.headers.token); 
     } */
    const res = response.data;
    if (res.code === 'SUCCESS' || res.code === 20000) {
      return res.result;
    } else {
      if (
        res.size ||
        res.code === 'INVESTMENT_ENTERPRISE_RECOMMEND_ADD_ERROR' ||
        res.code === 'INVESTMENT_ENTERPRISE_RECOMMEND_BATCH_COPY_ERROR'
      ) {
        return res;
      } else if (res.code === 'NO_CHAIN_ACL') {
        if (!isgo) {
          return;
        }
        isgo = false;
        let maxscreen = [
          '/dashboard',
          '/IndustryGraph',
          '/MapEcharts',
          '/attractInvestment',
        ];
        const currentRoute = router.currentRoute.path;
        // console.log(currentRoute,'currentRoute');
        resetMessage.error('暂无该产业链权限');
        setTimeout(() => {
          if (maxscreen.includes(currentRoute)) {
            isgo = true;
            return router.push('/home-page');
          } else {
            isgo = true;
            return this.$router.push(`/403`);

            // window.$wujie?.bus.$emit('newRouter', { path: '/403' });
          }
        }, 1000);
      } else {
        /* if(res?.msg.indexOf('地图数据不存在')>-1){
          return res.result
        } */
        Message({
          message: res.msg || '请求错误',
          type: 'error',
          duration: 5 * 1000,
        });
        // if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
        //   // to re-login
        //   MessageBox.confirm('', {
        //     confirmButtonText: 'Re-Login',
        //     cancelButtonText: 'Cancel',
        //     type: 'warning'
        //   }).then(() => {
        //     store.dispatch('user/resetToken').then(() => {
        //       location.reload()
        //     })
        //   })
        // }
        return Promise.reject(new Error(res.msg || 'Error'));
      }
    }
  },
  (error) => {
    if (error?.response?.status == 401 || error?.response?.status == 403) {
      Message({
        message: '未登录或登录超时',
        type: 'error',
        duration: 1000,
      });
      store.dispatch('user/logout');
      router.push('/login');
      // // let route=  ['/IndustryGraph','/MapEcharts','/dashboard','/dashboard']
      // let redirectUrl = window.location.href.split("#")?.[1]?.split("?")?.[0]
      // let redirectId = getPathId()
      // localStorage.setItem('redirectId', redirectId)
      // // redirectUrl=  route.includes(redirectUrl) ?'/dashboard':redirectUrl
      // error?.response?.status == "401" && localStorage.setItem('redirect', `${redirectUrl}`)
      // let redirectName = JSON.parse(localStorage.getItem('MAINUSERINFO') || "{}").username
      // error?.response?.status == "401" && localStorage.setItem('redirectName', redirectName)

      // this.$router.push(error?.response?.status == 401 ? '/login' : '/403')

      // window.$wujie?.bus.$emit('newRouter', { path: error?.response?.status == 401 ? '/login' : '/403' });
      // const currentTime = Date.now();
      // if (isExecuting && currentTime - lastExecutedTime < 1000) {
      //   return Promise.reject(error);
      // }
      // lastExecutedTime = currentTime;
      // isExecuting = true;
      // store.dispatch('user/logout');
      // router.push('/login');
      // resetMessage.error(error.response.data.message || '未登录或登录超时')
      // setTimeout(() => {
      //   isExecuting = false;
      // }, 1000);
    } else if (error.message.includes('timeout')) {
      Message({
        message: '网络连接超时,请稍后再试',
        type: 'error',
        duration: 5 * 1000,
      });
    } else {
      Message({
        message: '网络连接超时,请稍后再试',
        type: 'error',
        duration: 5 * 1000,
      });
    }
    return '';
  }
);

export default service;
