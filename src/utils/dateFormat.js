import dayjs  from "dayjs";

export function findNodeByCode(nodes, targetCode) {
  for (let node of nodes) {
    if (node.code === targetCode) {
      return getAllChildCodes(node);
    } else if (node.children) {
      let result = findNodeByCode(node.children, targetCode);
      if (result) {
        return result;
      }
    }
  }
  return null;
}

export function getAllChildCodes(node) {
  let childCodes = [];
  if (node.children) {
    for (let child of node.children) {
      childCodes.push(child.code);
      if (child.children) {
        childCodes = childCodes.concat(getAllChildCodes(child));
      }
    }
  }
  return childCodes;
}
export function disableTree(treeData) {
  var newTreeData = JSON.parse(JSON.stringify(treeData)); // 克隆一份树形结构数据，避免直接修改原数据
  for (var i = 0; i < newTreeData.length; i++) {
    var node = newTreeData[i];
    node.disabled = true; // 将当前节点禁用
    if (node.aclDTOList) {
      node.aclDTOList = disableTree(node.aclDTOList); // 递归遍历子节点，并禁用每个子节点
    }
  }
  return newTreeData; // 返回禁用后的树形结构数据
}
export function xzdisableTree(treeData,list) {
  var newTreeData = JSON.parse(JSON.stringify(treeData)); // 克隆一份树形结构数据，避免直接修改原数据
  for (var i = 0; i < newTreeData.length; i++) {
    var node = newTreeData[i];
     if(list.indexOf(node.id)!=-1){
      node.disabled = true; // 将当前节点禁用
    } 
    if (node.childDeptList) {
      node.childDeptList = xzdisableTree(node.childDeptList,list); // 递归遍历子节点，并禁用每个子节点
    }
  }
  return newTreeData; // 返回禁用后的树形结构数据
}
/**
 * 时间格式化
 * @param time 为空时，返回数据对应的key为空字符串
 * @param keys
 * @returns {{}}
 */
export function formTimeSwtich (time, keys = ['staTime', 'endTime']) {
  let result = {}
  if (Array.isArray(keys) && keys.length === 2) {
    result[keys[0]] = time && time[0] ? time[0] : ""
    result[keys[1]] = time && time[1] ? time[1] : ""
    //time && time[0] && (result[keys[0]] = time[0])
    //time && time[1] && (result[keys[1]] = time[1])
  }
  return result
}

export function getTreeLength(data) {
  let length = 0;
  for (let i = 0; i < data.length; i++) {
    length++; 
    if (data[i].aclDTOList) {
      length += getTreeLength(data[i].aclDTOList); 
    }
  }
  return length;
}
export function getTreeLength2(data) {
  let length = 0;
  for (let i = 0; i < data.length; i++) {
    length++; 
    if (data[i].childDeptList) {
      length += getTreeLength2(data[i].childDeptList);
    }
  }
  return length;
}
/**
 * 时间搓转换
 * @param time 参数时间
 * @param type  date，time
 * @returns {string}
 */
export function timestamp(time,type='date'){
  let timestamp4 = new Date(time);
  let getTime = timestamp4.toLocaleDateString().replace(/\//g, "-");
  if(type == 'time'){
    getTime += " " + timestamp4.toTimeString().substr(0, 8)
  }
  return getTime;
}

/**
 * 时间搓转换
 * @param time 参数时间
 * @param type 所取格式  date，time
 * @returns {string}
 */
export function formatTime(time,type='date'){
  if(!time){return '-'}
  let crtTime = time;
  let o = {
    y : crtTime.getFullYear(),
    M : crtTime.getMonth()+1,     //月份 
    d : crtTime.getDate(),     //日 
    h : crtTime.getHours(),     //小时 
    m : crtTime.getMinutes(),     //分 
    s : crtTime.getSeconds(),     //秒 
    S : crtTime.getMilliseconds()    //毫秒 
  }; 
  let getTime = o.y + '-' + (o.M > 9 ? o.M : ('0'+o.M)) +'-'+ (o.d > 9 ? o.d : ('0'+o.d))
  if(type == 'time'){
    getTime += ' '+(o.h > 9 ? o.h : '0'+o.h)+':'+(o.m > 9 ? o.m:'0'+o.m)+':'+(o.s > 9 ? o.s : '0'+o.s)
  }
  return getTime;
}

/**
 * 格式化日期，返回相应格式的日期
 * @param date 字符串类型
 * @param fmt 格式
 * @returns {string}
 */
export function formatDate(date="",fmt){
  return dayjs(date).format(fmt)
}

/**
 * 获取当前时间
 * @param fmt ：空时返回time,其他进行格式化
 * @returns {*|number}
 */
export function getNowDate(fmt=""){
  return fmt?dayjs().format(fmt):Date.now()
}

/**
 * 获取两个 Dayjs 对象的时间差，默认毫秒
 * @param date1 "2019-01-25"
 * @param date2 "2018-06-05"
 * @param type "day,month, year .."
 */
export function diff(date1,date2,type="millisecond"){
  const dt1=dayjs(date1)
  const dt2=dayjs(date2)
  let d=dt2.diff(dt1,type)
  return d
}

/**
 * 格式化时间
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string')) {
      if ((/^[0-9]+$/.test(time))) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * 计算时间与当前时间差
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function diffFormatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
        d.getMonth() +
        1 +
        '月' +
        d.getDate() +
        '日' +
        d.getHours() +
        '时' +
        d.getMinutes() +
        '分'
    )
  }
}

/**
 * 获取时间范围
 * @param diff 时间差， 毫秒
 * 当前：0
 * 1周：3600 * 1000 * 24 * 7；
 * 1月：3600 * 1000 * 24 * 30；
 * 3月：3600 * 1000 * 24 * 90；
 * @returns {string[]}
 */
export function dateRange(diff=0){
  let end = new Date();
  let start=new Date();
  if(diff==0){
    start = formatDate(start,"YYYY-MM-DD 00:00:00");
  }else{
    start.setTime(start.getTime() - diff);
  }
  let dateRange = [formatDate(start, diff==0?"YYYY-MM-DD 00:00:00":"YYYY-MM-DD HH:mm:ss"), formatDate(end, diff==0?"YYYY-MM-DD 23:59:59":"YYYY-MM-DD HH:mm:ss")]
  return dateRange
}