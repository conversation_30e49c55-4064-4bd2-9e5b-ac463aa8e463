/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 *
 * Parse the time to string
 * @deprecated 过期
 * @see dateFormat#parseTime
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
 export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string')) {
      if ((/^[0-9]+$/.test(time))) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 *
 * @deprecated 过期方法，
 * @see #diffFormatTime
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
        d.getMonth() +
        1 +
        '月' +
        d.getDate() +
        '日' +
        d.getHours() +
        '时' +
        d.getMinutes() +
        '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i)
    if (code > 0x7f && code <= 0x7ff) {
      s++
    } else if (code > 0x7ff && code <= 0xffff) {
      s += 2
    }
    if (code >= 0xDC00 && code <= 0xDFFF) {
      i--
    }
  }
  return s
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) {
    return ''
  }
  return cleanArray(
      Object.keys(json).map(key => {
        if (json[key] === undefined) {
          return ''
        }
        return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
      })
  ).join('&')
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

/**
 * 合并两个对象，给予最后一个优先级
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  try {
    return Object.assign(target, source);
  } catch (err) {
    Object.keys(source).forEach(property => {
      const sourceProperty = source[property]
      if (typeof sourceProperty === 'object') {
        target[property] = objectMerge(target[property], sourceProperty)
      } else {
        target[property] = sourceProperty
      }
    })
    return target
  }
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString =
        classString.substr(0, nameIndex) +
        classString.substr(nameIndex + className.length)
  }
  element.className = classString
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) {
          context = args = null
        }
      }
    }
  }

  return function (...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) {
      timeout = setTimeout(later, wait)
    }
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

// 定义一个深拷贝函数  接收目标target参数
export function deepClone2(target) {
  // 定义一个变量
  let result;
  // 如果当前需要深拷贝的是一个对象的话
  if (typeof target === 'object') {
    // 如果是一个数组的话
    if (Array.isArray(target)) {
      result = []; // 将result赋值为一个数组，并且执行遍历
      for (let i in target) {
        // 递归克隆数组中的每一项
        result.push(deepClone2(target[i]))
      }
      // 判断如果当前的值是null的话；直接赋值为null
    } else if(target===null) {
      result = null;
      // 判断如果当前的值是一个RegExp对象的话，直接赋值
    } else if(target.constructor===RegExp){
      result = target;
    }else {
      // 否则是普通对象，直接for in循环，递归赋值对象的所有值
      result = {};
      for (let i in target) {
        // 时间类型处理
        if(target[i] instanceof Date) {
          result[i] = new Date(target[i].valueOf());
        } else {
          result[i] = deepClone2(target[i]);
        }
      }
    }
    // 如果不是对象的话，就是基本数据类型，那么直接赋值
  } else {
    result = target;
  }
  // 返回最终结果
  return result;
}

/**
 * 浅拷贝
 * @param src
 * @returns {{}}
 */
export function shallowCopy(src) {
  let dst = {}
  for (let prop in src) {
    if (src.hasOwnProperty(prop)) {
      dst[prop] = src[prop]
    }
  }
  return dst
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}

/**
 *去重复所选停车点
 * @param arr 集合/数组
 * @param isEqual(obj1,obj2) 判断条件函数，return true/ false
 * @returns {*} Array
 */
export function uniqueArray(arr,isEqual=function (){}){
  for(let i = 0; i<arr.length; i++){
    for(let j=i+1; j<arr.length; j++){
      if(isEqual(arr[i],arr[j])){
        arr.splice(j,1);
        j--;
      }
    }
  }
  return arr;
}


/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + ''
  const randomNum = parseInt((1 + Math.random()) * 65536) + ''
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) {
    ele.className += ' ' + cls
  }
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}

/**
 * 验证不可输入特殊字符
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function checkInputString(rule, value, callback) {
  if (value) {
    if (value.match(/^[\u4E00-\u9FA5A-Za-z0-9_]+?$/)) {
      callback()
    } else {
      callback(new Error('不可输入特殊字符'))
    }
  }
}

/**
 * 验证数字可为正负，数值大小，小数位数验证 参数 isNegative(是否可为负数，默认为false)，maxValue(不超过的最大值,默认为10000000)，decimal(小数位数，默认7位)
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function checkDecimal(rule, value, callback) {
  if (value) {
    value += ''
    var negative = rule.isNegative ? rule.isNegative : false
    var maxValue = rule.maxValue ? rule.maxValue : 10000000
    var minValue = 0
    var decimal = rule.decimal ? rule.decimal : 7
    if (rule.decimal === 0) {
      decimal = 0
    }
    var re = null
    if (negative) {
      minValue = '-' + maxValue
      // eslint-disable-next-line no-eval
      re = eval('/^(\\-)?(0|[1-9][0-9]*)(\\.\\d{0,' + decimal + '})?$/')
    } else {
      // eslint-disable-next-line no-eval
      re = eval('/^(([1-9]{1}\\d*)|(0{1}))(\\.\\d{0,' + decimal + '})?$/')
    }
    if (isNaN(value)) {
      callback(new Error('请输入数字'))
    } else {
      if (value.match(re)) {
        var num = parseFloat(value)
        if (negative) {
          if (num > minValue && num < maxValue) {
            callback()
          } else {
            callback(new Error('超出有效输入范围'))
          }
        } else {
          if (num >= minValue && num < maxValue) {
            callback()
          } else {
            callback(new Error('超出有效输入范围'))
          }
        }
      } else {
        callback(new Error('输入有误'))
      }
    }
  } else {
    callback()
  }
}

/**
 * 验证数字输入范围在0-10000000之间，且小数可7位
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function checkNumber(rule, value, callback) {
  if (value) {
    value += ''
    if (value.match(/^\d+$/)) {
      var num = parseFloat(value)
      if (num >= 0 && num < 10000000) {
        callback()
      } else {
        callback(new Error('超出有效输入范围'))
      }
    } else {
      callback(new Error('请输入正整数'))
    }
  } else {
    callback()
  }
}

/**
 * 验证经度输入范围在-180-180之间，且小数可7位
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function checkLon(rule, value, callback) {
  if (value) {
    value += ''
    if (value.match(/^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,7})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/)) {
      callback()
    } else {
      callback(new Error('经度为-180~180,小数限7位!'))
    }
  } else {
    callback()
  }
}

/**
 * 验证经度输入范围在-180-180之间，且小数可7位
 * @param longitude
 * @returns {boolean}
 */
export function checkLongitude(longitude){
  if(longitude){
    let longRegx=/^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,7})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/
    return longRegx.test(longitude)
  }
  return false
}

/**
 * 验证纬度输入范围在-90~90之间，且小数可7位
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function checkLat(rule, value, callback) {
  if (value) {
    value += ''
    if (value.match(/^(\-|\+)?([0-8]?\d{1}\.\d{0,7}|90\.0{0,6}|[0-8]?\d{1}|90)$/)) {
      callback()
    } else {
      callback(new Error('纬度为-90~90,小数限7位'))
    }
  } else {
    callback()
  }
}

/**
 * 验证纬度输入范围在-90~90之间，且小数可7位
 * @param latitude
 * @returns {boolean}
 */
export function checkLatitude(latitude){
  if(latitude){
    let latitudeRegx = /^(\-|\+)?([0-8]?\d{1}\.\d{0,7}|90\.0{0,6}|[0-8]?\d{1}|90)$/
    return latitudeRegx.test(latitude)
  }
  return false
}

/**
 * 匹配中文，英文字母和数字
 * @param value 内容
 * @returns {boolean} true 正确，false不正确
 */
export function regxZhEnNum(value){
  let regx=/^[\u4e00-\u9fa5a-zA-Z0-9]+$/g
  return regx.test(value)
}

/**
 * 判断内空是否有特殊字符
 * @param value
 * @returns {boolean}
 */
export function regx(value){
  let regx=/[`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、]/g
  return  regx.test(value)
}




/**
 * 验证输入长度
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function checkLong(rule, value, callback) {
  if (value) {
    if (value.length < rule.length && value.length >= 0) {
      callback()
    } else {
      callback(new Error('输入不可超过' + rule.length + '个字符'))
    }
  } else {
    callback()
  }
}

/**
 * 验证不能为空
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function checkNull(rule, value, callback) {
  if (!value) {
    if (value === 0) {
      callback()
    } else {
      callback(new Error(rule.message))
    }
  } else {
    value += ''
    if (value.length > 0) {
      callback()
    } else {
      callback(new Error(rule.message))
    }
  }
}

/**
 * 验证数组
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export function checkArray(rule, value, callback) {
  // console.log(value)
  if (value.length > 0) {
    callback()
  } else {
    callback(new Error(rule.message))
  }
}

/**
 * 移除某个对象中为空的属性,返回一个新对象
 * @param data
 * @returns {{}}
 */
export function rmUselessProperty(data) {
  let nd = {};
  for (let p in data) {
    let prop = data[p];
    if (typeof (prop) == 'string' || typeof (prop) == 'object' || typeof (prop)
        == 'undefined') {
      if (!prop) {
        continue;
      }
    }
    nd[p] = prop;
  }
  return nd;
}



/**
 * 判断val是否为空：
 * @param val
 * @returns {空:true, 非空：false}
 * isEmpty("")=true ;
 * isEmpty([])=true ;
 * isEmpty({})=true ;
 * isEmpty(0)=true ;
 * isEmpty(undefined)=true ;
 * isEmpty(null)=true ;
 * isEmpty(1)=false
 */
export function isEmpty(val) {
  // null or undefined
  if (val == null) {
    return true;
  }
  if (typeof val === 'boolean') {
    return false;
  }
  if (typeof val === 'number') {
    return !val;
  }
  if (val instanceof Error) {
    return val.message === '';
  }
  switch (Object.prototype.toString.call(val)) {
      // String or Array
    case '[object String]':
    case '[object Array]':
      return !val.length;
      // Map or Set or File
    case '[object File]':
    case '[object Map]':
    case '[object Set]': {
      return !val.size;
    }
      // Plain Object
    case '[object Object]': {
      return !Object.keys(val).length;
    }
  }
  return false;
}

/**
 * 带 ¥ 单位的金额
 * @param val 金额：分
 * @param prefix 前缀 ¥
 * @param suffix 单位 元
 * @returns {string}
 */
export function moneyFormatUnit(val,prefix="¥",suffix=""){
  return prefix+" "+moneyFormat(val)+" " +suffix
}

/**
 * 金额转换 分->元 保留2位小数 并每隔3位用逗号分开 1,234.56
 * @param val 金额 分
 * @param isDivide 是否使用逗号分隔，默认分隔
 * @returns {string}
 */
export function moneyFormat(val,isDivide=false){
  if(!isEmpty(val)){
    let str = (val/100).toFixed(2) + '';
    if(isDivide){
      //取到整数部分
      let intSum = str.substring(0,str.indexOf(".")).replace( /\B(?=(?:\d{3})+$)/g, ',' );
      //取到小数部分搜索
      let dot = str.substring(str.length,str.indexOf("."))
      let ret = intSum + dot;
      return ret;
    }else{
      return str
    }
  }else{
    // 为0时，直接返回，不带小数
    return "0"
  }
}

/**
 * 获取对象属性中值，
 * @param obj 对象
 * @param propertyName 属性名称 a 或 a.b.c
 * @returns {string|*|string}
 */
export function getPropertyValue(obj,propertyName) {
  if(!obj || !propertyName){
    return ""
  }
  //多个属性值
  if(propertyName.indexOf('.')!==-1){
    let names=propertyName.split('.')
    let tmp
    for (const idx in names) {
      tmp= tmp? (tmp.hasOwnProperty(names[idx])?tmp[names[idx]]:"") : (obj.hasOwnProperty(names[idx])?obj[names[idx]]:"")
    }
    return tmp
  }else{
    return obj.hasOwnProperty(propertyName)? obj[propertyName]:""
  }
}

/**
 * 获取url文件名
 * @param path
 * @returns {string|*}
 */
export function getFileName(path) {
  let pos1 = path.lastIndexOf('/');
  let pos2 = path.lastIndexOf('\\');
  let pos = Math.max(pos1, pos2)
  if (pos < 0) {
    return path;
  } else {
    return path.substring(pos + 1);
  }
}

/**
 * 验证车牌是否输入正确
 * @param plate 当前车牌号
 * @returns {boolean}
 */
export function checkPlate(plate){
  if(isEmpty(plate))return false
  //转换成大写
  let upperPlate=plate.toUpperCase()
  //包含绿牌车和普通车
  let regx=/^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[a-zA-Z](([DF]((?![IO])[a-zA-Z0-9](?![IO]))[0-9]{4})|([0-9]{5}[DF]))|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1})$/
  return regx.test(upperPlate)
  //
  // let reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;
  // let bool=reg.test(upperPlate)
  // if(!bool){
  //   //绿牌车
  //   reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/;
  //   return reg.test(upperPlate)
  // }else{
  //   return true
  // }
}

/**
 * 校验车牌是否输入正确
 * @param rule
 * @param value
 * @param callback
 */
export function validatePlate(rule, value, callback) {
  if (checkPlate(value)) {
    callback()
  } else {
    callback(new Error(rule.message))
  }
}

/**
 * 验证手机号
 * @param rule
 * @param value
 * @param callback
 */
export function validateMobile(rule, value, callback) {
  if (!value) {
    callback(new Error(rule.message));
  } else {
    //this.ruleForm.mobile =value.replace(/[^0-9]/g, '', '');
    if (!(/^[1][3,4,5,7,8,9][0-9]{9}$/.test(value))) {
      callback(new Error(rule.message));
    } else {
      callback();
    }
  }
}

/**
 * 遍历JSON树
 * @param json json
 * @param key 节点key
 * @param nodeId 数据
 * @returns {null|*}
 */
export function getJsonTreeNode (json, key, nodeId) {
  // 1.第一层 root 深度遍历整个JSON
  let result = null
  for (let i = 0; i < json.length; i++) {
    let obj = json[i]
    // 没有就下一个
    if (!obj) {
      continue
    }
    // 2.有节点就开始找，一直递归下去
    if (obj[key] === nodeId) {
      // 找到了与nodeId匹配的节点，结束递归
      return obj
      // break
    } else {
      // 3.如果有子节点就开始找
      if (obj.children && obj.children.length > 0) {
        // 递归往下找
        result = getJsonTreeNode(obj.children, key, nodeId)
        if (result) {
          break
        }
        // console.log(node)
      } else {

      }
    }
  }
  return result
}

/**
 * 判断一个对象中是否存在数据
 * @param object 当前对象
 * @param exceptKeys 需要排除属性Keys
 * @returns {boolean} true:有值 ， false：无值
 */
export function isObjectHasValue(object,exceptKeys=[]){
  if(isEmpty(object)){
    return false
  }
  for(let key in object){
    // console.log("isObjectHasValue: key:"+key+" value:"+object[key])
    if(exceptKeys.length!=0 && exceptKeys.includes(key)){
      continue
    }
    if(!isEmpty(object[key])){
      return true
    }
  }
  return false
}

// utils.js
export const download = (res) => {
  // 创建文件对象
  let reader = new FileReader()
  // 监听读取完毕
  reader.onloadend = function () {
    // 返回base64位的文件内容
    let url = reader.result
    // 下载
    window.location.href = url
  }
  // 开始读取指定的Blob中的内容。一旦完成，result属性中将包含一个data: URL格式的Base64字符串以表示所读取文件的内容。
  reader.readAsDataURL(res)
}


	/**
			* 通过文件流下载文件
			* @file 文件流，必输
			* @name 下载的文件名，必输
			* @type 下载的文件类型，默认xls，非必输
			**/
      export function  downloadFile(file, name, type) {
				if (!file) {
					return
				}
				const url = window.URL.createObjectURL(
					new Blob([file], { 
						type: type || 'application/vnd.ms-excel' 
					})
				);
				const link = document.createElement('a');
				link.style.display = 'none';
				link.href = url;
				// 设置文件名；download属性定义了下载链接的地址而不是跳转路径
				link.setAttribute('download', name);
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link); // 下载完成后移除元素
				window.URL.revokeObjectURL(url); // 释放掉blob对象
			}
