import axios from 'axios'
import { getPathId } from '@/utils/utils'

// eslint-disable-next-line no-unused-vars
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import resetMessage from './resetMessage'
// import { getToken } from '@/utils/auth'
import { getToken, getOrg } from "@/utils/auth"; // get token from cookie
import router from '@/router'
//const baseURL ='https://pangustg.idicc.cn/'//预发
const baseURL = process.env.VUE_APP_PORT_URL

// create an axios instance
const service = axios.create({
  baseURL,
  // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 30000// request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    config.headers['appId'] = 12;
    if (getToken()) {
      let orgCode = localStorage.getItem('orgCode')
      let selectedOrgCode = sessionStorage.getItem('selectedOrg')
      if (!!orgCode && selectedOrgCode && orgCode !== selectedOrgCode) {
        sessionStorage.setItem('selectedOrg', orgCode);
        window.location.reload()
      }
      config.headers['Content-Type'] = 'multipart/form-data; boundary=----WebKitFormBoundaryyBCdfJo8qTLX6LQF'
      config.headers['Content-Type'] = 'application/json'
      config.headers['token'] = getToken()
      config.headers['OrgCode'] = getOrg()
    } else {
      config.headers['Content-Type'] = 'application/json'
      if (config.params && config.params.token) {
        config.headers['token'] = config.params.token
        delete config.params.token
      }
      if (config.data && config.data.token) {
        config.headers['token'] = config.data.token
        delete config.data.token
      }
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

let lastExecutedTime = 0;
let isExecuting = false;
let isgo = true
// response interceptor
service.interceptors.response.use(
  response => {
    /* 
     if(response.headers?.token){
       store.dispatch("user/logins", response.headers.token); 
     } */
    const res = response.data;
    if (res.code === "SUCCESS" || res.code === 20000) {
      return res
    } if (response.config.url === "/admin/enterprise/upload" || response.config.url === "http://pangudev.idicc.cn/admin/enterprise/upload") {
      return res
    } if (response.config.url === "/admin/orgIndustryChainRelation/atlasEnterpriseListExport" || response.config.url === "http://pangudev.idicc.cn/admin/orgIndustryChainRelation/atlasEnterpriseListExport") {
      return res
    }
    if (response.config.url === "/admin/orgIndustryChainRelation/mapEnterpriseListExport" || response.config.url === "http://pangudev.idicc.cn/admin/orgIndustryChainRelation/mapEnterpriseListExport") {
      return res
    }
    if (response.config.url === "/admin/industryLabel/bind/enterprise/excel" || response.config.url === "http://pangudev.idicc.cn/admin/industryLabel/bind/enterprise/excel") {
      return res
    }
    if (response.config.url === "/admin/enterprise/label/upload/bind" || response.config.url === "http://pangudev.idicc.cn/admin/enterprise/label/upload/bind") {
      return res
    }
    if (response.config.url === "/admin/listed/company/upload" || response.config.url === "http://pangudev.idicc.cn/admin/listed/company/upload") {
      return res
    }
    if (response.config.url === "/admin/org/business/enterprise/downloadTemplate" || response.config.url === "http://pangudev.idicc.cn/admin/org/business/enterprise/downloadTemplate") {
      return res
    } if (res.code === 'NO_CHAIN_ACL') {
      if (!isgo) {
        return
      }
      isgo = false
      let maxscreen = ['/dashboard', '/IndustryGraph', '/MapEcharts', '/attractInvestment']
      const currentRoute = router.currentRoute.path;
      // console.log(currentRoute,'currentRoute');
      resetMessage.error('暂无该产业链权限')
      setTimeout(() => {
        if (maxscreen.includes(currentRoute)) {
          isgo = true
          return router.push('/home-page');
        } else {
          isgo = true
          return this.$router.push('/403')
          // window.$wujie?.bus.$emit('newRouter', { path: '/403' });
        }
      }, 1000)
    }
    else {
      Message({
        message: res.msg || '请求错误',
        type: 'error',
        duration: 5 * 1000
      })
      // if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
      //   // to re-login
      //   MessageBox.confirm('', {
      //     confirmButtonText: 'Re-Login',
      //     cancelButtonText: 'Cancel',
      //     type: 'warning'
      //   }).then(() => {
      //     store.dispatch('user/resetToken').then(() => {
      //       location.reload()
      //     })
      //   })
      // }
      return Promise.reject(new Error(res.msg || 'Error'))
    }
  },
  error => {
    if (error?.response?.status == 401 || error?.response?.status == 403) {
      const currentTime = Date.now();
      if (isExecuting && currentTime - lastExecutedTime < 1000) {
        return Promise.reject('未登录或登录超时');
      }
      lastExecutedTime = currentTime;
      isExecuting = true;
      // resetMessage.error(error.response.data.message || '未登录或登录超时')
      setTimeout(() => {
        isExecuting = false;
      }, 1000);
      // let route=  ['/IndustryGraph','/MapEcharts','/dashboard','/dashboard']
      // let redirectUrl = window.location.href.split("#")?.[1]?.split("?")?.[0]
      // let redirectId=getPathId()
      // localStorage.setItem('redirectId',redirectId)
      // redirectUrl=  route.includes(redirectUrl) ?'/dashboard':redirectUrl
      // error?.response?.status == "401"&& localStorage.setItem('redirect',`/pangu${redirectUrl}`)
      // let redirectName=   JSON.parse(localStorage.getItem('MAINUSERINFO')||"{}")?.value?.username
      // error?.response?.status == "401"&&  localStorage.setItem('redirectName',redirectName)
      // window.$wujie?.bus.$emit('newRouter',{path:error?.response?.status == 401?'/login': '/403'});
      store.dispatch('user/logout');
      router.push('/login');
    } else if (error.message.includes('timeout')) {
      resetMessage.error('网络连接超时,请稍后再试')
    } else {
      Message({
        message: '网络连接超时,请稍后再试',
        type: 'error',
        duration: 5 * 1000
      })
    }
    return ''
  }
)

export default service
