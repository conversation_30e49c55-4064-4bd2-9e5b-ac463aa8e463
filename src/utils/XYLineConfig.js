export const YAxis = () => {
  return {
      axisTick: { show: false,}, //是否展示y轴坐标刻度
      splitLine: {
        show: true,
        lineStyle: {
         color: '#FFFFFF',
          width: 1,
         opacity:'0.18',
         type: 'dashed'
        }},
      axisLine: {
        lineStyle: {
          color: '#FFFFFF',
          width: 1,
         opacity:'0.18',
        }
      },
      axisLabel: {
        formatter: '{value} ',
        color: "#C9D2DD",
         
      }
    }
 
}
export const XAxis = (data,formatter) => {
  return {
  data,
    interval:0, // 展示所有标签
    // 倾斜
    axisLabel: {
      color: "#C9D2DD",
      interval: 0,
      formatter: function (value) {
        if (formatter) {
          return value.slice(0, 2)
        } else {
          return value
        }
      }
    },
    inside: false,
    axisTick: { //x轴坐标刻度
      show: false,
    },
  }
}