// 按需引入 Element UI 组件
import Vue from 'vue';
import {
  Button,
  Input,
  Select,
  Option,
  Table,
  TableColumn,
  Pagination,
  Dialog,
  Form,
  FormItem,
  DatePicker,
  Cascader,
  Tooltip,
  Loading,
  Message,
  MessageBox,
  Progress,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  // 添加更多可能用到的组件
  Radio,
  RadioGroup,
  Checkbox,
  CheckboxGroup,
  Switch,
  Upload,
  Tree,
  Steps,
  Step,
  Tabs,
  TabPane
} from 'element-ui';

// 注册组件
const components = [
  Button,
  Input,
  Select,
  Option,
  Table,
  TableColumn,
  Pagination,
  Dialog,
  Form,
  FormItem,
  DatePicker,
  Cascader,
  Tooltip,
  Progress,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Radio,
  RadioGroup,
  Checkbox,
  CheckboxGroup,
  Switch,
  Upload,
  Tree,
  Steps,
  Step,
  Tabs,
  TabPane
];

components.forEach(component => {
  Vue.use(component);
});

// 注册插件
Vue.use(Loading.directive);
Vue.prototype.$loading = Loading.service;
Vue.prototype.$message = Message;
Vue.prototype.$confirm = MessageBox.confirm;
Vue.prototype.$alert = MessageBox.alert;
Vue.prototype.$msgbox = MessageBox;
Vue.prototype.$notify = MessageBox;
