/**
 * Created by CTKJ-0224 on 2021/4/30 13:23.
 * @Description: 重置message，防止重复点击,重复弹出message弹框,
 *  挂载message:只能在vue.use(ElementUI)后面 ,否则无效
 *
 */

import {Message} from 'element-ui';

//Symbol('showMessage')
let messageInstance = null;
const resetMessage = (options) => {
  if (messageInstance) {
    messageInstance.close()
  }
  options.customClass='mzindex'
  messageInstance = Message(options)
}

//弹出前判断是否有相同弹出框，若有则关闭它再弹出新的
['error', 'success', 'info', 'warning'].forEach(type => {
  resetMessage[type] = options => {

    if (typeof options === 'string') {
      options = {
        message: options
      }
    }
    options.type = type,
    options.customClass='mzindex'
    return resetMessage(options)
  }
})
export const message = resetMessage

