// rem等比适配配置文件
// 基准大小 - 增加基准字体大小
const baseSize = 16; // 从 16 增加到 20
// 设计稿尺寸
const designWidth = 1920;
const designHeight = 1080;

// 设置 rem 函数
function setRem() {
  const clientWidth = document.documentElement.clientWidth;
  const clientHeight = document.documentElement.clientHeight;
    // 计算当前屏幕宽高比
    const currentRatio = clientWidth / clientHeight;
    // 设计稿宽高比
    const designRatio = designWidth / designHeight;
    
  // 当前页面宽度相对于 1920宽的缩放比例，可根据自己需要修改。
  let scale;

    
  if (currentRatio > designRatio) {
    // 如果当前屏幕更宽，则以高度为基准计算缩放比例
    scale = clientHeight / designHeight;
    // 处理内容居中
    const contentWidth = designWidth * scale;
    // const margin = (clientWidth - contentWidth) / 2;
    // document.body.style.margin = `0 ${margin}px`;
  } else {
    // 如果当前屏幕更窄或等于设计稿比例，则以宽度为基准
    scale = clientWidth / designWidth;
    document.body.style.margin = '0';
  }
  //console.log(scale,'scale');
  //console.log(baseSize, 'baseSize');
  //console.log( baseSize * scale, ' baseSize * scale');
  
  // 设置页面根节点字体大小，提高最大缩放比例并增加最小字体限制
  const fontSize = Math.max(baseSize * Math.min(scale, 2), 12); // 最大缩放2.5倍，最小字体18px
  document.documentElement.style.fontSize = fontSize + "px";
}
// 初始化
setRem();
// 改变窗口大小时重新设置 rem
window.onresize = function() {
  setRem();
};
setRem();
// 改变窗口大小时重新设置 rem
window.onresize = function() {
  setRem();
};

 