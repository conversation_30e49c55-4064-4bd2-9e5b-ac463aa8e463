import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { HEADER_TOKEN_KEY } from "@/api/apiConfig";
import { getToken, getOrg } from "@/utils/auth"; // get token from cookie

import { getPathId } from '@/utils/utils'

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_PORT_URL, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 10000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    let orgCode = localStorage.getItem('orgCode')
    let selectedOrgCode = sessionStorage.getItem('selectedOrg')
    if (!!orgCode && selectedOrgCode && orgCode !== selectedOrgCode) {
      sessionStorage.setItem('selectedOrg', orgCode);
      window.location.reload()
      // window.$wujie?.bus.$emit('reload')
    }
    // else{
    config.headers['Content-Type'] = 'multipart/form-data; boundary=----WebKitFormBoundaryyBCdfJo8qTLX6LQF'
    //'MAuthorization'
    config.headers[HEADER_TOKEN_KEY] = getToken()
    config.headers['OrgCode'] = getOrg()
    // }
    // consfig.processData = false
    // consfig.contentType = false
    return config
  },
  error => {
    // console.log(error)
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code != "0") {
      Message({
        message: res.msg || 'Error',
        type: 'error',
        duration: 5 * 1000
      })
      if (res.code == "401" || res.code == "403") {
        // // let route=  ['/IndustryGraph','/MapEcharts','/dashboard','/dashboard']
        //   let redirectUrl = window.location.href.split("#")?.[1]?.split("?")?.[0]
        //   let redirectId=getPathId()
        //   localStorage.setItem('redirectId',redirectId)
        //   // redirectUrl=  route.includes(redirectUrl) ?'/dashboard':redirectUrl
        //   res.code == "401"&&  localStorage.setItem('redirect',`/pangu${redirectUrl}`)
        //   let redirectName=   JSON.parse(localStorage.getItem('MAINUSERINFO')||"{}")?.value?.username
        //   res.code == "401"&& localStorage.setItem('redirectName',redirectName)
        //   window.$wujie?.bus.$emit('newRouter',{path:res.code == "401"?'/login': '/403'});

        store.dispatch('user/logout')
        this.$router.push("/login");
      }
      return res
      // return Promise.reject(new Error(res.msg || 'Error'))
    } else {
      return res
    }
  },
  error => {
    // console.log('err' + error) // for debug
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
