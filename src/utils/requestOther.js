import axios from 'axios'

// eslint-disable-next-line no-unused-vars
import { Message } from 'element-ui'
import { getToken, getOrg } from '@/utils/auth'
//const baseURL ='https://pangustg.idicc.cn/'//预发
const baseURL = process.env.VUE_APP_PORT_URL
// process.env.VUE_APP_PORT_URL
// create an axios instance
const service = axios.create({
  baseURL,
  // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 10000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    config.headers['appId'] = 12;
    if (getToken()) {
      let orgCode = localStorage.getItem('orgCode')
      let selectedOrgCode = sessionStorage.getItem('selectedOrg')
      if (!!orgCode && selectedOrgCode && orgCode !== selectedOrgCode) {
        sessionStorage.setItem('selectedOrg', orgCode);
        window.location.reload()
        // window.$wujie?.bus.$emit('reload')

      }
      // else{
      config.headers['Content-Type'] = 'multipart/form-data; boundary=----WebKitFormBoundaryyBCdfJo8qTLX6LQF'
      config.headers['Content-Type'] = 'application/json'
      config.headers['token'] = getToken()
      config.headers['OrgCode'] = getOrg()
      // }
    }
    return config
  },
  error => {
    // console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  response => {
    const res = response.data;
    return res
  },
  error => {
    // console.log('err' + error) // for debug
    Message({
      message: error,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
