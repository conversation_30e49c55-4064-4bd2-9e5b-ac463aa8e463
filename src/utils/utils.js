/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone');
  }
  const targetObj = source.constructor === Array ? [] : {};
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
}

export const formatDate = function (format, date = new Date()) {
  var o = {
    'M+': date.getMonth() + 1, //月份
    'd+': date.getDate(), //日
    'H+': date.getHours(), //小时
    'm+': date.getMinutes(), //分
    's+': date.getSeconds(), //秒
    'q+': Math.floor((date.getMonth() + 3) / 3), //季度
    S: date.getMilliseconds(), //毫秒
  };
  if (/(y+)/.test(format))
    format = format.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length)
    );
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return format;
};

/**
 * 过滤空值的属性
 * @param obj
 * @constructor
 */
export const filterData = function (obj) {
  for (var key in obj) {
    if (obj[key] == null || obj[key] === '' || obj[key].length === 0) {
      delete obj[key];
    }
  }
};

export const getPathId = function () {
  let path = localStorage.getItem('routerQuery');
  return path;
};

export const getEnterpriseIconByType = function (showLabelType, fileType) {
  let img =`https://static.idicc.cn/cdn/pangu/svg/0.${fileType}`;
  if (!showLabelType) {
    return img;
  }
  showLabelType = Number(showLabelType);
  switch (showLabelType) {
    case 1:
    case 8:
    case 9:
      img =`https://static.idicc.cn/cdn/pangu/svg/1-8-9.${fileType}`;
      break;
    case 2:
    case 11:
    case 20:
      img =`https://static.idicc.cn/cdn/pangu/svg/2-11-20.${fileType}`;
      break;
    case 7:
    case 19:
      img =`https://static.idicc.cn/cdn/pangu/svg/7-19.${fileType}`;
      break;
    case 16:
      img =`https://static.idicc.cn/cdn/pangu/svg/16.${fileType}`;
      break;
    case 6:
    case 22:
      img =`https://static.idicc.cn/cdn/pangu/svg/6-22.${fileType}`;
      break;
    case 18:
      img =`https://static.idicc.cn/cdn/pangu/svg/18.${fileType}`;
      break;
    case 3:
    case 4:
    case 12:
    case 13:
    case 14:
      img =`https://static.idicc.cn/cdn/pangu/svg/3-4-12-13-14.${fileType}`;
      break;
    case 15:
    case 17:
      img =`https://static.idicc.cn/cdn/pangu/svg/15-17.${fileType}`;
      break;
    case 5:
    case 21:
      img =`https://static.idicc.cn/cdn/pangu/svg/5-21.${fileType}`;
      break;
    default:
      img =
        `https://static.idicc.cn/cdn/pangu/svg/0.${fileType}`;
      break;
  }
  return img;
};

export const getListIcon = (name) => {
  let names = {
    主板: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/1.png',
    创业板: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/1.png',
    上市企业: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/1.png',
    上市板块: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/1.png',
    新三板: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/1.png',
    天使轮: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/1.png',
    科创板: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/1.png',
    港股: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/1.png',
    独角兽企业: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/2.png',
    国家级独角兽企业: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/2.png',
    省级独角兽企业: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/2.png',

    瞪羚企业: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/5.png',
    省级瞪羚企业: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/5.png',

    省级技术先讲型服务企业:
      'https://static.idicc.cn/cdn/aiChat/NewVersionicon/7.png',
    科技小巨人企业: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/8.png',
    省级科技小巨人企业:
      'https://static.idicc.cn/cdn/aiChat/NewVersionicon/8.png',
    省级雏鹰企业: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/9.png',

    专精特新小巨人企业:
      'https://static.idicc.cn/cdn/aiChat/NewVersionicon/3.png',
    专精特新企业: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/3.png',
    国家级专精特新小巨人企业:
      'https://static.idicc.cn/cdn/aiChat/NewVersionicon/3.png',
    省级专精特新中小企业:
      'https://static.idicc.cn/cdn/aiChat/NewVersionicon/3.png',
    省级专精特新小巨人企业:
      'https://static.idicc.cn/cdn/aiChat/NewVersionicon/3.png',
    省级创新型中小企业:
      'https://static.idicc.cn/cdn/aiChat/NewVersionicon/6.png',
    省级技术创新示范企业:
      'https://static.idicc.cn/cdn/aiChat/NewVersionicon/6.png',
    隐形冠军企业: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/4.png',
    省级隐形冠军企业: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/4.png',
    其他: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/0.png',
    // 上市: "https://static.idicc.cn/cdn/aiChat/NewVersionicon/1.png",
    //     独角兽: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/2.png',
    //   专精特新: "https://static.idicc.cn/cdn/aiChat/NewVersionicon/3.png",
    //   隐形冠军: "https://static.idicc.cn/cdn/aiChat/NewVersionicon/4.png",
    //   瞪羚: "https://static.idicc.cn/cdn/aiChat/NewVersionicon/5.png",
    //   创新: "https://static.idicc.cn/cdn/aiChat/NewVersionicon/6.png",
    //   技术先进: "https://static.idicc.cn/cdn/aiChat/NewVersionicon/7.png",
    //   科技: "https://static.idicc.cn/cdn/aiChat/NewVersionicon/8.png",
    // 雏鹰: "https://static.idicc.cn/cdn/aiChat/NewVersionicon/9.png",
    // "天使轮":
    //   '科创板'
    //   高新技木企业: 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/0.png',
    // 国家级高新技术企业
    // 国家级高新技术企业
  };
  let url = names[name];
  // console.log(url, name);
  return url ? url : 'https://static.idicc.cn/cdn/aiChat/NewVersionicon/0.png';
};
