@import '@/styles/public.scss';
.number {
  width: 70%;
  height: 40px;
  margin-top: 25px;
  margin-left: 25px;
  background: rgba(0, 125, 255, 0.06);
  padding: 5px 0;
  .init {
    height: 30px;
    background: linear-gradient(
      270deg,
      rgba(0, 133, 255, 0) 0%,
      rgba(0, 125, 255, 0.1) 100%
    );
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .text {
    padding: 0 15px;
    font-size: 14px;
    color: #ffffff;
    text-shadow: 0px 0px 6.44px rgba(30, 198, 255, 0.8);
  }
  .num {
    padding: 0 8px;

    @include YouSheBiaoTi24(30px, normal, #0dcaf5, #9be5ff, #fff);
  }
  .int {
    font-size: 14px;
    color: #c9d2dd;
  }
}