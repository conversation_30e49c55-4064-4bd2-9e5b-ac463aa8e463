
/* theme color */
$--color-primary:#3370ff;
$--color-success: #13ce66;
$--color-warning: #ffba00;
$--color-danger: #ff4949;
// $--color-info: #1E1E1E;
/* 停车状态 颜色 */
$--color-gray:#999;

/*标题背景色*/
$--color-title-bg:#fbfbfb;

$--button-font-weight: 400;

// $--color-text-regular: #1f2d3d;

$--border-color-light: #dfe4ed;
$--border-color-lighter: #e6ebf5;

$--table-border: 1px solid #dfe6ec;

/* icon font path, required */
$--font-path: "~element-ui/lib/theme-chalk/fonts";

@import "~element-ui/packages/theme-chalk/src/index";

:export {
  theme:$--color-primary;
  grayPark:$--color-gray;
  yellowPark:$--color-warning;
  redPark:$--color-danger;
}