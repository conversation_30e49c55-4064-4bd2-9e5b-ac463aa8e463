@import './variables.scss';
@import './sidebar.scss';
// @import './transition.scss';
@import './dws.scss';
@import url(
//at.alicdn.com/t/c/font_4374794_qlwwcc7hoe.css

);
@font-face {
  font-family: "iconfont logo";
  src: url('https://at.alicdn.com/t/font_985780_km7mi63cihi.eot?t=1545807318834');
  src: url('https://at.alicdn.com/t/font_985780_km7mi63cihi.eot?t=1545807318834#iefix') format('embedded-opentype'),
    url('https://at.alicdn.com/t/font_985780_km7mi63cihi.woff?t=1545807318834') format('woff'),
    url('https://at.alicdn.com/t/font_985780_km7mi63cihi.ttf?t=1545807318834') format('truetype'),
    url('https://at.alicdn.com/t/font_985780_km7mi63cihi.svg?t=1545807318834#iconfont') format('svg');
}
/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
// @font-face {
//   font-family: "阿里巴巴普惠体 2.0 65 Medium";font-weight: 500;
//   src: url("//at.alicdn.com/wf/webfont/AkYqaPxYj7H1/XS2LryvcPSY9.woff2") format("woff2"),
//   url("//at.alicdn.com/wf/webfont/AkYqaPxYj7H1/ruoJ814AjXRE.woff") format("woff");
//   font-display: swap;
// }
/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
  font-family: 'iconfont';  /* Project id 4374794 */
  src: url('//at.alicdn.com/t/c/font_4374794_k10y595pbw.woff2?t=1753437657368') format('woff2'),
       url('//at.alicdn.com/t/c/font_4374794_k10y595pbw.woff?t=1753437657368') format('woff'),
       url('//at.alicdn.com/t/c/font_4374794_k10y595pbw.ttf?t=1753437657368') format('truetype');
}
.iconfont{
    font-family:"iconfont" !important;
    font-size:16px;font-style:normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0.2px;
    -moz-osx-font-smoothing: grayscale;}
@font-face {
  font-family: "puhuiti";
  font-weight: 500;
  src: url(~@/text/alibaba1.woff) format("woff2"),
  url(~@/text/alibaba2.woff2) format("woff");
  font-display: swap;
}
@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url(~@/text/YouSheBiaoTiHei-2.ttf);
}

@font-face {
  font-family: "DINPro";
  src: url(~@/text/FontsFree-Net-DINPro-1.ttf);
}

// @font-face {
//   font-family: "PingFangSC";
//   src: url(~@/text/PingFang-SC-Regular.ttf);
// }


body {
  height: 100%;
  padding-right: 0px !important;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  // font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

body::-webkit-scrollbar {
  width: 0px !important;
  height: 0px !important;
  background-color: #010711;
}


html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
  min-width: 1024px;
  min-height: 568px;
  // background: #010711;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: #20a0ff;
    }
  }
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: #20a0ff;
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}


.screen-info {
  background: url('~@/assets/attract/title-ar.png') no-repeat !important;
  background-position: 14px 6px !important;
}

.admin-tips-message{
  background:  #03203f !important;
  border: 0.8px solid #00A5FE !important;
  border-radius: 3px;
  padding: 12px 15px;
  
  .el-message__content {
    color: #ffffff !important;
    font-size: 16px !important;
  }
}