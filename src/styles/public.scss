@mixin viewsText{
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

// 字体从上到下的颜色渐变
@mixin YouSheBiaoTi24($fontSize: 24px,$fontWeight:normal,$firstColor:#FFFFFF,$secondColor:#FFFFFF,$thirdColor:#9BE5FF,) {
  font-family: YouSheBiaoTiHei;
  font-size: $fontSize;
  font-weight: $fontWeight;
  letter-spacing: 2px;
  background:linear-gradient(0deg, $firstColor 20%, $secondColor 48%, $thirdColor 70%);
@include viewsText()
}
// 字体从左到右的颜色渐变
@mixin YouSheBiaoTi28($fontSize: 28px,$fontWeight:normal, $startColor: #b7f3fe, $endColor: #31bffd) {
  font-family: YouSheBiaoTiHei;
  font-size: $fontSize;
  font-weight: $fontWeight;
  letter-spacing: 0em;
  font-variation-settings: normal;
  background: linear-gradient(90deg,#d0f8ff 2%,$startColor 5%, $endColor 100%);
  text-shadow: 0px 0px 10px rgba(24, 143, 192, 0.5);
  @include viewsText()
}

@mixin YouSheBiaoTi30($fontSize: 30px, $startColor: rgba(255, 255, 255, 0.22), $endColor: rgba(80, 140, 229, 0.22)) {
  font-family: YouSheBiaoTiHei;
  font-size: $fontSize;
  font-weight: normal;
  letter-spacing: 2.8px;
  background: linear-gradient(90deg, $startColor 2%, $endColor 100%);
  text-shadow: 1.75px 2.62px 4.37px rgba(17, 20, 22, 0.25);
  @include viewsText()
}
@mixin PingFangSC($fontSize: 30px,$color:#fff) {
  // font-family: PingFangSC;
  font-size: $fontSize;
  font-weight: normal;
  letter-spacing: 0em;
  color: $color;
}
@mixin YouSheBiaoTi ($fontSize: 16px,$color:#fff,$fontWeight:normal,) {
  font-family: YouSheBiaoTiHei;
  font-size: $fontSize;
  color: $color;
  font-weight: $fontWeight;
  letter-spacing: 0em;

}
@mixin Puhuiti($fontSize: 30px,$color:#fff,$fontWeight:normal,) {
  font-family: puhuiti;
  font-size: $fontSize;
  font-weight: $fontWeight;
  letter-spacing: 0em;
  color: $color;
}
// 字体从上到下的颜色渐变
@mixin PuhuitiGradient($fontSize: 18px,$fontWeight:normal,$firstColor:#09E0F3,$secondColor:#09A1F3,$thirdColor:#9BE5FF,) {
  font-family: puhuiti;
  font-size: $fontSize;
  font-weight: $fontWeight;
  letter-spacing: 2px;
  background:linear-gradient(0deg, $firstColor 20%, $secondColor 48%, $thirdColor 70%);
@include viewsText()
}
// 字体从上到下的颜色渐变
@mixin PuhuitiLeftToRight($fontSize: 16px,$fontWeight:normal,$firstColor:#B7F3FE,$secondColor:#31BFFD,$thirdColor:#9BE5FF,) {
  font-family: puhuiti;
  font-size: $fontSize;
  font-weight: $fontWeight;
  letter-spacing: 2px;
  background: linear-gradient(90deg,#B7F3FE 2%,$firstColor 5%, $secondColor 100%);

@include viewsText()
}