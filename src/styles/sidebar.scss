@import './variables.scss';
@import './transferCommon.scss';
#app {
  .mzindex{
    z-index:99999 !important;
  }

  font-family: 'pingfang SC','hiragino sans gb','helvetica neue',arial,'microsoft yahei ui','microsoft yahei',simsun,sans-serif;
  .btns{
    // background: black;
  // color: #fff;
  }
  // .center{text-align: center;}
  .vue-ms-main-sel .ms-doc {background: transparent;}
  .ms-doc, .panel-wrapper{background: #fff;}
.margin-bt20{margin-bottom: 20px;}
.margin-bt30{margin-bottom: 30px;}
.margin30{margin: 30px;}
.margin-left20{margin-left: 20px;}
.el-loading-mask{z-index: 6 !important; background-color:#ffffffa6;} //  组件遮罩层
// el-tab 
.details-info .el-tabs .el-tabs__content{
  // overflow: auto;min-height: calc(100vh - 120px);
  // padding-bottom: calc(100vh - 120px);
}
  // ************************************************  table  样式设置 开始 ************************************************
.el-dropdown-menu--medium{z-index: 999999999;}
.vue-ms-main .ms-tab{ border-radius: 3px 3px 0px 0px;}
  // ******************************** table 条件搜索 border-bottom:#f5f5f5 1px solid;
  .ms-tab{height: 50px;  background: #fafafa; padding: 0px 26px; border-bottom:#e4e7ed 1px solid;}
  .ms-tab .title{position: relative;line-height:36px;float: left; padding: 0px 8px; margin-right: 18px; font-size: 16px; color: #999; }
  .ms-tab .title.active{ color: #333; }
  .ms-tab .title:hover{cursor:pointer}
  .ms-tab .title.active em{position: absolute ; content: ' '; width: 2px; z-index: 2; left: -3px; top:9px;height: 17px; }
  .ms-tab .ms-tab-btn{float: right; display: flex; }
  .ms-tab .ms-tab-btn>div{margin-left: 12px;}
  .ms-tab .ms-tab-btn .params-show-btn{width:26px; text-align: center;padding-top: 6px; font-weight: 600; 
    i{font-weight: 600; font-size: 18px; color: #666; transition:all .28s;}
  }
  .ms-tab .el-form-item{margin-bottom: 0px;}


  
.vue-ms-main-sel{margin: 8px;  padding:0px; position: relative; border-radius: 3px 3px 0px 0px; }
.vue-ms-main-sel .el-table--border{ border-left: none }//!important;
.vue_search{ position: relative; width: 100%; margin: 0px; margin-top: -10px; padding: 0px; padding-top: 30px; overflow: hidden;background: #fff; border-radius: 0px 0px 3px 3px;box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.10); }
.vue_search .params-show-btn{position: absolute; right: 0px; top: 0px; }
.vue_search.is-hide{height: 0px;}
.vue_search .demo-form-inline{display: flex;flex-wrap: wrap;
  margin: 20px 0px 10px 0px; overflow: hidden; margin-top: 5px; }
.vue_search .el-form-item{ margin-left:14px; width:24%; margin-bottom:8px;overflow: hidden;}
.vue_search .form-wrap-date{width:300px !important;}
.vue_search .form-wrap-time{width:468px !important;}
.vue_search .form-wrap-two{width:360px !important;}
.vue_search .from-plate{width:360px !important;
  .row-2>div:first-child{width: 36% !important;}
  .row-2>div:last-child{width: 64% !important;}
}

.panel-search .vue_search .el-form{display: flex;flex-wrap: wrap;margin:0px;}
.panel-search .vue_search .el-form-item{width: 300px !important;}

@media screen and (max-width: 1100px){
  .vue_search .el-form-item{  width: 300px;}
}

@media screen and (min-width: 1100px) and (max-width: 1400px){
  .vue_search .el-form-item{  width: 23.4%;}
}

@media screen and (min-width: 1400px) and (max-width: 1500px){
  .vue_search .el-form-item{width: 18.8%;}
}

@media screen and (min-width: 1500px) and (max-width: 1800px){
  .vue_search .el-form-item{width: 18.6%;}
}

@media screen and (min-width: 1800px) and (max-width: 2600px){
  .vue_search .el-form-item{width: 300px;}
}
.table-column-stu{ color: $light-blue; }
.table-column-stu .no{color: $red;} 
.vue_search .el-form-item.el-btn{width: 70px;  }
.vue_search .el-form-item .el-date-editor .el-range-separator{ padding: 0 1px; }
.vue_search .el-form-item .el-date-editor .el-range-input{ font-size: 13px;}
.vue_search .sel-btn{overflow: hidden; min-width: 220px; display: inline-block;}
.vue_search .row-2>div{width: 106px;}
// .vue_search .el-input__inner{height: 36px; line-height: 36px;padding-right:16px;}
.vue_search .el-date-editor.el-input, .vue_search .el-date-editor.el-input__inner, 
.vue_search .el-form-item .el-select{width:100%;}
.vue_search .sel-info{ overflow: hidden;display: flex;flex-wrap: wrap;}
// .el-form-item__label{font-weight: 400;
//   line-height: 36px;
//   height: 38px;
// }

// 表格操作行
.action-model{display: flex; width: 100%;height: 48px; margin-top: 8px;padding: 8px; background: #fff;
  align-items: center;justify-content: space-between; border-radius:3px 3px 0px 0px;
  em{color: #999;font-style: normal;}
  .el-tag--medium{height: 32px;line-height: 32px;}
}
// 添加这个,是位了表格错乱问题
.el-table th > .cell{white-space: nowrap;}
.el-table thead th,
.el-table__header-wrapper .el-table th,
.el-table__header-wrapper .el-table td{background: #fafafa !important; border-color: #f0f0f0; color:#666 }
.el-button.is-circle{border-radius: 4px !important;}
.el-table__empty-text{font-size: 22px;     color: #9092983d;}
.vue-ms-main .el-table--enable-row-transition .el-table__body td{  border-bottom: 1px solid #eef2f5;  }
.vue-ms-main .el-table--striped .el-table__body tr.el-table__row--striped td{background: #fafafa; border-right: 1px solid #eef3f7; }
// fix:解决table超出100条数据之后表格分格线样式问题
.vue-ms-main .el-table--border th, .el-table--border td {border-right: 1px solid #ebecec !important;}
.vue-ms-main .el-table th.is-leaf, .el-table td {border-bottom: 1px solid #f2f3f4 !important;}

.el-table .el-table__fixed,.el-table .el-table__fixed-right{background: #fff;}
// 右边
// .el-table:not(.el-table–scrollable-x) .el-table__fixed-right{
//   height: calc(100% + 2px)!important;
//   background: #fff;
//   bottom:0px;
//   }
// //   //左边
//  .el-table:not(.el-table–scrollable-x) .el-table__fixed{
//    height: calc(100% + 2px)!important;
//    background: #fff;
//    bottom:0px;
//  }

// // 没有 X 轴滚动 
// .vue-ms-main-sel .el-table:not(.el-table--scrollable-x) { 
//   // 左侧高度设置
//   .el-table__fixed{
//     height: 100% !important;  
//     .el-table__fixed-body-wrapper{
//       height: calc(100% - 47px) !important;
//       top: 45px !important;
//     }
//   }

//   .el-table__fixed-right{
//     height: 100% !important;  
//     .el-table__fixed-body-wrapper{
//       height: calc(100% - 47px) !important;
//       top: 67px !important;
//     }
//   }
// }
// // 没有 X 轴滚动 
// .vue-ms-main-sel .el-table .el-table--scrollable-x { 
//   // 左侧高度设置
//   .el-table__fixed,
//   .el-table__fixed-right{
//     height: 100% !important;  
//     .el-table__fixed-body-wrapper{
//       height: calc(100% - 47px) !important;
//     }
//   }
  
// }
// // 当表格 X 轴 没有滚动条时
// .el-table__body-wrapper.is-scrolling-none~.el-table__fixed-right{
//   height: 100% !important;
//   bottom: 0 !important;
//   box-shadow: none !important;
// }

// .ns-bg-main .el-table__body-wrapper{overflow: hidden !important;overflow-y: scroll;}

// .vue-ms-main .el-table--scrollable-x .el-table__body-wrapper{}
// .vue-ms-main .el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar { width: 10px;height: 12px;}
// .vue-ms-main .el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar-thumb {border-radius: 10px;-webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);background: #7c7c7c;}
// .vue-ms-main .el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar-track {-webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);border-radius: 10px;}
.el-table--scrollable-x .el-table__body-wrapper{
  min-height: 350px !important;
}
// 滚动条样式问题
.vue-ms-main .el-table{
  // .el-table__body-wrapper {
  //   // &::-webkit-scrollbar {
  //   //   height: 17px;
  //   //   width: 17px;
  //   // }
  //   &::-webkit-scrollbar-track {
  //     background-color: #fff;
  //   }
  //   &::-webkit-scrollbar-thumb {
  //     background-color: #ccc;
  //   }
  //   &::-webkit-scrollbar-thumb:hover {
  //     background-color: #666;
  //   }
  // }

// .vue-ms-main-sel .el-table:not(.el-table--scrollable-y){  padding-right: 8px !important; }
// .vue-ms-main-sel .el-table .el-table--scrollable-y{  padding-right: 0px !important; }
/* FIXME: 设置fixed 后底部会有多余横线或是被遮挡*/


// // 有 X 轴滚动
// .vue-ms-main-sel .el-table .el-table--scrollable-x{
//   // 左侧高度设置
//   .el-table__fixed,
//   .el-table__fixed-right{
//     height: calc(100% - 7px) !important;  
//     // box-shadow: -5px -2px 10px rgba(0,0,0,.12) !important;
//     .el-table__fixed-body-wrapper{
//       height: calc(100% - 48px) !important;
//     }
//   }
//  }

//   // 解决表格固定列问题
 

//   // 当表格有纵向滚动条时
//   .el-table--scrollable-y .el-table__fixed-right{
//     right: 10px !important;
//   }
//   // 当表格只有横向滚动条，没有纵向滚动条时
//   .el-table--scrollable-x:not(.el-table--scrollable-y) .el-table__fixed-right{
//     height: calc(100% - 10px) !important; 
//   }

  
  
}
// 分页颜色
.el-pager li{ color: #999;}
.el-pager li.active{  color: #1A305F;  }
// ******************************** table  样式设置 结束
 
// ************************************************  info 数据添加  ************************************************
.vue-ms-main-add .el-tabs--border-card{box-shadow: 0px 0px 0px 0px #fff;}
.ms-updmain{background: #fff; padding: 0px;}
.ms-updmain .info{
  //max-width: 650px;
  text-align: left; overflow: hidden;width: 100%;}
  // .vue-ms-main-add {position: relative;}
.vue-ms-main-add .info{display: flex;flex-direction: row;flex-wrap: wrap;}
.vue-ms-main-add .info .el-form-item{  width: 300px;}
.vue-ms-main-add .info .el-form-item .el-form-item__content>div, .vue-ms-main-add .el-input-number{width:100%}
.vue-ms-main-add .details-info .info p{
  font-size: 14px;
  line-height: 14px;
  padding: 0px 12px;
}
.vue-ms-main-add .details-info .el-form-item{margin: 0px;padding: 0px; border: 1px solid #ebeef5; height: 40px;}
.vue-ms-main-add .details-info  .el-form-item label {
  padding: 0px 12px;
  height: 100%;
  text-align: center;
  color: #4A4A4A;
  font-weight: 700;
  background: #F9F9F9;
  border-right: 1px solid #ebeef5;
  display: flex;
  line-height: 22px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
}
.el-pagination{
  background: #fff;
  //position: absolute;
  //right: 0px;
  //bottom: -48px;
  width: 100%;
  text-align: right;
  height: 50px;
  padding-top: 10px;
  //box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.10);  border-radius: 0px 0px 3px 3px;
}.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #409eff;
  color: #fff;}
.vue-ms-main-add .add-bottom-btn{ z-index: 99; position: absolute; bottom: 0px; left: 100px; height: 40px; background: #fff;}
// ****************************************************************  form 表单 开始 ************************************************
// .el-dialog__header{background: #f9f9f9 ; border-radius: 10px 10px 0px 0px !important; }
.el-dialog__body{
  padding: 10px 20px;
  position: relative;
  .vue-ms-main-add {padding:0 10px 60px 29px; margin:0px;overflow-y: auto;}
  .vue-ms-main-add .ms-updmain{border: none;  padding: 0;}
  .vue-ms-main-add .el-form-item.full-wrap-2{width: 45.9%;}
  .vue-ms-main-add .el-form-item.full-wrap-3{width: 209px;}
  .vue-ms-main-add .el-form-item.full-no-margin{margin: 0px;}
  
  .vue-ms-main-add .full-wrap-31{width: 10%;  margin: 0px;   margin-bottom: 22px;}
  .vue-ms-main-add .el-form-item{width: 438px; margin-left: 20px;
    .el-select{ width:100%; }
    .el-input{width: 100%;}
    .el-rate__icon{line-height: 40px;}
    .el-form-item__content .line{ text-align: center; }
  }
  .vue-ms-main-add .el-form .full-line{
    width: calc(100% -  71px);
    position: absolute;
    bottom: 4px;
    right: 51px;
    background: #fff;
    border-top: #f9f9f9 1px solid;
    padding-top: 10px;
    z-index: 9;
    .el-button{float: right; margin-left: 20px;}
  }

  .vue-ms-main-add::-webkit-scrollbar {
    width:2px;
    height:10px;
    background-color: #fff;
  }
    /* 滚动槽 */
  .vue-ms-main-add::-webkit-scrollbar-track {
    border-radius:10px;
    }
    .vue-ms-main-add::-webkit-scrollbar-track-piece {
      background: #fff;
    }
    /* 滚动条滑块 */
  .vue-ms-main-add::-webkit-scrollbar-thumb {
    border-radius:10px;
    background:#999;
  }

  // &::-webkit-scrollbar-track-piece {
  //   background: #d3dce6;
  // }

  // &::-webkit-scrollbar {
  //   width: 6px;
  // }

  // &::-webkit-scrollbar-thumb {
  //   background: #99a9bf;
  //   border-radius: 20px;
  // }
}

.vue-ms-main-add {margin: 6px;  background: #fff;}
.vue-ms-main-add .el-form{display: flex;  flex-direction: row;flex-wrap: wrap;}
.vue-ms-main-add .wrap-2 {width:700px;}
.vue-ms-main-add .wrap-1 {width:300px;}

.vue-ms-main-add .wrap-full-1 {min-width: 340px; max-width: 600px; margin-right: 4%;}
.vue-ms-main-add .wrap-full-1 .el-form-item, .vue-ms-main-add .wrap-full-1 .el-select,
.vue-ms-main-add .wrap-full-1 .el-cascader--medium,.vue-ms-main-add .wrap-full-1 .el-cascader--medium, .wrap-full-1 .el-date-editor, .wrap-full-1 .el-input-number {width: 100%;}
.vue-ms-main-add .el-form .full-line,.vue-ms-main-add .wrap-full-1 .el-input, 
.vue-ms-main-add .el-date-editor--datetimerange.el-input,.vue-ms-main-add .el-date-editor--daterange.el-input__inner,.vue-ms-main-add .el-cascader{width: 100%;}
.vue-ms-main-add .el-form-item{width: 300px; margin-left: 20px;}
.vue-ms-main-add .el-form-item .xing{position: absolute;z-index: 9;left: -11px;top: 5px;color: red;font-size: 20px;}
.vue-ms-main-add .ms-top{ 
  .crumbs {margin-top: 8px; color: #333; font-size: 14px; font-weight: 600;height: 40px;padding:14px 30px;

    border-bottom: none;
   .el-breadcrumb__inner{color: #333; font-size: 16px;  margin-left: 11px;}
 }
}

.vue-ms-main-add .ms-updmain {
  border-top: 1px solid #ececec;
  padding: 30px 60px 200px 30px;
}
.ms-updmain .demo-ruleForm span.field{line-height: 18px;padding: 10px;display: block;}
.ms-updmain .demo-ruleForm label{text-align: right;
  float: left;
  font-size: 14px;
  color: #606266;
  line-height: 14px;
  padding: 12px 10px 14px 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;}
// ****************************************************************  form 表单 结束 ************************************************
 
// ****************************************************************  详情 开始 ************************************************
.el-dialog__body {
  .vue-ms-main-add .wrap-2{width: 100%;}
  .vue-ms-main-add .wrap-2 .el-form-item{width: 47%;}
  .vue-ms-main-add .el-form-item, .vue-ms-main-add .el-input-number--medium{width: 100%;}
  .details-info .el-form-item .wrap-2 {width:250px;}
}
.details-info .el-form-item{margin-left: 0px;}
.ms-updmain .info p{color: #000;}
.micro-title{text-align: left;width: 100%; font-weight: 600; color: #666; font-size: 16px; line-height: 16px;margin: 30px 0px 20px 0px; margin: 30px 0px 20px 0px;
  border-left: #8c8c8c 10px solid;padding-top: 3px;
  padding-left: 10px; }
.micro-title-table{overflow: hidden; text-align: left;width: 100%; font-weight: 600; color: #666; font-size: 16px;margin: 30px 0px 0px 0px;
    background: #f5f5f5; border-radius: 1px; padding-left: 12px;height: 40px; line-height: 40px;}
.details-info .info .flash-info .row:nth-child(1) p{color: #666;}
.details-info .info .flash-info .row p{flex: 1; width: 120px; max-width:120px; margin: 16px;  color: #666; display: inline-block; white-space: nowrap;overflow: hidden;text-overflow:ellipsis;}
.details-info .info .flash-info .row p:nth-child(3),.details-info .info .flash-info .row p:nth-child(4){width: 180px}
.trade_tags_row_info{display: block; width: 100%;}
.trade_tags_row_info .row{  text-align: left; overflow: hidden;}
.trade_tags_row_info .row:first-child{background: #fafafa;  }
.trade_tags_row_info .row{padding: 2px 20px;border-bottom: #f1f1f1 1px solid;}
.trade_tags_row_info .row p{float: left;width: 50%; padding: 6px 0px;margin: 0px; font-size: 14px; color: #333;}
.vue-ms-main-add .details-info .el-form-item{margin-left: 0px;}
.details-info .wrap-1{width: 300px;}
.details-info .wrap-2{width: 600px;}
.details-info .wrap-3{width: 900px;}
.details-info .el-tabs__item{font-size: 16px; }



.vue-ms-main-add .details-info .full-line-1{width: 600px;}
.vue-ms-main-add .details-info.ms-updmain{ padding: 30px 30px 200px 30px; }

.el-dialog__body{
  .vue-ms-main-detail{padding: 0px 20px;     min-height: calc(100vh - 108px); }
  .ms-updmain{padding: 0px;}
  .ms-updmain .wrap-line-2 .el-form-item{width: 250px;}
  .details-info .wrap-2 {width:600px !important;
    .el-form-item{width: 260px;}
  }
}
@media screen and (min-width: 1300px) and (max-width: 1900px){
  .details-info .wrap-4{width: 100%;}
  .details-info.ms-updmain .info.wrap-4 .el-form-item, .info.wrap-4 .row-info{width: 25% !important; min-width: 25%;}
  .vue-ms-main-add .details-info .info.wrap-4 .row-info.full-line-0{width: 100% !important;}

}

@media screen and (min-width: 1300px) and (max-width: 1500px){
  .detail-ruleForm .row-info .value span.row1 {width: 88px;}
}

@media screen and (min-width: 1500px) and (max-width: 1800px){
  .detail-ruleForm .row-info .value span.row1 {width: 100px;}
}

@media screen and (min-width: 2000px){
  .details-info .info{max-width: 1200 !important;}
}


.detail-ruleForm:hover{cursor:default }

.detail-ruleForm{

  .info{display: flex;}
  
  .row-info{ width: 300px; margin: 0px; display: flex;
    padding: 0px;
    border: 1px solid #ebeef5;overflow: hidden;
    min-height: 40px;}
  .full-line-0{width: 600px;}
  b{
    float: left;
    width: 110px;
    min-width: 110px;
    max-width: 110px;
    padding: 0px 12px;
    height: 100%;
    font-size: 14px;
    text-align: center;
    color: #4A4A4A;
    font-weight: 700;
    background: #F9F9F9;
    border-right: 1px solid #ebeef5;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    line-height: 22px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
  .value{float: left; padding: 10px 12px;     font-size: 14px;
    line-height:18px;
    display: flex;
    span{ float: left; 
      // word-break:break-all;
      // display:-webkit-box;
      // -webkit-line-clamp:1;
      -webkit-box-orient:vertical;
      overflow:hidden;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap !important;

    }
    span.row1{
      width: 140px;
      word-break:break-all;
      display:-webkit-box;
      -webkit-line-clamp:1;
      -webkit-box-orient:vertical;
      overflow:hidden; }
      
    .dian{float: left; background: #333; color:  #333; width: 6px;height: 6px; border-radius: 50%;
      margin-top: 14px;
      margin-right: 10px;}
  }
  

}
// ****************************************************************  详情 结束 ************************************************
 
 
  .main-container {
    background: transparent;
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: $sideBarWidth;
    // position: relative;
    // z-index: 4;
    margin-left: 256px;
    border-radius: 20px;
  }
 
  .sidebar-container {
    transition: 0.28s;
    width: 256px !important;
    background: #e9f0ff;
    height: 100%;
    position: fixed;
    z-index: 99999;
    font-size: 0px;
    top: 49px;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    padding: 15px;
 
    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }
 
    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }
 
    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }
 
    .el-scrollbar {
      height: 90%;
    }
 
    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 90px);
      }
    }
 
    .is-horizontal {
      display: none;
    }
 
    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }
 
    .svg-icon {
      margin-right: 8px;
    }
 
    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
 
    .el-menu {
      background: #e9f0ff !important;
      border: none;
      height: 100%;
      width: 100% !important;
      border-radius: 8px;
      //margin-left: 12px;
      box-sizing: border-box;
    }
    .el-submenu__title{
       font-size: 1rem !important;
      height: 42px !important;
      line-height: 42px !important;
    }
    .el-menu-item{
              font-size: 1rem !important;
      height: 42px !important;
      line-height: 42px !important;
    }
    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      &:hover {
        border-radius: 8px;
        background-color: $menuHover !important;
      }
    }
 
    .is-active>.el-submenu__title {
      color: $subMenuActiveText !important;
    }
 
    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $sideBarWidth !important;
      background-color: $subMenuBg !important;
 
      &:hover {
        border-radius: 8px;
        background-color: $subMenuHover !important;
      }
    }
  }
 
  .hideSidebar {
    .sidebar-container {
      width: 80px !important;
    }
 
    .main-container {
      margin-left: 80px;
    }
 
    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;
 
      .el-tooltip {
        padding: 0 !important;
 
        .svg-icon {
          margin-left: 20px;
        }
 
        .sub-el-icon {
          margin-left: 19px;
        }
      }
    }
 
    .el-submenu {
      overflow: hidden;
 
      &>.el-submenu__title {
        font-size: 1rem !important;
        padding: 0 !important;
 
        .svg-icon {
          margin-left: 20px;
        }
 
        .sub-el-icon {
          margin-left: 19px;
        }
 
        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }
 
    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }
 
  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }
 
  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }
 
    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth !important;
    }
 
    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }
 
  .withoutAnimation {
 
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}
 
// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }
  .nest-menu .el-submenu>.el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      border-radius: 8px;
      background-color: $menuHover !important;
    }
  }
 
  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;
 
    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }
 
    &::-webkit-scrollbar {
      width: 6px;
    }
 
    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

//fix-issue:element-ui中Message消息提示与Dialog对话框同时使用的遮罩问题
.mzindex{
  z-index:3000 !important;
}
.selfApp{
  .app-main{
    .main-container.hasTagsView{
      margin-left: 0px !important;
    }
  }
 
}
