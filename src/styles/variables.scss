// base color
$blue:#324157;
$light-blue:#3A71A8;
$red:#C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;
/*switch button 开启颜色*/
$switch-btn-active-color:#13ce66;
/*switch button 关闭颜色*/
$switch-btn-inactive-color:#bfcbd9;

// sidebar
//未选择的文字颜色
$menuText:#000000;
// 选中的文字颜色
$menuActiveText:#fff;
// 选中的父节点颜色
$subMenuActiveText:#3370FF; // https://github.com/ElemeFE/element/issues/12951
//未选中的背景颜色
$menuBg:#e9f0ff;
// 鼠标父节点经过
$menuHover:#e2e8f7;// #f2f2f2;
// 背景颜色
$subMenuBg:#E9F0FF;//#00112d
//经过子节点
$subMenuHover:#e2e8f7;

$sideBarWidth: 210px;

label { font-weight: 700; }

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}