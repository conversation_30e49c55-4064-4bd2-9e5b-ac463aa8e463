import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

// 统一使用完整引入
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

Vue.use(ElementUI, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

//滑块验证码
import SlideVerify from 'vue-monoplasty-slide-verify';
import '@/styles/index.scss' // global css
import vueMiniPlayer from 'vue-mini-player'
import 'vue-mini-player/lib/vue-mini-player.css'

import App from './App'
import store from './store'
import router from './router'

import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log

import * as filters from './filters' // global filters


Vue.use(dataV)

Vue.use(SlideVerify)
Vue.use(vueMiniPlayer)

Vue.use((req, res, next) => {
  // 路径判断等等
  // req.headers.origin ||
  req.set({
    "Access-Control-Allow-Credentials": true,
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "X-Requested-With,Content-Type",
    "Access-Control-Allow-Methods": "PUT,POST,GET,DELETE,OPTIONS",
    "Content-Type": "application/json; charset=utf-8",
  });
  // 其他操作
});
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (process.env.NODE_ENV === 'production') {
  const { mockXHR } = require('../mock')
  mockXHR()
}
// const time = new Date();
// console.log(
//   '发布时间：'+time.getFullYear()+'-'+time.getMonth()+1+'-'+time.getDate()+' '+
// time.getHours()+':'+time.getMinutes());
// console.log('=https://geo.datav.aliyun.com',);
// console.log('发布时间：2023-01-17 10:44');
/* Vue.use(Element, {
  size: Cookies.get('size') || 'medium', // set element-ui default size
  locale: enLang // 如果使用中文，无需设置，请删除
}) */

// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

let instance;
instance = new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})