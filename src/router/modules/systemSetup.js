import Layout from '@/layout'

export const officialAccounts = {
  // 二级菜单路由
  systemSetup: () => import("@/views/system-manage/systemSetup"),
  // 密码设置
  PasswordSetting: () => import("@/views/system-manage/systemSetup/components/PasswordSetting"),
  //账号设置
  accountManagement: () => import("@/views/system-manage/systemSetup/components/accountManagement"),
  // 账号信息
  accountInformation: () => import("@/views/system-manage/systemSetup/components/accountInformation"),
  // 机构管理
  organizationalManagement: () => import("@/views/system-manage/organizationalManagement"),
  //  用户管理
  userControl: () => import("@/views/system-manage/userControl"),
  //  账号信息
  roleManagement: () => import("@/views/system-manage/roleManagement"),
}


const userManagerRouter = {
  path: '/systemSetup',
  component: Layout,
  name: '系统设置',
  alwaysShow: true,
  meta: {
    title: '系统设置',
    //icon: 'el-icon-user'
    icon: 'system'
  },
  children: [
    /*     {
            path: 'organizationalManagement',
            component:officialAccounts.organizationalManagement,
            name: 'organizationalManagement',
            meta: { title: '机构管理' , keepAlive:true,list:true }
        }, */
    {
      path: 'userControl',
      component: officialAccounts.userControl,
      name: '用户管理',
      meta: { title: '用户管理', list: true }
    },
    {
      path: 'roleManagement',
      component: officialAccounts.roleManagement,
      name: '角色管理',
      meta: { title: '角色管理', list: true }
    },


  ]
}
export default userManagerRouter
