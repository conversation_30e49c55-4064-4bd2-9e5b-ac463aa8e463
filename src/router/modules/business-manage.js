/**
 * 机构管理
 */
import Layout from '@/layout'

export const systemManagementComponents = {
  // 产业链管理
  IndustrialChainManagement: () => import("@/views/system-manage/industry-chain/list"),
  // 企业数据管理
  enterpriseDataManagement: () => import("@/views/system-manage/enterprise-data/list"),
  // 产业链管理配置
  configuration: () => import("@/views/system-manage/industry-chain/configuration"),
  // 企业数据管理详情
  particulars: () => import("@/views/system-manage/enterprise-data/components/configuration"),
  report: () => import("@/views/system-manage/officialwebsite/reportManage")
}


const systemManageRouter = {
  path: '/business',
  component: Layout,
  name: '业务运营管理',
  alwaysShow: true,
  hidden: true,
  meta: {
    title: '业务运营管理',
    icon: '&#xe606;'
  },
  children: [
    {
      path: 'IndustrialChainManagement',
      component: systemManagementComponents.IndustrialChainManagement,
      name: '产业链管理',
      meta: { title: '产业链管理', list: true },
    },
    {
      path: 'configuration',
      component: systemManagementComponents.configuration,
      name: "产业链配置",
      meta: { title: '产业链配置', list: true },
      hidden: true
    },
    {
      path: 'enterpriseDataManagement',
      component: systemManagementComponents.enterpriseDataManagement,
      name: '企业数据管理',
      meta: { title: '企业数据管理', list: true }
    },
    {
      path: 'particulars',
      component: systemManagementComponents.particulars,
      name: '企业详情',
      meta: { title: '企业详情', list: true },
      hidden: true
    },
    {
      path: 'report',
      component: systemManagementComponents.report,
      name: '报告管理',
      meta: { title: '报告管理', list: true }
    },

  ]
}
export default systemManageRouter
