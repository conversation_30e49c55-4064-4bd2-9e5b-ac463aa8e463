/**
 * 机构管理
 */
import Layout from '@/layout'
//import store from '@/store';

export const systemManagementComponents = {
  // 入驻机构管理
  organizationOccupancy: () => import("@/views/system-manage/organization-occupancy/list"),
  // 机构产业链配置
  industryConfiguration: () => import("@/views/system-manage/industry-configuration/list"),
  // 招商配置
  //  attractInvestment :()=>import("@/views/system-manage/industry-configuration/components/attractInvestment"),
  // 委托招商管理
  entrustAttract: () => import("@/views/system-manage/entrustAttract"),
  // 招商推荐管理
  attractRecommend: () => import("@/views/system-manage/attractRecommend")
}


const systemManageRouter = {
  path: '/system',
  component: Layout,
  name: '机构运营管理',
  alwaysShow: true,
  hidden: true,
  meta: {
    title: '机构运营管理',
    icon: 'Frame'
  },
  children: [
    /* {
      path: 'organization-occupancy',
      component:systemManagementComponents.organizationOccupancy,
      name: '入驻机构管理',
      meta: { title: '入驻机构管理' , keepAlive:true,list:true }
    }, */
    {
      path: 'industryConfiguration',
      component: systemManagementComponents.industryConfiguration,
      name: '机构产业链配置',
      meta: { title: '机构产业链配置', list: true },
      /*        beforeEnter:(to,from,next) =>{
              if(store.getters.user.accountType == '1'){
                next()//调用next才会往下走
              }else{
                next('/home-page')
              }
            } */
    },
    /*      {
          path: 'organization-entrustAttract',
          component:systemManagementComponents.entrustAttract,
          name: 'entrustAttract',
          meta: { title: '委托招商管理' , keepAlive:true,list:true }
        },
        {
          path: 'organization-attractRecommend',
          component:systemManagementComponents.attractRecommend,
          name: 'attractRecommend',
          meta: { title: '招商推荐管理' , keepAlive:true,list:true }
        },  */
    // {
    //   path: 'attractInvestment',
    //   component:systemManagementComponents.attractInvestment,
    //   name: '招商配置',
    //   meta: { title: '招商配置' ,list:true },
    //   hidden: true
    // }

  ]
}
export default systemManageRouter
