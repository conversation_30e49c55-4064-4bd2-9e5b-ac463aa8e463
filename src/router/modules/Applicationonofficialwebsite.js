/**
 * 机构管理
 */
import Layout from '@/layout'

export const Applicationonofficialwebsite = {
  // 业务资讯
  business: () => import("@/views/system-manage/officialwebsite/Businessinformation"),
  // 预约体验
  makeanappointment: () => import("@/views/system-manage/officialwebsite/Reservationexperience"),
  // 获取报告
  Getareport: () => import("@/views/system-manage/officialwebsite/Getareport"),
}


const systemManageRouter = {
  path: '/official',
  component: Layout,
  name: '官网申请管理',
  alwaysShow: true,
  hidden: true,
  meta: {
    title: '官网申请管理',
    icon: ''
  },
  children: [
    {
      path: 'business',
      component: Applicationonofficialwebsite.business,
      name: '业务咨询',
      meta: { title: '业务咨询', list: true },
    },
    {
      path: 'makeanappointment',
      component: Applicationonofficialwebsite.makeanappointment,
      name: '预约体验',
      meta: { title: '预约体验', list: true }
    },
    {
      path: 'getReport',
      component: Applicationonofficialwebsite.Getareport,
      name: '获取报告',
      meta: { title: '获取报告', list: true },
    },
  ]
}
export default systemManageRouter
