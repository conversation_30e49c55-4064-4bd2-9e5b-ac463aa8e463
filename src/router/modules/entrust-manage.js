// entrustManagerRouter
import Layout from '@/layout'

// 招商推荐管理
const entrustManagerRouter = {
  path: '/recommendationInvestment',
  component: Layout,
  name: '招商推荐管理',
  alwaysShow: true,
  hidden: true,
  meta: {
    title: '招商推荐管理',
    icon: ''
  },
  children: [
    {
      path: 'entrust',
      component: () => import('@/admin/entrust/index'),
      name: '招商委托管理',
      meta: { title: '招商委托管理', list: true }
    },
    {
      path: 'information',
      component: () => import('@/admin/zstjgl/index'),
      name: '招商情报管理',
      meta: { title: '招商情报管理', list: true }
    },
    /*  {
       path: 'zszx',
       component: () => import('@/admin/zszx/index'),
       name: 'zszx',
       meta: { title: '招商资讯管理' , keepAlive:true,list:true }
     }, */
  ]
}
export default entrustManagerRouter

