/*
 * @Author: jhy
 * @Date: 2023-06-05 11:30:02
 * @LastEditTime: 2023-06-05 15:44:03
 * @LastEditors: jhy
 * @Description:
 * @FilePath: /QT-SASS-PC/src/router/modules/scan.js
 */

import Layout from "@/layout";
// import store from '@/store';

const scan = {
  path: "/idicc",
  name: "idicc",
  component: Layout,
  redirect: "/idicc",
  meta: {
    title: "产业洞察",
    icon: "&#xe603;",
  },
  children: [
    {
      path: "view",
      name: "idiccView",
      component: () => import("@/views/system-manage/idicc-scan/index"),
      meta: { title: "产业速览", affix: true },
    },
    {
      path: "analysis",
      name: "idiccAnalysis",
      component: () => import("@/views/system-manage/idicc-analysis/index"),
      meta: { title: "产业分析", affix: true },
    },
  ],
};
export default scan;
