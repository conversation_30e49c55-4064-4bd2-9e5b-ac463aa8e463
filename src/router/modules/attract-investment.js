/**
 * 机构管理
 */
import Layout from '@/layout'

export const systemManagementComponents = {
  // 产业360
  IntelligentSearch: () => import("@/views/system-manage/attract-investment/IntelligentSearch"),
  // 招商智推
  intelligentRecommendation: () => import("@/views/system-manage/attract-investment/intelligentRecommendation"),
  // 招商管理
  //manage: () => import("@/views/system-manage/attract-investment/manage"),
  manage: () => import("@/views/system-manage/attract-investment/newSmartManagement"),
  //委托招商
  entrust: () => import("@/views/system-manage/attract-investment/entrust"),
}


const systemManageRouter = {
  path: '/attract',
  name: 'attract',
  component: Layout,
  alwaysShow: true,
  meta: {
    title: '数智招商',
    icon: '&#xe606;'
  },
  children: [
    {
      path: 'intelligentSearch',
      name: 'attractIntelligentSearch',
      component: systemManagementComponents.IntelligentSearch,
      meta: { title: '产业360', list: true },
    },
    {
      path: 'intelligentRecommendation',
      name: 'attractIntelligentRecommendation',
      component: systemManagementComponents.intelligentRecommendation,
      meta: { title: '招商智推', list: true }
    },
    {
      path: 'manage',
      name: 'attractManage',
      component: systemManagementComponents.manage,
      meta: { title: '招商智管', list: true },
    },
    {
      path: 'entrust',
      name: 'attractEntrust',
      component: systemManagementComponents.entrust,
      meta: { title: '委托招商', list: true },
    },
  ]
}
export default systemManageRouter
