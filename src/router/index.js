import Vue from 'vue'
import Router from 'vue-router'
Vue.use(Router)

import Layout from '@/layout'
import systemManageRouter from "./modules/system-manage"
import businessManage from "./modules/business-manage"
import officialAccounts from './modules/official-accounts'
import entrustManage from './modules/entrust-manage'
import scan from './modules/scan'
import attractinvestment from './modules/attract-investment'
import Applicationonofficialwebsite from './modules/Applicationonofficialwebsite'

export const allAccounts = {
  // 二级菜单路由
  systemSetup: () => import("@/views/system-manage/systemSetup"),
  // 密码设置
  PasswordSetting: () => import("@/views/system-manage/systemSetup/components/PasswordSetting"),
  //账号设置
  accountManagement: () => import("@/views/system-manage/systemSetup/components/accountManagement"),
  // 账号信息
  accountInformation: () => import("@/views/system-manage/systemSetup/components/accountInformation"),
  // 机构管理
  organizationalManagement: () => import("@/views/system-manage/organizationalManagement"),
  //  用户管理
  userControl: () => import("@/views/system-manage/userControl"),
  //  账号信息
  roleManagement: () => import("@/views/system-manage/roleManagement"),
}

export const constantRoutes = [

  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login/login"),
    hidden: true,
  },
  {
    path: "/ChartsRelation",
    component: () =>
      import("@/views/system-manage/attract-investment/ChartsRelation"),
    hidden: true,
  },
  {
    path: "/retrievePassword",
    component: () => import("@/views/login/retrievePassword"),
    hidden: true,
  },
  {
    path: "/MapEcharts",//产业地图
    component: () => import("@/views/echarts/map-echarts"),
    hidden: true,
    meta: {
      title: "产业地图",
      keepAlive: true,
      isJN: true
    },
  },
  {
    path: "/IndustryGraph",//产业全景
    component: () => import("@/views/echarts/industry-graph"),
    hidden: true,
    meta: {
      title: "产业全景",
      keepAlive: true,
      isJN: true
    },
  },
  {
    path: "/overview",
    component: () => import("@/views/overview"),
    hidden: true,
    meta: {
      title: "产业概览",
      keepAlive: true,
      isJN: true
    },
  },
  {
    path: "/parkOverView",//园区概览
    component: () => import("@/views/parkOverView"),
    hidden: true,
    meta: {
      title: "园区概览",
      keepAlive: true,
      isJN: true
    },
  },
  {
    path: "/dashboard",
    component: () => import("@/views/count/admin/index"),
    hidden: true,
    meta: {
      title: "产业洞察",
      isJN: true
    },

  },
  {
    path: "/attractInvestment",//产业招商
    component: () => import("@/views/attractinvestment/admin/index"),
    hidden: true,
    meta: {
      title: "产业招商",
      keepAlive: true,
      isJN: true
    },
  },

  {
    name: '404', path: "/404",
    component: () => import('@/views/error-page/404'),
    meta: { title: '404', icon: '', affix: true },
    hidden: true
  },
  {
    path: "/noOrg",
    component: () => import("@/views/error-page/noOrg"),
    hidden: true,
    meta: {
      title: "哒达招商",
    },
  },
];

export const asyncRoutes = [
  systemManageRouter,
  businessManage,
  officialAccounts,
  //systemSetup,
  entrustManage,
  scan,
  attractinvestment,
  Applicationonofficialwebsite,
  {
    path: "/home-page",//首页
    name: "homePage",
    component: () => import("@/views/homePage"),
    hidden: false,
    meta: {
      title: "产业金脑",
      icon: '&#xe608;',
      isJN: true
    },
  },
  {
    path: '/systemSetups',
    name: 'systemSetups',
    component: Layout,
    alwaysShow: true,
    meta: {
      title: '个人中心',
      icon: '&#xe602;',
    },
    children: [
      {
        path: 'accountInformation',
        name: 'systemSetupsAccountInformation',
        component: allAccounts.accountInformation,
        meta: { title: '账号信息', list: true }
      },
      {
        path: 'PasswordSetting',
        name: 'systemSetupsPasswordSetting',
        component: allAccounts.PasswordSetting,
        meta: { title: '密码设置', list: true }
      },
    ]
  },
  { path: '*', redirect: '/404', hidden: true }

]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
