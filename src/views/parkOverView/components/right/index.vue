<template>
  <div class="content">
    <div v-if="showFlag === 1">
      <Titles title="园区营养食品重点企业" />
      <div
        v-if="detailList?.length>0"
        class="companyList"
      >
        <div
          v-for="(detail, idx) in detailList"
          :key="idx"
          class="detail"
        >
          <CompanyCard
            :detail-data="detail"
            :active-state="activeState"
            :enterprise-show="enterpriseShow"
            :tag-show="detail.tagShow"
            :more-tag="detail.tagMore"
            @openDetailSingle="openDetailSingle"
            @attention="attention"
          />
        </div>
      </div>
      <div
        v-else
        class="noDataSelf"
      >
        <NoData />
      </div>
    </div>
    <div v-if="showFlag === 2">
      <!-- 企业详情 -->
      <AtlasDetailCompany
        v-if="showFlag == 2"
        :child-id="detailSingleParams.childId"
        :enterprise-label-type="enterpriseLabelType"
        from-path="zhumadian"
        @closeDetail="closeDetailSingle"
        @getEnterpriseList="getEnterpriseList"
      />
    </div>
  </div>
</template>
<script>
import Titles from '../component/titles.vue';
import NoData from '@/views/overview/components/component/noData';
import CompanyCard from '../component/companyCard/index.vue';
import {getParkOverView} from '@/views/count/admin/apiUrl.js'
import AtlasDetailCompany from '@/views/echarts/large-screen/companyDetail.vue'; // 图谱 - 企业详情
import { getPathId } from '@/utils/utils';

export default {
  components: {
    Titles,
    NoData,
    CompanyCard,
    AtlasDetailCompany,
  },
 
  data() {
    return {
      detailList: [  ],
      enterpriseLabelType: null,
      showFlag: 1, // 1 打开企业列表  2 打开详情
      detailSingleParams: {
        type: 'graph',
        level: 3,
        paraimsId: null,
        childId: null,
        titleName: '',
        node: null,
      },
      enterpriseShow: false,
 activeState: '',
    };
  },
 

  mounted() {
    this.init( );
  },
  methods: {
    init(val) {
            let queryId = this.$route.query.id || getPathId() || null;
       let data = {
            orgChainRelationId: queryId, // 机构产业链关联ID
            code: 'zmd20250612', // 机构产业链关联ID
            type: 'vip',
            pageNum:1,
            pageSize: 5,
          };
      getParkOverView(data).then(res => {
        // console.log(res, 'resres')
        
        this.detailList = res?.records

                for (let i = 0; i < this.detailList.length; i++) {
              if (this.detailList[i].enterpriseLabelNames) {
                this.detailList[i].new =
                  this.detailList[i].enterpriseLabelNames.split(',');
                this.detailList[i].tagShow = this.detailList[i].new.slice(0, 2);
                if (this.detailList[i].tagShow.join('').length > 8) {
                  this.detailList[i].tagShow = [this.detailList[i].tagShow[0]];
                }
                this.detailList[i].tagMore = this.detailList[i].new.filter(
                  (item) => !this.detailList[i].tagShow.includes(item)
                );
              } else {
                this.detailList[i].new = null;
              }
                }
            
  })
    },

    // 打开 - 企业详情
    openDetailSingle(params) {
      this.enterpriseLabelType = params.enterpriseLabelType;
      this.showFlag = 2;
      this.detailSingleParams.childId = params.id;
      this.detailSingleParams.titleName = params.enterpriseName;
    },
    attention() {
      
    },
    closeDetailSingle() {
       this.showFlag = 1;
    },
    getEnterpriseList() {
      
    }
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
@import '@/views/echarts/large-screen/companyPublic.scss';
@import '@/views/echarts/large-screen/enterprise.scss';
.content {
  width: 100%;
  height: 100%;
}
.companyList {
  width: 100%;
  height: calc(100% - 40px);
  padding-left: 24px;

}
.detail {
  height: 115px !important;
  .title{
    padding-top: 16px;
  }
}
.noDataSelf{
  width: 100%;
  height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}
</style>
