<template>
  <div class="content">
    <div class="containerTop">
      <Titles title="园区总体情况" />
      <div class="top">
        <TopView :show-data="topShowData" />
      </div>
    </div>
    <div class="middleTop">
      <Titles title="园区产业结构" />
      <MiddleView :show-data="middleShowData" />
    </div>
    <div class="middleBottom">
      <Titles title="企业增长情况" />
      <MiddleCharts :show-data="middleCharts" />
    </div>
    <Titles title="园区科技企业" />
    <div class="bottom">
      <BottomView :show-data="bottomShowData" />
    </div>
  </div>
</template>
<script>
import Titles from "../component/titles.vue";
import TopView from "./component/top.vue";
import MiddleView from "./component/middle.vue";
import MiddleCharts from "./component/middleCharts.vue";
import BottomView from "./component/bottom.vue";
export default {
  components: {
    Titles,
    TopView,
    BottomView,
    MiddleView,
    MiddleCharts,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      parkOverView: this.$store.getters.parkOverView,
      middleShowData: null,
      topShowData: null,
      bottomShowData: null,
      middleCharts: null,
    };
  },
  watch: {
    data(val) {
      this.init(val);
    },
  },

  mounted() {
    this.init(this.data);
  },
  methods: {
    init(val) {
      this.parkOverView = val;
      let { park, industrialStructure, parkGrouthStat, techEnterprises } = val;
      // // 园区总体情况  "park":
      //   园区产业结构  industrialStructure
      //  园区增长情况  parkGrouthStat
      // console.log("allViewData", val);
      this.topShowData = park;
      this.middleShowData = industrialStructure;
      this.middleCharts = parkGrouthStat;
      this.bottomShowData = techEnterprises; //园区科技企业
      // console.log("topShowData", this.topShowData);
      // console.log("middleShowData", val);
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  height: 100%;
  padding: 20px 20px 40px 20px;
  // background: url('~@/assets/screen/new/left_bg.png') center/cover no-repeat;
  // background-size: 95%;
}

.top,
.middleTop,
.middleBottom,
.bottom {
  // background: rgba(128, 120, 121, 0.547);
  color: white;
}

.containerTop {
  // height: calc(70% - 40px);
  height: 25%;

  .top {
    height: 85%;
    // padding: 20px 30px;
  }

  // .middle {
  //   height: calc(50% - 80px);
  //   // padding: 10px 30px;
  // }
}

.middleTop {
  height: 27%;
}
.middleBottom {
  height: 26%;
}
.bottom {
  height: calc(22% - 40px);
}

@media screen and (max-width: 1450px) {
  .content {
    padding: 10px 10px 10px 0;
  }
}
</style>