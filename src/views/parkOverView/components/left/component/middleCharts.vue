<template>
  <!-- <div>企业增长情况</div> -->
  <div
    id="middleCharts"
    class="echart"
  />
</template>

<script>
import * as echarts from 'echarts';
// import { provinceList } from '../countData'
import { YAxis, XAxis } from '@/utils/XYLineConfig';

export default {
  name: 'MiddleCharts',
  props: {
    showData: {
      type: Object,
      default: function () {
        return {  };
      },
    },
    cityType: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      myChartStyle: { float: 'left', width: '100%', height: '400px' }, //图表样式
    };
  },
  watch: {
    showData: {
      deep: true,
      handler(val) {
        val && this.initEcharts(val);
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initEcharts(this.showData);
    });
  },

  methods: {
    // xFromatData(params){
    //   let newCityList = []
    //   if(this.cityType == 5){
    //     params.forEach((city) => {
    //       const province =  provinceList.find((item )=>{
    //         if(item[1] === city){
    //           return true
    //         }
    //       })
    //       if(province){
    //         newCityList.push(province[0])
    //       }else{
    //         newCityList.push(city);
    //       }
    //     });
    //     return newCityList;
    //   }else{
    //     return params;
    //   }
    // },
    initEcharts(chartData) {
      let datas =chartData
      // {
      //   year: [2021, 2022, 2023, 2024, 2025],
      //   data: {
      //     营养食品: [10, 2, 13, 29, 6],
      //     微生物: [20, 30, 58, 90, 67],
      //     功能食品: [23, 34, 55, 92, 17],
      //     预制食品: [13, 14, 15, 12, 17],
      //   },
      // };
      // const { id = 'city', type = 'bar' } = datas;
      const { year, data } = datas;
      let texts = '数量(家)';
      let unit1 = '家';
      let lengend = [];
      let seriesData = [];
      let lineConfig = {
        type: 'line',
        smooth: true,
        emphasis: {
          itemStyle: {
            borderWidth: 1, // 设置选中状态下的边框宽度为1
            opacity: 1, // 不透明
          },
        },
        symbol: 'circle', // 设置点的形状为圆形
        symbolSize: 5, // 设置每个点的大小为10
        // 在折线图上显示对应的数值
        label: {
          show: true,
          position: 'top',
          color: '#fff', // 将标签颜色设为白色
        },
      };
      let colors = ['#188FFE', '#B401FD', '#FF7B00', '#0BE5F5'];
      let index = 0;
      for (let key in data) {
        lengend.push(key);
          // 确保线条颜色也一致
        let lineStyle = {
          width: 1.5,
          color: colors[index],
        };
        let itemData = {
          lineStyle,
          ...lineConfig,
          data: data[key],
          name: key, // 确保每个系列都有name属性，这对legend显示很重要
          // 直接为每个系列设置颜色，这样legend会继承这个颜色
        
          
        // 设置每个点的大小和颜色
        itemStyle: {
     color: colors[index],
          borderWidth: 0,
          opacity: 1, // 不透明
        },
      
        };
        seriesData.push(itemData);
        index++;
      }
      let dataX = [];
      let dataY = [];
      // if (chartData.values) {
      //   this.chartData.values.forEach((item) => {
      //     if (item.dicKey) {
      //       dataX.push(item.dicKey);
      //     }
      //   });
      //   chartData.values.forEach((item) => {
      //     if (item.dicValue) {
      //       dataY.push(item.dicValue);
      //     }
      //   });
      // } else {
      //   dataX = chartData.xData1;
      //   dataY = chartData.yData1;
      // }
      //  let xData = this.xFromatData(xData1);

      // 基本柱状图
      const option = {
        // 主图形位置
        grid: {
          left: 0,
          right: 24,
          bottom: 0,
          // top: '25%',
          containLabel: true, // containLabel  x/y轴内容 是否在内部
        },
        legend: {
          show: true, // 确保图例显示
          right: 10,
          top: 10,
          itemWidth: 10,
          itemHeight: 10,
          icon: 'rect', // 使用矩形图标更容易看到
          textStyle: {
            color: 'white',
            fontSize: 12,
          },
          itemGap: 10, // 增加图例项之间的间隔
          data: lengend,
          formatter: function(name) {
            // 限制图例文字长度，防止过长影响显示
            return name.length > 6 ? name.slice(0, 6) + '...' : name;
          }
        },
 
        title: {
          text: texts,
          x: -5,
          y: 16,
          textStyle: {
            color: '#C9D2DD',
            fontSize: 12,
            fontWeight: 'normal',
          },
        },
        //x, y轴数据属性设置
        xAxis: {
          // type: 'category',
          // 去除x轴的背景线条
          splitLine: {
            show: false,
          },
          axisLabel: {
            color: '#C9D2DD',
          },
          // 隐藏x轴的刻度线和线条
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          data: year,
        },
        // XAxis(dataX, true),
        yAxis: [YAxis()],
        series: seriesData,
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#00112dbf', // 提示框浮层的背景颜色。
          borderColor: '#0066FF', // 提示框浮层的边框颜色。
          borderWidth: 1, // 提示框浮层的边框宽。

          axisPointer: {
            type: 'shadow', // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
            axis: 'auto', // 指示器的坐标轴。
            snap: true, // 坐标轴指示器是否自动吸附到点上
            label: {
              color: '#fff',
              show: false,
              backgroundColor: '#00112D',
            },
          },
          textStyle: {
            // 提示框浮层的文本样式。
            color: '#fff',
            fontStyle: 'normal',
            fontWeight: 'normal',
            fontFamily: 'sans-serif',
            fontSize: 12,
          },
          //实例
          formatter: function (params) {
            // console.log(params)
              let res = params[0].name  +params[0].seriesName + ': ' +'<span style="color: #00FFF0;font-size: 16px;">' +params[0].value+'</span> '+ unit1;
              return res;
            }
        },
        animation: true,
        animationDuration: function () {
          return 3000;
        },
        animationDurationUpdate: function () {
          return 3000;
        },
      };
      const myChart = echarts.init(document.getElementById('middleCharts'));
      myChart.setOption(option);
      //随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize();
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.echart {
  width: 100%;
  height: 175px;
   padding-left: 20px;
}
</style>
