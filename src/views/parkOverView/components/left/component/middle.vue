<template>
  <div class="mainContant">
    <el-table
      :data="showData"
      size="mini"
      style="width: 100%; height: 100%"
      stripe
    >
      <el-table-column
        prop="chainNodeName"
        label="主导产业类型"
      />
      <el-table-column
        prop="totalEnterprise"
        label="企业数量"
      >
        <template slot-scope="{ row }">
          <span class="num"> {{ row.totalEnterprise }}</span>
          <span class="int">家</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="onScaleEnterprise"
        label="规上企业"
      >
        <template slot-scope="{ row }">
          <span class="num"> {{ row.onScaleEnterprise }}</span>
          <span class="int">家</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="yearValue"
        label="年度产值"
      >
        <template slot-scope="{ row }">
          <span v-if="row.yearValue && row.yearValue !== '0.0'">
            <span class="num"> {{ row.yearValue }}</span>
            <span class="int">亿元</span>
          </span>
          <span v-else> - </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="yearTexValue"
        label="年度税收"
      >
        <template slot-scope="{ row }">
          <span v-if="row.yearValue && row.yearTexValue !== '0.0'">
            <span class="num"> {{ row.yearTexValue }}</span>
            <span class="int">亿元</span>
          </span>

          <span v-else> - </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    showData: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/public.scss";

.mainContant {
  width: 97%;
  height: calc(100% - 40px);
  overflow: hidden;
  margin-left: 4%;
  margin-top: 2px;
}
.num {
  @include YouSheBiaoTi28(16px, 400);
}
.int {
  @include PingFangSC(11px, #c9d2dd);
}
</style>
<style lang="scss">
@import "@/styles/public.scss";
#app {
  .mainContant {
    background: transparent !important;
    .el-table__body-wrapper {
      min-height: 200px !important;
      overflow: hidden;
    }
    .el-table {
      background: transparent !important;
      &::before {
        height: 0 !important;
      }
      thead th {
        @include Puhuiti(11px, #ffffff);
        background: #1b4786 !important;
        color: #ffffff !important;
        font-weight: 500;
        border-bottom: 0;
      }
    }
    tbody tr td {
      color: #ffffff !important;
      border-bottom: 0 !important;
    }
    tbody tr.el-table__row td {
      background: #07163b !important;
    }
    tbody tr.el-table__row--striped td {
      background: #1b2b48 !important;
    }
  }
}
</style>
