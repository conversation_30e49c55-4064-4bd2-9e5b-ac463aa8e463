<template>
  <div class="dataList">
    <div
      v-for="(item, index) in dataList"
      :key="item.id"
      class="itemList"
      :class="item.clickAble?'hoverAble':''"
      @click="showDetial(item)"
    >
      <div :class="['icon', 'icon' + index]" />
      <div class="content">
        <div class="text">
          {{ item.name }}
        </div>
        <div class="num">
          <span class="number"> {{ item.num }}</span><span class="int">{{ item.int }}</span>
        </div>
      </div>
    </div>
    <EnterpriseList
      v-if="isShowList"
      show-list="default"
      :api-url="apiUrl"
      area-code=""
      :nodemessage="industryType"
      :state="state"
      :type="type"
      @closeList="closeList"
    />
  </div>
</template>
<script>
import EnterpriseList from '@/views/count/admin/Enterpriselist.vue';

export default {
  components: {
    EnterpriseList
  },
  props: {
    showData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      dataList: [],
      // 本地 / 全部切换
      selNum: 0,
      // 企业类型
      selTypeList: 4,
      // 本地 / 全部切换
      tab: 4,
      // 全国本地企业切换
      showLocality: false,
      // 全国本地企业切换
      elements: null,
      isShowList: false,
      apiUrl: 'parkView',
      industryType: '',
      state: 1,
             type:''
    };
  },
  watch: {
    showData(newValue) {
      this.init(newValue);
    },
  },
  mounted() {
    this.init(this.showData);
  },
  methods: {
    init(val) {
      if (val) {
        let localIndustryBaseDTO = val;
        let {
          totalArea,
          firstPhaseArea,
          totalEnterprise,
          onScaleEnterprise,
        } = localIndustryBaseDTO;
        this.dataList = [
          {
            id: '1',
            name: '园区总面积',
            int: '平方公里',
            num: totalArea || 0,
            clickAble: false,
             type:''

            
          },
          {
            id: '2',
            name: '一期面积',
            int: '平方公里',
            num: firstPhaseArea || 0,
            clickAble: false,
             type:''
             
          },
          {
            id: '4',
            name: '企业总数量',
            int: '家',
            num: totalEnterprise || 0,
            clickAble: true,
             type:''
          },
          {
            id: '5',
            name: '规上企业数',
            int: '家',
            num: onScaleEnterprise || 0,
            clickAble: true,
             type:'onScale'
          },
          
        ];
      }
    },
       closeList() {
      this.isShowList = false;
    },
    showDetial(item) {
      if (!item.clickAble) {
        return
      }
      else {
        this.industryType = item.name
        this.type=item.type
         this.isShowList = true;
      }
      
    }
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.hoverAble{
  cursor: pointer;
}
.dataList {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  padding:  0px 10px;
  height: 100%;
  /* border-bottom: 1px solid #eee; */
}

.itemList {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 30%;
  width: 50%;
  /* border-bottom: 1px solid #eee; */
}

.icon {
  width: 50%;
  height: 100%;
  background: #f00;
}

.icon0 {
  background: url('~@/assets/screen/new/icon0.png') center/80% no-repeat;
}

.icon1 {
  background: url('~@/assets/screen/new/icon1.png') center/80% no-repeat;
}

.icon2 {
  background: url('~@/assets/screen/new/icon2.png') center/80% no-repeat;
}

.icon3 {
  background: url('~@/assets/screen/new/icon3.png') center/80% no-repeat;
}

.icon4 {
  background: url('~@/assets/screen/new/icon4.png') center/80% no-repeat;
}

.icon5 {
  background: url('~@/assets/screen/new/icon5.png') center/80% no-repeat;
}

.content {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  width: 55%;
  height: 100%;

  .text {
    width: 100%;
    margin-left: -5px;
    font-size: 0.88rem;
    color: #C9D2DD;
    font-family: PingFangSC;
  }


  .num {
    margin-left: -5px;
    font-size: 1.38rem;
    font-family: PingFangSC;
    font-weight: bold;

    .number {
      @include  YouSheBiaoTi28();
    }
    .int {
      padding-left: 10px;

      font-size: 0.75rem;
      font-family: PingFangSC;
      // font-weight: 400;
      color: #cad3de;
      // line-height: 33px;
    }
  }
}
</style>
