<template>
  <div class="keyEnterpriseBody">
    <div
      v-if="showValues && showValues.length > 0"
      class=" valuesList"
    >
      <div
        ref="scrollContent"
        class="amount-content"
        @mouseenter="pauseAnimation"
        @mouseleave="resumeAnimation"
      >
        <!-- 第一部分数据（用于循环结束后的衔接） -->
        <div
          v-for="items in showValues"
          :key="items.dicKey+2"
          class="valuesCard"
          @click="checkCard(items)"
        >
          <div class="dicValue">
            {{ items.dicValue }}
          </div>
          <div class="dicKey">
            {{ items.dicKey }}
          </div>
        </div>
        <!-- 主数据 -->
        <div
          v-for="items in showValues"
          :key="items.dicKey+3"
          class="valuesCard"
          @click="checkCard(items)"
        >
          <div class="dicValue">
            {{ items.dicValue }}
          </div>
          <div class="dicKey">
            {{ items.dicKey }}
          </div>
        </div>
        <!-- 第二部分数据（用于滚动衔接） -->
        <div
          v-for="items in showValues"
          :key="items.dicKey"
          class="valuesCard"
          @click="checkCard(items)"
        >
          <div class="dicValue">
            {{ items.dicValue }}
          </div>
          <div class="dicKey">
            {{ items.dicKey }}
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      class="no-data"
    >
      <NoData />
    </div>

    <EnterpriseList
      v-if="isShowList"
      show-list="default"
      :api-url="apiUrl"
      area-code=""
      :nodemessage="industryType"
      :state="state"
      :type="type"
      @closeList="closeList"
    />
  </div>
</template>

<script>
import * as echarts from 'echarts';
import EnterpriseList from '@/views/count/admin/Enterpriselist.vue';
import {
  toolTips,
  toolTipAxis,
} from '@/views/overview/components/component/toolTips';
import NoData from '@/views/overview/components/component/noData';
export default {
  name: 'BottomModuleAmount',
  components: {
    EnterpriseList,
    NoData,
  },
  props: {
    showData: {
      type: Array,
      default: ()=>[],
    },
  },
  data() {
    return {
      isShowList: false,
      dataList: [],
      showValues: [],
      nodemessage: '产业重点企业情况',
      apiUrl: 'parkView',
      industryType: '',
      state: 1,
      num: 0,
      sliceData: [],
      index: 0,
      svgIcons: {},
      currentTranslate: 0,
      currentIndex: 0,
      animating: false,
      isAnimationPaused: false,
      type:''
    };
  },
  watch: {
    showData(newValue) {
      this.init(newValue);
    },
  },
  mounted() {
    this.init(this.showData);
  },
  methods: {
    init(val) {
      if (val) {
        // let industryEnterpriseDTO = val;
        // let { values } = industryEnterpriseDTO;
        // console.log(val, 'values');
        this.showValues = val;
        // 监听动画迭代完成事件，实现平滑重置
        this.$nextTick(() => {
          this.setupAnimation();
        });
      }
    },
    checkCard(params) {
      this.type= params.dicKey;
      this.industryType = params.dicKey;
      this.isShowList = true;
      // 展示弹窗
    },
    closeList() {
      this.isShowList = false;
    },
    setupAnimation() {
      const scrollContent = this.$refs.scrollContent;
      if (!scrollContent) return;

      // 计算单组数据的高度，用于动画重置
      this.$nextTick(() => {
        // 添加动画迭代事件监听
        scrollContent.addEventListener(
          'animationiteration',
          this.onAnimationIteration
        );
      });
    },
    pauseAnimation() {
      const scrollContent = this.$refs.scrollContent;
      if (!scrollContent) return;

      this.isAnimationPaused = true;
      scrollContent.style.animationPlayState = 'paused';
    },

    resumeAnimation() {
      const scrollContent = this.$refs.scrollContent;
      if (!scrollContent || !this.isAnimationPaused) return;

      this.isAnimationPaused = false;
      scrollContent.style.animationPlayState = 'running';
    },
  },
};
</script>
<style lang="scss" scoped>
@import '@/styles/public.scss';
.amount-content {
  width: 100%;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  animation: smoothScroll 40s linear infinite;
}
@keyframes smoothScroll {
  0% {
    transform: translateY(0);
  }
  100% {
    /* 滚动距离为一组数据的高度 */
    transform: translateY(-33.33%);
  }
}
.no-data {
  display: flex;
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
}

.keyEnterprise,
.keyEnterpriseBody {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 8px 16px 0px 24px;
}
.valuesList {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  border-radius: 8px;
  background-color: rgba(2, 48, 91, 0.4); /* 卡片背景色 */
  padding: 10px 16px;
  overflow: hidden;
  &::before {
    content: '';
    position: absolute;
    margin: 8px 16px 0px 24px;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 8px;
    padding: 1px; /* 控制边框宽度 */
    opacity: 0.4;
    background: linear-gradient(180deg, #00ccff 0%, #00489a 100%);
    -webkit-mask: linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none; /* 确保不会影响卡片内容的点击 */
  }
}
.valuesCard {
  width: 50%;
  height: 55px;
  position: relative;
  text-align: center;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  flex-wrap: wrap;
  cursor: pointer;
  // border-bottom: 1px solid;
  &:not(:nth-last-child(1)):not(:nth-last-child(2)) {
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background: center / contain no-repeat
        url(https://static.idicc.cn/cdn/pangu/assets/screen/newScreen/line.webp);
    }
  }

  .dicKey {
    width: 100%;
    @include Puhuiti(14px, #f4f4f4, 400);
  }
  .dicValue {
    width: 100%;

    @include YouSheBiaoTi28(24px);
  }
}
</style>
