<template>
  <div class="content">
    <div class="top">
      <div class="middleTopDataList">
        <div
          v-for="(i, index) in middleTopDataListItem"
          :key="i.id"
          class="middleTopDataListItem"
        >
          <div :class="['text', index === 0 ? 'autoWith' : '']">
            <span> {{ i.name }}</span>
            <el-tooltip
              v-if="index === 0"
              :content="i.toolTip"
              placement="top"
            >
              <i
                class="el-icon-warning-outline"
                style="color: rgba(255, 255, 255, 0.4)"
              />
            </el-tooltip>
          </div>
          <div class="value">
            {{ i.value }}
          </div>
        </div>
      </div>

      <div
        ref="screenRef"
        class="topMap"
      >
        <TopVideo :data="data" />
      </div>
    </div>
    <!-- 下 -->
    <Titles
      title="产业发展特征"
      bg-large
    />
    <div class="developBottom">
      <div class="bottomLeft">
        <TitlePoints title="近5年园区产业企业增长趋势" />
        <!-- v-if="newEnterprise && newEnterprise.length > 0"  -->
        <div
          id="growthTrend"
          class="growthTrend"
        />
        <div
          v-if="!(newEnterprise && newEnterprise?.length > 0)"
          class="no-data"
        >
          <NoData />
        </div>
      </div>
      <div class="bottomRight">
        <TitlePoints title="园区产业企业环节分布" />
        <PercentagePlot
          :data-list="percentagePlot"
          text="驻马店市"
          text1="园区"
        />
        <div
          v-if="!(chainStat && chainStat.length > 0)"
          class="no-data"
        >
          <NoData />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts";

import TopVideo from "./component/topVideo.vue";
import Titles from "../component/titles.vue";
import TitlePoints from "@/views/overview/components/component/titlePoint.vue";
import { tooltipShadow } from "@/views/overview/components/component/toolTips";
import NoData from "@/views/overview/components/component/noData";
// import ScreenFull from '@/components/Screenfull';
// import MapDialog from '../component/dialogs.vue';xpgxp
import PercentagePlot from "@/components/PercentagePlot";
export default {
  components: {
    // MapMain,
    Titles,
    TitlePoints,
    NoData,
    // ScreenFull,
    // MapDialog,
    PercentagePlot,
    TopVideo,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      elements: null,
      isFullscreen: false,
      isDialog: false,
      name: "middle",
      middleTopDataListItem: [],

      active: 5,
      roundBg: require("../../../../assets/screen/new/round.png"), //你的图片地址
      mapShowData: null,
      chainStat: [],
      newEnterprise: [],
      percentagePlot: {
        modelName: "",
        values: [],
      },
    };
  },
  watch: {
    data(val) {
      this.init(val);
    },
  },

  mounted() {
    this.elements = this.$refs.screenRef;
    this.init(this.data);
  },

  methods: {
    init(newData) {
      let val = this.allData || newData;
      let chainDevelopFeatureDTO = val?.chainGrouthStat; //产业发展特征
      // 近5年新增企业,产业链企业环节分布情况
      let { newEnterprise, chainStat } = chainDevelopFeatureDTO;
      this.setTopData(val);
      let data = [];
      newEnterprise?.map((item) => {
        if (item.enterpriseNum > 0) {
          data.push(item);
        }
      });
      // console.log(chainStat, 'chainStat')
      if (data && data.length > 0) {
        this.newEnterprise = newEnterprise;
        this.growthTrend(newEnterprise);
      }
      if (chainStat && chainStat.length > 0) {
        this.percentagePlot = {
          modelName: "",
          values: chainStat?.map((e) => {
            const { allNumber, industryNodeId, chainNodeName, number } = e;
            return {
              dicValue: number,
              dicValueCountry: allNumber,
              dicKey: chainNodeName,
              industryNodeId,
            };
          }),
        };
        // console.log( this.percentagePlot ,' this.percentagePlot ')
        this.chainStat = chainStat;
        // this.localIndustry(chainStat);
      }
    },
    setTopData(val) {
      let keyFeatureDTO = val?.chainEnterprises;
      let { totalEnterprise, totalTexValue, totalValue } = keyFeatureDTO;

      this.middleTopDataListItem = [
        {
          id: 1,
          name: "企业总数量",
          value: totalEnterprise || 0,
          toolTip: "该园区所选产业链企业数",
          //   `数据截止至${outputValue?.year || ''}年Q${
          //   outputValue?.season || ''
          // }（年度范围内）`,
          // 总产值：
        },
        {
          id: 2,
          name: "年度产值（亿元）",
          value: totalTexValue || 0,
          toolTip: ``,
        },
        {
          // 规上工业增加值：
          id: 3,
          name: "年度税收（亿元）",
          value: totalValue || 0,
          toolTip: "",
          //   `数据截止至${scaleIndustrialAdded?.year || ''}年${
          //   scaleIndustrialAdded?.month || ''
          // }月（年度范围内）`,
        },
        // {
        //   id: 4,
        //   name: '同比增速(%)',
        //   value: scaleIndustrialAdded?.yearGrowth || 0,
        //   toolTip: ``,
        // },
      ];
    },
    growthTrend(data) {
      let years = data?.map((e) => e.year);
      let enterpriseNum = data?.map((e, index) => e.enterpriseNum || 0);
      let chartDom = document.getElementById("growthTrend");

      let myChart = echarts.init(chartDom);
      let options = {
        // color: ['rgba(205, 208, 255, 1)'],
        title: {
          text: "",
        },
        tooltip: tooltipShadow,

        grid: {
          left: "3%",
          right: "4%",
          bottom: "7%",
          top: "10%",
          containLabel: true,
        },
        xAxis: {
          // 倾斜

          axisLabel: {
            interval: 0,
            rotate: 0,
            textStyle: {
              color: "#C9D2DD",
              fontSize: "12",
              itemSize: "",
              marginLeft: "2px",
            },
          },
          data: years,
          type: "category",
          boundaryGap: true,
          axisLine: {
            show: false,
            lineStyle: {
              color: "#57617B",
              fontSize: "20px",
            },
          },
          axisTick: {
            show: false, // 隐藏 x 轴刻度线
          },
        },
        yAxis: [
          {
            name: "单位(家)",
            nameLocation: "start",
            type: "value",
            nameTextStyle: {
              fontSize: "9",
              color: "#C9D2DD", //颜色
              opacity: 0.9,
              padding: [3, 0, 0, 0], //间距分别是 上 右 下 左
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#ccc",
                width: 1,
                opacity: 0.2,
                type: "dashed",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#fff",
                opacity: 0.2,
              },
            },
            axisLabel: {
              formatter: "{value} ",
              color: "#C9D2DD",
              opacity: 0.8,
              textStyle: {
                color: "#C9D2DD",
                fontSize: "12",
                itemSize: "",
                marginLeft: "2px",
              },
            },
          },
        ],
        series: [
          {
            name: "",
            type: "line",
            stack: "Total",
            label: {
              show: true,
              position: "top",
              textStyle: {
                //数值样式
                fontWeight: 500,
                color: "#fff",
                fontSize: 12,
              },
            },
            // 设置折线上数据点的样式
            itemStyle: {
              color: "#00D5FF", // 点的颜色
              // borderColor: '#00D5FF', // 点的边框颜色
              // borderWidth: 1, // 点的边框宽度
              // shadowColor: 'rgba(0, 213, 255, 0.5)', // 阴影颜色
              // shadowBlur: 2 // 阴影大小
            },
            symbolSize: 3,
            showSymbol: true,
            emphasis: {
              focus: "series",
            },
            smooth: false,
            lineStyle: {
              width: 2,
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0.5, color: "#00D5FF" },
                { offset: 0.5, color: "#00D5FF" },
                { offset: 0.5, color: "#00d5ff" },
                // { offset: 0.5, color: '#00d5ff' },
                // { offset: 1, color: 'rgba(255, 255, 255, 0)' }
              ]),
            },

            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#41ACFF",
                },
                {
                  offset: 0.2,
                  color: "rgba(0, 209, 255, 0.4)",
                },
                {
                  offset: 1,
                  color: "rgba(0, 167, 255, 0.16)",
                },
              ]),
            },

            data: enterpriseNum,
          },
        ],
      };
      options && myChart.setOption(options);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/public.scss";

.content {
  height: 100%;
  padding: 20px 20px 40px 20px;
}

.top {
  height: calc(70% - 40px);
}

.bottom {
  height: 30%;
}

.developBottom {
  display: flex;
  justify-content: space-between;
  height: 30%;
  width: 100%;
  // background: url('~@/assets/screen/new/middle_bg.png') center/cover no-repeat;
  background: center/cover no-repeat
    url("https://static.idicc.cn/cdn/pangu/middle_bg.png?x-oss-process=image/quality,q_85/format,webp");

  .bottomLeft {
    width: 45%;
    padding: 10px 15px 0 16px;
    position: relative;
  }

  .bottomRight {
    width: 55%;
    padding: 10px 0px 0 8px;
    position: relative;
  }

  .bottomRight {
    // width: 50%;
  }
}

.no-data {
  display: flex;
  position: absolute;
  top: 35px;
}

.no-data,
.localIndustry,
.growthTrend {
  width: 100%;
  height: calc(100% - 40px);
}

.topMapCopy.topMap {
  height: 100%;
}

.topMap {
  width: 100%;
  // display: flex;
  height: calc(100% - 100px);
  position: relative;

  .middleDataList {
    position: absolute;
    z-index: 22;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 70%;
    width: 8.75rem;
    flex-wrap: wrap;
    align-content: center;
  }

  .dataItem {
    width: 100%;
    height: 34px;
    background: url("https://static.idicc.cn/cdn/pangu/tab_normal.webp")
      center/contain no-repeat;
    background-size: 100% 100%;
    margin-bottom: 20%;
    cursor: pointer;

    &.active,
    &:hover {
      background: url("https://static.idicc.cn/cdn/pangu/tab_height.webp")
        center/contain no-repeat;
      .text {
        background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .text {
      line-height: 34px;
      text-align: center;
      width: 100%;
      height: 34px;
      @include YouSheBiaoTi(1rem);
    }
  }
}

.middleTopDataList {
  width: 100%;
  height: 5.625rem;
  padding: 0px 10%;
  display: grid;

  grid-template-columns: 1fr 1fr 1fr;
  background: url("~@/assets/screen/new/header_bg3.png") center/cover no-repeat;
  background-size: 100% 100%;
  padding-bottom: 15px;
  align-items: center;

  .middleTopDataListItem {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .text.autoWith {
      span {
        width: auto;
      }
    }

    .text {
      width: 100%;
      text-align: center;

      span {
        width: 100%;
        display: inline-block;
        font-size: 0.875rem;
        line-height: 2rem;
        font-family: "puhuiti";
        font-weight: bold;
        // line-height: 17px;
        letter-spacing: 0px;

        font-variation-settings: "opsz" auto;
        font-feature-settings: "kern" on;
        color: #ffffff;

        text-shadow: 5px 2px 5px rgba(17, 20, 22, 0.22),
          0px 0px 10px rgba(73, 223, 255, 0.32);
      }
    }

    .value {
      font-family: YouSheBiaoTiHei;
      font-size: 1.375rem;
      background: linear-gradient(180deg, #8cb0c4 0%, #ffffff 100%);
      letter-spacing: 0.36px;
      background: linear-gradient(
          180deg,
          rgba(156, 235, 255, 0.5) 0%,
          rgba(156, 235, 255, 0.5) 23%,
          rgba(255, 255, 255, 0.5) 69%
        ),
        #e4edf7;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
      margin-right: 10px;
    }
  }
}

@media screen and (max-width: 1450px) {
  .content {
    padding: 10px 0;
  }

  .topMap {
    .dataItem {
      .text {
        font-size: 0.85rem;
      }
    }
  }
}

.divider {
  width: 0.13rem;
  height: 1.88rem;
  background: rgba(255, 255, 255, 0.1);
}

.right-menu-item {
  position: absolute;
  right: 5%;
  top: 1%;
  z-index: 25;
  width: 4.5rem;
  height: 2rem;
  background: #0b254d;
  border-radius: 0.25rem;
  font-size: 0.8rem;
  font-weight: 400;
  color: rgba(174, 192, 252, 0.8);
  text-align: center;
  line-height: 2rem;

  &:hover {
    background: #112e5b;
    border-radius: 0.25rem;
    color: rgba(255, 255, 255, 0.8);
  }
}
</style>
