<template>
  <div class="center">
    <div class="left">
      <div class="topTitle">
        <!-- <Titles title="园区风采" /> -->
        <div :class="['header', 'title']">
          <span> 园区风采</span>
        </div>
      </div>
      <div class="introduction">
        <div class="introductionIn">
          <div class="introductionText">
            {{ introduction }}
            <span
              class="show"
              @click="showMore"
            >更多>></span>
          </div>

          <!-- <div class="introductionButton">
            <div
              :class="
                showText ? 'introductionButtonIn' : 'introductionButtonOut'
              "
              @click="hideText"
            ></div>
          </div> -->
        </div>
      </div>
    </div>
    <div class="right">
      <div class="tabText">
        <span
          :class="{ active: viewState === '1' }"
          @click="changViewState('1')"
        >
          风采图片</span>
        <span
          :class="{ active: viewState === '2' }"
          @click="changViewState('2')"
        >
          风采视频</span>
      </div>
      <div class="centerImg">
        <div
          v-if="viewState === '1'"
          class="centerImgs"
        >
          <!-- :height="350" -->
          <el-carousel
            v-if="imgValue.length > 0"
            trigger="click"
            height="320"
            arrow="never"
            :interval="3000"
          >
            <el-carousel-item
              v-for="item in imgValue"
              :key="item"
            >
              <div
                class="titleImg"
                :style="{ backgroundImage: `url(${item.url})` }"
              />
            </el-carousel-item>
          </el-carousel>
          <div
            v-else
            class="nodata"
          />
        </div>
        <div
          v-else
          class="centerVideo"
        >
          <!-- <div class="centerVideoIn" v-if="videoUrl"> -->
          <VideoView />
          <!-- </div>
          <div v-else class="centerVideoIn">
            <div class="nodata"></div>
          </div> -->
        </div>
      </div>
      <div
        v-if="innerVisible"
        class="dialogBg"
      >
        <el-dialog
          :visible="innerVisible"
          width="1000px"
          :modal-append-to-body="false"
          custom-class=" dialog_tip"
          @close="innerVisible = false"
        >
          <Introduce />
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import VideoView from './video.vue';
// import Titles from '../../component/titles.vue';
import Introduce from './introduce.vue';
// import Content from './content.vue'
export default {
  components: {
    VideoView,
    // Titles,
    Introduce,
    // Content
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      viewState: '1',
      introduction:
        '中国(驻马店)国际农产品加工产业园是农业农村部批复建设的国家级园区，由河南省印发建设总体方案，规划建设“四基地一中心”，包括农产品精深加工基地、高端营养食品基地、食品机械装备基地、农产品科技研发基地，和国际贸易物流中心。园区规划120平方公里，一期建设61平方公里，位于驻马店...',
      imgValue: [
        {
          url: 'https://static.idicc.cn/cdn/pangu/zhumadian/01.webp',
        },
        {
          url: 'https://static.idicc.cn/cdn/pangu/zhumadian/02.webp',
        },
        {
          url: 'https://static.idicc.cn/cdn/pangu/zhumadian/03.webp',
        },
          {
          url: 'https://static.idicc.cn/cdn/pangu/zhumadian/04.webp',
        },
      ],
      showText: false, // 添加 showText 变量
      innerVisible: false, // 添加 innerVisible 变量用于控制弹窗
    };
  },
  methods: {
    changViewState(state) {
      this.viewState = state;
    },
    showMore() {
      // 可以在这里添加显示更多内容的逻辑
      this.innerVisible = true;
    },
    hideText() {
      // 添加 hideText 方法
      this.showText = !this.showText;
    },
  },
};
</script>
<style scoped lang="scss">
@import '@/styles/public.scss';
@import '@/views/attractinvestment/admin/components/dialog_tip.scss';
.topTitle {
  //  padding: 20px 0px 0px 20px;
  width: 100%;
  .header {
    background: url('~@/assets/screen/new/title_bg1.png') center/cover no-repeat;
    background-size: 100% 50%;
    background-position: bottom;
  }
  .title {
    width: 100%;
    height: 40px;
    // margin-top: -10px;
    span {
      @include YouSheBiaoTi24(16px);
      line-height: 40px;
      padding-left: 25px;
    }
  }
}

.nodata {
  width: 100%;
  height: 350px;
  // background-image: url(../../../../../../assets/cooperation/nodata.jpg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

// .el-carousel{
//     height: 350px;
// }
.demonstration {
  color: var(--el-text-color-secondary);
}

.el-carousel__item h3 {
  color: #475669;
  opacity: 0.75;
  line-height: 150px;
  margin: 0;
  text-align: center;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}

.center {
  width: 100%;
  height: 100% !important;
  display: flex;
  justify-content: space-between;
  padding: 20px;

  .left {
    width: 25%;
    border-right: 1px solid #1a7dda;

    // &::before{

    // }
  }
  .right {
    width: 75%;
    height: 100% !important;
    position: relative;
    padding: 10px 10px 10px 0px;
  }

  width: 100%;
  height: 425px;
  display: flex;
  position: relative;
  flex-wrap: nowrap;
  background-position: center;
  background-image: url('../../../../../assets/screen/park/bg3.png');
  background-size: 100% 100%;

  .centerImg {
    width: 100%;
    height: 100%;
    // padding: 35px 30px 0 0;
    .centerImgs {
      width: 100%;
      height: 100%;
      // padding: 10px 0;
      display: flex;
      align-items: center;
    }

    .titleImg {
      width: 100%;
      height: 100%;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
    }

    .centerVideoIn {
      width: 75%;
      // margin: 0 auto;
      float: right;

      .amis-scope .antd-Page-body {
        padding: 0 !important;
      }
    }

    .centerVideo {
      width: 100%;
      height: 100%;

      background: #13244c;
    }
  }

  .introduction {
    width: 100%;
    height: calc(100% - 30px);
    overflow: hidden;
    .introductionIn {
      width: 100%;
      height: 100%;
      .introductionText {
        width: 100%;
        height: 100%;
        padding: 16px;
        font-size: 14px;
        color: #ffffff;
        line-height: 26px;
        text-align: left;
        background: center / contain no-repeat
          url(https://static.idicc.cn/cdn/pangu/name-1.png);
        background-size: 100% 1000%;
      }

      .introductionButton {
        width: 17px;
        height: 100%;
        display: flex;
        align-items: center;

        .introductionButtonIn {
          width: 100%;
          height: 50px;
          // background: center/60% no-repeat url('@/assets/images/view/honer-left.png');
        }

        .introductionButtonOut {
          width: 100%;
          height: 50px;
          // background: center/60% no-repeat url('@/assets/images/view/honer-right.png');
        }
      }
    }
  }

  .middle {
    width: 570px;
    height: 440px;
    // background: center / contain no-repeat url('@/assets/images/view/view.png');
  }
}

.show {
  cursor: pointer;
  color: #38f1e0;
}
</style>
<style lang="scss">
.tabText {
  display: flex;
  flex-wrap: nowrap;
  // width: 180px;
  position: absolute;
  right: 20px;
  top: 20px;
  z-index: 111;

  span {
    cursor: pointer;
    // width: 90px;
    display: inline-block;
    padding: 2px 10px;
    background: #0c2753;

    opacity: 0.7;
    font-size: 14px;
    font-weight: 400;
    color: #ffffff;
    box-shadow: 0 0 0 0px;

    // border: 0;
    &:first-child {
      &.active {
        // border: 1px solid #3695FF;
        box-shadow: 0 0 3px 1px #3695ff inset;
      }

      border-radius: 4px 0px 0px 4px;

      border-right: 0;
    }

    &:last-child {
      &.active {
        // border: 1px solid #3695FF;
        box-shadow: 0 0 3px 1px #3695ff inset;
      }

      border-radius: 0px 4px 4px 0px;
    }
  }
}
.centerVideoIn {
  .amis-scope .antd-Page-body {
    padding: 0 !important;
  }
}
.centerImgs .el-carousel__button {
  width: 10px;
}
.centerImgs {
  .el-carousel {
    width: 100%;
    height: 100%;
    .el-carousel__container {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
