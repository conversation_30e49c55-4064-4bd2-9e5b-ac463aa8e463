<template>
  <vueMiniPlayer
    ref="vueMiniPlayer"
    :video="video"
    :mutex="true"
    @fullscreen="handleFullscreen"
  />
</template>
<script>
export default {
  data() {
    return {
      video: {
        url: 'https://static.idicc.cn/cdn/pangu/zhumadian/驻马店农产园宣传片-2023.mp4',
        cover:'',
          // 'https://static.idicc.cn/cdn/pangu/zhumadian/cover.jpg',
        muted: false,
        loop: false,
        preload: 'auto',
        poster: '',
        volume: 1,
        autoplay: false,
      },
    };
  },
  computed: {
    // video() {
    //   return this.$refs.vueMiniPlayer.$video;
    // },
  },
  methods: {
    handleFullscreen() {},
  },
};
</script>
<style>
._play-btn,.qun-base-controls{
  z-index: 99999 !important;
}
</style>
