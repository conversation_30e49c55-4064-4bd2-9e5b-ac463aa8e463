<template>
  <div :class="[bgLarge ? 'bgLarge' : 'bgDefault', 'title']">
    <span> {{ title }}</span>
  </div>
</template>
<script>
export default {
  name: 'OverViewTitle',
  components: {
    // logout,
    // Industry
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    bgLarge: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
    }
  },
  created () {
  },
  mounted () {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/public.scss';
.bgDefault {
  background: url('~@/assets/screen/new/title_bg1.png') center/cover no-repeat;
  background-size: 100% 50%;
  background-position: bottom;
}

.bgLarge {
  background: url('~@/assets/screen/new/title_bg2.png') center/cover no-repeat;
  background-size: 100% 50%;
  background-position: bottom;
}

.title {
  width: 100%;
  height: 40px;
  // background: url('~@/assets/screen/new/title_bg1.png') center/cover no-repeat;

  span {
    @include YouSheBiaoTi24(1.5rem,  );
    
    // font-style: italic;
    // color: #F6F9FE;
    line-height: 40px;
    padding-left: 55px;
    padding-right: 20px;
 
  }
}

// @media screen and (max-width: 1450px) {
//   .title {
//     span {
//       font-size: 1.25rem;
//     }
//   }
// }
</style>