<template>
  <div class="cardContent">
    <div class="title">
      <div class="info">
        <div>
          <div class="infoList">
            <img
              class="iconImg"
              :src="getIconByType(detailData.enterpriseLabelType)"
            >
            <div
              class="center"
              @click="openDetailSingle(detailData)"
            >
              {{ detailData.enterpriseName }} 
              <!-- </el-popover> -->
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="tagShow?.length>0"
        class="tag"
      >
        <span
          v-for="(tag, index) in tagShow"
          :key="index"
          class="tagName"
        >{{ tag }}
        </span>
        <span
          v-if="moreTag.length > 0"
          class="more"
          @mouseenter="activeStates = detailData.id"
          @mouseleave="activeStates = null"
        >
          更多标签 >>
        </span>
      </div>
      <!-- -->

      <!-- <div v-else class="tag" /> -->
    </div>
    <div
      
      class="showdetails"
    >
      <span>所属产业链环节：</span><span>{{ detailData?.secondChainNodeNames?.join(',') ||'-' }}</span>
      <span style="margin-left: 2rem">成立时间：</span><span>{{ detailData.registerDate }}</span>
      <!-- <el-popover
        v-if="detailData.registeredCapital"
        placement="top-start"
        width="200"
        trigger="hover"
        :disabled="detailData.registeredCapital.length < 9"
        :content="detailData.registeredCapital"
        :open-delay="500"
        popper-class="tag-popover"
      >
        <span slot="reference">
          <span style="margin-left: 2rem">注册资金：</span><span>{{ detailData.registeredCapital }}</span>
        </span>
      </el-popover> -->
    </div>
    <div
      class="showdetails"
    >
      <span>企业规模：</span><span>{{ detailData.scale }}</span>
      <span style="margin-left: 2rem">注册资金：</span><span>{{ detailData.registeredCapital }}</span>
    </div>
    <div
      v-if="activeStates === detailData.id && !enterpriseShow"
      class="moreTag"
      :style="{ bottom: showdTime ? '4rem' : '2rem' }"
    >
      <span
        v-for="(tag, index) in moreTag"
        :key="index"
      >#{{ tag }}</span>
      <!-- <div
        v-if="activeState === detailData.id && enterpriseShow"
        class="moreTag"
        :class="{ enterpriseShow: enterpriseShow }"
      >
        {{ detailData.enterpriseName }}
      </div> -->
    </div>
  </div>
</template>
<script>
import { getEnterpriseIconByType } from '@/utils/utils'
export default {
  props: {
    detailData: {
      type: Object,
      default: ()=>{},
    },
    showdTime: {
      type: Boolean,
      default: true,
    },
    enterpriseShow: {
      type: Boolean,
      default: false,
    },
    // activeState: {
    //   type:string ,
    //   default:'',
    // },
    tagShow: {
      type: Array,
      default: () => [],
    },
    moreTag: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      activeStates: '',
    };
  },
  // watch: {
  //   detailData(newVal) {
  //     console.log('newVal', newVal);
     
  //   },
  // },
  mounted() {
    // console.log('detailData', this.detailData);
  },
  methods: {
    getIconByType (type) {
      return getEnterpriseIconByType(type, 'webp');
    },
    openDetailSingle(data) {
      this.$emit('openDetailSingle', data);
    }
  },
};
</script>
<style lang="scss" scoped>
::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
//
@import './index.scss';
</style>
