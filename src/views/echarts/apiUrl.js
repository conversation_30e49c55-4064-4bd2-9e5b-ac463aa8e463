const apiDomain2 =
  'https://www.fastmock.site/mock/cd339c8f48c9e13af2c490b1481ffe1c/qingteng';
import interfaceRequest from '@/utils/interfaceRequest';
import { getToken } from '@/utils/auth';

// import requestOther from '@/utils/requestOther'
import axios from 'axios';
import {
  apiUrl,
  // cityJsonUrl
} from '@/api/user';
import store from '@/store';
// import { download } from '@/utils';
const apiDomain = apiUrl;

/**
 * 大屏
 */
export const chartApi = {
  atlasRelation: apiDomain + '/admin/orgIndustryChainRelation/atlas', // 图谱列表
  mapRelation: apiDomain + '/admin/orgIndustryChainRelation/map', // 地图列表
  //enterpriseList:apiDomain+'/admin/orgIndustryChainRelation/enterpriseList', //  图谱 - 企业清单
  enterpriseList:
    apiDomain + '/admin/orgIndustryChainRelation/atlasEnterpriseList', //  图谱 - 企业清单
  mapenterpriseList:
    apiDomain + '/admin/orgIndustryChainRelation/mapEnterpriseList', //  地图 - 企业清单
  //enterpriseListExport:apiDomain+'/admin/orgIndustryChainRelation/enterpriseListExport', // 图谱 - 导出企业清单
  enterpriseListExport:
    apiDomain + '/admin/orgIndustryChainRelation/atlasEnterpriseListExport', // 图谱 - 导出企业清单
  //download:apiDomain+'/admin/orgIndustryChainRelation/download', // 图谱 - 导出地图企业清单
  download:
    apiDomain + '/admin/orgIndustryChainRelation/mapEnterpriseListExport', // 图谱 - 导出地图企业清单
  enterpriseDetail: apiDomain + '/admin/enterprise/detail', // 图谱 - 企业详情
  cityList: apiDomain + '/admin/administrativeDivision/list', //图谱 - 导出企业清单
  enterpriseParticulars:
    apiDomain + '/admin/orgIndustryChainRelation/enterpriseDetail', //获取指定企业详情
  enterpriseParticularsV2:
    apiDomain + '/admin/orgIndustryChainRelation/enterpriseDetailV2', //获取指定企业详情
  //loadByParent:apiDomain+'/admin/industryChainNode/loadByParent', // 地图 - 产业链节点数据
  loadByParent: apiDomain + '/admin/industryChainNode/loadByOrgId', // 地图 - 产业链节点数据
  // chainRelationGeo:apiDomain+'/admin/orgIndustryChainRelation/geo/data', // 地图 - 产业链节点数据
  chainRelationGeo: apiDomain + '/admin/orgIndustryChainRelation/geo/data', // 地图 - 产业链节点数据
  pageBusinessEnterpriseList:
    apiDomain + '/admin/orgIndustryChainRelation/pageBusinessEnterpriseList', // 招商舆情
  batchUpdateXYByNodeIds:
    apiDomain + '/admin/industryChainNode/batchUpdateXYByNodeIds', // 批量更新节点xy坐标值
  queryLeafNodeEnterpriseNum:
    apiDomain + '/admin/orgIndustryChainRelation/queryLeafNodeEnterpriseNum', // 获取挂载企业节点企业总数和本地企业总数
  queryAtlasByNode:
    apiDomain + '/admin/orgIndustryChainRelation/queryAtlasByNode', // 根据节点查询关联的子节点和企业数据

  queryAtlasSecondQueryParam:
    apiDomain + '/admin/orgIndustryChainRelation/queryAtlasSecondQueryParam', // 获取图谱二级查询条件数据

  listLineInfo: apiDomain + '/admin/industryChainNode/listLineInfo', // 获取机构产业链关联的节点线信息
  saveLineInfo: apiDomain + '/admin/industryChainNode/saveLineInfo', // 保存产业链节点线信息
  attentionEnterprise:
    apiDomain + '/admin/business/enterprise/attentionEnterprise', // 关注企业

  lineAdd: apiDomain + '/admin/line/add', // 新增机构产业链图谱线记录
  lineList: apiDomain + '/admin/line/list', // 获取指定机构产业链id关联的图谱线记录列表
  lineDelete: apiDomain + '/admin/line/delete', // 删除指定id的图谱线记录
  pointAdd: apiDomain + '/admin/point/add', // 新增机构产业链图谱点记录
  pointList: apiDomain + '/admin/point/list', // 获取指定机构产业链id关联的图谱点记录列表
  pointDelete: apiDomain + '/admin/point/delete', // 删除指定id的图谱点记录
};
// 新增机构产业链图谱线记录
export function lineAdd(data) {
  return interfaceRequest({
    url: chartApi.lineAdd,
    method: 'POST',
    data,
  });
}
// 获取指定机构产业链id关联的图谱线记录列表
export function lineList(data) {
  return interfaceRequest({
    url: apiUrl + chartApi.lineList,
    method: 'GET',
    params: { ...data },
  });
}
// 删除指定id的图谱线记录
export function lineDelete(data) {
  return interfaceRequest({
    url: apiUrl + chartApi.lineDelete,
    method: 'GET',
    params: { ...data },
  });
}
// 新增机构产业链图谱点记录
export function pointAdd(data) {
  return interfaceRequest({
    url: chartApi.pointAdd,
    method: 'POST',
    data,
  });
}
// 获取指定机构产业链id关联的图谱点记录列表
export function pointList(data) {
  return interfaceRequest({
    url: apiUrl + chartApi.pointList,
    method: 'GET',
    params: { ...data },
  });
}
// 删除指定id的图谱点记录
export function pointDelete(data) {
  return interfaceRequest({
    url: apiUrl + chartApi.pointDelete,
    method: 'GET',
    params: { ...data },
  });
}

export const pageTypeList = () => {
  let orgCode = localStorage.getItem('orgCode')
  let zhumadianCode = process.env.VUE_APP_ZHU_ORG_CODE;
  console.log(orgCode, zhumadianCode, 'zhumadianCode');

  let park = [{
    value: '/parkOverView',
    label: '园区概览',
  }];
  let defaultPath = [
    {
      value: '/overview',
      label: '产业概览',
    },
    {
      value: '/dashboard',
      label: '产业洞察',
    },
    {
      value: '/IndustryGraph',
      label: '产业全景',
    },
    {
      value: '/MapEcharts',
      label: '产业地图',
    },
    {
      value: '/attractInvestment',
      label: '产业招商',
    },
  ];
  let newPath =
    zhumadianCode === orgCode ? [...park, ...defaultPath] : defaultPath;
  return newPath;
};
export function getCityJson(code, areaType, cityData) {
  const cityUrl = apiUrl + chartApi.chainRelationGeo;
  let params = '';
  if (code == 110100) {
    params = `110000_full`;
  } else if (code == 310100) {
    params = `310000_full`;
  } else if (code == 120100) {
    params = `120000_full`;
  } else if (code == 500100) {
    params = `500000_full`;
  } else {
    // 台湾
    if (code == 710000) {
      params = `${code}`;
      // 东莞
    } else if (code == 441900) {
      params = `${code}`;
    } else if (
      areaType === '1' ||
      areaType === '2' ||
      ['110100'].includes(code)
    ) {
      params = `${code}_full`;
    } else if (areaType === '3' && cityData.length == 3) {
      params = `${code}_full`;
    } else {
      params = `${code}`;
    }
  }
  return interfaceRequest({
    url: cityUrl,
    params: { code: params },
    method: 'GET',
  });
}

// 城市 - json 数据
export function getCityJson2(code, areaType) {
  const cityUrl = apiUrl + chartApi.chainRelationGeo;
  let params = '';
  if (code == 110100) {
    params = `110000_full`;
  } else if (code == 310100) {
    params = `310000_full`;
  } else if (code == 120100) {
    params = `120000_full`;
  } else if (code == 500100) {
    params = `500000_full`;
  } else {
    if (areaType === '1' || areaType === '2') {
      params = `${code}_full`;
    } else {
      params = `${code}`;
    }
  }
  return interfaceRequest({
    url: cityUrl,
    params: { code: params },
    method: 'GET',
  });
}

// https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=650000_full
// export function getCityJson(code,areaType) {
//   let params = ''
//   if(areaType === '1' || areaType === '2'){
//     params = `geojson?code=${code}_full`;
//   }else{
//     params = `geojson?code=${code}`;
//   }
//   const cityUrl = cityJsonUrl+`/areas_v3/bound/${params}`;
//   return requestOther({
//       url:cityUrl,
//       method:'GET',
//   })
// }

export function getList(url, data) {
  let dUrl = apiUrl + url;
  return interfaceRequest({
    url: dUrl,
    method: 'POST',
    data,
  });
}

// 文件下载
export function enterpriseListExportApi(data) {
  let dUrl = apiUrl + chartApi.enterpriseListExport;
  return axios({
    method: 'post',
    url: dUrl,
    headers: {
      'Content-Type': 'application/json',
      token: getToken(),
      //  store.getters.user.token
    },
    data: data, // 参数
    responseType: 'blob' || '',
    // responseType: 'blob' // 表明返回服务器返回的数据类型
  });
}

// 地图 - 产业链节点数据
export function loadByParent(data) {
  return interfaceRequest({
    url: apiUrl + chartApi.loadByParent,
    method: 'GET',
    params: { ...data },
  });
}

export function getListMock(url, data) {
  return interfaceRequest({
    url: apiDomain2 + url,
    method: 'POST',
    data,
  });
}

export function getDetail(url, data) {
  return interfaceRequest({
    url: apiUrl + url,
    method: 'GET',
    params: { ...data },
  });
}

// 城市 - 列表
// export function getCity(data) {
//   return interfaceRequest({
//       url:apiUrl+chartApi.cityList,
//       method:'GET',
//       params:{...data}
//   })
// }

export function getCity(data) {
  return interfaceRequest({
    url: apiUrl + chartApi.cityList,
    method: 'GET',
    params: { ...data },
  });
}

export function exportWay(data) {
  let dUrl = apiUrl + chartApi.download;
  return axios({
    method: 'post',
    url: dUrl,
    headers: {
      'Content-Type': 'application/json',
      token: getToken(),
      // store.getters.user.token
    },
    data: data, // 参数
    responseType: 'blob' || '',
    //responseType: 'blob' // 表明返回服务器返回的数据类型
  });
}
/**
 * 招商舆情
 * @param {*} data
 * @returns
 */
export function EnterpriseListAPI(data) {
  return interfaceRequest({
    url: apiUrl + chartApi.pageBusinessEnterpriseList,
    method: 'POST',
    data,
  });
}
/**
 * 批量更新节点xy坐标值
 * @param {*} data
 * @returns
 */
export function batchUpdateXY(data) {
  return interfaceRequest({
    url: apiUrl + chartApi.batchUpdateXYByNodeIds,
    method: 'POST',
    data,
  });
}
/**
 * 获取挂载企业节点企业总数和本地企业总数
 * @param {*} data
 * @returns
 */
export function getEnterpriseNum(data) {
  return interfaceRequest({
    url: apiUrl + chartApi.queryLeafNodeEnterpriseNum,
    method: 'POST',
    data,
  });
}
/**
 * 根据节点查询关联的子节点和企业数据
 * @param {*} data
 * @returns
 */
export function getAtlasByNode(data) {
  return interfaceRequest({
    url: apiUrl + chartApi.queryAtlasByNode,
    method: 'POST',
    data,
  });
}
export function industryChainNodeAPI(params) {
  return interfaceRequest({
    url: '/admin/industryChainNode/loadSecondNodeByOrgId',
    method: 'GET',
    params,
  });
}
/**
 * 获取当前登录用户所在机构的最小政区划
 * @param {*} params
 * @returns
 */
export function getMinAreaByTokenAPI(params) {
  return interfaceRequest({
    url: '/admin/orgIndustryChainRelation/getMinAreaByOrgIndustryRelationId',
    method: 'GET',
    params,
  });
}

/**
 * 获取机构产业链关联的节点线信息
 * @param {*} params
 * @returns
 */
export function listLineInfo(params) {
  return interfaceRequest({
    url: apiUrl + chartApi.listLineInfo,
    method: 'GET',
    params,
  });
}

/**
 * 保存产业链节点线信息
 * @param {*} data
 * @returns
 */
export function saveLineInfo(data) {
  return interfaceRequest({
    url: apiUrl + chartApi.saveLineInfo,
    method: 'POST',
    data,
  });
}
/**
 * 关注企业
 * @param {*} data
 * @returns
 */
export function attentionEnterpriseAPI(data) {
  return interfaceRequest({
    url: apiUrl + chartApi.attentionEnterprise,
    method: 'POST',
    data,
  });
}
/**
 * 产业链图谱
 * @param {*} data
 * @returns
 */
export function atlasAPI(data) {
  return interfaceRequest({
    url: apiUrl + '/admin/orgIndustryChainRelation/atlas',
    method: 'POST',
    data,
  });
}
/**
 * 获取图谱二级查询条件数据
 * @param {*} params
 * @returns
 */
export function queryAtlasSecondQueryParamAPI(params) {
  return interfaceRequest({
    url: apiUrl + '/admin/orgIndustryChainRelation/queryAtlasSecondQueryParam',
    method: 'GET',
    params,
  });
}

/**
 * 获取图谱二级查询条件数据
 * @param {*} params
 * @returns
 */
export function getTree(data) {
  return interfaceRequest({
    url: apiUrl + '/admin/orgIndustryChainRelation/atlasChainNodeTree',
    method: 'POST',
    data,
  });
}
/**
 * 获取图谱二级查询条件数据
 * @param {*} params
 * @returns
 */
export function getChartsData(data) {
  return interfaceRequest({
    url: apiUrl + '/admin/orgIndustryChainRelation/atlasNodeDetail',
    method: 'POST',
    data,
  });
}

/**
 * 企业详情-查询企业招商策略
 * @returns
 */
export function getByEnterpriseIdAPI_V2(params) {
  return interfaceRequest({
    url: '/admin/enterprise/strategy',
    method: 'get',
    params,
  });
}

/**
 * 强延补推荐情况和新增企业推荐情况统计
 * @returns
 */
export function getModelTypesAPI(params) {
  return interfaceRequest({
    url: '/pg/admin/enterprise/getModelTypes',
    method: 'GET',
    params,
  });
}

/**
 * 任务处理记录
 * @returns
 */
export function getClueDealRecordAPI(data) {
  return interfaceRequest({
    url: '/miniapps/clueDealRecord/enterprise',
    method: 'POST',
    data,
  });
}

/**
 * 任务处理记录-指派路径
 * @returns
 */
export function clueAssignRecordAPI(data) {
  return interfaceRequest({
    url: '/miniapps/clueAssignRecord/page',
    method: 'POST',
    data,
  });
}
