<!-- 产业图谱 -->
<template>
  <MapEcharts 
    slot="screenMain"
    class-name="chart-container" 
    height="100%" 
    width="100%" 
    :sel-num="selNum"
    :search-data="searchData"
    :get-map-relation="getMapRelation"
    @onGetChild="onGetChild"
    @setFetchParams="setFetchParams"
    isCount="1"
  />
  <!-- :search-city="searchCity"  地图触发 -->
  <!-- :search-data="searchData"  头部查询条件触发-->
</template>

<script>
import { getPathId } from '@/utils/utils'

import { chartApi, getList } from './apiUrl'
import MapEcharts from './components/charts-map-count.vue'
export default {
  name: 'MapEchart2',
  components: { MapEcharts},
  props:{
    // 地图将数据返回到 查询条件
    // searchCity:{
    //   type:Object,
    //   default:null
    // },
    searchData:{
      type:Object,
      default:null
    },
    selNum:{
      type:Number,
      default:0
    }
  },
  data(){
    return{
      changeId:null,
      optionsList:[], // 产业链数据
      searchValue:null,
      searchValueData:null,
      dataList:{name:'222'},
      queryId:this.$route.query.id|| getPathId()|| null,
      cityParams:null,
      detailInfoParams:{
        type:'',
        level:"",
        paraimsId:this.$route.query?.id || getPathId() || null,
        childId:null
      },
    }
  },
  
  beforeCreate(){
    this.queryId =this.$route.query.id|| getPathId()||null;
  },
  created(){
  },
  mounted(){
    // this.getMapRelation();
  },
  
  methods:{
    
    formatWay(childNodes){
      let nodeList = []
      childNodes?.forEach((item)=>{
        let children = null;
        if(item?.childNodes){
          children = this.formatWay(item?.childNodes);
        }
        nodeList.push({value:item.id, label:item.nodeName, children:children});
      });
      return nodeList;
    },

    // 点击节点 - 打开详情
    onGetChild(params){
      this.detailInfoParams.type = 'map';
      this.detailInfoParams.childId = params.name;
      this.changeId = params.name;
    },

    // 地图双击 - 查询条件 - 传参 - 省/市/区
    setFetchParams(params){
      this.cityParams = params;
      this.$emit('mapDBCityData',params)
    },

    // 获取地图数据
    getMapRelation(params){
      const { nodeId = null } = params;
      let paramsData = {id:this.queryId,...params, chainNodeId:nodeId || null };
      paramsData.nodeId && delete paramsData.nodeId;
      return new Promise((resolve, reject) => {
        getList(chartApi.mapRelation,paramsData).then((res)=> {
          this.dataList = res;
          resolve(res)
        }).catch((error) =>{
            reject(error);
        }) 
      })     
    },  
    
    // 关闭详情 
    closeDetail(){
      this.detailInfoParams.type = ''
    } 
  }
}
</script>

<style scoped lang="scss">
.chart-container{
  position: relative;
  width: 100%;
  height: calc(100vh - 84px);
}
::v-deep{
  .el-cascader{
    width: calc(100% - 13rem);
    font-size: 14px;
    line-height: 2rem;
    position: absolute;
    left: 3rem;
    height: 2rem;
    margin-top: 0.3rem;
  }
  .el-cascader .el-input .el-input__inner{
    font-size: 0.8rem;
  }
  .el-cascader .el-input .el-icon-arrow-down{
    font-size: 0.8rem;
    height: 2.4rem;
    line-height: 2rem;
  }
  .el-cascader-panel{
    position: absolute;
    background: #09132a;
    top: -16px;
  }
  .el-cascader-node__label{
    color: #fff;
  }
  .el-cascader .el-input{
    width: 23rem;
  }
}
</style>

