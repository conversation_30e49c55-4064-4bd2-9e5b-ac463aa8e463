<template>
  <div class="cardContent">
    <div class="title">
      <div
        v-if="!showdTime"
        style="  margin-top: 6px"
        class="showdetails"
      />
      <div class="info">
        <div>
          <div class="infoList">
            <img
              class="iconImg"
              :src="getIconByType(detailData.enterpriseLabelType)"
            >
            <div
              class="center"
              @click="openDetailSingle(detailData)"
            >
              {{ detailData.enterpriseName }} 
              <!-- </el-popover> -->
            </div>
            
            <div
              v-if="!detailData.isLocal&& !(fromPath&&fromPath==='zhumadian')"
              :class="[detailData.isInvestClue? 'attention2' : 'attention']"
              @click="attention(detailData)"
            >
              <!-- <div >

            </div> -->
           
              <div class="icon2">
                <span>{{ detailData.isInvestClue?'已纳入意向' : '纳入意向' }}  </span>
              </div>
            </div>
            <!-- <div
             v-else
              class="attention"
             isInvestClue
            >
              <span> 纳入意向 </span>
            </div> -->
          </div>
        </div>
      </div>
      <div class="tag">
        <span
          v-for="(tag, index) in tagShow"
          :key="index"
          class="tagName"
        >{{ tag }}
        </span>
        <span
          v-if="moreTag.length > 0"
          class="more"
          @mouseenter="activeStates = detailData.id"
          @mouseleave="activeStates = null"
        >
          更多标签 >>
        </span>
      </div>
      <!-- -->

      <!-- <div v-else class="tag" /> -->
    </div>
    <div
      v-if="showdTime"
      class="showdetails"
    >
      <span>成立日期：</span><span>{{ detailData.registerDate }}</span>
      <el-popover
        v-if="detailData.registeredCapital"
        placement="top-start"
        width="200"
        trigger="hover"
        :disabled="detailData.registeredCapital.length < 9"
        :content="detailData.registeredCapital"
        :open-delay="500"
        popper-class="tag-popover"
      >
        <span slot="reference">
          <span style="margin-left: 2rem">注册资本：</span><span>{{ detailData.registeredCapital }}</span>
        </span>
      </el-popover>
    </div>
 
    <div
      v-if="activeStates === detailData.id && !enterpriseShow"
      class="moreTag"
      :style="{ bottom: showdTime ? '4rem' : '2rem' }"
    >
      <span
        v-for="(tag, index) in moreTag"
        :key="index"
      >#{{ tag }}</span>
      <!-- <div
        v-if="activeState === detailData.id && enterpriseShow"
        class="moreTag"
        :class="{ enterpriseShow: enterpriseShow }"
      >
        {{ detailData.enterpriseName }}
      </div> -->
    </div>
  </div>
</template>
<script>
import { getEnterpriseIconByType } from '@/utils/utils'
export default {
  props: {
    detailData: {
      type: Object,
      default: ()=>{},
    },
    showdTime: {
      type: Boolean,
      default: true,
    },
    enterpriseShow: {
      type: Boolean,
      default: false,
    },
    // activeState: {
    //   type:string ,
    //   default:'',
    // },
    tagShow: {
      type: Array,
      default: () => [],
    },
    moreTag: {
      type: Array,
      default: () => [],
    },
        fromPath: {
      type: String,
      default: '', 
    }
  },
  data() {
    return {
      activeStates: '',
    };
  },
  // watch: {
  //   detailData(newVal) {
  //     console.log('newVal', newVal);
     
  //   },
  // },
  mounted() {
    // console.log('detailData', this.detailData);
  },
  methods: {
    getIconByType (type) {
      return getEnterpriseIconByType(type, 'webp');
    },
    openDetailSingle(data) {
      this.$emit('openDetailSingle', data);
    },
    attention(data) {
     if(data.isInvestClue){
        return
     }
      this.$emit('attention', data);
    },
  },
};
</script>
<style lang="scss" scoped>
::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
//
@import './index.scss';
</style>
