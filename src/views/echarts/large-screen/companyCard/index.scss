@import "@/styles/public.scss";
.cardContent {
  position: relative;
}
.title {
  display: flex;
  flex-direction: column;
}

.logo {
  display: flex;
  margin: auto;
  width: 60px;
  height: 60px;
  border-radius: 4px;
  background: #52abfb;
  border-radius: 4px;
  overflow: hidden;

  span {
    width: 32px;
    height: 36px;
    font-size: 16px;
    color: #ffffff;
    line-height: 20px;
    margin: auto;
    text-align: center;
    font-weight: 400;
  }
}

.info {
  flex: 1;
  // margin-left: 30px;
  height: 17px;
  font-size: 18px;
  line-height: 30px;
  font-weight: 400;
  height: 30px;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  // padding: 0.2rem 0;
  display: flex;
  .infoList {
    width: 100%;
    height: 45px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    .center {
      color: #fff;
      flex: 1;
      font-size: 15px;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: auto 0;
      cursor: pointer;
      line-height: 20px;
    }
  }

  .attention {
    border-radius: 3px;
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;

    background-image: linear-gradient(to right, rgba(0, 82, 248, 0.6), rgba(0, 181, 253, 0.8)),
      radial-gradient(
        circle at 0 50%,
        rgba(0, 126, 250, 1),
        rgba(0, 126, 250, 0.8) 20%,
        rgba(0, 126, 250, 0.5) 45%,

        rgba(255, 255, 255, 1) 100%
      ) !important;
    box-sizing: content-box;
    border-radius: 3px;
    cursor: default;
  }
  .attention2,
  .attention {
    width: 90px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 3px;
    font-size: 16px;
    background-size: cover;
    margin-right: 16px;
    @include YouSheBiaoTi(14px);
  }
  .attention2 {
    color: #fff;
    border-radius: 3px;
    /*     background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    background-image: linear-gradient(to right, rgba(1, 71, 121, 1), rgba(4, 36, 80, 1)),
      radial-gradient(
        circle at 0 50%,
        rgba(0, 126, 250, 1),
        rgba(0, 126, 250, 0.8) 20%,
        rgba(0, 126, 250, 0.5) 45%,

        rgba(255, 255, 255, 1) 100%
      ) !important; */
    box-sizing: content-box;
    background: linear-gradient(270deg, #042450 0%, #014779 100%);
    cursor: default;
  }
}
.enterpriseShow.moreTag {
  font-size: 12px;
  padding: 8px;
  top: 32px;
}
.moreTag {
  cursor: pointer;
  width: 250px;
  max-height: 60px;
  z-index: 2222;
  position: absolute;
  right: 5rem;
  padding: 5px;
  margin-top: 9px;
  overflow-y: scroll;
  border-radius: 4px;
  background-color: rgb(0, 16, 45) !important;
  color: #ffffff !important;
  border: 1px solid #003485 !important;
  span {
    font-size: 12px;
    padding: 0px 5px;
    line-height: 16px;
    color: #ffffffd4;
    background: transparent;
  }
}
.tag {
  line-height: 16px;
  overflow: hidden;
  width: 17rem;
  height: 24px;
  margin-left: 70px;
  //margin-top: -0.4rem;
  // margin-top: -10px;
  text-overflow: ellipsis;
  // display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .more {
    font-size: 12px;
    position: relative;
    padding: 1px 10px;
    cursor: pointer;
    color: rgba(71, 175, 255, 1);
    // background-color: transparent;
  }

  .tagName {
    float: left;
    padding: 1px 10px;
    font-size: 12px;
    background: rgba(71, 175, 255, 0.2);
    border-radius: 2px;
    color: rgba(71, 175, 255, 1);
    line-height: 24px;
    font-family: puhuiti;
    margin-right: 10px;
  }
}

.btn {
  line-height: 20px;

  img {
    height: auto;
  }

  span {
    line-height: 14px;
    font-size: 14px;
    text-align: left;
  }
}

.describe-info {
  margin-left: 30px;

  .describe-list {
    margin-top: 14px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    line-height: 14px;
    color: #ffffffb3;
  }

  .describe {
    width: 19rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .time {
  }
}

.showdetails {
  margin-left: 70px;
  margin-top: 10px;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: rgba(255, 255, 255, 0.8);
  font-family: puhuiti;
}

.iconImg {
  position: relative;
  width: 24px;
  height: 24px;
  margin-right: 18px;
  margin-left: 28px;
}
