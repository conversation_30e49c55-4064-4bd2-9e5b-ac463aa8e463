@import "@/styles/public.scss";

::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px;
  /* 对应横向滚动条的宽度 */
  height: 0px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #324156;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  display: none;
}

::v-deep {
  .el-loading-spinner .path {
    stroke: #3370ff;
  }

  .el-pagination--small .btn-prev,
  .el-pagination--small .btn-next,
  .el-pagination--small .el-pager li,
  .el-pagination--small .el-pager li.btn-quicknext,
  .el-pagination--small .el-pager li.btn-quickprev,
  .el-pagination--small .el-pager li:last-child {
    background-color: transparent;
  }

  .el-pagination .btn-prev .el-icon,
  .el-pagination .btn-next .el-icon {
    font-size: 12px;
    color: #fff;
  }

  .el-pager li.active {
    color: #fff !important;
  }

  .el-pagination {
    background-color: transparent !important;
    position: unset !important;
    text-align: center !important;
  }

  .el-pagination__total {
    color: #fff;
  }

 
}

.ye {
  width: 100%;
  height: 2rem;
  //padding-bottom: 4.5rem;
  margin: 10px 0;
  color: #fff;

  p {
    text-align: center;
  }
}

.btn {
  position: relative;
  cursor: pointer;
  float: left;
  color: #fff;
  font-size: 1rem;
  height: 2.4rem;
  text-align: center;

  img {
    position: absolute;
    z-index: 9;

    top: -2px;
    width: 100%;
    height: 100%;
  }

  span {
    cursor: pointer;
    position: absolute;
    z-index: 99;
    text-align: center;
    line-height: 2.4rem;
    text-align: center;
    // width: 100%;
    // height: 100%;
    left: 0;
    top: 0;
  }
}

.no-font {
  span {
    position: absolute;
    font-size: 23px;
    text-align: center;
    line-height: 200px;
    width: 100%;
    display: inline-block;
  }
}

.no-detail {
  display: flex;
  position: relative;
  min-height: calc(100vh - 38rem);

  .no-font {
    font-size: 2rem;
    height: 15rem;
    width: 100%;
    margin: auto;
    background-image: linear-gradient(to bottom right, #1b53a8, #00122e) !important;
    border-radius: 10px;
    color: #fff;

    span {
      position: absolute;
      font-size: 23px;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

.detail-main1 {
  position: fixed;
  z-index: 999;
  right: 0px;
  width: 25vw;
  height: 100vh;
  top: 4.2rem;
  right: 1rem;
  color: #fff;
  min-width: 460px;
  background: rgba(11, 26, 45, 0.2);

  .detail-sel {
    width: 100%;
    height: calc(100% - 3.5rem);
    overflow-y: hidden;
    // margin-top: 3rem;
    //background-color: red;
  }


  .lists {
    min-height: 200px;
    padding: 0px 0 16px;
  }

  .searchBody {
    height: calc(100% - 60px);
    overflow-y: hidden;
  }
  .detail-sel {
    height: 100%;
  }
}



.tabsContent {
  position: relative;
  z-index: 111;
}

::v-deep {
  .tabss {
    .el-tabs__item {
      color: white;
    }
  }
}
.searchInput {
  background: rgba(5, 26, 55, 0.8);
  width: 22rem !important;
  border: 1px solid;
  border-image: linear-gradient(136deg, rgba(28, 145, 255, 0.2), rgba(0, 255, 240, 0.2), rgba(28, 145, 255, 0.2)) 1 1;
  border-radius: 2px;
}

.detail-list{
  padding: 0 16px;
}
.right-charts{
  height: 100%;
}
::v-deep {
  .el-pagination{
    ul.el-pager {
      li.active{
        background: linear-gradient(180deg, #1CCEFF 0%, rgba(34, 69, 243, 0.88) 100%) !important;
        border-radius: 50% !important;
       }
    }
  }
}
