<!-- eslint-disable vue/no-v-for-template-key -->
<template>
  <div class="screen-main">
    <div class="screen-infos">
      <div class="screen-title">
        <!--  产业全景-->
        <Headers>
          <div slot="view-container">
            <div class="screen-content">
              <slot name="screenMain" />
            </div>
          </div>
        </Headers>
      </div>
    </div>
  </div>
</template>

<script>
import Headers from "@/components/header.vue";
export default {
  components: {
    Headers,
  },
  props: {
    className: {
      type: String,
      default: "chart",
    },
    showscreen: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      pageTypeName: "产业全景",
      color: ["#4bccee", "#d7431c", "#eaac18"],
      chainName: "产业链",
      name: "",
      selTypeList: [
        { value: 4, label: "全国企业", active: true },
        { value: 1, label: "本地企业", active: false },
        // {value:2,label:'招商企业',active:false},
      ],
      localEnterprise: 4,
    };
  },
  computed: {},
  mounted() {},
  beforeDestroy() {},
  methods: {
    // 左下角筛查
    onSelType(type, typeIdx) {
      this.$emit("closeDetail");
      this.localEnterprise = type;
      let typeList = this.selTypeList;
      this.selTypeList.forEach((item, idx) => {
        typeList[idx].active = false;
      });
      typeList[typeIdx].active = true;
      this.selTypeList = typeList;
      this.$emit("onSelType", { selTypeList: type.value });
      this.$refs.cutbtm.active = "";
    },

    onGetSecondQueryParam(secondQueryParam) {
      this.$emit("onSelType", { secondQueryParam: secondQueryParam });
      this.$emit("closeDetail");
    },

    async logout() {
      this.$confirm("确定退出登录吗?", "退出登录", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        center: true,
      }).then(async () => {
        await this.$store.dispatch("user/logout");
        this.$router.push("/login");
        // 清空token
        this.$message({
          type: "success",
          message: "退出登录成功",
          customClass: "admin-tips-message",
          duration: 3000,
          showClose: true,
          center: false, // 是否居中
        });
      });
    },
    // 查询
    OnSearch() {},

    // 查看舆情
    OnCheckOpinion() {},

    // 查看统计
    OnViewStatistics() {},
  },
};
</script>
<style lang="scss" scoped>
.navigation {
  .navigation-info {
    margin-left: 8rem;
    display: flex;
    .el-button {
      background: rgba(136, 200, 255, 0.2);
      height: 2.6rem;
      width: 8rem;
      border: 1px;
      font-size: 1rem;
      margin-left: 4rem;
      color: #fff;
      border-radius: 8px;
      position: relative;
      span {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
    .highlight {
      background: url(~@/assets/screen/tabBtn.jpg);
      background-size: cover;
    }
    :hover.el-button {
      background: url(~@/assets/screen/tabBtn.jpg);
      background-size: cover;
    }
  }
}
// 标题
.left-headline {
  .node-name {
    z-index: 99;
    font-weight: 600;
    margin-left: 7vw;
    top: 0px;
    left: 0px;
    .name-info {
      font-size: 1.6rem;
      color: #fff;
      line-height: 2.5rem;
      float: left;
      min-width: 10rem;
      margin-right: 2rem;
      .suffix {
        position: relative;
        font-size: 0.9rem;
        font-style: normal;
        margin-left: 1rem;
      }
      .suffix::after {
        position: absolute;
        content: "";
        background: #fff;
        height: 0.2rem;
        width: 0.2rem;
        top: 50%;
        left: -10px;
      }
    }
    .navigation {
      float: left;
      height: 2.4rem;
      line-height: 2.4rem;
    }
  }
}

::v-deep {
  .el-popper[x-placement^="bottom"] .popper__arrow {
    display: none;
  }
  .navigation-select {
    .el-input--suffix .el-input__inner {
      background: #0f2ba900;
      border: #468ae79e 1px solid;
      color: #fff;
      font-size: 0.8rem;
      height: 100%;
      height: 2.4rem;
      line-height: 2.4rem;
    }
    .el-select-dropdown__list {
      background-color: #091e76;
    }
    .el-popper[x-placement^="bottom"] {
      border: #3370ff 1px solid;
    }
    .el-input.is-focus {
      border: #468ae7d9 1px solid;
      border-radius: 3px;
    }
    .el-input__icon.el-icon-arrow-up {
      height: 2.4rem;
      line-height: 2.4rem;
    }
    .el-select-dropdown__item {
      color: #fff;
    }
    // 鼠标点击后移入的颜色
    .el-select-dropdown__item.selected {
      color: #fff;
    }
    // 下拉框移入的颜色
    .el-select-dropdown__item:hover {
      background-image: linear-gradient(to bottom right, #2484ef, #09217b);
      color: #fff;
    }
    // 移出不要丢失颜色
    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-image: linear-gradient(to bottom right, #2484ef, #09217b);
      color: #fff;
    }
  }
}

.screen-main {
  background-color: #070d61;
  width: 100vw;
  height: 100vh;
  position: relative;
  min-width: 1024px;
  .btn {
    position: relative;
    cursor: pointer;
    float: left;
    color: #fff;
    font-size: 1rem;
    height: 2.6rem;
    text-align: center;
    width: 6rem;
    img {
      position: absolute;
      z-index: 9;
      left: 0;
      top: -2px;
      width: 100%;
      height: 100%;
    }
    span {
      position: absolute;
      z-index: 99;
      text-align: center;
      line-height: 2.4rem;
      text-align: center;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
    }
  }
  .screen-infos {
    position: absolute;
    width: 100%;
    // height: 100%;
    left: 0px;
    top: 0px;
    z-index: 99;
    background: transparent; 
    position: fixed;
    z-index: 999;
    // background-size: 100% 100%;
    background: 16px 25px/67% 135% no-repeat
        url("~@/assets/attract/title-ar.png"),
      0 0 / cover no-repeat url(https://static.idicc.cn/cdn/pangu/atlasBgc.jpg);
    // background-position: 14px 6px !important;
  }
  .screen-title {
    position: absolute;
    // height: 80px;
    z-index: 99;
    width: 100%;
    position: relative;

    .title-bg-img {
      position: absolute;
      z-index: 9;
      pointer-events: none;
      width: 70%;
      height: 10rem;
    }
    .title-top-info {
      position: absolute;
      width: 100%;
      z-index: 99;
      display: flex;
      justify-content: space-between;
      padding: 0.6rem 0.8rem;
    }
    .el-dropdown-link {
      color: #fff;
      float: right;
      padding: 10px 0;
      margin-left: 1rem;
    }

    // 右侧查询
    .right-search {
      display: flex;
      padding-top: 0.2rem;
      .search-main {
        display: flex;
        .input-icon {
          height: 1.2rem;
          width: auto;
          float: left;
          display: flex;
          margin: auto;
          margin-left: 1.8rem;
        }
        .input-main {
          position: relative;
          // min-width: 33rem;
          height: 3rem;
        }
        .input-bg {
          width: auto;
          height: 2.4rem;
        }
        .input-info {
          position: absolute;
          z-index: 99;
          width: 100%;
          height: 100%;
          display: flex;
          height: 2.6rem;
          border-radius: 8px;
          line-height: 2.6rem;
          min-width: 30vw;
          max-width: 30%;
          top: -0.1rem;
          .input-icon {
          }
          .value-info {
            float: left;
            flex: 1;
          }
        }
        .btn {
          width: 4.8rem;
          height: 3rem;
          img {
            top: 0.01rem;
            height: 2.4rem;
          }
          span {
            position: absolute;
            z-index: 99;
            text-align: center;
            line-height: 2.5rem;
            text-align: center;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            font-size: 0.8rem;
          }
        }
      }
      .check-other {
        display: flex;
        margin-left: 2rem;
      }
      .btn {
        float: left;
      }
    }
    // 左侧2按钮
    .shortcut-btn {
      position: absolute;
      top: 4.2rem;
      i.icon {
        width: 0.4rem;
        height: 0.4rem;
        margin-left: 2rem;
        display: block;
        position: absolute;
        z-index: 99;
        top: 1rem;
        left: 2.7rem;
      }
      .btn {
        float: inherit;
        cursor: pointer;
        position: relative;
        width: 15rem;
        height: 4rem;
        margin-left: -2rem;
        margin-bottom: -0.3rem;
        overflow: hidden;

        img {
          width: 12rem;
          height: 6rem;
          left: 0px;
          top: 0rem;
        }
        span {
          font-size: 0.9rem;
          top: 1.67rem;
          left: -0.6rem;
        }
      }
      .active {
        span {
          color: #fff;
        }
        span::after {
          position: absolute;
          border: none;
          background: #fff;
          left: 3.4rem;
          top: 1rem;
          content: "";
          z-index: 999;
          width: 0.4rem;
          height: 0.4rem;
          border-radius: 50%;
        }
      }

      span::after {
        position: absolute;
        content: "";
        z-index: 9;
        border: none;
        background: #fff;
        width: 0.5rem;
        height: 0.5rem;
        border-radius: 4px;
        left: 1.2rem;
        top: 0.9rem;
      }
    }
  }

  .screen-content {
    width: 100%;
    height: calc(100vh - 60px);
    // margin-top: 60px;
    // position: absolute;
    // top:0px;

    overflow: hidden;
  }
}
::v-deep {
  .el-input__inner {
    background: transparent;
    color: #fff;
    line-height: 2.5rem;
    border: none;
    padding: 0 0.2rem;
    height: 100%;
    font-size: 0.8rem;
  }
}
</style>
