.detail-main {
  height: calc(100vh - 7rem) !important;
  > div {
    height: 100%;
  }
  .detail-sel {
    height: calc(100% - 60px);
  }
  .detail-list {
    background: rgba(0, 24, 53, 0.8);
    height: 100%;
    padding: 0 16px;
    margin-top: 16px;
    overflow: scroll;
    .sel-main{
      margin-bottom: 2rem;
    }
  }

}
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px;
  /* 对应横向滚动条的宽度 */
  height: 0px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #324156;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  display: none;
}

::v-deep {
  .el-pagination--small .btn-prev,
  .el-pagination--small .btn-next,
  .el-pagination--small .el-pager li,
  .el-pagination--small .el-pager li.btn-quicknext,
  .el-pagination--small .el-pager li.btn-quickprev,
  .el-pagination--small .el-pager li:last-child {
    background-color: transparent;
  }

  .el-pagination .btn-prev .el-icon,
  .el-pagination .btn-next .el-icon {
    font-size: 12px;
    color: #fff;
  }

  .el-pager li.active {
    color: #fff !important;
  }

  .el-pagination {
    background-color: transparent !important;
    position: unset !important;
    text-align: center !important;
  }

  .el-pagination__total {
    color: #fff;
  }

  .el-loading-spinner .path {
    stroke: #3370ff;
  }
}

.ye {
  width: 100%;
  height: 2rem;
  margin-top: 0.5rem;
  //padding-bottom: 4.5rem;
  color: #fff;

  p {
    text-align: center;
  }
}

.btn {
  position: relative;
  cursor: pointer;
  float: left;
  color: #fff;
  font-size: 1rem;
  height: 2.4rem;
  text-align: center;

  img {
    position: absolute;
    z-index: 9;
    left: 0;
    top: -2px;
    width: 100%;
    height: 100%;
  }

  span {
    cursor: pointer;
    position: absolute;
    z-index: 99;
    text-align: center;
    line-height: 2.4rem;
    text-align: center;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
}

.no-font {
  span {
    position: absolute;
    font-size: 23px;
    margin-top: 20rem;
    margin-left: 10rem;
  }
}

.no-detail {
  display: flex;
  position: relative;
  min-height: calc(100vh - 38rem);

  .no-font {
    font-size: 2rem;
    height: 15rem;
    width: 100%;
    margin: auto;
    background-image: linear-gradient(to bottom right, #1b53a8, #00122e) !important;
    border-radius: 10px;
    color: #fff;

    span {
      position: absolute;
      font-size: 23px;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
::v-deep {
  .el-pagination{
    ul.el-pager {
      li.active{
        background: linear-gradient(180deg, #1CCEFF 0%, rgba(34, 69, 243, 0.88) 100%) !important;
        border-radius: 50% !important;
       }
    }
  }
}
