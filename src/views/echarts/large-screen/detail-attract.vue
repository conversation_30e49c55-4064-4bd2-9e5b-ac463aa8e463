<!-- 图谱 - 行业选中 -  展示所有企业列表 -->
<template>
  <div>
    <div
      v-if="showFlag == 1"
      class="detail-main"
    >
      <div class="type-name btn">
        <img
          src="https://static.idicc.cn/cdn/pangu/detail-title.png"
          alt=""
        >
        <span>招商舆情</span>
        <div
          class="close"
          @click="closeDetail"
        >
          <i class="el-icon-close" />
        </div>
      </div>
      <div class="detail-sel">
        <!-- 企业列表 -->
        <div
          v-if="detailList.length > 0"
          class="detail-list"
        >
          <div
            v-for="(detail, idx) in detailList"
            :key="idx"
            class="detail"
          >
            <div class="title">
              <div class="logo">
                <span>{{ detail.enterpriseName.slice(0, 4) }}</span>
              </div>
              <div class="info">
                <span
                  class="name"
                  @click="openDetailSingle(detail)"
                >{{
                  detail.enterpriseName
                }}</span>
                <div class="tag">
                  <span
                    v-for="(tag, index) in detail.new"
                    v-show="index < 2"
                    :key="index"
                  >{{
                    tag
                  }}</span>
                  <!--  <el-popover
                    v-if="detail.new &&detail.new.length > 2"
                    placement="bottom-start"
                    width="200"
                    visible-arrow="false"
                    trigger="hover"
                    popper-class="tag-popover"
                  >
                    <div slot="reference">
                      更多标签
                    </div>
                    <span>
                      <span
                        v-for="(tag, index) in detail.new"
                        :key="index"
                      >{{ tag }}
                      </span>
                    </span>
                  </el-popover> -->
                </div>
              </div>
              <!--               <div
                v-if="detail.industrySector"
                class="btn"
              >
                <img src="https://static.idicc.cn/cdn/pangu/detail-btn.png">
                <span>{{ detail.industrySector }}</span>
              </div> -->
            </div>
            <!-- 企业舆情 -->
            <div
              v-if="detail.enterpriseNews && detail.enterpriseNews.length >= 1"
              class="describe-info"
            >
              <a
                v-for="(news, idx2) in detail.enterpriseNews"
                :key="idx2"
                class="describe-list"
                :href="
                  news.newsUrl || 'https://www.baidu.com/?tn=02003390_19_hao_pg'
                "
                target="_blank"
              >
                <span class="describe">{{ news.newsSummary }}</span>
                <span class="time">{{ news.publicDate }}</span>
              </a>
            </div>
          </div>
          <div class="ye">
            <el-pagination
              small
              :current-page.sync="form.pageNum"
              :page-size.sync="form.pageSize"
              :total="+total"
              layout="total, prev, pager, next"
              @size-change="getEnterpriseList"
              @current-change="getEnterpriseList"
            />
          </div>
        </div>
        <div
          v-else
          class="no-detail"
        >
          <div class="no-font">
            <span>暂无数据</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 企业详情 -->
    <AtlasDetailCompany
      v-if="showFlag == 2"
      :child-id="detailSingleParams.childId"
      @closeDetail="closeDetailSingle"
    />
  </div>
</template>
  
  <script>
import {
  chartApi,
  getList,
  enterpriseListExportApi,
  EnterpriseListAPI,
} from "../apiUrl";
import { getPathId } from '@/utils/utils'

import AtlasDetailCompany from "./companyDetail.vue"; // 图谱 - 企业详情
export default {
  name: "AtlasDetail",
  components: {
    AtlasDetailCompany,
  },
  props: {
    // 节点id
    childId: {
      type: String,
      default: null,
    },
    // 节点参数
    detailInfoParams: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {
        titleName: "",
        type: "graph",
        level: 1,
        paraimsId: null,
        childId: null,
      },
    },
  },
  data() {
    return {
      loading: false,
      showFlag: 1, // 1 打开企业列表  2 打开详情
      detailSingleParams: {
        type: "graph",
        level: 3,
        paraimsId: null,
        childId: null,
        titleName: "",
      },
      seek: true,
      total: "",
      selValue: "",
      detailTypeList: [
        { value: 0, label: "全国", active: true },
        { value: 1, label: "本地", active: false },
        // {value:2,label:'招商',active:false},
      ],
      detailTypeValue: 0,
      form: {
        pageSize: 10,
        pageNum: 1,
      },
      export: "",
      detailList: [],
    };
  },

  watch: {},
  mounted() {
    this.getEnterpriseList();
  },
  beforeDestroy() {},
  methods: {
    // 获取 - 所有招商列表
    getEnterpriseList() {
      const id = this.$route.query?.id || getPathId()|| null;
      if (!id) {
        return;
      }
      let data = {
        pageSize: this.form.pageSize, //integer 页码
        pageNum: this.form.pageNum, //integer 当前页
        id, //integer 机构产业链关系id
      };
      return new Promise((resolve, reject) => {
        // 换成招商舆情接口即可
        EnterpriseListAPI(data)
          .then((res) => {
            this.detailList = res.records;
            this.total = res.total;
            for (let i = 0; i < this.detailList.length - 1; i++) {
              if (this.detailList[i].enterpriseLabelNames) {
                this.detailList[i].new =
                  this.detailList[i].enterpriseLabelNames.split(",");
              } else {
                this.detailList[i].new = null;
              }
            }
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          })
          .finally(() => {});
      });
    },
    // 打开 - 企业详情
    openDetailSingle(params) {
      this.showFlag = 2;
      this.detailSingleParams.childId = params.id;
      this.detailSingleParams.titleName = params.enterpriseName;
    },

    // 关闭 - 企业详情
    closeDetailSingle() {
      this.showFlag = 1;
    },

    // 关闭 - 企业列表
    closeDetail() {
      this.$emit("closeDetail");
    },
  },
};
</script>
  <style lang="scss" scoped>
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px;
  /* 对应横向滚动条的宽度 */
  height: 0px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #324156;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  display: none;
}
/* ::v-deep{
    .el-pager{
      background-image: linear-gradient(to bottom right, #1b53a8, #00122e);
    }
    .el-pager li.active{
      background-image: linear-gradient(to bottom right, #1b53a8, #00122e);
      color: #fff !important;
    } 
    .el-pagination{
    background-image: linear-gradient(to bottom right, #1b53a8, #00122e) !important;
    position: unset !important;
    text-align: center !important;
    opacity: 0.97 !important;
    border-radius: 10px !important; 
  }
  .el-pagination__total{
    color: #fff;
  }
   .el-pagination button:disabled{
    background-image: linear-gradient(to bottom right, #1b53a8, #00122e);
  }
    .el-icon-arrow-left:before{
      background-image: linear-gradient(to bottom right, #1b53a8, #00122e);
    } 
  } */
::v-deep {
  .el-pagination--small .btn-prev,
  .el-pagination--small .btn-next,
  .el-pagination--small .el-pager li,
  .el-pagination--small .el-pager li.btn-quicknext,
  .el-pagination--small .el-pager li.btn-quickprev,
  .el-pagination--small .el-pager li:last-child {
    background-color: #0d3169;
  }
  .el-pagination .btn-prev .el-icon,
  .el-pagination .btn-next .el-icon {
    font-size: 12px;
    color: #fff;
  }
  .el-pager li.active {
    color: #fff !important;
  }
  .el-pagination {
    background-image: linear-gradient(
      to bottom right,
      #1b53a8,
      #00122e
    ) !important;
    position: unset !important;
    text-align: center !important;
  }
  .el-pagination__total {
    color: #fff;
  }
}

.ye {
  width: 100%;
  height: 2rem;
  padding-bottom: 4.5rem;
  color: #fff;
  p {
    text-align: center;
  }
}
.btn {
  position: relative;
  cursor: pointer;
  float: left;
  color: #fff;
  font-size: 1rem;
  height: 2.4rem;
  text-align: center;
  img {
    position: absolute;
    z-index: 9;
    left: 0;
    top: -2px;
    width: 100%;
    height: 100%;
  }
  span {
    cursor: pointer;
    position: absolute;
    z-index: 99;
    text-align: center;
    line-height: 2.4rem;
    text-align: center;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
}
.no-detail {
  display: flex;
  position: relative;
  min-height: calc(100vh - 38rem);
  .no-font {
    font-size: 2rem;
    height: 15rem;
    width: 100%;
    margin: auto;
    background-image: linear-gradient(
      to bottom right,
      #1b53a8,
      #00122e
    ) !important;
    border-radius: 10px;
    color: #fff;
    span {
      position: absolute;
      font-size: 23px;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
.detail-main {
  position: fixed;
  top: 1px;
z-index: 2147483648;
  right: 0px;
  width: 39rem;
  height: calc(100vh - 5rem);
  top: 5rem;
  color: #fff;
  min-width: 460px;
  background-image: linear-gradient(
    to right,
    #00112d00 1%,
    #00112da8 20%,
    #00112da8 99%
  );
  // padding-left: 4rem;
  padding-right: 1rem;

  .type-name {
    width: 100%;
    height: 1rem;
    .el-icon-close {
      position: absolute;
      right: 0rem;
      top: 0.8rem;
      z-index: 999;
      font-size: 1rem;
    }
    img {
      position: absolute;
      z-index: 9;
      left: 0;
      top: 1.2rem;
      width: 100%;
      height: 100%;
    }
    span {
      text-align: left;
      margin-left: 1.8rem;
    }
    span::after {
      position: absolute;
      border: none;
      background: #fff;
      left: -1.1rem;
      top: 1rem;
      content: "";
      z-index: 999;
      width: 0.4rem;
      height: 0.4rem;
      border-radius: 50%;
    }
  }
  .detail-sel {
    height: 3rem;
    margin-top: 3rem;
  }
  .sel-main {
    display: flex;
    .tab-main {
      overflow: hidden;
      min-width: 8rem;
      .tab {
        float: left;
        width: 2.4rem;
        text-align: center;
        height: 1.8rem;
        font-size: 0.85rem;
        line-height: 1.6rem;
        margin-right: 0.12rem;
        border-radius: 4px 4px 0px 0px;
        border-bottom: #00fff094 2px solid;
        overflow: hidden;
      }
      .btn.tab.active {
        border-color: #00fff0;
        span {
          color: #fff;
        }
      }
      .btn.tab {
        img {
          top: 0px;
          height: 2.1rem;
        }
        span {
          font-size: 0.8rem;
          line-height: 2.1rem;
          color: #ffffffc4;
        }
      }
    }
    .input-sel {
      margin-left: 0.1rem;
      min-width: 12rem;
      display: flex;
      .input-info {
        width: 11rem;
        height: 2.2rem;
        .btn {
          font-size: 0.8rem;
        }
      }
      .sel-btn {
        cursor: pointer;
        background-image: radial-gradient(#c6c9df1f, #0659a7);
        width: 3.6rem;
        height: 1.8rem;
        font-size: 0.8rem;
        text-align: center;
        line-height: 1.89rem;
        border-radius: 10rem;
        margin-top: 0.1rem;
      }
    }
    .import-btn {
      cursor: pointer;
      width: 3.6rem;
      height: 1.8rem;
      line-height: 1.63rem;
      border-radius: 10rem;
      border: 1.8px solid #3370FF;
      color: #fff;
      font-size: 0.8rem;
      text-align: center;
      margin-left: 0.3rem;
      margin-top: 0.1rem;
    }
  }
  .detail-list {
    max-height: calc(100vh - 11rem);
    overflow: scroll;
    .title {
      display: flex;
    }
    .detail {
      background-image: linear-gradient(to bottom right, #1b53a8, #00122e);
      opacity: 0.97;
      border-radius: 10px;
      padding: 16px;
      margin: 10px 0;
      .logo {
        display: flex;
        margin: auto;
        width: 60px;
        height: 60px;
        border-radius: 4px;
        background: #52abfb;
        border-radius: 4px;
        overflow: hidden;
        span {
          width: 32px;
          height: 36px;
          font-size: 16px;
          color: #ffffff;
          line-height: 20px;
          margin: auto;
          text-align: center;
          font-weight: 400;
        }
      }
      .info {
        flex: 1;
        margin-left: 10px;
        height: 17px;
        font-size: 18px;
        line-height: 30px;
        font-weight: 400;
        height: 60px;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        padding: 0.2rem 0;
        display: flex;
        .name {
          font-size: 16px;
          font-weight: 400;
          margin: auto 0;
          cursor: pointer;
          line-height: 20px;
        }
        .tag {
          line-height: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;

          span {
            float: left;
            height: 20px;
            font-size: 12px;
            background: #52abfb3d;
            padding: 5px 9px;
            margin: 2px;
            margin-right: 4px;
            border-radius: 2px;
            color: #ffffffd4;
            line-height: 10px;
          }
        }
      }
      .btn {
        line-height: 20px;
        img {
          height: auto;
        }
        span {
          line-height: 14px;
          font-size: 14px;
          text-align: left;
        }
      }
      .describe-info {
        margin-top: 20px;
        .describe-list {
          margin-top: 14px;
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          line-height: 14px;
          color: #ffffffb3;
        }
        .describe {
          width: 22rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .time {
        }
      }
    }
  }
}
::v-deep {
  .input-info .el-input {
    font-size: 1rem;
    line-height: 2.4rem;
    margin-left: 0.9rem;
    .el-input__inner {
      background: transparent;
      color: #fff;
      line-height: 2.5rem;
      border: none;
      padding: 0 0.2rem;
      height: 100%;
      font-size: 0.8rem;
      width: 8rem;
      margin-left: 0rem;
      float: left;
      margin-top: 0.5rem;
    }
  }
  .el-input__inner {
    background: transparent;
    color: #fff;
    line-height: 2.5rem;
    border: none;
    padding: 0 0.2rem;
    height: 100%;
    font-size: 1rem;
  }
}
</style>
  