@import "@/styles/public.scss";

.detailpage {
  padding-top: 10px;
  height: calc(100% - 132px) !important;
}

::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px;
  /* 对应横向滚动条的宽度 */
  height: 0px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #324156;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  display: none;
}

.texte {
  line-height: 27px;
  font-size: 14px;
}

.ovo {
  width: 40%;
  white-space: nowrap;
  display: block;
  text-overflow: ellipsis;
}

::v-deep {
  .el-loading-spinner .path {
    stroke: #3370ff;
  }

  .el-pagination--small .btn-prev,
  .el-pagination--small .btn-next,
  .el-pagination--small .el-pager li,
  .el-pagination--small .el-pager li.btn-quicknext,
  .el-pagination--small .el-pager li.btn-quickprev,
  .el-pagination--small .el-pager li:last-child {
    background-color: transparent;
  }

  .el-pagination .btn-prev .el-icon,
  .el-pagination .btn-next .el-icon {
    font-size: 12px;
    color: #fff;
  }

  .el-pager li.active {
    color: #fff !important;
  }

  .el-pagination {
    background-color: transparent !important;
    position: unset !important;
    text-align: center !important;
  }

  .el-pagination__total {
    color: #fff;
  }

  .input-info .el-input .el-input__inner {
    margin-top: 0.4rem !important;
  }
}

.nodata {
  p {
    font-size: 16px;
    text-align: center;
    margin-top: 4rem;
  }
}

.el-row {
  display: flex;
  margin-bottom: 5px;

  &:last-child {
    margin-bottom: 0;
  }
}

.el-col {
  display: flex;
  min-height: 3.5rem;
  padding-right: 0px !important;
  padding-left: 15px !important;

  .name {
    width: 100%;
    min-height: 30px;
    display: flex;
    flex-direction: column;
    .title {
      color: rgba(107, 121, 137, 1) !important;
      font-size: 14px;
      margin-left: 1.5rem;
      margin-right: 0.2rem;
      max-width: 11.5rem; // 设置容器最大宽度
      white-space: nowrap; // 设置段落文本不换行(不换行才有可能行溢出)；
      overflow: hidden; // 关闭滚动条，超出部分隐藏；
      text-overflow: ellipsis; // 超出部分添加省略号

      &::before {
        content: "";
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: #057ca0;
        border-radius: 50%;
        margin-right: 5px;
        margin-top: 4px;
      }
    }

    .span2 {
      color: #ccd2d8 !important;
      line-height: 22px;
      margin-left: 1.5rem;
      margin-right: 0.2rem;
      margin-top: 8px;
      font-size: 14px;
    }
  }
 

  .content {
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 37, 93, 0.1);
    //border: 1px solid #FFFFFF;
    border-radius: 0px 4px 4px 0px;
    width: 60%;
    border: #ffffff14 1px solid;
    border-left: none;
    box-sizing: content-box;

 
    span {
      color: #fff;
      padding: 5px 10px;
   
    }

 
  }

 
}

.Summary {
  width: 100%;
  //height: 2rem;
  padding-bottom: 20px;
}

.newsSummary {
  display: flex;
  margin-top: 5px;
  font-size: 14px;
  color: #fff;
  flex-wrap: wrap;
  padding: 15px 10px;
  background: rgba(216, 216, 216, 0.07);
  border-radius: 4px;
  .ttle {
    color: #fff;
    font-weight: 500;
    margin-bottom: 10px;
    &::before {
      content: "";
      display: inline-block;
      width: 6px;
      height: 6px;
      background-color: rgba(0, 255, 240, 1);
      border-radius: 50%;
      margin-right: 5px;
      margin-bottom: 2px;
    }
  }
  .ttle,
  .times,
  .content {
    width: 100%;
    font-size: 14px;
    line-height: 25px;
  }
  .times,
  .content {
    margin-left: 10px;
  }
  .times {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
  }
}

.btn {
  position: relative;
  cursor: pointer;
  float: left;
  color: #fff;
  font-size: 1rem;
  height: 2.6rem;
  text-align: center;
  width: 6rem;

  img {
    position: absolute;
    z-index: 9;
    left: 0;
    top: 6px;
    width: 100%;
    height: 100%;
  }

  span {
    position: absolute;
    z-index: 99;
    text-align: center;
    line-height: 2.4rem;
    text-align: center;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
}

.detail-main {
 
  position: fixed;
  top: 1px;
z-index: 2147483648;
  right: 0px;
  width: 39rem;
  height: calc(90vh) !important;
  top: 3rem !important;
  color: #fff;
  min-width: 460px;
  background-image: -webkit-gradient(
    linear,
    left top,
    right top,
    color-stop(1%, #00112d00),
    color-stop(20%, #00112da8),
    color-stop(99%, #00112da8)
  );
  background-image: linear-gradient(
    to right,
    #00112d00 1%,
    #00112da8 20%,
    #00112da8 99%
  );
 
  padding-right: 1rem;
  margin-top: 32px;
  .detail-sel {
    height: 100%;
  }
  .detail-list {
    max-height: calc(100vh);
    height: 100% !important;
    overflow: scroll;
 

    .title {
      display: flex;
    }
    .detail3.detail2 {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;
      overflow: hidden;
    }
    .detail2 {
      padding: 16px;
      height: calc(100% - 60px);
      flex: 1;
      overflow: auto;
      .addfollow {
        height: 35px;
        width: 100%;
        .total {
          font-size: 14px;
          font-family: puhuiti;
          color: #9aa1aa;
        }
        .int {
          font-size: 14px;
          font-family: puhuiti;
          color: #16d0ff;
          margin: 0 2px;
          font-weight: bold;
        }
        // 新增记录按钮
        .smallBtn {
          background: #1c91ff;
          color: #fff;
          border: 0px solid #0b3c79;
          float: right;
          font-size: 14px;
        }
      }

      .followList {
        flex: 1;
        position: relative;
        width: 100%;
        height: calc(100% - 100px);
        > div {
          height: 100%;
        }

        .followList-el {
          margin-top: 10px;
          background: rgba(216, 216, 216, 0.07);
          font-size: 14px;
          height: 31%;
          border-radius: 6px;
          padding: 10px 16px;
 
          .circle {
            width: 6px;
            height: 6px;
            background-color: #24ffba;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
          }

          .nocircle {
            width: 6px;
            height: 6px;
            background-color: #b9c4c0;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
          }
          .nocircleText {
            background: rgba(216, 216, 216, 0.1);
            padding: 2px 10px;
            color: #b9c4c0;
            border-radius: 15px;
          }
          .circleText {
            background: rgba(216, 216, 216, 0.1);
            padding: 2px 13px;
            border-radius: 15px;
            color: #24ffba;
          }
        }
        .followList-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          padding-bottom: 10px;
          word-break: break-all;
        }
      }
      .ye {
        width: 100%;
        height: 30px;
      }
    }

    .companyCard {
 
      background: rgba(5, 45, 77, 0.73);
      border: 0.8px solid #00a5fe;
      overflow: scroll;
      border-radius: 3px;
      opacity: 0.98;
      padding: 0.8rem;
      position: relative;
      z-index: 11111;
      height: 120px;
      .close {
 
    position: absolute;
    right: 5px;
    top: 7px;
    z-index: 111;
    font-size: 16px;
    display: flex;
    justify-content: flex-end;
      }

 
      .logo {
        display: flex;
        margin: auto;
        width: 60px;
        height: 60px;
        border-radius: 4px;
        background: #52abfb;
        border-radius: 4px;
        overflow: hidden;

        span {
          width: 32px;
          height: 36px;
          font-size: 16px;
          color: #ffffff;
          line-height: 20px;
          margin: auto;
          text-align: center;
          font-weight: 400;
        }
      }

      .info {
        flex: 1;
        margin-left: 10px;
        height: 17px;
        font-size: 18px;
        line-height: 30px;
        font-weight: 400;
        height: 60px;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        padding: 0.2rem 0;
        display: flex;

        .attention {

          width: 80px;
          height: 30px;
          border: 1px solid #3695ff;
          border-radius: 4px;
          opacity: 0.8;
          background-size: cover;
          margin-left: 20rem;
          position: absolute;
          margin-top: 0.7rem;
          color: #fff;
          font-size: 12px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;

          .icon {
            cursor: pointer;
            display: flex;
            align-items: center;
          }
        }

        .attention2 {

          width: 80px;
          height: 30px;

          background-color: #0c2753;
          border-radius: 4px;
          background-size: cover;
          margin-left: 20rem;
          position: absolute;
          margin-top: 0.7rem;
          color: #fff;
          font-size: 12px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;

          .icon {
            cursor: pointer;
            display: flex;
            align-items: center;
          }
        }

        .name {
          font-size: 16px;
          font-weight: 400;
          white-space: nowrap;
          width: 17rem;
          overflow: hidden;
          text-overflow: ellipsis;
          margin: auto 0;
          cursor: pointer;
          line-height: 20px;
        }

        .tag {
          line-height: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;

          .more {
            background-color: transparent;
          }

          span {
            float: left;
            height: 20px;
            font-size: 12px;
            background: #52abfb3d;
            padding: 5px 9px;
            margin: 2px;
            margin-right: 4px;
            border-radius: 2px;
            color: #ffffffd4;
            line-height: 10px;
          }
        }
      }

      .btn {
        line-height: 20px;

        img {
          height: auto;
        }

        span {
          line-height: 14px;
          font-size: 14px;
          text-align: left;
        }
      }

      .describe-info {
        display: flex;
        margin-top: 20px;

        .describe-list {
          margin-top: 14px;
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          line-height: 14px;
          color: #ffffffb3;
        }
      }
    }
  }

 
  .business-list,
  .describe-info {
    width: 100%;
    display: flex;
  }

  .describe-info {
    flex-wrap: wrap;
  }

  .business-list {
    width: calc(50% - 0.5rem);
    margin-right: 0.5rem;
    margin-bottom: 0.4rem;

    .business {
      width: 100%;
      display: flex;
      justify-content: space-between;
      font-size: 0.8rem;
      line-height: 1rem;

      .name {
   
        width: 5.3rem;
        background: #3370ff;
        padding: 0.4rem 0.6rem;
        border-radius: 0.2rem 0 0 0.2rem;
        text-align: center;
      }

      .value {
        flex: 1;
        border: 0.09rem solid #ffffff0d;
        padding: 0.4rem 0.6rem;
        border-radius: rem 0.2rem 0.2rem 0;
        text-align: center;
      }
    }
  }
}

::v-deep {
  .input-info .el-input {
    font-size: 1rem;
    line-height: 2.4rem;
    margin-left: 1.2rem;
  }

  .el-input__inner {
    background: transparent;
    color: #fff;
    line-height: 2.5rem;
    border: none;
    padding: 0 0.2rem;
    height: 100%;
    font-size: 1rem;
  }
}

.enterpriseShow.moreTag {
  font-size: 12px;
  padding: 8px;
  margin-top: 15px;
  top: 11%;
}

.moreTag {
  font-size: 12px;
  cursor: pointer;
  width: 250px;
 
  z-index: 99222;
  position: fixed;
  top: 14%;
  right: 4%;
  padding: 5px;
  margin-top: 15px;
  overflow-y: scroll;
  border-radius: 4px;
  background-color: rgb(0, 16, 45) !important;
  color: #ffffff !important;
  border: 1px solid #003485 !important;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;

  .text {
    font-size: 12px;
    padding: 3px 5px;
    line-height: 13px;
    height: 18px;
    color: #ffffffd4;
 
    background: transparent;
  }
}

.center {
  font-size: 16px;
  font-weight: 400;
  white-space: nowrap;
  width: 17rem;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: auto 0;
  cursor: pointer;
  line-height: 20px;
}
.strategy,
.descriptions {
  width: 100%;
  word-break: break-all;
}
.descriptions {
  .value {
    font-weight: 600;
  }
  .des_span {
    font-size: 14px;
    line-height: 20px;
  }
}
.btn {
  width: 50%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;

  .leftBtn {
    width: 85px;
    height: 25px;
    background: url("https://static.idicc.cn/cdn/pangu/assets/screen/new/btn1.png") center/contain no-repeat;
    background-size: 100% 100%;
    border-radius: 4px;
    margin-right: 10px;
    color: rgba(191, 200, 215, 0.8);
    line-height: 25px;
    text-align: center;
    font-size: 0.75rem;
    cursor: pointer;

    &.active {
      background: url("https://static.idicc.cn/cdn/pangu/assets/screen/new/btn2.png") center/contain no-repeat;
      background-size: 102% 108%;
      color: #ffffff;
    }
  }
}

.detail4-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  .detail4-card {
    &:first-child {
      margin-right: 2%;
    }
    width: 46%;
    height: 93px;
 
    border-radius: 10px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
 
  }
}
.detail4-list {
  margin-top: 16px;
  border-radius: 10px;
  .list-title {
    font-size: 16px;
    font-weight: 500;
    // padding: 10px;
  }
  .describe {
    padding: 10px 10px 0px 10px;
    &::before {
      content: "";
      display: inline-block;
      width: 7px;
      height: 7px;
      background: #aadcff;
      box-shadow: 0px 0px 14px 8px rgba(45, 125, 219, 0.3);

      margin-right: 12px;
      margin-bottom: 2px;
    }
  }
  .describeItems {
  
    .describeTag {
      font-size: 14px;
      padding: 5px 15px 5px 27px;
      line-height: 36px;
      background: rgba(216, 216, 216, 0.16);
      border-radius: 20px;
      position: relative;

      &::before {
        content: "";
        display: block;
        width: 6px;
        height: 6px;
        position: absolute;
        border-radius: 6px;
        left: 15px;
        top: 40%;
        background: linear-gradient(
            265deg,
            rgba(255, 255, 255, 0.39) 10%,
            rgba(0, 0, 0, 0.39) 90%
          ),
          #1ea2ff;
      }
    }
    .describeTagName {
      margin-top: 10px;
      width: 100%;
      font-size: 14px;
      color: #ffffff;
      line-height: 24px;
    }
  }
}
.card-title {
  margin: 16px 0 8px;
  font-size: 16px;
  color: #ffffff;
  font-weight: 500;
}
.introduce.detail4-peopleList {
  .list-items {
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
  }
}
.detail4-peopleList {
  .list-items {
    padding: 10px 10px;
    background: rgba(216, 216, 216, 0.08);
    border-radius: 6px;
    margin-bottom: 10px;
  }
  .list-items-name {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .icon {
      width: 50px;
      height: 34px;
      border-radius: 50%;
      margin-right: 5px;
      margin-top: 13px;
      background: center / contain no-repeat
        url("https://static.idicc.cn/cdn/pangu/assets/screen/new/member.svg");
    }
    .name {
      max-width: calc(100% - 100px);
      font-size: 16px;
      color: #ffffff;
      display: flex;
      align-items: center;
      margin-right: 10px;
    }
  }
  .positions {
    background: rgba(216, 216, 216, 0.16);
    border-radius: 2px 2px 2px 2px;
    padding: 5px 10px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
  }

  .items-detail {
    padding-left: 55px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;

    > div {
      margin-top: 5px;
    }
    span {
      color: #ffffff;
    }
  }
  .list-items.line-height {
    font-size: 14px;
    line-height: 25px;
    // 首行缩进
    text-indent: 2em;
  }
}

.detail-sel-en {
  width: 100%;
}

::v-deep.el-timeline {
  padding-left: 1px;
  .el-timeline-item {
    padding-bottom: 8px !important;
  }
  .el-timeline-item__tail {
    left: 4px;
    border-left: 4px solid #103974;
  }
}
.dot {
  display: flex;
  .dot-raido {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 12px;
    height: 12px;
    background: #bedaff;
    border-radius: 50%;
    .dit {
      width: 6px;
      height: 6px;
      background: #4086ff;
      border-radius: 50%;
    }
  }
}
.dot-box {
  .timeView {
    display: flex;
    align-items: center;
    .time {
      color: #a8abb2;
      font-size: 14px;
      line-height: 22px;
    }
    .stateDot {
      margin-left: 30px;
      width: 5px;
      height: 5px;
      background: #74ff99;
      border-radius: 50%;
      margin-right: 5px;
    }
    .state {
      font-size: 14px;
      color: #74ff99;
      font-weight: 350;
    }
    .stateDot0 {
      background: #ff1f34 !important;
    }
    .stateDot1 {
      background: #3370ff !important;
    }
    .stateDot2 {
      background: #74ff99 !important;
    }
    .state0 {
      color: #ff1f34 !important;
    }
    .state1 {
      color: #3370ff !important;
    }
    .state2 {
      color: #74ff99 !important;
    }
  }
}

.card {
  background: rgba(216, 216, 216, 0.08);
  border-radius: 6px;
  padding: 8px 12px;
  box-sizing: border-box;
  .cardTop {
    display: flex;
    justify-content: space-between;
  }
  .span1 {
    font-weight: 600;
    font-size: 14px;
    color: #f1f1f1;
    line-height: 25px;
  }
  .span2 {
    font-size: 14px;
    color: #a8abb2;
    line-height: 25px;
  }
  .spanBtn {
    line-height: 25px;
    font-size: 14px;
    color: #1c91ff;
    margin-left: 4px;
    cursor: pointer;
  }
}

.const {
  height: 300px;
  display: block;
  overflow-y: scroll;
  .contents {
    position: relative;
    height: 80px;
    border-radius: 10px;
    margin-left: 42px;
    margin-top: 10px;
    padding: 0px 10px;
    font-size: 28px;
    color: #a8abb2;

    .titletext {
      display: flex;
      padding-top: 8px;
      font-size: 15px;
    }

    .assignor {
      display: flex;
      margin-top: 20px;
      font-size: 13px;

      .time {
        width: 50%;
      }
    }
  }
}
.contents::before {
  background-color: #3e80ff;
  content: "";
  position: absolute;
  top: 6px;
  left: -20px;
  border: 4px solid #bedaff;
  width: 16px;
  height: 16px;
  z-index: 6;
  border-radius: 50%;
}

.contents:not(:last-child)::after {
  background-color: #f0f0f0;
  content: "";
  position: absolute;
  top: 19px;
  z-index: 5;
  left: -13px;
  width: 2px;
  height: 80px;
}
::v-deep {
  .el-pagination{
    ul.el-pager {
      li.active{
        background: linear-gradient(180deg, #1CCEFF 0%, rgba(34, 69, 243, 0.88) 100%) !important;
        border-radius: 50% !important;
       }
    }
  }
}

.popover_tip_content {
  height: 23.5rem !important;
}
::v-deep .custom-tabs {
  .el-tabs__nav {
    border: none;
  }

  .el-tabs__nav-wrap {
    &::after {
      display: none; // 移除默认底部边框
    }

    // 为左右箭头添加样式
    .el-tabs__nav-prev,
    .el-tabs__nav-next {
      line-height: 40px;
      color: #fafafa;
      font-size: 18px;
    }
  }
  .el-tabs__active-bar {
    width: 30px;
    background: linear-gradient(180deg, #09e0f3 0%, #09a1f3 100%);
    transform: translateX(35%);
  }
  .el-tabs__item {
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
    font-size: 16px;
    color: #fafafa;
    border: none;
    background-color: transparent;
    position: relative;
    transition: color 0.3s;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      left: 100%;
      top: 13px;
      width: 1px;
      height: 16px;
      background: rgba(216, 216, 216, 0.36);
    }

    // 当选中时的样式
    &.is-active {
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      font-weight: 500;
      background-image: linear-gradient(to bottom, #09e0f3, #09a1f3);
      border-bottom: 0px;
    }

    &:hover:not(.is-active) {
      color: rgba(250, 250, 250, 0.8);
    }
  }
}