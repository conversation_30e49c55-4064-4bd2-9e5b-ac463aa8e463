<!-- 图谱 - 行业选中 -  展示所有企业列表 -->
<template>
  <div>
    <div
      v-if="showFlag == 1"
      class="detail-main1"
    >
      <div class="type-name">
        <span>{{ detailInfoParams.titleName || '-' }} ({{
          detailInfoParams.current === 'national'
            ? detailInfoParams?.node?.nodeHoverData?.countryCount || 0
            : total || 0
        }})</span>

        <div
          class="close"
          @click="closeDetail"
        >
          <i class="el-icon-close" />
        </div>
      </div>

      <div
        class="detailpage"
        :class="{ maxHeight: currentTab === 1 }"
      >
        <!-- tab切换 -->
        <div
          v-if="detailInfoParams.current === 'national'"
          class="tabs"
        >
          <div
            v-for="(item, index) in activeName"
            :key="index"
            :class="currentTab == item.id ? 'opttab' : 'tab'"
            @click="cutTabs(item)"
          >
            {{ item.name
            }}{{
              item.id === 2
                ? `(${detailInfoParams.node.nodeHoverData.countryCount || 0})`
                : item.id === 3
                  ? `(${detailInfoParams.node.nodeHoverData.localCount || 0})`
                  : ''
            }}
          </div>
        </div>

        <div class="detail-sel-en detail-sel">
          <div
            v-if="detailInfoParams.current === 'national' && currentTab === 1"
            class="right-charts"
          >
            <!-- 企业统计 -->
            <EnterpriseCharts
              v-if="detailInfoParams"
              :detail-info-params="detailInfoParams"
            />
          </div>
          <!-- 全国、本地 -->
          <div
            v-if="
              detailInfoParams.current === 'national'
                ? currentTab === 2 || currentTab === 3
                : true
            "
            class="detail-list"
          >
            <!-- s搜索 -->
            <div class="sel-main">
              <div class="btn input-info">
                <el-input
                  v-model="selValue"
                  placeholder="请输入企业名称"
                  @input="isSearch"
                />
              </div>
              <div
                class="import-btn"
                @click="search"
              >
                搜索
              </div>
            </div>
            <!-- s搜索  结果-->

            <div class="searchBody">
              <div
                v-if="detailList.length > 0"
                v-loading="listLoading"
                class="lists"
                element-loading-background="rgba(2, 28, 60, 0.8)"
              >
                <div
                  v-for="(detail, idx) in detailList"
                  :key="idx"
                  class="detail"
                >
                  <CompanyCard
                    :detail-data="detail"
                    :active-state="activeState"
                    :enterprise-show="enterpriseShow"
                    :tag-show="detail.tagShow"
                    :more-tag="detail.tagMore"
                    @openDetailSingle="openDetailSingle"
                    @attention="attention"
                  />
                </div>
              </div>

              <div
                v-if="detailList.length > 0"
                class="ye"
              >
                <el-pagination
                  small
                  :current-page.sync="form.pageNum"
                  :page-size.sync="form.pageSize"
                  :total="+total"
                  layout="prev, pager, next"
                  @size-change="getEnterpriseList"
                  @current-change="getEnterpriseList"
                />
              </div>
              <div
                v-else
                v-loading="listLoading"
                class="lists"
              >
                <div class="no-font">
                  <NoData v-if="!listLoading" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 企业详情 -->
    <AtlasDetailCompany
      v-if="showFlag == 2"
      :child-id="detailSingleParams.childId"
      :enterprise-label-type="enterpriseLabelType"
      :firm-typedetail="firmTypedetail"
      :second-query-param="detailInfoParams.secondQueryParam"
      @closeDetail="closeDetailSingle"
      @getEnterpriseList="getEnterpriseList"
    />
  </div>
</template>

<script>
import { chartApi, getList } from '../apiUrl';
import AtlasDetailCompany from './companyDetail.vue'; // 图谱 - 企业详情
import EnterpriseCharts from './enterpriseCharts/index.vue'; // 图谱 - 企业详情
import CompanyCard from './companyCard/index.vue'; //  企业 card
import { updatainclusionIntention } from '@/api/CattractInvestment';
import { getPathId } from '@/utils/utils';
import NoData from '@/views/overview/components/component/noData2';
export default {
  components: {
    AtlasDetailCompany,
    EnterpriseCharts,
    CompanyCard,
    NoData
  },
  filters: {
    roundNumbers(value) {
      let result = value.replace(/([\d.]+)万?元?人民币/, function (match, p1) {
        let num = parseFloat(p1);
        if (match.endsWith('万')) {
          num *= 10000;
        }
        return num.toFixed(0) + '万元人民币';
      });
      // console.log(result);
      return result;
    },
  },
  props: {
    isFullscreen: {
      type: Boolean,
      default: false,
    },
    // 节点id
    childId: {
      type: String,
      default: null,
    },
    firmTypedetail: {
      type: Object,
      default: () => {},
    },
    // 节点参数
    detailInfoParams: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {
        titleName: '',
        type: 'graph',
        level: 1,
        paraimsId: null,
        childId: null,
        node: null,
      },
    },
    searchValueData: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      showFlag: 1, // 1 打开企业列表  2 打开详情
      enterpriseLabelType: null,
      detailSingleParams: {
        type: 'graph',
        level: 3,
        paraimsId: null,
        childId: null,
        titleName: '',
        node: null,
      },
      seek: true,
      listLoading: false,
      total: '',
      totals: '',
      selValue: '',
      detailTypeList: [
        { value: 4, label: '全国', active: true },
        { value: 1, label: '本地', active: false },
        //{ value: 3, label: "外地", active: false },
        //{ value: 2, label: "招商", active: false },
      ],
      detailTypeValue: 4,
      form: {
        pageSize: 5,
        pageNum: 1,
      },
      export: '',
      detailList: [
        /*     {logo:'企业控股',enterpriseName:'杭州企业控股有限公司',
        enterpriseLabelNames:['小微企业','小微企业'],
        industrySector:'企业行业',
        enterpriseNews:[{newsSummary:'这里是企业舆情摘要，点击可打开新标签页浏览详情',publicDate:'2022.10.01'},{info:'这里是企业舆情摘要，点击可打开新标签页浏览详情',time:'2022.10.01'},]},
        {logo:'企业控股',enterpriseName:'杭州企业控股有限公司',
        industrySector:'企业行业',
        enterpriseLabelNames:['小微企业','小微企业'],
        enterpriseNews:[{newsSummary:'这里是企业舆情摘要，点击可打开新标签页浏览详情',publicDate:'2022.10.01'},{info:'这里是企业舆情摘要，点击可打开新标签页浏览详情',time:'2022.10.01'},]
        } */
      ],
      top: false,
      activeName: [
        { name: '企业统计', id: 1 },
        { name: '全国', id: 2 },
        { name: '本地', id: 3 },
      ],
      currentTab: 1,
      activeState: '',
      enterpriseShow: false,
    };
  },
  watch: {
    // childId() {
    //   console.log(this.childId)

    //   // this.closeDetail();
    // },
    // detailInfoParams(val) {
    //   console.log("=val=2=", val);
    // },
    searchValueData(val) {
      this.setTabs(val);
    },
    'detailInfoParams.childId'() {
      // console.log('=val=2=', val);
      // console.log(this.childId)
      this.form.pageNum = 1;
      this.selValue = '';
      this.showFlag = 1;
      this.setTabs(this.searchValueData);
      this.getEnterpriseList();
    },
  },
  mounted() {
    this.getscreenWidth();
    this.setTabs(this.searchValueData);
    this.getEnterpriseList();
    // console.log('data==>',this.detailInfoParams)
  },
  beforeDestroy() {},
  methods: {
    setTabs(val) {
      if (val) {
        this.activeName = [
          // { name: "企业统计", id: 1 },
          { name: '全国', id: 2 },
          { name: '本地', id: 3 },
        ];
      } else {
        this.activeName = [
          { name: '企业统计', id: 1 },
          { name: '全国', id: 2 },
          { name: '本地', id: 3 },
        ];
      }
      this.currentTab = this.activeName[0].id;
    },
    getscreenWidth() {
      var screenWidth = window.innerWidth;
      // console.log(screenWidth, '屏幕宽度');
      if (screenWidth >= 1600) {
        // console.log(true);
        this.top = true;
      }
    },
    showPopover(item) {
      this.enterpriseShow = item.state;
      this.activeState = item.id;
    },
    async attention(item) { 
      const res = await updatainclusionIntention({
        clueSource: 1,
        uniCode: item?.unifiedSocialCreditCode,
      });
      if (res.code === 'SUCCESS') {
        //this.$message.success("关注企业成功")
        const h = this.$createElement;
        this.$message({
          message: h('div', null, [
            h(
              'span',
              { style: 'color:white' },
              '纳入意向企业成功，该企业已进入'
            ),
            h(
              'span',
              {
                style: 'color:#11CE66;cursor:pointer',
                on: {
                  click: () => {
                    this.skip();
                  },
                },
              },
              '产业招商-招商管理'
            ),
            h('span', { style: 'color:white' }, '中'),
          ]),
          type: 'success',
          duration: 5000,
          customClass: 'admin-tips-message',
        });
        this.getEnterpriseList();
      }
    },
    skip() {
      this.$message.closeAll(); //关闭message弹窗
      this.$router.push({
        path: '/attractInvestment',
        query: { id: getPathId() || null, show: 1 },
      });
    },
    isSearch() {
      if (this.selValue == '') {
        this.form.pageNum = 1;
        this.getEnterpriseList();
      }
    },
    async search() {
      this.form.pageNum = 1;
      this.getEnterpriseList();
    },
    // // 本地 / 全部 选择
    // onSelType(type, typeIdx) {
    //   this.localEnterprise = type;
    //   let typeList = this.detailTypeList;
    //   this.detailTypeList.forEach((item, idx) => {
    //     typeList[idx].active = false;
    //   });
    //   typeList[typeIdx].active = true;
    //   this.detailTypeValue = type.value;
    //   this.detailList = [];
    //   this.getEnterpriseList();
    // },

    // 获取 - 所有企业列表
    getEnterpriseList() {
      const id = this.$route.query?.id || getPathId() || null;
      if (!id) {
        return;
      }
      // console.log(this.currentTab, 'this.detailInfoParams节点参数');
      let data = {
        chainNodeId: this.detailInfoParams.childId, // integer产业链节点id,
        pageSize: this.form.pageSize, //integer 页码
        pageNum: this.form.pageNum, //integer 当前页
        //localEnterprise: this.detailTypeValue, //integer 是否本地企业
        enterpriseType: this.currentTab === 2 ? 4 : 1, //企业类型
        // 1、本地；2、招商；3、外地；4、全国
        id: this.detailInfoParams.paraimsId, //integer 机构产业链关系id
        enterpriseName: this.selValue, //string 企业名称
        secondQueryParam: this.detailInfoParams.secondQueryParam, // 二级筛选
      };
      this.listLoading = true;
      return new Promise((resolve, reject) => {
        getList(chartApi.enterpriseList, data)
          .then((res) => {
            this.detailList = res.records;
            this.totals = res.total;
            this.total = res.total;
            // 接口只能查1w条数据
            if (res.total > 10000) {
              this.total = 10000;
            }

            for (let i = 0; i < this.detailList.length; i++) {
              if (this.detailList[i].enterpriseLabelNames) {
                this.detailList[i].new =
                  this.detailList[i].enterpriseLabelNames.split(',');
                this.detailList[i].tagShow = this.detailList[i].new.slice(0, 2);
                if (this.detailList[i].tagShow.join('').length > 8) {
                  this.detailList[i].tagShow = [this.detailList[i].tagShow[0]];
                }
                this.detailList[i].tagMore = this.detailList[i].new.filter(
                  (item) => !this.detailList[i].tagShow.includes(item)
                );
              } else {
                this.detailList[i].new = null;
              }
            }
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          })
          .finally(() => {
            this.seek = true;
            this.listLoading = false;
          });
      });
    },

    // // 导出 - 企业列表
    // onExport() {
    //   const id = this.$route.query?.id || null;
    //   if (!id) {
    //     return;
    //   }
    //   let data = {
    //     chainNodeId: this.detailInfoParams.childId, // 产业链节点id
    //     id: this.detailInfoParams.paraimsId, // 机构产业链关系
    //     enterpriseName: this.selValue, //"string" // 企业名称
    //     //localEnterprise:this.export
    //     enterpriseType: this.detailInfoParams.enterpriseType || 4, //企业类型
    //   };
    //   return new Promise((resolve, reject) => {
    //     this.loading = true;
    //     enterpriseListExportApi(data)
    //       .then((res) => {
    //         /*             if (
    //           res.data.type == "application/json" &&
    //           this.detailList.length <= 0
    //         ) {
    //           this.$message.error("没有符合条件的数据导出");
    //         } else {
    //           // downloadFile(res,'aa',"text/csv,charset=UTF-8");
    //           let blob = new Blob([res.data], {
    //             type: "text/csv,charset=UTF-8",
    //           });
    //           let objectUrl = URL.createObjectURL(blob);
    //           window.location.href = objectUrl;
    //         } */
    //         let blob = new Blob([res.data], {
    //           type: "text/csv,charset=UTF-8",
    //         });
    //         let objectUrl = URL.createObjectURL(blob);
    //         window.location.href = objectUrl;
    //         resolve(res);
    //         // const blob = new Blob([res.data]);
    //         // const fileName = Date.now()+'_企业.xls'
    //         // if ('download' in document.createElement('a')) { // 非IE下载
    //         //   const elink = document.createElement('a')
    //         //   elink.download = fileName
    //         //   elink.style.display = 'none'
    //         //   elink.href = URL.createObjectURL(blob)
    //         //   document.body.appendChild(elink)
    //         //   elink.click()
    //         //   URL.revokeObjectURL(elink.href) // 释放URL 对象
    //         //   document.body.removeChild(elink)
    //         // } else { // IE10+下载
    //         //   navigator.msSaveBlob(blob, fileName)
    //         // }
    //       })
    //       .catch((error) => {
    //         reject(error);
    //       })
    //       .finally(() => {
    //         this.loading = false;
    //       });
    //   });
    // },

    // 打开 - 企业详情
    openDetailSingle(params) {
      this.enterpriseLabelType = params.enterpriseLabelType;
      this.showFlag = 2;
      this.detailSingleParams.childId = params.id;
      this.detailSingleParams.titleName = params.enterpriseName;
    },

    // 关闭 - 企业详情
    closeDetailSingle() {
      this.showFlag = 1;
    },

    // 关闭 - 企业列表
    closeDetail() {
      this.$emit('closeDetail');
    },
    // handleClick(tab, event) {
    //   // console.log(tab, event);

    // },
    cutTabs(item) {
      this.currentTab = item.id;
      if (item.id === 2 || item.id === 3) {
        this.selValue == '';
        this.search();
      }
    },
  },
};
</script>
<style lang="scss">
.lists {
  .el-loading-mask {
    background: transparent !important;
  }
}

.el-loading-spinner .circular {
  display: none;
  width: 0;
  height: 0;
}

.tag-popover {
  background:  #03203f !important;
  border: 0.8px solid #00A5FE !important;
  color: #ffffff !important;
  z-index: 99007 !important;
}
</style>
<style lang="scss" scoped>
// tab 公共配置
@import './companyPublic.scss';
@import './enterprise.scss';
</style>
