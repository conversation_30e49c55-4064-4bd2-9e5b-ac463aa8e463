<template>
  <div class="title">
    <div class="leftIcon" />
    <div class="rightText">
      {{ rightText }}
    </div>
  </div>
</template>
<script>
import { string } from 'clipboard';

export default {
  name: 'PointTitle',
  components: {},
  props: {
    rightText: {
      type: String,
      default: '',
    },
  },
};
</script>
<style scoped lang="scss">
@import '@/styles/public.scss';
.title {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  height: 30px;
  margin-bottom: 10px;
  .leftIcon {
    width: 30px;
    height: 30px;
    background: center/cover no-repeat
      url('~@/assets/screen/newScreen/pount.webp');
  }

  .rightText {
    // padding-left: 10px;
    width: calc(100% - 20px);
    @include PuhuitiLeftToRight(16px, bold);
  }
}
</style>