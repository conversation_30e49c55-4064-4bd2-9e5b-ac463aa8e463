<template>
  <div>
    <div :class="'visView'">
      <div class="firstTitle">
        <div class="topImg" />
        <div class="txt">
          上市板块
        </div>
      </div>
      <div class="box">
        <div
          class="item"
        />
        <div
          v-for="item in list"
          :key="item.label"
          class="item"
          @click="checkEvent(item.label)"
        >
          <div
            :class="[active === item.label ? 'on' : '']"
            class="txt"
          >
            <div class="innerText">
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="secondFilter"
      :class="'visView'"
    >
      <div class="firstTitle">
        <div class="topImg" />
        <div class="txt">
          科技创新
        </div>
      </div>
      <div class="box">
        <div
          class="item"
        />
        <!-- <div class="otherTitles"> -->
        <div
          v-for="item in list2"
          :key="item.label"
          class="item"
          @click="checkEvent(item.label)"
        >
          <div
            :class="[active === item.label ? 'on' : '']"
            class="txt"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- </div> -->
</template>

<script>
import { chartApi, getDetail } from '../apiUrl';
export default {
  props: {
    currentState: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      list: [
        {
          name: '主板',
          label: '主板',
        },
        {
          name: '港股',
          label: '港股',
        },
        {
          name: '创业板',
          label: '创业板',
        },
        {
          name: '科创板',
          label: '科创板',
        },
        {
          name: '北交所',
          label: '北交所',
        },
        {
          name: '新三板',
          label: '新三板',
        },
        // {
        //   name: '新四板',
        //   label: '新四板',
        // },
      ],
      //     国家级专精特新小巨人企业、国家级独角兽企业、省级专精特新中小企业、省级专精特新小巨人企业、省级创新型中小企业、省级技术先进型服务企业、省级瞪羚企业、省级独角兽企业、省级技术创新示范企业、省级隐形冠军企业、省级科技小巨人企业、省级雏鹰企业
      list2: [
        {
          name: '国家级专精特新小巨人企业',
          label: '国家级专精特新小巨人企业',
        },
        {
          name: '国家级独角兽企业',
          label: '国家级独角兽企业',
        },
        {
          name: '省级专精特新中小企业',
          label: '省级专精特新中小企业',
        },
        {
          name: '省级专精特新小巨人企业',
          label: '省级专精特新小巨人企业',
        },
        {
          name: '省级创新型中小企业',
          label: '省级创新型中小企业',
        },
        {
          name: '省级技术先进型服务企业',
          label: '省级技术先进型服务企业',
        },
        {
          name: '省级瞪羚企业',
          label: '省级瞪羚企业',
        },
        {
          name: '省级独角兽企业',
          label: '省级独角兽企业',
        },
        {
          name: '省级技术创新示范企业',
          label: '省级技术创新示范企业',
        },
        {
          name: '省级隐形冠军企业',
          label: '省级隐形冠军企业',
        },
        {
          name: '省级科技小巨人企业',
          label: '省级科技小巨人企业',
        },
        {
          name: '省级雏鹰企业',
          label: '省级雏鹰企业',
        },
      ],
      styleWith: 420,
      active: '',
      moveNum: 0,
      pitch: null,
    };
  },
  // watch: {
  // active(newValue) {
  //   if (newValue === '') {
  //     this.$emit('onGetSecondQueryParam', '');
  //   } else {
  //     this.$emit('onGetSecondQueryParam', this.list[newValue].label);
  //   }
  // },
  // },
  created() {
    // this.queryAtlasSecondQueryParam();
  },
  methods: {
    /*  handleMouseOver(k){
     this.pitch=k
     console.log('移入', this.pitch);
    },
    handleMouseOut(){
      this.pitch=null
      console.log('移出', this.pitch);
    }, */
    // 获取数据
    // queryAtlasSecondQueryParam() {
    //   return new Promise((resolve, reject) => {
    //     getDetail(chartApi.queryAtlasSecondQueryParam, {})
    //       .then((res) => {
    //         // console.log('res---', res)
    //         // this.$set(this, 'list', res)
    //         this.styleWith = 82 * res.length + 12;
    //         resolve(res);
    //       })
    //       .catch((error) => {
    //         reject(error);
    //       });
    //   });
    // },
    // checkSecondEvent(i) {
    //   if (i === this.active) {
    //     this.active = '';
    //   } else {
    //     this.active = i;
    //     this.pitch = i;
    //   }
    //   this.$emit('onGetSecondQueryParam', i);
    // },
    checkEvent(i) {
      /*  if (i == 7) {
        return;
      } */
      if (i === this.active) {
        this.active = '';
        this.$emit('onGetSecondQueryParam', '');
      } else {
        this.active = i;
        this.pitch = i;
        this.$emit('onGetSecondQueryParam', i);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import './../../../assets/font/iconfont.css';
.visView {
  .firstTitle {
  .topImg{
      background: center/contain no-repeat url(~@/assets/screen/new/title1.webp);
      background-size: 150%;
    }
  }
}
.visView.secondFilter {
  .firstTitle {
    .topImg {
      background: center/contain no-repeat url(~@/assets/screen/new/title2.webp);
      background-size: 160%;
    }
  }
}
.firstTitle {
  width: 78px;
  height: 3.6875rem;
  font-family: YouSheBiaoTiHei;
  font-size: 16px;
  z-index: 11;
  // background: rgba(255, 255, 255, 0.05);
  // background: center/contain no-repeat
  //   url(./../../../assets/bigScreen/firstTitleBg.png);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  flex-direction: column;

.topImg {
    display: block;
    width: 53px;
    height: 54px;
    
  }
}
.otherTitles {
}
.visView {
  width: 53rem;
    height: 75px;
  margin: 10px auto;
  display: flex;
  flex-wrap: nowrap;
  align-items: flex-end;
  // .box .item {
  //   &:last-child {
  //     border-right: 0px solid;
  //   }
  // }
  .innerText{
    font-size: 14px;
  }
}
.visView.secondFilter {
  bottom: 35px;
  width: 85rem;
  .item {
    width: 5rem !important;
    &:first-child {
        width: 4.5rem !important;
      }
  }
  
}
.visView {
  bottom: 120px;
}
.visView {
  position: fixed;
  left: 23px;
  box-sizing: border-box;
  overflow: hidden;
 
  &.on {
    right: 23px;
    left: auto;
  }

  .box {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    z-index: 9;
    background: rgba(21,38,64,0.9);
    border-radius: 6px;
    flex: 1;
    height: 40px;
    margin-left: -65px;
    .item {
      width: 115px;
      height: 100%;
      // border-right: 1px solid rgba(255, 255, 255, 0.1);
      box-sizing: border-box;
      cursor: pointer;
      display: flex;
      align-items: center;
      text-align: center;
      &:first-child {
        width: 60px;
      }
      &:hover {
        color: #FFFFFF;
      //   background: rgba(255, 255, 255, 0.2);
      //   .icon,
      //   .txt {
      //     color: #fff;
      //   }
      //   .it-icon {
      //     top: -150px;
      //     filter: drop-shadow(0px 150px 0px #fff);
      //   }

      }
      .txt {
        font-size: 12px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.8);
        width: 100%;
        display: flex;
        height: 100%;
        align-items: center;
        justify-content: center;
        // padding: 3px 15px 0 15px;
        height: 40px;
        &.on {
          background: center / contain no-repeat
            url(https://static.idicc.cn/cdn/pangu/tabBg.webp);
          color: #fff;
          background-size: 98% 100%;
        }
      }
    }
  }
}
</style>
