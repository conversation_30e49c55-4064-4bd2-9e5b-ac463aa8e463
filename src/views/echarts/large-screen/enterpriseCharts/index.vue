<template>
  <div class="enterprise">
    <!-- 企业统计 -->
    <!-- 节点名称 -->
    <div class="title">
      <div class="leftIcon" />
      <div class="rightText">
        {{ showData?.chainNodeNames }}
      </div>
    </div>

    <div
      v-if="showData"
      class="topCharts"
    >
      <TopCharts :show-data="showData" />
    </div>
    <div
      v-if="showData"
      class="middleCharts"
    >
      <MiddleCharts :show-data="showData" />
    </div>
    <div
      v-if="showData"
      class="botomCharts"
    >
      <BottomCharts :show-data="showData" />
    </div>
    <div
      v-if="loading"
      v-loading="loading"
      class="loading-info"
      customClass="loading-info-icon"
      element-loading-text=""
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgb(0 0 0 / 24%)"
      element-loading-color="#fff"
    />
  </div>
</template>
<script>
import TopCharts from './top.vue';
import MiddleCharts from './middle.vue';
import BottomCharts from './bottom.vue';
import { getPathId } from '@/utils/utils';

import { getChartsData } from '../../apiUrl.js';
export default {
  components: {
    TopCharts,
    MiddleCharts,
    BottomCharts,
  },
  props: {
    // 节点参数
    detailInfoParams: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {
        titleName: '',
        type: 'graph',
        level: 1,
        paraimsId: null,
        childId: null,
        node: null,
      },
    },
  },
  data() {
    return {
      showData: null,
      loading: false,
    };
  },
  watch: {
    'detailInfoParams.childId'(val) {
      val && this.getDataInfo();
    },
  },
  mounted() {
    this.getDataInfo();
  },
  methods: {
    getDataInfo() {
      this.loading = true;
      const id = this.$route.query?.id || getPathId() || null;
      let data = {
        id,
        chainNodeId: this.detailInfoParams?.node?.nodeId,
      };
      if (this.detailInfoParams?.node?.nodeId) {
        getChartsData(data)
          .then((res) => {
            this.loading = false;
            this.showData = res;
          })
          .catch(() => {
            this.loading = false;
          });
      }
    },
  },
};
</script>
<style scoped lang="scss">
@import '@/styles/public.scss';

.enterprise {
  height:100%;
  padding: 15px 20px 0;
}
.topCharts,.middleCharts,.botomCharts{
height: 32%;
}
.botomCharts{
  height: 24%;
}
.title {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  height: 30px;
  margin-bottom: 10px;
  .leftIcon {
    width: 30px;
    height: 30px;
    background: center/cover no-repeat
      url('~@/assets/screen/newScreen/pount.webp');
  }

  .rightText {
    // padding-left: 10px;
    width: calc(100% - 20px);
    @include PuhuitiLeftToRight(16px, bold);
  }
}
</style>
<style lang="scss">
.enterprise {
  .el-loading-parent--relative {
    position: absolute !important;
    top: 50%;
    left: 50%;
  }

  .el-loading-spinner .circular {
    width: 0;
    height: 0;
  }
}
</style>
