<template>
  <div>
    <Contents title="与全国当前行政维度优质企业平均值对比">
      <div
        slot="main"
        class="contain"
      >
        <div
          id="middleCharts"
          class="charts"
        />
      </div>
    </Contents>
  </div>
</template>
<script>
import Contents from './components/index.vue';
import * as echarts from 'echarts';
import {YAxis,XAxis} from '@/utils/XYLineConfig'


export default {
  components: {
    Contents,
  },
  props: {
    showData: {
      type: Object,
      default: null,
    },
  },
  watch: {
    showData(val) {
      val && this.bastEnterprise();
    },
  },
  mounted() {
    this.bastEnterprise();
    //  this.showData.diffEnterpriseIncrement);
  },
  methods: {
    // 优质企业
    bastEnterprise() {
      let source = [];
      let colorList = [
        {
          colorStart: 'rgba(72, 104, 177, 0)',
          colorEnd: '#46ACFF',
        },
        {
          colorStart: 'rgba(29, 255, 232, 0.01)',
          colorEnd: '#46FF9B',
         
        },
      ];
      let data = this.showData?.quarterCompare;
      data?.map((e, index) => {
        let name = ['上市', '新三板', '专精特新小巨人', '专精特新', '隐形冠军'];
        source.push([name[index], e.avgGlobal, e.localAmount]);
      });
      let chartDom = document.getElementById('middleCharts');
      let myChart = echarts.init(chartDom);
      let option;

      option = {
        legend: {
          right: '6%',
          top: '6%',
          textStyle: {
            color: '#fff',
            fontSize: 14,
            //   opacity:0.8,
            //   lineHeight: 26, // 设置文字之间的上下间距
          },
          itemWidth: 10,
          itemHeight: 10, //修改icon图形大小
        },
        grid: {
          left: '8%',
          right: '6%',
          bottom: '10%',
          top: '25%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        dataset: {
          source: [['product', '全国', '本地'], ...source],
        },
        xAxis: {
          type: 'category',
          // axisLabel: {
          //   interval: 0,
          //   rotate: -20, //倾斜的程度
          // },
          textStyle: {
            color: '#E6F7FF',
            fontSize: 10,
          },
          nameTextStyle: {
            color: '#E6F7FF',
          },
          axisLabel: {
            color: '#E6F7FF',
            // color: 'rgba(255, 255, 255, 0.18)',
            interval: 0,
            // rotate:-45,
          },
        },
        yAxis: {
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgba(255, 255, 255, 0.18)',
            },
          },
          type: 'value',
          axisLine: {
            show: false,
          },
          name: '数量(家)',
          axisLabel: {
            color: '#E6F7FF',
            fontSize: 12,
            fontWeight: 'normal',
            // color: 'rgba(255, 255, 255, 0.18)',
          },
          nameTextStyle: {
            top: 0,
            fontSize: 14,
            color: '#E6F7FF',
          },
        },
        series: [
          {
            type: 'bar',
            barWidth: 14,
            itemStyle: {
              normal: {
                // barBorderRadius: 1,
                type: 'linear',
                color: () => {
                  // 设置描边宽度

                  return new echarts.graphic.LinearGradient(1, 1, 0, 0, [
                    {
                      //左、下、右、上
                      offset: 0,
                      color: colorList[0].colorStart,
                    },
                    {
                      offset: 1,
                      color: colorList[0].colorEnd,
                    },
                  ]);
                },
              },
            },
            barMinHeight: 2,
            barMaxWidth: 10,
          },
          {
            type: 'bar',
            barWidth: 14,
            itemStyle: {
              normal: {
                // barBorderRadius: 1,
                type: 'linear',
                color: () => {
                  // 设置描边宽度

                  return new echarts.graphic.LinearGradient(1, 1, 0, 0, [
                    {
                      //左、下、右、上
                      offset: 0,
                      color: colorList[1].colorStart,
                    },
                    {
                      offset: 1,
                      color: colorList[1].colorEnd,
                    },
                  ]);
                },
              },
            },
            barMinHeight: 2,
            barMaxWidth: 10,

          },
        ],
      };

      option && myChart.setOption(option);
    },
  },
};
</script>
<style scoped lang="scss">
.contain {
  width: 100%;
  height:  21vh !important;
  display: flex;
  flex-wrap: nowrap;
  .charts {
    width: 100%;
    height: 100%;
  }
}
</style>
