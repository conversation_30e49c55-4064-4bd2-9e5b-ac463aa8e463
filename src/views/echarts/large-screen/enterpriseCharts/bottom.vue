<template>
  <div>
    <Contents title="产业分布城市TOP3">
      <div slot="main">
        <div
          v-if="showData?.countTop3?.length > 0"
          class="contain"
        >
          <div
            v-for="(item, index) in showData?.countTop3"
            :key="index"
            class="charts"
          >
            <div :class="['icon', 'icon' + index]" />
            <div class="country">
              {{ item.location }}
            </div>
            <div class="num">
              <span class="number">  {{ item.num }}</span>
              <span class="int">家</span>
            </div>
          </div>
        </div>
        <NoData
          v-else
          style="margin-top: 40px;"
        />
      </div>
    </Contents>
  </div>
</template>
<script>
import Contents from './components/index.vue';
import NoData from '@/views/overview/components/component/noData';
export default {
  components: {
    Contents,
    NoData
  },
  props: {
    showData: {
      type: Object,
      default: null,
    },
  },
};
</script>
<style scoped lang="scss">
@import '@/styles/public.scss';

.noData {
  font-size: 14px;
  color: rgba(181, 193, 209, 0.7);
  line-height: 15vh;
  width: 100%;
  text-align: center;
}
.contain {
  width: 100%;
  height:  21vh !important;
  // padding: 20px;
  display: flex;
  flex-wrap: nowrap;
  padding: 20px 0px 10px 0;

  .charts {
    color: white;
    width: 34%;
    height: 80%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    text-align: center;
    .icon {
      width: 90px;
      height: 90px;
    }
    .icon0 {
      background: center/contain no-repeat url('~@/assets/screen/new/top1.webp');
    }
    .icon1 {
      background: center/contain no-repeat
        url('~@/assets/screen/new/top2.webp');
    }
    .icon2 {
      background: center/contain no-repeat
        url('~@/assets/screen/new/top3.webp');
    }

    .country {
      width: 100%;
      font-size: 16px;
      color: rgba(181, 193, 209, 0.7);
      line-height: 15px;
      padding: 6px 0;
      @include Puhuiti(14px,rgba(230, 247, 255, 0.7),600)
    }

    .num {
      text-align: center;
      width: 100%;
      padding-left: 15px;
      .number{
        @include  YouSheBiaoTi28(24px,500);
      }

      .int {
        font-size: 14px;
        color: rgba(130, 146, 168, 0.54);
      }
    }
  }
}
</style>
