<template>
  <div>
    <Contents title="企业数量对比">
      <div
        slot="main"
        class="contain"
      >
        <div
          v-show="showData?.countryCount && showData?.localCount"
          id="topCharts"
          class="charts"
        />
        <div
          v-if="showData?.rank && showData?.rank !== '0'"
          class="number"
        >
          <div class="box">
            <div class="num">
              NO.
            </div>
            <div class="rank">
              {{ showData?.rank }}
            </div>
          </div>
          <div class="name">
            全国排名
          </div>
        </div>
        <NoData
          v-else
        />
      </div>
    </Contents>
  </div>
</template>
<script>
import Contents from './components/index.vue';
import NoData from '@/views/overview/components/component/noData';

import * as echarts from 'echarts';

export default {
  components: {
    Contents,
    NoData
  },
  props: {
    showData: {
      type: Object,
      default: null,
    },
  },
  watch: {
    showData(val) {
      val && this.bar();
    },
  },
  mounted() {
    this.bar();
  },
  methods: {
    bar() {
      let chartDom = document.getElementById('topCharts');
      let myChart = echarts.init(chartDom);
      let option;
      let circleFsize = 15;
      if (window.innerWidth < 1500) {
        circleFsize = 12;
      }
      if (this.showData?.countryCount && this.showData?.localCount) {
        let colorList = [
          ['rgba(100, 255, 99, 1)', 'rgba(29, 255, 232, 1)'],
          ['rgba(71, 98, 172, 1)', 'rgba(80, 177, 252, 1)'],
        ];
        option = {
          tooltip: {
            trigger: 'item',
          },
          legend: {
            bottom: '9%',
            left: 'center',
            textStyle: {
              color: '#fff',
              fontSize: 14,
              //   opacity:0.8,
              //   lineHeight: 26, // 设置文字之间的上下间距
            },
            itemWidth: 10,
            itemHeight: 10, //修改icon图形大小
          },
          // 圆心文字
          title: [
            {
              text: `${(
                (this.showData?.localCount / this.showData?.countryCount) *
                100
              ).toFixed(2)}%`,
              top: '35%',
              textAlign: 'center',
              left: '48%',
              textStyle: {
                color: '#FFFFFF',
                fontSize: circleFsize,
                fontWeight: '600',
                fontFamily: 'Source Han Sans CN-Regular, Source Han Sans CN',
              },
            },
          ],
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 0,
            color: (list) => {
              // 设置描边宽度
              return new echarts.graphic.LinearGradient(1, 1, 0, 0, [
                {
                  //左、下、右、上
                  offset: 0,
                  color: colorList[list.dataIndex][0],
                },
                {
                  offset: 1,
                  color: colorList[list.dataIndex][1],
                },
              ]);
            },
            //
          },
          series: [
            //   {
            //   type: 'pie',
            //   zlevel: 2,
            //   radius: ['45%', '55%'],
            //   animationDuration: 1500,
            //   animationDurationUpdate: 1500,
            //   itemStyle: {
            //     borderRadius: 10,
            //   },
            //   left: '-50%', //2top10%
            //   top: '0%',
            //   emphasis: {},
            //   label: {
            //     show: false, // 不显示名称
            //   },
            //   data:  [{ value: this.showData?.countryCount, name: '全国' },
            //       { value: this.showData?.localCount, name: '本地' },]

            //   //minAngle: 5 // 设置最小角度为5度
            // },

            {
              //   name: 'Access From',
              center: ['50%', '40%'],
              // left: '0',
              type: 'pie',
              minAngle: 5,
              radius: ['48%', '55%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 0,
                color: (list) => {
                  // 设置描边宽度
                  return new echarts.graphic.LinearGradient(1, 1, 0, 0, [
                    {
                      //左、下、右、上
                      offset: 0,
                      color: colorList[list.dataIndex][0],
                    },
                    {
                      offset: 1,
                      color: colorList[list.dataIndex][1],
                    },
                  ]);
                },
                //
              },
              label: {
                show: false,
                position: 'center',
              },
              labelLine: {
                show: false,
              },

              data: [
                { value: this.showData?.countryCount, name: '全国' },
                { value: this.showData?.localCount, name: '本地' },
              ],
            },
            {
              type: 'gauge',
              zlevel: 1,
              // center: ['25%', '50%'],
              center: ['50%', '40%'],
              radius: '44%',
              startAngle: 90,
              endAngle: -270,
              axisLine: {
                show: false,
              },
              axisTick: {
                distance: -6,
                length: 6,
                lineStyle: {
                  color: '#585e67',
                  width: 2,
                },
              },
              axisLabel: {
                show: false,
              },
              splitNumber: 6,
              splitLine: {
                show: true,
                distance: -6,
                length: 6,
                lineStyle: {
                  color: 'rgba(255, 255, 255, 0.18)',
                  width: 2,
                },
              },
              data: [],
              detail: {},
              pointer: {
                show: false,
              },
            },
          ],
        };

        option && myChart.setOption(option);
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: '40%',
            y: '40%',
            textStyle: {
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: 12,
              fontWeight: '400',
            },
          },
          tooltip: '',
          series: {
            name: '',
            //   top: '-15%',
            type: 'pie',
            radius: 0,
            // roseType: 'radius',
            //   emphasis: {
            //     label: {
            //       show: false
            //     }
            //   },
            //   label: {
            //     show: false,
            //   },
            data: null,
          },
        };
        myChart.setOption(option);
      }
    },
  },
};
</script>
<style scoped lang="scss">
@import '@/styles/public.scss';

.contain {
  width: 100%;
  height:  21vh !important;
  // padding: 20px;
  display: flex;
  flex-wrap: nowrap;

  .charts,
  .number {
    width: 50%;
    height: 100%;
    display: flex;
    justify-content: center;
    /* align-items: center; */
    flex-wrap: wrap;
  }

  .number {
    padding: 30px 20px 7px;
    .box {
      width: 130px;
      height: 115px;
      background: center/contain no-repeat
        url('~@/assets/screen/new/national.webp');
      line-height: 100px;
      text-align: center;
      font-size: 21px;
      // font-style: italic;
      color: #ffffff;
      font-weight: bold;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-bottom: 35px;
      .num{
        @include YouSheBiaoTi30(14px, #ffffff, #fff);
      }
      .rank{
        @include YouSheBiaoTi30(30px, #ffffff, #fff);
      }
    }

    .name {
      width: 100%;
      text-align: center;
      padding-top: 16px;
      // font-size: 14px;
      color: #ffffff;
      @include YouSheBiaoTi30(16px, #ffffff, #fff);
    }
  }
}

.noData {
  font-size: 12px;
  color: rgb(184 192 202);
  line-height: 155px;
  width: 100%;
  text-align: center;
}
</style>
