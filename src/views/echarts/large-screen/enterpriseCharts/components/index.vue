<template>
  <div
    class="contents"
    :style="{ height: height || 'auto' }"
  >
    <div class="title">
      <div class="text">
        {{ title }}
      </div>
    </div>
    <slot name="main" />
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    height: {
      type: String,
      default: 'auto',
    },
  },
};
</script>
<style lang="scss" scoped>
@import '@/styles/public.scss';

.contents {
  width: 100%;

  // height: 16.5rem;
}

.title {
  width: 100%;
  height: 35px;
  background: center/contain no-repeat
    url('~@/assets/screen/new/en_title_bg.webp');
  position: relative;
  .text {
    position: absolute;
    top: -13px;
    left: 24px;
    line-height: 35px;
    @include YouSheBiaoTi28(20px, normal, #fff, #fff);
    color: #fff;
    text-shadow: 0 0 0;
    background: transparent;
  }
}
</style>
