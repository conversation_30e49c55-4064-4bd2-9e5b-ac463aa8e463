<!-- 委托跟踪信息 -->
<template>
  <div class="tracke-info">
    <!-- 记录表单区域 -->
    <div class="road">
      <el-button
        v-if="
          selectedRow?.clueDealState === '0' &&
            selectedRow?.beAssignPersonId === $store.getters.user.userId
        "
        size="small"
        type="primary"
        @click="addNewRecord"
      >
        新增记录
      </el-button>
      <el-button
        v-if="assignPath?.length > 0"
        size="small"
        type="primary"
        @click="addRecord"
      >
        指派路径
      </el-button>
    </div>
    <div class="record-form">
      <div class="form-content">
        <div
          v-if="latestAssignClue.assignDate"
          class="info-item"
        >
          <span class="label">指派日期：</span>
          <span class="value">{{ latestAssignClue?.assignDate }}</span>
        </div>
        <div
          v-if="latestAssignClue.operateUserName"
          class="info-item"
        >
          <span class="label">指派人：</span>
          <span class="value">{{ latestAssignClue?.operateUserName }}</span>
        </div>
        <div
          v-if="latestAssignClue.remark"
          class="info-item"
        >
          <span class="label">线索流转备注</span>
          <span class="value">{{ latestAssignClue?.remark }}</span>
        </div>
        <div
          v-if="latestAssignClue.assignUserName"
          class="info-item"
        >
          <span class="label">跟进人：</span>
          <span class="value">{{ latestAssignClue.assignUserName }}</span>
        </div>
        <div
          v-if="selectedRow?.clueDealState || selectedRow?.isNotFollowMonth"
          class="info-item"
        >
          <span class="label">当前进度：</span>
          <span
            :class="[
              'status',
              ['ing', 'success', 'completed'][selectedRow?.clueDealState],
            ]"
          >{{
            ['跟进中', '签约成功', '签约失败'][selectedRow?.clueDealState]
          }}</span>
          <span
            v-if="selectedRow?.isNotFollowMonth"
            class="value progress-tag"
            :class="{
              'no-follow': formData?.currentProgress === '近一个月未跟进',
            }"
          >
            <i class="el-icon-warning-outline" />
            {{ formData?.currentProgress }}
          </span>
        </div>
      </div>
    </div>

    <!-- 处理记录列表 -->
    <time-line :records="followUpRecords" />

    <!-- 指派路径抽屉 -->
    <el-drawer
      title="指派路径"
      :visible.sync="showRode"
      direction="rtl"
      size="25%"
      custom-class="assign-path-drawer"
      append-to-body
    >
      <div class="assign-path-content">
        <div class="timeline-container">
          <div
            v-for="(item, index) in assignPath"
            :key="index"
            class="timeline-item"
          >
            <div class="time-point">
              <div class="dot-circle" />
            </div>
            <div class="timeline-date">
              {{ item.assignDate }}
            </div>
            <div class="timeline-info">
              <div class="assign-info">
                <span class="label">指派人：</span>
                <span class="value">{{ item.operateUserName }}</span>
              </div>
              <div class="follow-info">
                <span class="label">跟进人：</span>
                <span class="value">{{ item.assignUserName }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 新增记录抽屉 -->
    <el-drawer
      title="添加跟进记录"
      :visible.sync="showAddRecord"
      direction="rtl"
      size="30%"
      custom-class="assign-path-drawer add-record-drawer"
      append-to-body
    >
      <div class="add-record-content">
        <el-form
          ref="recordForm"
          :model="recordForm"
          :rules="recordRules"
          label-width="80px"
          label-position="top"
          class="record-form-container"
        >
          <div class="company-name">
            <span>公司名称</span>
            <div class="company-value">
              {{ companyName || '' }}
            </div>
          </div>

          <div class="form-item-label required">
            当前进度
          </div>
          <el-form-item prop="currentState">
            <el-radio-group v-model="recordForm.currentState">
              <el-radio :label="0">
                跟进中
              </el-radio>
              <el-radio :label="1">
                签约成功
              </el-radio>
              <el-radio :label="2">
                签约失败
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <div class="form-item-label">
            跟进日期
          </div>
          <el-form-item prop="followDate">
            <el-date-picker
              v-model="recordForm.followDate"
              disabled
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              style="width: 100%"
            />
          </el-form-item>

          <div class="form-item-label">
            跟进人
          </div>
          <el-form-item>
            <div class="follow-person">
              {{ currentUser || '' }}
            </div>
          </el-form-item>

          <div class="form-item-label required">
            跟进概述
          </div>
          <el-form-item prop="followDesc">
            <el-input
              v-model="recordForm.followDesc"
              type="textarea"
              :rows="4"
              placeholder="请输入跟进概述"
              :maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <div class="form-actions">
            <el-button @click="showAddRecord = false">
              取 消
            </el-button>
            <el-button
              type="primary"
              @click="submitRecord"
            >
              保 存
            </el-button>
          </div>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  getFollowUpRecord,
  getLatestAssignClue,
  getAssignPath,
  addClueDealRecord, // 假设这是添加跟进记录的API
} from '@/api/CattractInvestment';
import TimeLine from '../public/TimeLine/index.vue';

export default {
  name: 'FollowUpRecord',
  components: {
    TimeLine,
  },
  props: {
    clueId: {
      type: String,
      default: '',
    },
    selectedRow: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      followUpRecords: [], // 存储从API获取的跟进记录
      assignPath: {
        assignClueList: [], // 初始化为空数组，避免渲染时出错
      },
      latestAssignClue: {},
      showRode: false,
      showAddRecord: false,
      recordForm: {
        currentState: 0, // 默认待处理
        followDate: new Date().toISOString().split('T')[0], // 当前日期
        followDesc: '',
      },
      recordRules: {
        currentState: [
          { required: true, message: '请选择当前进度', trigger: 'change' },
        ],
        followDesc: [
          { required: true, message: '请输入跟进概述', trigger: 'blur' },
        ],
      },
      companyName: '',
      currentUser: '',
    };
  },
  watch: {
    clueId: {
      handler(newVal) {
        if (newVal) {
          this.getFollowUpRecordList();
        }
      },
      immediate: true,
    },
  },
  created() {
    // 获取当前用户名
    this.currentUser = this.$store.getters.user?.realName || '当前用户';

    // 如果有选中的行，获取公司名称
    if (this.selectedRow && this.selectedRow.enterpriseName) {
      this.companyName = this.selectedRow.enterpriseName;
    }
  },
  methods: {
    getFollowUpRecordList() {
      // 跟进记录
      getFollowUpRecord({ clueId: this.clueId })
        .then((res) => {
          if (res && res.code === 'SUCCESS') {
            this.followUpRecords = res.result || [];
          } else {
            this.$message.error(res.msg || '获取跟进记录失败');
            this.followUpRecords = [];
          }
        })
        .catch((error) => {
          // console.error('获取跟进记录出错:', error);
          this.$message.error('获取跟进记录出错');
          this.followUpRecords = [];
        });
      // 获取最新一次指派记录
      getLatestAssignClue({ clueId: this.clueId }).then((res) => {
        if (res && res.code === 'SUCCESS') {
          this.latestAssignClue = res.result || {};
        } else {
          this.$message.error(res.msg || '获取跟进记录失败');
          this.latestAssignClue = {};
        }
      });
      // 指派路径
      getAssignPath({ clueId: this.clueId }).then((res) => {
        if (res && res.code === 'SUCCESS') {
          this.assignPath = res.result || { assignClueList: [] };
        } else {
          this.$message.error(res.msg || '获取指派路径失败');
          this.assignPath = { assignClueList: [] };
        }
      });
    },
    addRecord() {
      this.showRode = true;
    },
    addNewRecord() {
      // 重置表单
      this.recordForm = {
        currentState: 0,
        followDate: new Date().toISOString().split('T')[0],
        followDesc: '',
      };

      if (this.$refs.recordForm) {
        this.$refs.recordForm.resetFields();
      }

      this.showAddRecord = true;
    },
    submitRecord() {
      this.$refs.recordForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }

        try {
          const params = {
            clueId: this.clueId,
            clueDealState: this.recordForm.currentState,
            overview: this.recordForm.followDesc,
          };

          // 确保正确调用API
          const res = await addClueDealRecord(params);

          if (res && res.code === 'SUCCESS') {
            // 修正了拼写错误 SCUUESS -> SUCCESS
            this.$message.success('添加跟进记录成功');
            this.showAddRecord = false;

            // 重新加载跟进记录列表
            this.getFollowUpRecordList();
          }
        } catch (error) {
          // console.error('添加跟进记录失败:', error);
          // this.$message.error(error?.msg||'添加跟进记录失败，请稍后重试');
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tracke-info {
  overflow: scroll;
  width: 100%;
  font-family: puhuiti;
  .road {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-content: center;
    margin-bottom: 16px;
  }
  .record-form {
    background: rgba(216, 216, 216, 0.08);
    border-radius: 6px;
    margin-bottom: 20px;
    position: relative;
    .form-content {
      // padding: 24px 16px 8px;

      .info-item {
        padding: 24px 16px 0px 24px;
        // margin-bottom: 15px;
        // line-height: 1.5;
        font-family: puhuiti;
        font-size: 14px;
        &:last-child{
            padding-bottom: 24px;
        }
        .label {
          color: #f1f1f1;
          margin-right: 10px;
          min-width: 120px;
          display: inline-block;
          width: 100px;
          text-align: left;
        }

        .value {
          color: #a8abb2;
        }
        .status {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
        }
        .completed {
          background: rgba(224, 72, 72, 0.2);
          color: #e04848;
        }
        .ing {
          background: rgba(51, 112, 255, 0.2);
          color: #3370ff;
        }
        .success {
          background: rgba(76, 193, 105, 0.2);
          color: #4cc169;
        }

        .progress-tag {
          background: rgba(51, 112, 255, 0.2);
          padding: 2px 8px;
          border-radius: 2px;
          font-size: 12px;

          &.no-follow {
            background: rgba(255, 153, 0, 0.2);
            color: #ff9900;
          }
        }
      }
    }
  }

  .process-records {
    // background-color: #273047;
    border-radius: 4px;
    padding: 20px;
  }
  .records-header {
    margin-bottom: 20px;
    font-size: 14px;

    .record-count {
      font-family: puhuiti;
      color: #16d0ff;
      font-size: 14px;
      font-weight: bold;
    }
  }

  .timeline {
    position: relative;

    .timeline-item {
      display: flex;
      padding-bottom: 30px;
      position: relative;

      &:last-child {
        padding-bottom: 0;

        .time-line {
          display: none;
        }
      }

      .time-point {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #3370ff;
        margin-top: 10px;
        z-index: 2;

        &.active {
          box-shadow: 0 0 0 4px rgba(51, 112, 255, 0.2);
        }
      }

      .time-line {
        position: absolute;
        left: 5px;
        top: 20px;
        bottom: 0;
        width: 2px;
        background: #103974;
      }

      .timeline-content {
        flex: 1;
        margin-left: 20px;
        padding-top: 7px;
        .timeline-header {
          display: flex;
          justify-content: flex-start;
          margin-bottom: 15px;

          .date-time {
            color: #a8abb2;
            font-size: 14px;
          }

          .status {
            margin-left: 16px;
            display: flex;
            align-items: center;

            &.completed {
              .status-text {
                background: rgba(224, 72, 72, 0.2);
                color: #e04848;
              }

              .status-desc {
                color: #8392a5;
                margin-left: 5px;
                font-size: 12px;
              }
            }

            &.ing {
              .status-text {
                background: rgba(51, 112, 255, 0.2);
                color: #3370ff;
              }
            }
            &.success {
              .status-text {
                background: rgba(76, 193, 105, 0.2);
                color: #4cc169;
              }
            }

            .status-text {
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
            }
          }
        }

        .timeline-body {
          padding: 16px;
          background: rgba(216, 216, 216, 0.08);
          border-radius: 6px;
          .info-item {
            margin-bottom: 15px;
            // line-height: 1.5;
            font-family: puhuiti;
            font-size: 14px;
            .label {
              color: #f1f1f1;
              margin-right: 10px;
              min-width: 120px;
              display: inline-block;
              width: 100px;
              text-align: left;
            }

            .value {
              color: #a8abb2;
            }
          }
        }
      }
    }
  }
}
// 添加指派路径抽屉相关样式
.assign-path-drawer {
  .assign-path-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0 20px;
  }

  .timeline-container {
    flex: 1;
    overflow-y: auto;
    margin-top: 20px;
  }

  .timeline-item {
    position: relative;
    padding-left: 40px;
    margin-bottom: 30px;

    &:last-child {
      margin-bottom: 0;
    }

    &::before {
      content: '';
      position: absolute;
      left: 20px;
      top: 30px;
      bottom: -30px;
      width: 2px;
      background: #3370ff;
    }

    &:last-child::before {
      display: none;
    }

    .time-point {
      position: absolute;
      left: 13px;
      top: 10px;
      width: 16px;
      height: 16px;
      background: #09a1f3;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .dot-circle {
        width: 6px;
        height: 6px;
        background: white;
        border-radius: 50%;
      }
    }

    .timeline-date {
      font-size: 16px;
      color: #09a1f3;
      padding-top: 7px;
      margin-bottom: 10px;
    }

    .timeline-info {
      background: #011735;
      padding: 15px;
      border-radius: 4px;

      .assign-info,
      .follow-info {
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #fafafa;
          margin-right: 10px;
        }

        .value {
          color: #a8abb2;
        }
      }
    }
  }

  .drawer-footer {
    padding: 20px 0;
    display: flex;
    justify-content: center;
  }
}

::v-deep .assign-path-drawer {
  .el-drawer__header {
    padding: 15px 20px;
    margin-bottom: 0;
    font-size: 16px;
    color: #fafafa;
    font-weight: 500;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background-color: #071336;
  }

  .el-drawer__body {
    background-color: #071336;
    padding: 0;
    overflow: hidden;
  }
}

// 新增记录抽屉样式
.add-record-drawer {
  ::v-deep .el-drawer__header {
    padding: 15px 20px;
    margin-bottom: 0;
    font-size: 16px;
    color: #333;
    font-weight: 500;
    border-bottom: 1px solid #e4e7ed;
    background-color: #011735;
    color: #fafafa;
  }

  ::v-deep .el-drawer__body {
    background-color: #011735;
    padding: 0;
    overflow: hidden;
  }

  .add-record-content {
    height: 100%;
    padding: 20px;

    .record-form-container {
      .company-name {
        margin-bottom: 20px;

        span {
          font-size: 14px;
          color: #fafafa;
          opacity: 0.8;
        }

        .company-value {
          font-size: 16px;
          color: #fafafa;
          margin-top: 8px;
        }
      }

      .form-item-label {
        color: #fafafa;
        font-size: 14px;
        margin-bottom: 8px;

        &.required::before {
          content: '*';
          color: #f56c6c;
          margin-right: 4px;
        }
      }

      ::v-deep .el-form-item {
        margin-bottom: 20px;

        .el-form-item__label {
          color: #fafafa;
          font-size: 14px;
        }

        .el-form-item__error {
          color: #f56c6c;
        }

        .el-radio {
          color: #fafafa;
          margin-right: 20px;
        }

        .el-input__inner {
          background-color: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: #fafafa;
        }

        .el-textarea__inner {
          background-color: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: #fafafa;
        }

        .el-input__count {
          color: rgba(255, 255, 255, 0.6);
          background: transparent;
        }
      }

      .follow-person {
        color: #fafafa;
        font-size: 14px;
      }

      .form-actions {
        display: flex;
        justify-content: center;
        margin-top: 40px;

        .el-button {
          min-width: 100px;
          margin: 0 15px;
        }
      }
    }
  }
}
</style>
