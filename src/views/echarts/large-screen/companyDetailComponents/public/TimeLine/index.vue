<!-- 委托跟踪信息 -->
<template>
  <div class="tracke-info">
    <!-- 处理记录列表 -->
    <div
      v-if="records && records?.length > 0"
      class="process-records"
    >
      <div class="records-header">
        当前共
        <span class="record-count">{{ records.length }}</span> 条任务处理记录
      </div>

      <div class="timeline">
        <div
          v-for="(record, index) in records"
          :key="index"
          class="timeline-item"
        >
          <div class="time-point active" />
          <div class="time-line" />
          <div class="timeline-content">
            <div class="timeline-header">
              <span class="date-time">{{ record.followUpDate }}</span>
              <div
                :class="[
                  'status',
                  ['ing', 'success', 'completed'][record.clueDealState],
                ]"
              >
                <span class="status-text">{{
                  ['跟进中', '签约成功', '签约失败'][record.clueDealState]
                }}</span>
              </div>
            </div>
            <div class="timeline-body">
              <div class="info-item">
                <span class="label">处理人：</span>
                <span class="value">{{ record.fillInPerson }}</span>
              </div>
              <div class="info-item">
                <span class="label">处理概述：</span>
                <span class="value">{{ record.overview }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TimeLine',
  props: {
    records: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // records: [
      //   {
      //     time: '2025-07-20 20:00:00',
      //     status: 'completed',
      //     statusText: '签约失败',
      //     handler: '琬爱张谁',
      //     description: '内容内容内容内容内容内容内容内容内容内容内容内容。',
      //   },
      //   {
      //     time: '2025-07-20 20:00:00',
      //     status: 'success',
      //     statusText: '签约成功',
      //     handler: '琬爱张谁',
      //     description: '内容内容内容内容内容内容内容。',
      //   },
      // ],
    };
  },

};
</script>

<style lang="scss" scoped>
.tracke-info {
  overflow: scroll;
  width: 100%;
  font-family: puhuiti;

  .process-records {
    // background-color: #273047;
    border-radius: 4px;
    padding: 20px;
  }
  .records-header {
    margin-bottom: 20px;
    font-size: 14px;

    .record-count {
      font-family: puhuiti;
      color: #16d0ff;
      font-size: 14px;
      font-weight: bold;
    }
  }

  .timeline {
    position: relative;

    .timeline-item {
      display: flex;
      padding-bottom: 30px;
      position: relative;

      &:last-child {
        padding-bottom: 0;

        .time-line {
          display: none;
        }
      }

      .time-point {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #3370ff;
        margin-top: 10px;
        z-index: 2;

        &.active {
          box-shadow: 0 0 0 4px rgba(51, 112, 255, 0.2);
        }
      }

      .time-line {
        position: absolute;
        left: 5px;
        top: 20px;
        bottom: 0;
        width: 2px;
        background: #103974;
      }

      .timeline-content {
        flex: 1;
        margin-left: 20px;
        padding-top: 7px;
        .timeline-header {
          display: flex;
          justify-content: flex-start;
          margin-bottom: 15px;

          .date-time {
            color: #a8abb2;
            font-size: 14px;
          }

          .status {
            margin-left: 16px;
            display: flex;
            align-items: center;

            &.completed {
              .status-text {
                background: rgba(224, 72, 72, 0.2);
                color: #e04848;
              }

              .status-desc {
                color: #8392a5;
                margin-left: 5px;
                font-size: 12px;
              }
            }
            &.ing {
              .status-text {
                background: rgba(51, 112, 255, 0.2);
                color: #3370ff;
              }
            }
            &.success {
              .status-text {
                background: rgba(76, 193, 105, 0.2);
                color: #4cc169;
              }
            }

            .status-text {
              padding: 4px 8px;
              border-radius: 2px;
              font-size: 12px;
            }
          }
        }

        .timeline-body {
          padding: 16px ;
          background: rgba(216, 216, 216, 0.08);
          border-radius: 6px;
          .info-item {
            margin-bottom: 15px;
            // line-height: 1.5;
            font-family: puhuiti;
            font-size: 14px;
            .label {
              color: #f1f1f1;
              margin-right: 10px;
              min-width: 120px;
              display: inline-block;
              width: 100px;
              text-align: left;
            }

            .value {
              color: #a8abb2;
            }
          }
        }
      }
    }
  }
}
</style>
