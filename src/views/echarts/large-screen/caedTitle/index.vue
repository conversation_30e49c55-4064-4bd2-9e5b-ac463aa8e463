<template>
  <div class="card">
    <div :class="['card-left','left'+index]" />
    <div class="card-right">
      <div class="right-text">
        {{ title }}
      </div>
      <div class="number">
        {{ detail }}
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'CaedTitle',
  components: {},
  props: {
    title: {
      type: String,
      default:'', 
    },
    detail: {
      type: String,
      default: '',
    },
    index: {
      type: String,
      default: '',
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.card {
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.card-left {
  width: 77px;
  height: 77px;
  
 
}
.left1{
  background: center/contain no-repeat
  url('https://static.idicc.cn/cdn/pangu/assets/screen/new/card/icon2.svg');
  background-size: 90%;

}
.left2{
  background: center/contain no-repeat
  url('https://static.idicc.cn/cdn/pangu/assets/screen/new/card/icon1.svg');
  background-size: 90%;
}
.card-right {
  flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    margin-left: 1rem;
    width: 77px;
  .right-text {
    width: 100%;
    font-size: 16px;
    color: #d6deea;
    font-family: puhuiti;
  }
  .number {
    @include YouSheBiaoTi28();
  }
}
</style>
