<!-- 地图 - 企业列表 -  展示所有企业列表 -->
<template>
  <div class="detail-main">
    <div v-if="showFlag == 1">
      <div class="type-name">
        <span>{{ detailInfoParams.childId }}({{ totals }})</span>
        <div
          class="close"
          @click="closeDetail"
        >
          <i class="el-icon-close" />
        </div>
      </div>
      <div class="detail-sel">
        <!-- <div class="sel-main"> -->
        <!--           <div
            v-loading="loading"
            class="import-btn"
            @click="onExport"
          >
            导出
          </div> -->
        <!-- </div> -->
        <div class="detail-list">
          <div class="sel-main">
            <div class="input-info">
              <el-input
                v-model="selValue"
                placeholder="请输入企业名称"
                @input="isSearch"
              />
            </div>
            <div
              class="import-btn"
              @click="search"
            >
              搜索
            </div>
          </div>

          <div
            v-if="detailList.length > 0"
            v-loading="listLoading"
            element-loading-background="rgba(2, 28, 60, 0.8)"
          >
            <div
              v-for="(detail, idx) in detailList"
              :key="idx"
              class="detail"
            >
              <CompanyCard
                :detail-data="detail"
                :showd-time="true"
                :enterprise-show="enterpriseShow"
                :tag-show="detail.tagShow"
                :more-tag="detail.tagMore"
                @openDetailSingle="openDetailSingle"
                @attention="attention"
              />
            </div>
          </div>

          <div
            v-if="detailList.length > 0"
            class="ye"
          >
            <el-pagination
              small
              :current-page.sync="form.pageNum"
              :page-size.sync="form.pageSize"
              :total="+total"
              layout="prev, pager, next"
              @size-change="getEnterpriseList"
              @current-change="getEnterpriseList"
            />
          </div>
          <div
            v-else
            v-loading="listLoading"
            element-loading-background="rgba(2, 28, 60, 0.8)"
          >
            <div class="no-font">
              <NoData v-if="!listLoading" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="showFlag == 2">
      <AtlasDetailCompany
        :child-id="detailSingleParams.childId"
        :enterprise-label-type="enterpriseLabelType"
        :second-query-param="secondQueryParam"
        @closeDetail="closeDetailSingle"
        @getEnterpriseList="getEnterpriseList"
      />
    </div>
  </div>
</template>

<script>
import AtlasDetailCompany from "./companyDetail.vue"; // 图谱 - 企业详情
import { chartApi, getList, exportWay } from "../apiUrl";
import { updatainclusionIntention } from "@/api/CattractInvestment";
import { getPathId } from '@/utils/utils'
import CompanyCard from './companyCard/index.vue'; //  企业 card
import NoData from '@/views/overview/components/component/noData2';
export default {
  components: {
    AtlasDetailCompany,
    CompanyCard,
    NoData,
  },
  props: {
    detailNum: {
      type: Number,
      default: 0,
    },
    secondQueryParam: {
      type: String,
      default: ''
    },
    searchValue: {
      type: String,
      default: null
    },
    childId: {
      type: String,
      default: "",
    },
    detailInfoParams: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {
        type: "graph",
        level: 1,
        paraimsId: null,
        childId: null,
      },
    },
    cityData: {
      type: Array,
      default: function () {
        return [];
      },
    },
    searchValueData: {
      type: String,
      default: null,
    },
  },
  data () {
    return {
      enterpriseShow:false,
      detailSingleParams: {
        type: "graph",
        level: 3,
        paraimsId: null,
        childId: null,
        titleName: "",
      },
      enterpriseLabelType: null,
      seek: true,
      total: "",
      listLoading: false,
      loading: false,
      showFlag: 1,
      detailName: "地图识别行业", // 详情 标题
      selValue: "",
      detailTypeList: [
        { value: -1, label: "全部", active: false },
        { value: 1, label: "本地", active: true },
        { value: 2, label: "招商", active: false },
      ],
      detailTypeValue: -1,
      totals: "",
      detailList: [
        /* {logo:'企业控股',enterpriseName:'杭州企业控股有限公司',
        tag:['小微企业','小微企业'],
        industrySector:'这里是企业舆情摘要，点击可打开新标签页浏览详情',
        describe:[{info:'这里是企业舆情摘要，点击可打开新标签页浏览详情',time:'2022.10.01'},{info:'这里是企业舆情摘要，点击可打开新标签页浏览详情',time:'2022.10.01'},]},
        {logo:'企业控股',name:'杭州企业控股有限公司',
        tag:['小微企业','小微企业'],
        describe:[{info:'这里是企业舆情摘要，点击可打开新标签页浏览详情',time:'2022.10.01'},{info:'这里是企业舆情摘要，点击可打开新标签页浏览详情',time:'2022.10.01'},]
        } */
      ],
      form: {
        pageSize: 5,
        pageNum: 1,
      },
      top: false
    };
  },

  watch: {
    childId () {
      this.form.pageNum = 1;
      this.selValue = ''
      this.getEnterpriseList();
      this.showFlag = 1;
    },
    detailNum () { },
  },
  mounted () {
    this.getEnterpriseList();
    this.getscreenWidth()
  },
  beforeDestroy () { },
  methods: {
    getscreenWidth () {
      var screenWidth = window.innerWidth;
      if (screenWidth >= 1600)
      {
        this.top = true
      }
    },
    async attention(item) {
      const res = await updatainclusionIntention({
         clueSource: 1,
        uniCode: item?.unifiedSocialCreditCode,
      })
      if (res.code === 'SUCCESS')
      {
        const h = this.$createElement
        this.$message({
          message: h('div', null, [
            h('span', { style: "color:gray" }, '纳入意向成功，该企业已进入'),
            h('span', {
              style: "color:green;cursor:pointer", on: {
                click: () => {
                  this.skip()
                }
              }
            }, '产业招商-招商管理'),
            h('span', { style: 'color:white', }, '中')
          ]),
          type: 'success',
          duration: 5000,
          customClass: 'admin-tips-message',
        });
        this.getEnterpriseList();
      }

    },
    skip () {
      this.$message.closeAll(); //关闭message弹窗
      this.$router.push({ path: '/attractInvestment', query: { id: getPathId() || null, show: 1 } });
    },
    isSearch () {
      if (this.selValue == "")
      {
        this.form.pageNum = 1;
        this.getEnterpriseList();
      }
    },
    async search () {
      this.form.pageNum = 1;
      this.getEnterpriseList();
    },
    openDetailSingle (del) {
      this.enterpriseLabelType = del.enterpriseLabelType
      this.showFlag = 2;
      this.detailSingleParams.childId = del.id;
      this.detailSingleParams.titleName = del.enterpriseName;
    },
    closeDetailSingle () {
      this.showFlag = 1;
    },
    async seekfn () {
      this.seek = false;
      this.getEnterpriseList();
    },
    // 获取所有企业列表
    getEnterpriseList () {
      const noData = ['重庆市', '北京市', '上海市', '天津市']
      const id = this.$route.query?.id || getPathId() || null;
      if (!id)
      {
        return;
      }
      let province = "";
      let city = "";
      let area = "";
      if (this.cityData.length == 1)
      {
        province = this.detailInfoParams.childId;
        city = "";
        area = "";
      } else if (noData.includes(this.cityData[1].countArea))
      {
        province = this.cityData[1].countArea;
        city = this.cityData[1].countArea;
        area = this.detailInfoParams.childId
      } else if (this.cityData.length == 2)
      {
        province = this.cityData[1].countArea;
        city = this.detailInfoParams.childId;
        area = "";
      } else
      {
        province = this.cityData[1].countArea;
        city = this.cityData[2].countArea;
        area = this.detailInfoParams.childId;
      }
      let data = {
        province, //省
        city, //市
        area, //区
        //chainNodeId:this.detailInfoParams.paraimsId, // integer产业链节点id
        //chainNodeId:287, // integer产业链节点id
        id: this.detailInfoParams.paraimsId,
        pageSize: this.form.pageSize,
        pageNum: this.form.pageNum,
        //localEnterprise:0
        enterpriseName: this.selValue,
        chainNodeId: this.searchValue,
        secondQueryParam: this.secondQueryParam
      };
      this.listLoading = true
      return new Promise((resolve, reject) => {
        getList(chartApi.mapenterpriseList, data)
          .then((res) => {
            //this.listLoading=true
            this.detailList = res.records;
            this.totals = res.total;
            this.total = res.total;
            if (res.total > 10000)
            {
              this.total = 10000;
            }
            for (let i = 0; i < this.detailList.length; i++)
            {
              if (this.detailList[i].enterpriseLabelNames)
              {
                this.detailList[i].new =
                  this.detailList[i].enterpriseLabelNames.split(",");
                this.detailList[i].tagShow = this.detailList[i].new.slice(0, 2);
                if (this.detailList[i].tagShow.join('').length > 8)
                {
                  this.detailList[i].tagShow = [this.detailList[i].tagShow[0]];
                }
                this.detailList[i].tagMore = this.detailList[i].new.filter(item => !this.detailList[i].tagShow.includes(item));
              } else
              {
                this.detailList[i].new = null;
              }
            }
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          })
          .finally(() => {
            this.seek = true;
            this.listLoading = false
          });
      });
    },

    onExport () {
      const id = this.$route.query?.id || getPathId() || null;
      if (!id)
      {
        return;
      }
      let data = {
        area: this.detailInfoParams.childId, // integer产业链节点id,
        //pageSize:1, //integer 页码
        //pageNum:10, //integer 当前页
        //localEnterprise: this.detailTypeValue, //integer 是否本地企业
        id: this.detailInfoParams.paraimsId, //integer 机构产业链关系id
        enterpriseName: this.selValue, //string 企业名称
      };
      return new Promise((resolve, reject) => {
        this.loading = true;
        exportWay(data)
          .then((res) => {
            let blob = new Blob([res.data], {
              type: "text/csv,charset=UTF-8",
            });
            let objectUrl = URL.createObjectURL(blob);
            window.location.href = objectUrl;
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },

    // 关闭
    closeDetail () {
      this.$emit("closeDetail", { type: "detail" });
    },
  },
};
</script>
<style lang="scss" scoped>
@import './companyPublic.scss';
@import './detail-map.scss'
</style>
