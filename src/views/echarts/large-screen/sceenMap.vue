<template>
  <div class="screen-main">
    <img
      class="screen-bg"
      src="https://static.idicc.cn/cdn/pangu/atlasBgc.jpg"
    >
    <div class="screen-info">
      <!--产业地图  -->
      <Headers state="MapEcharts">
        <div slot="view-container">
          <slot name="screenTitleSearch" />
          <slot name="DetailInfo" />
          <div class="screen-content">
            <slot name="screenMain" />
          </div>
        </div>
      </Headers>
    </div>
  </div>
</template>

<script>
import DetailInfo from "./detail-info.vue";
import Headers from "@/components/header.vue";
import { getPathId } from "@/utils/utils";

export default {
  components: {
    // eslint-disable-next-line vue/no-unused-components
    DetailInfo,
    Headers,
  },
  props: {
    className: {
      type: String,
      default: "chart",
    },
  },

  data() {
    return {
      pageTypeName: "产业地图",
      name: "",
    };
  },
  computed: {},
  mounted() {},
  beforeDestroy() {},
  methods: {
    // skip(value) {
    //   if (value == "/nodata") {
    //     return this.$message("开发中~敬请期待");
    //   }
    //   const id =this.$route.query.id|| getPathId()|| null;
    //   this.$router.push({ path: value, query: { id } });
    //   // newRouter
    //   localStorage.setItem('routerQuery',id)
    // },
    // // 查询
    // OnSearch() {},
    // // 查看舆情
    // OnCheckOpinion() {},
    // // 查看统计
    // OnViewStatistics() {},
  },
};
</script>
<style lang="scss" scoped>
.navigation {
  .navigation-info {
    margin-left: 8rem;
    display: flex;

    .el-button {
      background: rgba(136, 200, 255, 0.2);
      height: 2.6rem;
      width: 8rem;
      border: 1px;
      font-size: 1rem;
      margin-left: 4rem;
      color: #fff;
      border-radius: 8px;
      position: relative;

      span {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .highlight {
      background: url(https://static.idicc.cn/cdn/pangu/assets/attract/title-button.png);
      background-size: cover;
    }

    :hover.el-button {
      background: url(https://static.idicc.cn/cdn/pangu/assets/attract/title-button.png);
      background-size: cover;
    }
  }
}

// 标题
.left-headline {
  .node-name {
    z-index: 99;
    font-weight: 600;
    margin-left: 7vw;
    top: 0px;
    left: 0px;

    .name-info {
      font-size: 1.6rem;
      color: #fff;
      line-height: 2.5rem;
      float: left;
      min-width: 10rem;
      margin-right: 2rem;

      .suffix {
        position: relative;
        font-size: 0.9rem;
        font-style: normal;
        margin-left: 1rem;
      }

      .suffix::after {
        position: absolute;
        content: "";
        background: #fff;
        height: 0.2rem;
        width: 0.2rem;
        top: 50%;
        left: -10px;
      }
    }

    .navigation {
      float: left;
      height: 2.4rem;
      line-height: 2.4rem;
    }
  }
}

::v-deep {
  .el-popper[x-placement^="bottom"] .popper__arrow {
    display: none;
  }

  .navigation-select {
    .el-input--suffix .el-input__inner {
      background: #0f2ba900;
      border: #468ae79e 1px solid;
      color: #fff;
      font-size: 0.8rem;
      height: 100%;
      height: 2.4rem;
      line-height: 2.4rem;
    }

    .el-select-dropdown__list {
      background-color: #091e76;
    }

    .el-popper[x-placement^="bottom"] {
      border: #3370ff 1px solid;
    }

    .el-input.is-focus {
      border: #468ae7d9 1px solid;
      border-radius: 3px;
    }

    .el-input__icon.el-icon-arrow-up {
      height: 2.4rem;
      line-height: 2.4rem;
    }

    .el-select-dropdown__item {
      color: #fff;
    }

    // 鼠标点击后移入的颜色
    .el-select-dropdown__item.selected {
      color: #fff;
    }

    // 下拉框移入的颜色
    .el-select-dropdown__item:hover {
      background-image: linear-gradient(to bottom right, #2484ef, #09217b);
      color: #fff;
    }

    // 移出不要丢失颜色
    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-image: linear-gradient(to bottom right, #2484ef, #09217b);
      color: #fff;
    }
  }
}

.search-main {
  float: right;
  display: flex;
  margin-top: 4rem;

  .input-icon {
    height: 1.2rem;
    width: auto;
    float: left;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin: auto;
    margin-left: 1.8rem;
    overflow: hidden;
    margin-right: 0rem;
  }

  .input-main {
    position: relative;
    // min-width: 33rem;
    height: 3rem;
  }

  .input-bg {
    width: auto;
    height: 2.4rem;
  }

  .input-info {
    position: absolute;
    z-index: 99;
    width: 100%;
    height: 100%;
    display: flex;
    height: 2.6rem;
    border-radius: 8px;
    line-height: 2.6rem;
    width: 100%;
    top: -0.1rem;

    .input-icon {
    }

    .value-info {
      float: left;
    }
  }

  .btn {
    width: 4.8rem;
    height: 3rem;

    img {
      top: 0.01rem;
      height: 2.4rem;
    }

    span {
      position: absolute;
      z-index: 99;
      text-align: center;
      line-height: 2.5rem;
      text-align: center;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      font-size: 0.8rem;
    }
  }
}

.none {
  width: 0px;
  height: 0px;
}

.screen-main {
  background-color: #070d61;
  width: 100vw;
  height: 100vh;
  position: relative;
  min-width: 1080px;

  .btn {
    position: relative;
    cursor: pointer;
    float: left;
    color: #fff;
    font-size: 1rem;
    height: 2.6rem;
    text-align: center;
    width: 6rem;

    img {
      position: absolute;
      z-index: 9;
      left: 0;
      top: -2px;
      width: 100%;
      height: 100%;
    }

    span {
      position: absolute;
      z-index: 99;
      text-align: center;
      line-height: 2.4rem;
      text-align: center;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
    }
  }

  .screen-bg,
  .screen-info {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
    overflow: hidden;
  }

  .screen-bg {
    background-image: linear-gradient(
      to bottom,
      #e4e4e421,
      #ffffff14 3%,
      #00800000 20%
    );
    z-index: 9;
  }

  .screen-info {
    z-index: 99;
    background: transparent;
    position: fixed;
    z-index: 999;
    position: relative;
  }

  .screen-title {
    position: absolute;
    // height: 80px;
    z-index: 99;
    width: 100%;
    position: relative;

    .title-bg-img {
      position: absolute;
      pointer-events: none;
      z-index: 9;
      width: 70%;
      height: 10rem;
    }

    .title-top-info {
      position: absolute;
      width: 100%;
      z-index: 99;
      display: flex;
      justify-content: space-between;
      padding: 0.6rem 0.8rem;
    }

    .el-dropdown-link {
      color: #fff;
      float: right;
      padding: 10px 0;
      margin-left: 1rem;
    }

    // 右侧查询
    .right-search {
      display: flex;
      padding-top: 0.2rem;

      .search-main {
        display: flex;

        .input-icon {
          height: 1.2rem;
          width: auto;
          float: left;
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          margin: auto;
          margin-left: 1.8rem;
          overflow: hidden;
          margin-right: 0rem;
        }

        .input-main {
          position: relative;
          // min-width: 33rem;
          height: 3rem;
        }

        .input-bg {
          width: auto;
          height: 2.4rem;
        }

        .input-info {
          position: absolute;
          z-index: 99;
          width: 100%;
          height: 100%;
          display: flex;
          height: 2.6rem;
          border-radius: 8px;
          line-height: 2.6rem;
          width: 100%;
          top: -0.1rem;

          .input-icon {
          }

          .value-info {
            float: left;
          }
        }

        .btn {
          width: 4.8rem;
          height: 3rem;

          img {
            top: 0.01rem;
            height: 2.4rem;
          }

          span {
            position: absolute;
            z-index: 99;
            text-align: center;
            line-height: 2.5rem;
            text-align: center;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            font-size: 0.8rem;
          }
        }
      }

      .check-other {
        display: flex;
        margin-left: 2rem;
      }

      .btn {
        float: left;
      }
    }

    // 左侧2按钮
    .shortcut-btn {
      position: absolute;
      top: 4.2rem;

      .btn {
        float: inherit;
        cursor: pointer;
        position: relative;
        width: 15rem;
        height: 4rem;
        margin-left: -2rem;
        margin-bottom: -0.3rem;
        overflow: hidden;

        img {
          width: 12rem;
          height: 6rem;
          left: 0px;
          top: 0rem;
        }

        span {
          font-size: 0.9rem;
          top: 1.67rem;
          left: -1rem;
          color: #ffffffb8;
        }
      }

      .active {
        span {
          color: #fff;
        }

        span::after {
          position: absolute;
          border: none;
          background: #fff;
          left: 4rem;
          top: 1rem;
          content: "";
          z-index: 999;
          width: 0.4rem;
          height: 0.4rem;
          border-radius: 50%;
        }
      }

      span::after {
        position: absolute;
        content: "";
        z-index: 9;
        border: none;
        background: #fff;
        width: 0.5rem;
        height: 0.5rem;
        border-radius: 4px;
        left: 1.2rem;
        top: 0.9rem;
      }
    }
  }

  .screen-content {
    width: 100%;
    height: calc(100vh - 60px);
    // position: absolute;
    // top: 60px;
  }
}

/* ::v-deep {
  .value-info {
    width: calc(100% - 6rem);

    .el-input.el-input--suffix {
      height: 100%;
    }
    .el-cascader__tags {
      background: transparent;
      .el-cascader__search-input {
        display: none;
      }
      .el-tag:not(.is-hit) {
        background: #ffffff42;
        color: #fff;
      }
      .el-icon-close {
        background: #5aafff82;
      }
    }

    .el-input {
      background: transparent;
    }
    .el-input__inner {
      height: 100%;
      background: transparent;
    }
    .el-cascader__search-input {
      background: transparent;
    }
  }
  .el-cascader-menu {
    background: #00112d !important;
  }
  .el-input__inner {
    background: transparent;
    color: #fff;
    line-height: 2.5rem;
    border: none;
    padding: 0 0.2rem;
    height: 100%;
    font-size: 0.8rem;
  }
  .el-cascader-menu {
    background: red;
  }
  .el-cascader-menu__list {
    background: #00112d;
  }
} */
</style>
