<!-- 图谱 - 企业选中 -  展示当前企业详情 -->
<template>
  <div
    v-if="enterpriseName"
    class="detail-main"
  >
    <div class="detail-sel">
      <div class="detail-list">
        <!-- 企业卡片 -->
        <div
          v-if="detail?.enterpriseName"
          class="companyCard"
        >
          <div
            class="close"
            @click="closeDetail"
          >
            <i class="el-icon-close" />
          </div>
          <CompanyCard
            :detail-data="detail"
            :showd-time="false"
            :active-state="activeState"
            :enterprise-show="enterpriseShow"
            :tag-show="tagShow"
            :more-tag="isMore"
            :from-path="fromPath"
            @openDetailSingle="openDetailSingle"
            @attention="attention"
          />
        </div>
        <!-- 下面详细Tab -->
        <div
          v-loading="tab5Loading"
          class="detailpage"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
        >
          <div class="tabs">
            <el-tabs
              v-model="istabs"
              class="custom-tabs"
              :style="{ width: '100%' }"
            >
              <el-tab-pane
                v-for="item in activeName"
                :key="item.id"
                :label="item.name"
                :name="item.id.toString()"
              >
                {{ item.content }}
              </el-tab-pane>
            </el-tabs>
            <!-- <div
              v-for="(item, index) in activeName"
              :key="index"
              :class="[istabs == item.id ? 'opttab' : 'tab', item.id == 6 ? (istabs == item.id ? 'opttab6' : 'tab6') : '']"
              @click="cutTabs(item)"
            >
              <span>
                {{ item?.name }}
              </span>
            </div> -->
          </div>
          <!-- 基本信息 -->
          <div
            v-if="istabs === '1'"
            class="detail2"
          >
            <div class="describe-info">
              <div class="del">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="name">
                      <span class="title">通讯电话</span>
                      <span class="span2">{{ detail?.mobile }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="name">
                      <span class="title"> 成立日期</span>
                      <span class="span2">{{ detail?.registerDate }}</span>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="name">
                      <span class="title">企业规模</span>
                      <span class="span2">{{ detail?.scale }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="name">
                      <span class="title">融资轮次</span>
                      <span class="span2">{{
                        detail?.enterpriseFinancingRoundsNames?.join("、") ||
                          "-"
                      }}</span>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="name">
                      <span class="title">法定代表人</span>
                      <span class="span2">{{ detail?.legalPerson }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="name">
                      <span class="title">登记状态</span>
                      <span class="span2">{{ detail?.registerStatus }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="name">
                      <span class="title">参保人数</span>
                      <span class="span2">{{
                        detail?.insuredPersonsNumber
                      }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="name">
                      <span class="title">专利个数</span>
                      <span class="span2">{{
                        detail?.patentNumber || "-"
                      }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <div class="name">
                      <span class="title">所在地区</span>
                      <div class="span2">
                        {{ detail?.region }}
                      </div>
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <div class="name">
                      <span class="title">所属行业</span>
                      <div class="span2">
                        {{ detail?.involved }}
                      </div>
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <div class="name">
                      <span class="title">企业类型</span>

                      <div class="span2">
                        {{ detail?.enterpriseType }}
                      </div>
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <div class="name">
                      <span class="title">注册资本</span>
                      <div class="span2">
                        {{ detail?.registeredCapital }}
                      </div>
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <div class="name">
                      <span class="title">统一社会信用代码</span>
                      <div class="span2">
                        {{ detail?.unifiedSocialCreditCode }}
                      </div>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="24">
                    <div class="name">
                      <span class="title">详细地址</span>
                      <div class="span2">
                        {{ detail?.enterpriseAddress }}
                      </div>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="24">
                    <div
                      class="name"
                      style="min-height: 200px"
                    >
                      <span class="title">经营范围</span>
                      <div class="span2">
                        {{ detail?.businessScope }}
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
          <!-- 企业动态 -->
          <div
            v-if="istabs === '2'"
            class="detail2"
          >
            <div v-if="detail.eventList && detail.eventList.length > 0">
              <div
                v-for="(describe, idx) in detail.eventList"
                :key="idx"
                class="describe-info"
              >
                <div class="Summary">
                  <div class="newsSummary">
                    <div class="ttle">
                      {{ describe.eventTitle }}
                    </div>
                    <div class="times">
                      {{ describe.publishDate | time }}
                    </div>
                    <div class="content">
                      {{ describe.eventContent }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div
              v-else
              class="nodata"
            >
              <p>暂无数据</p>
            </div>
          </div>
          <!-- 走访记录 -->
          <div
            v-if="istabs == 3"
            class="detail2 detail3"
          >
            <div class="addfollow">
              <span class="total">
                当前共<span class="int">{{ record.total }}</span>条走访记录
              </span>
              <el-popover
                placement="bottom"
                width="450"
                trigger="click"
                popper-class="el_popover_class popover_tip"
              >
                <div class="popover_tip_content">
                  <div class="popover_tip_header">
                    新增记录
                  </div>
                  <el-form
                    ref="form"
                    label-position="left"
                    label-width="80px"
                    :model="form"
                    hide-required-asterisk
                    :rules="rules"
                  >
                    <el-form-item
                      prop="followUpDate"
                      label="走访日期:"
                    >
                      <div class="block">
                        <el-date-picker
                          v-model="form.followUpDate"
                          value-format="timestamp"
                          type="date"
                          placeholder="选择日期"
                          :picker-options="pickerOptions"
                        />
                      </div>
                    </el-form-item>
                    <el-form-item
                      prop="followUpPersonId"
                      label="走访人："
                    >
                      {{ $store.getters.user.realName }}
                    </el-form-item>
                    <el-form-item
                      prop="havaInvestmentIntention"
                      label="投资意向:"
                    >
                      <el-radio
                        v-model="form.havaInvestmentIntention"
                        label="true"
                      >
                        有投资意向
                      </el-radio>
                      <el-radio
                        v-model="form.havaInvestmentIntention"
                        label="false"
                      >
                        无投资意向
                      </el-radio>
                    </el-form-item>
                    <el-form-item
                      prop="overview"
                      label="走访概述:"
                    >
                      <el-input
                        v-model="form.overview"
                        type="textarea"
                        maxlength="150"
                        show-word-limit
                        :autosize="{ minRows: 2, maxRows: 3 }"
                      />
                    </el-form-item>
                  </el-form>
                  <el-button
                    type="primary"
                    size="mini"
                    @click="preserve(row)"
                  >
                    保存
                  </el-button>
                </div>
                <el-button
                  slot="reference"
                  size="small"
                  class="smallBtn"
                  icon="el-icon-add"
                >
                  新增记录
                </el-button>
              </el-popover>
            </div>
            <div class="followList">
              <div v-if="followList.length > 0">
                <div
                  v-for="(item, index) in followList"
                  :key="index"
                  class="followList-el"
                >
                  <div class="followList-item">
                    <span>
                      走访日期：{{ item.followUpDate.split(" ")[0] }}
                      <!--     跟进日期：{{ item.followUpDate.split(' ')[0] }} -->
                    </span>
                    <span class="ovo">
                      走访人：
                      <span>
                        {{ item.fillInPerson }}
                      </span>
                    </span>
                    <!-- <el-tooltip
                      class="item"
                      effect="dark"
                      :content="item.fillInPerson"
                      placement="top-start"
                      popper-class="tag-popover"
                    > -->

                    <!-- </el-tooltip> -->
                  </div>
                  <div class="followList-item">
                    <span>
                      投资意向：
                      <span
                        :class="
                          item.havaInvestmentIntention == true
                            ? 'circleText'
                            : 'nocircleText'
                        "
                      ><i
                         :class="
                           item.havaInvestmentIntention == true
                             ? 'circle'
                             : 'nocircle'
                         "
                       />

                        {{
                          item.havaInvestmentIntention == true
                            ? "有投资意向"
                            : "无投资意向"
                        }}
                      </span>
                    </span>
                  </div>
                  <div class="followList-item">
                    <span> 走访概述：{{ item.overview }} </span>
                  </div>
                </div>
              </div>
              <div v-else>
                <p
                  style="text-align: center; margin-top: 3rem; font-size: 16px"
                >
                  暂无走访记录
                </p>
              </div>
            </div>
            <div class="ye">
              <el-pagination
                small
                layout="prev, pager, next"
                :total="+record.total"
                :current-page.sync="record.pageNum"
                :page-size.sync="record.pageSize"
                @size-change="followUpRecordPage"
                @current-change="followUpRecordPage"
              />
            </div>
          </div>
          <!-- 360洞察 -->
          <div
            v-if="istabs === '4'"
            class="detail2"
          >
            <div class="detail4-info">
              <div class="detail4-card">
                <CaedTitle
                  title="快速成长指数"
                  :detail="detail?.growthIndex || '-'"
                  index="1"
                />
              </div>
              <div class="detail4-card">
                <CaedTitle
                  title="扩张意愿指数"
                  :detail="detail?.expansionIndex || '-'"
                  index="2"
                />
              </div>
            </div>

            <div class="detail4-list">
              <div v-if="detail?.chainDTOS && detail?.chainDTOS.length > 0">
                <div
                  v-for="(item, index) in detail?.chainDTOS"
                  :key="index"
                  class="list-title"
                >
                  <PointTitle :right-text="item.chainName" />

                  <div class="describeItems">
                    <div
                      v-for="(itemlist, index2) in item?.chainNodeList"
                      :key="index2"
                      class="describe-list"
                    >
                      <span class="describeTag">
                        {{ itemlist?.nodeName }}
                      </span>
                      <div class="describeTagName">
                        <span>{{ itemlist?.productNames?.join(",") }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-if="detail?.introduction !== '-' || !detail?.introduction"
              class="card-title"
            >
              <PointTitle right-text="企业简介" />
            </div>
            <div
              v-if="detail?.introduction !== '-' || !detail?.introduction"
              class="detail4-peopleList introduce"
            >
              <div class="list-items line-height">
                {{ detail?.introduction }}
              </div>
            </div>

            <div
              v-if="detail?.associationList?.length > 0"
              class="card-title"
            >
              <PointTitle right-text="商会/协会/学会/校友会" />
            </div>
            <div class="detail4-peopleList">
              <div
                v-for="(item, index) in detail?.associationList"
                :key="index"
                class="list-items"
              >
                <div class="list-items-name">
                  <div class="icon" />
                  <div class="name">
                    {{ item?.relateName }}
                  </div>
                  <span
                    v-if="item?.positions && item?.positions?.length !== 0"
                    class="positions"
                  >
                    {{
                      item?.positions?.length != 0 && item?.positions != null
                        ? item?.positions?.join(",")
                        : ""
                    }}
                  </span>
                </div>
                <div class="items-detail">
                  <div v-if="item?.ancestorHome">
                    籍贯：
                    <span>
                      {{ item?.ancestorHome ? item?.ancestorHome : "-" }}
                    </span>
                  </div>
                  <div
                    v-if="item?.schools?.length != 0 && item?.schools != null"
                  >
                    毕业院校：
                    <span>
                      {{
                        item?.schools?.length != 0 && item?.schools != null
                          ? item?.schools?.join("、")
                          : "-"
                      }}
                    </span>
                  </div>
                  <div
                    v-if="
                      item?.commerceNames?.length != 0 &&
                        item?.commerceNames != null
                    "
                  >
                    所属商会：
                    <span>
                      {{
                        item?.commerceNames?.length != 0 &&
                          item?.commerceNames != null
                          ? item?.commerceNames?.join("、")
                          : "-"
                      }}
                    </span>
                  </div>
                  <div
                    v-if="
                      item?.alumniNames?.length != 0 &&
                        item?.alumniNames != null
                    "
                  >
                    所属校友会：
                    <span>
                      {{
                        item?.alumniNames?.length != 0 &&
                          item?.alumniNames != null
                          ? item?.alumniNames?.join("、")
                          : "-"
                      }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 招商策略 -->
          <div
            v-if="istabs === '5'"
            class="detail2"
          >
            <div class="describe-info">
              <div
                class="btn"
                style="width: 100%"
              >
                <div
                  v-for="(item, index) in modelTypelist"
                  :key="index"
                  :class="['leftBtn', active == item.modelType ? 'active' : '']"
                  @click="changeDataView(item.modelType)"
                >
                  {{ item.modelName }}
                </div>
              </div>
              <div
                v-if="active == 1"
                class="strategy"
              >
                <div
                  v-if="Enterprise?.recommend?.relationText"
                  class="descriptions"
                >
                  <p class="value">
                    亲缘信息
                  </p>
                  <span
                    class="des_span"
                    v-html="removeMarkdown(Enterprise?.recommend?.relationText)"
                  />
                </div>
                <div
                  v-if="Enterprise?.recommend?.recommendReason"
                  class="descriptions"
                >
                  <span
                    class="des_span"
                    v-html="
                      removeMarkdown(Enterprise?.recommend?.recommendReason)
                    "
                  />
                </div>
              </div>
              <div
                v-if="active == 3"
                class="strategy"
              >
                <div
                  v-if="Enterprise?.recommend?.recommendReason"
                  class="descriptions"
                >
                  <p class="value">
                    推荐理由
                  </p>
                  <span
                    class="des_span"
                    v-html="
                      removeMarkdown(Enterprise?.recommend?.recommendReason)
                    "
                  />
                </div>
                <div
                  v-if="Enterprise?.recommend?.modelReason"
                  class="descriptions"
                >
                  <p class="value">
                    AIR分析观点
                  </p>
                  <span
                    class="des_span"
                    v-html="removeMarkdown(Enterprise?.recommend?.modelReason)"
                  />
                </div>
                <!-- <div
                  v-if="Enterprise?.recommend?.enterpriseContact"
                  class="descriptions"
                >
                  <p class="value">
                    招商策略方式
                  </p>
                  <span
                    class="des_span"
                    v-html="
                      removeMarkdown(Enterprise?.recommend?.enterpriseContact)
                    "
                  />
                </div> -->
              </div>
              <div
                v-if="active == 5"
                class="strategy"
              >
                <div
                  v-if="Enterprise?.recommend?.modelReason"
                  class="descriptions"
                >
                  <p class="value">
                    哒达招商扫描
                  </p>
                  <span
                    class="des_span"
                    v-html="removeMarkdown(Enterprise?.recommend?.modelReason)"
                  />
                </div>
                <div
                  v-if="Enterprise?.recommend?.recommendReason"
                  class="descriptions"
                >
                  <span
                    class="des_span"
                    v-html="
                      removeMarkdown(Enterprise?.recommend?.recommendReason)
                    "
                  />
                </div>
                <!-- <div
                  v-if="Enterprise?.recommend?.enterpriseContact"
                  class="descriptions"
                >
                  <p class="value">
                    招商触达途径
                  </p>
                  <span
                    class="des_span"
                    v-html="
                      removeMarkdown(Enterprise?.recommend?.enterpriseContact)
                    "
                  />
                </div> -->
              </div>
              <div
                v-if="active == 7"
                class="strategy"
              >
                <div
                  v-if="
                    Enterprise?.recommend?.talentName ||
                      Enterprise?.recommend?.enterpriseAddress ||
                      Enterprise?.recommend?.researchField
                  "
                  class="descriptions"
                >
                  <p class="value">
                    人才关联
                  </p>
                  <div
                    v-if="Enterprise?.recommend?.talentName"
                    class="des_span"
                    style="margin-bottom: 10px"
                  >
                    人才姓名：{{ Enterprise?.recommend?.talentName }}
                  </div>
                  <div
                    v-if="Enterprise?.recommend?.enterpriseAddress"
                    class="des_span"
                    style="margin-bottom: 10px"
                  >
                    机构所在地：{{ Enterprise?.recommend?.enterpriseAddress }}
                  </div>
                  <div
                    v-if="Enterprise?.recommend?.researchField"
                    class="des_span"
                    style="margin-bottom: 10px"
                  >
                    研究方向：{{ Enterprise?.recommend?.researchField }}
                  </div>
                </div>
                <div
                  v-if="Enterprise?.recommend?.recommendReason"
                  class="descriptions"
                >
                  <span
                    class="des_span"
                    v-html="
                      removeMarkdown(Enterprise?.recommend?.recommendReason)
                    "
                  />
                </div>
                <div
                  v-if="Enterprise?.recommend?.enterpriseContact"
                  class="descriptions"
                >
                  <p class="value">
                    触达方式
                  </p>
                  <span
                    class="des_span"
                    v-html="
                      removeMarkdown(Enterprise?.recommend?.enterpriseContact)
                    "
                  />
                </div>
              </div>
              <div
                v-if="active == 8"
                class="strategy"
              >
                <div
                  v-if="Enterprise?.recommend?.modelReason"
                  class="descriptions"
                >
                  <p class="value">
                    经济高价值专利培育项目
                  </p>
                  <span
                    class="des_span"
                    v-html="removeMarkdown(Enterprise?.recommend?.modelReason)"
                  />
                </div>
                <div
                  v-if="Enterprise?.recommend?.recommendReason"
                  class="descriptions"
                >
                  <span
                    class="des_span"
                    v-html="
                      removeMarkdown(Enterprise?.recommend?.recommendReason)
                    "
                  />
                </div>
                <!-- <div
                  v-if="Enterprise?.recommend?.enterpriseContact"
                  class="descriptions"
                >
                  <p class="value">
                    招商策略方式
                  </p>
                  <span
                    class="des_span"
                    v-html="Enterprise?.recommend?.enterpriseContact"
                  />
                </div> -->
              </div>
            </div>
            <div style="height: 30px" />
          </div>
          <!-- 任务处理记录 -->
          <div
            v-if="istabs === '6'"
            class="detail2 detail3"
          >
            <div
              v-if="clueDealRecordList?.length"
              class="addfollow"
            >
              <div class="card">
                <div class="cardTop">
                  <div class="span1">
                    指派日期：<span class="span2">{{
                      pathObj.assignDate
                    }}</span>
                  </div>
                  <div class="span1">
                    跟进人：<span class="span2">{{
                      pathObj.beAssignPerson
                    }}</span>
                    <el-popover
                      placement="bottom"
                      width="450"
                      trigger="click"
                      popper-class="el_popover_class popover_tip"
                    >
                      <div class="popover_tip_content">
                        <div class="popover_tip_header">
                          指派路径
                        </div>
                        <div
                          v-if="pathList && pathList.length >= 1"
                          class="const"
                        >
                          <div
                            v-for="(item, index) in pathList"
                            :key="index"
                            class="contents"
                          >
                            <div class="text-box">
                              <span class="titletext">{{
                                item.assignDateStr
                              }}</span>
                            </div>
                            <div class="assignor">
                              <div class="time">
                                指派人：{{ item.assignPerson }}
                              </div>
                              跟进人：{{ item.beAssignPerson }}
                            </div>
                          </div>
                        </div>
                        <el-button
                          type="primary"
                          size="mini"
                          @click="cancel"
                        >
                          确定
                        </el-button>
                      </div>
                      <span
                        slot="reference"
                        class="spanBtn"
                      >指派路径</span>
                    </el-popover>
                  </div>
                </div>
                <div class="span1">
                  线索流转备注：<span class="span2">{{
                    pathObj.assignRemark
                  }}</span>
                </div>
              </div>
              <div
                class="total"
                style="margin-top: 20px"
              >
                当前共<span class="int">{{
                  clueDealRecordList?.length || 0
                }}</span>条任务处理记录
              </div>
              <el-timeline style="margin-top: 20px">
                <el-timeline-item
                  v-for="(activity, index) in clueDealRecordList"
                  :key="index"
                  hide-timestamp
                >
                  <template slot="dot">
                    <div class="dot">
                      <div class="dot-raido">
                        <div class="dit" />
                      </div>
                    </div>
                  </template>
                  <div class="dot-box">
                    <div class="timeView">
                      <div class="time">
                        {{ activity?.dealDateStr }}
                      </div>
                      <div
                        :class="['stateDot', `stateDot${activity.dealState}`]"
                      />
                      <div :class="['state', `state${activity.dealState}`]">
                        {{
                          activity.dealState == 0
                            ? "待处理"
                            : activity.dealState == 1
                              ? "处理中"
                              : "已完成"
                        }}
                      </div>
                    </div>
                    <div
                      class="card"
                      style="margin-top: 8px; margin-bottom: 8px"
                    >
                      <div class="span1">
                        处理人：<span class="span2">{{
                          activity?.dealPerson || "-"
                        }}</span>
                      </div>
                      <div class="span1">
                        处理概述：<span class="span2">{{
                          activity.remark || "-"
                        }}</span>
                      </div>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
          <!-- 自行跟进记录 -->
          <div
            v-if="istabs === '7'"
            class="detail2 detail3"
          >
            <FollowUpRecord
              :clue-id="isinterview ? rowID : detail?.clueId"
              :selected-row="selectedRow"
            />
          </div>
          <!-- 委托跟踪信息 -->
          <div
            v-if="istabs === '8'"
            class="detail2 detail3"
          >
            <TrackeInfo :uni-code="detail?.unifiedSocialCreditCode" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { chartApi, getDetail } from "../apiUrl";
import { opinionAPI, informationlistAPI } from "@/api/Feelings";
import {
  addfollowUpRecordAPI,
  getIndustryChainIdByIdAPI,
} from "@/api/CattractInvestment";
import { updatainclusionIntention } from "@/api/CattractInvestment";
import CompanyCard from "./companyCard/index.vue"; //  企业 card

import { getPathId } from "@/utils/utils";
import {
  getByEnterpriseIdAPI_V2, // 招商策略
  getModelTypesAPI,
  getClueDealRecordAPI, // 线索处理记录
  clueAssignRecordAPI, // 指派路径
} from "../apiUrl";
import { formatDate } from "@/utils/utils";
import CaedTitle from "@/views/echarts/large-screen/caedTitle/index.vue";
// import PointTitle from '@/views/echarts/large-screen/pointTitle/index.vue';
import PointTitle from "./pointTitle";
import FollowUpRecord from "./companyDetailComponents/FollowUpRecord";
import TrackeInfo from "./companyDetailComponents/TrackeInfo";

export default {
  components: {
    CompanyCard,
    CaedTitle,
    PointTitle,
    FollowUpRecord,
    TrackeInfo,
  },
  filters: {
    time: function (value) {
      return formatDate("yyyy-MM-dd", new Date(+value));
    },
  },
  props: {
    cylId: {
      type: String,
      default: null,
    },
    childId: {
      type: String,
      default: null,
    },
    rowID: {
      type: String,
      default: null,
    },
    isinterview: {
      type: Boolean,
      default: false,
    },
    firmTypedetail: {
      type: Object,
      default: () => {},
    },
    secondQueryParam: {
      type: String,
      default: "",
    },
    recommendRegionCode: {
      type: String,
      default: "",
    },
    selectedRow: {
      type: Object,
      default: () => {},
    },
    fromPath: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      enterpriseLabelType: "",
      enterpriseName: "",
      enterpriseNews: {
        list: [],
      },
      new: [],
      tagShow: [],
      isMore: [],
      activeName: [
        { name: "基本信息", id: "1" },
        { name: "招商策略", id: "5" },
        { name: "企业动态", id: "2" },
        //{ name: "跟进记录", id: 3 },
        { name: "360洞察", id: "4" },
        { name: "自行跟进记录", id: "7" },
        { name: "委托跟踪信息", id: "8" },
        // { name: "任务处理", id: '6' },
      ],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      record: {
        pageNum: 1,
        pageSize: 3,
        total: 0,
      },
      form: {},
      rules: {
        followUpDate: [
          { required: true, message: "走访日期不能为空", trigger: "blur" },
        ],
        havaInvestmentIntention: [
          { required: true, message: "投资意向不能为空", trigger: "blur" },
        ],
        overview: [
          { required: true, message: "走访概述不能为空", trigger: "blur" },
        ],
      },
      followList: [],
      istabs: "1", // 修改为字符串类型

      pageNum: 1,
      pageSize: 10,
      pages: "",
      total: "",
      detailName: "地图识别行业", // 详情 标题
      selValue: "",
      detailTypeList: [
        { value: -1, label: "全部" },
        { value: 1, label: "本地" },
        { value: 2, label: "招商" },
      ],
      detailTypeValue: -1,
      detail: {
        list: [],
        businessInfoDTO: {},
        orgEnterpriseDTO: {},
        /* logo:'企业控股',name:'杭州企业控股有限公司',
        tag:['小微企业','小微企业'],
        businessList:[
          {name:'成立日期',value:'2019-10-21'},
          {name:'成立日期',value:'2019-10-21'},
          {name:'成立日期',value:'2019-10-21'},
          {name:'成立日期',value:'2019-10-21'},
        ],
      describe:[{info:'这里是企业舆情摘要，点击可打开新标签页浏览详情',time:'2022.10.01'},{info:'这里是企业舆情摘要，点击可打开新标签页浏览详情',time:'2022.10.01'},] */
      },
      activeState: "",
      enterpriseShow: false,
      modelType: "",
      modelTypelist: [],
      Enterprise: {},
      active: 0,
      tab5Loading: false,

      clueDealRecordList: [],
      pathList: [], //指派路径列表
      pathObj: "",
    };
  },
  watch: {
    childId() {
      this.getEnterprise();
      this.feelings();
      this.istabs = "1";
    },
  },

  mounted() {
    this.getEnterprise();
    this.feelings();
  },
  beforeDestroy() {
    this.istabs = "1";
  },
  methods: {
    openDetailSingle() {},
    removeMarkdown: function (value) {
      let data = value.replaceAll("*", "").replaceAll("-", "");
      // console.log(data, "dataer");
      return data;
      // .relaceAll(""); // Remove HTML tags
      // RemoveMarkdown(value);
    },
    changeDataView(modelType) {
      if (modelType != this.active) {
        this.active = modelType;
        this.getByEnterprise();
      }
    },
    showPopover(item) {
      this.enterpriseShow = item.state;
      this.activeState = item.id;
    },
    async attention(item) {
      const res = await updatainclusionIntention({
        clueSource: 1,
        uniCode: item?.unifiedSocialCreditCode,
      });
      if (res.code === "SUCCESS") {
        //this.$message.success("关注企业成功")
        const h = this.$createElement;
        this.$message({
          message: h("div", null, [
            h("span", { style: "color:white" }, "纳入意向成功，该企业已进入"),
            h(
              "span",
              {
                style: "color:#11CE66;cursor:pointer",
                on: {
                  click: () => {
                    this.skip();
                  },
                },
              },
              "产业招商-招商管理"
            ),
            h("span", { style: "color:white" }, "中"),
          ]),
          type: "success",
          duration: 5000,
          customClass: "admin-tips-message",
        });
        this.$emit("formationList");
        this.getEnterprise();
      }
    },
    skip() {
      this.$message.closeAll(); //关闭message弹窗
      this.$router.push({
        path: "/attractInvestment",
        query: { id: getPathId() || null, show: 1 },
      });
    },
    cutTabs(item) {
      this.istabs = item.id.toString(); // 确保转换为字符串
    },
    // // 获取跟进记录列表
    async followUpRecordPage() {
      //   const res = await getFollowUpRecord({
      //     clueId: this.isinterview ? this?.rowID : this.detail?.clueId || null,
      //     // pageNum: this.record.pageNum,
      //     // pageSize: this.record.pageSize,
      //   });
      //   this.followList = res.result.records;
      //   this.record.total = res.result.total;
    },
    // 新增跟进记录
    async preserve() {
      await this.$refs.form.validate();
      await addfollowUpRecordAPI({
        clueId: this.isinterview ? this.rowID : this.detail?.clueId || null,
        followUpDate: this.form.followUpDate, //跟进日期
        havaInvestmentIntention: this.form.havaInvestmentIntention, //投资意向
        overview: this.form.overview, //跟进概述
      });
      document.body.click();
      this.$message({
        message: "走访记录新增成功！",
        type: "success",
        customClass: "admin-tips-message",
        duration: 3000,
        showClose: true,
        center: false, // 是否居中
      });
      this.form = {};
      this.record.pageNum = 1;
      // this.followUpRecordPage();
    },
    // 获取企业详情
    getEnterprise() {
      // console.log(this.childId, "childId");
      const id = this.$route.query?.id || getPathId() || null;
      if (!id) {
        return;
      }
      this.tab5Loading = true;
      let data = {
        enterpriseId: this.childId, // integer产业链节点id,
        secondQueryParam: this.secondQueryParam || "",
        orgIndustryChainRelationId:
          this.$route.query?.id || getPathId() || null,
      };
      return new Promise((resolve, reject) => {
        getDetail(chartApi.enterpriseParticularsV2, data)
          .then(async (res) => {
            this.detail = res;
            this.activeName = [
              { name: "基本信息", id: 1 },

              { name: "360洞察", id: 4 },
              // { name: "招商策略", id: 5 },
              // { name: '走访记录', id: 3 },
            ];

            if (res.eventList && res.eventList.length > 0) {
              this.activeName.push({ name: "企业动态", id: 2 });
            }

            if (res.isInvestClue) {
              this.activeName.push({ name: "自行跟进记录", id: "7" });
            }
            if (!this.isinterview && this?.detail?.clueId !== null) {
              this.followUpRecordPage();
            }

            if (this?.detail?.entrustOrNot) {
              this.activeName.push({ name: "委托跟踪信息", id: "8" });
            }

            this.modelTypelist = await getModelTypesAPI({
              id: this.childId,
              regionCode: this.recommendRegionCode,
            });
            if (this.modelTypelist.length > 0) {
              let chainId = await getIndustryChainIdByIdAPI({
                id: getPathId(),
              });
              const filteredData = this.modelTypelist
                .map((item) => {
                  const filteredChains = item.chains.filter(
                    (chain) => chain.id == chainId.result
                  );
                  return filteredChains.length > 0
                    ? { ...item, chains: filteredChains }
                    : null;
                })
                .filter((item) => item !== null);
              this.modelTypelist = filteredData;
              if (this.modelTypelist.length == 0) {
                // console.log("无匹配数据");
              } else {
                // console.log(filteredData, 'filteredData');
                this.active = this.modelTypelist[0].modelType;

                this.activeName.splice(1, 0, { name: "招商策略", id: "5" });
                let params = {
                  enterpriseId: this.childId,
                  modelType: this.active,
                  chainId: chainId.result || "",
                  regionCode: this.recommendRegionCode,
                };
                this.Enterprise = {};
                getByEnterpriseIdAPI_V2(params).then((res2) => {
                  // this.$set(this, 'Enterprise', res2);
                  this.Enterprise = res2;
                  // console.log(res2, 'Enterprise222');
                  for (const key in this.Enterprise.recommend) {
                    if (typeof this.Enterprise.recommend[key] === "string") {
                      this.Enterprise.recommend[key] =
                        this.Enterprise.recommend[key].replace(/\n/g, "<br>");
                    }
                  }
                  this.tab5Loading = false;
                });
              }
            }

            // 任务处理记录
            let that = this;
            // await getClueDealRecordAPI({
            //   enterpriseId: this.childId,
            // }).then((_res) => {
            //   if (
            //     _res?.dealResult &&
            //     _res.dealResult.records &&
            //     _res.dealResult.records.length > 0
            //   ) {
            //     that.activeName.push({ name: '任务处理记录', id: '6' });
            //     that.clueDealRecordList = _res.dealResult.records;
            //     this.pathObj = _res;
            //     that.designatetask(_res.clueId);
            //   }
            // });

            this.tab5Loading = false;
            // 省市区合集
            this.detail.region = res?.province + res?.city + res?.area;
            // 所属行业合集
            this.detail.involved =
              res?.nationalStandardIndustry +
              res?.nationalStandardIndustryBig +
              res?.nationalStandardIndustryMiddle;
            if (res?.enterpriseLabelNames) {
              this.new = res?.enterpriseLabelNames?.toString();
              this.tagShow = res?.enterpriseLabelNames.slice(0, 2);
              let data = res?.enterpriseLabelNames.slice(0, 2);
              if (data.join("").length > 8) {
                this.tagShow = [res?.enterpriseLabelNames[0]];
              }
              this.isMore =
                res?.enterpriseLabelNames?.length > this.tagShow?.length
                  ? res?.enterpriseLabelNames?.slice(
                      this.tagShow?.length,
                      res?.enterpriseLabelNames?.length
                    )
                  : [];
              //   res?.enterpriseLabelNames?.filter(
              //   (item) => !this.tagShow.includes(item)
              // );
            }
            this.enterpriseLabelType = res.enterpriseLabelType;
            this.enterpriseName = res.enterpriseName;
            resolve(res);
          })
          .catch((error) => {
            this.tab5Loading = false;
            reject(error);
          });
      });
    },
    async getByEnterprise() {
      this.tab5Loading = true;
      let chainId = await getIndustryChainIdByIdAPI({ id: getPathId() });
      let params = {
        enterpriseId: this.childId,
        modelType: this.active,
        chainId: chainId.result || "",
        regionCode: this.recommendRegionCode,
      };
      this.Enterprise = {};
      getByEnterpriseIdAPI_V2(params).then((res) => {
        this.Enterprise = res;
        // console.log(this.Enterprise, "Enterprise");

        for (const key in this.Enterprise.recommend) {
          if (typeof this.Enterprise.recommend[key] === "string") {
            this.Enterprise.recommend[key] = this.Enterprise.recommend[
              key
            ].replace(/\n/g, "<br>");
          }
        }
        this.tab5Loading = false;
      });
    },
    async feelings() {
      const res = await informationlistAPI({
        enterpriseId: this.childId,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      });
      this.enterpriseNews = res.result;
      this.$set(this.enterpriseNews, "list", res.result.records);
      this.total = res.result.totalNum;
    },
    // 关闭
    closeDetail() {
      this.istabs = "1";
      this.$emit("closeDetail");
      this.$emit("getEnterpriseList");
    },
    // 指派路径
    async designatetask(clueId) {
      await clueAssignRecordAPI({
        pageNum: 1,
        pageSize: 100,
        clueId,
      }).then((res) => {
        // console.log("clueId", clueId);
        this.pathList = res.records;
      });
    },
    cancel() {
      document.body.click();
    },
  },
};
</script>

<style lang="scss" scoped>
// tab 公共配置
@import "./companyPublic.scss";
@import "./companyDetail.scss";
@import "@/views/attractinvestment/admin/components/dialog_tip.scss";
</style>

<style lang="scss">
/* 确保左右切换按钮显示 */
.detailpage .tabs {
  padding: 0 16px;
  .el-tabs__nav-wrap {
    position: relative;
    overflow: hidden;

    &.is-scrollable {
      padding: 0 32px;
    }

    .el-tabs__nav-prev {
      background: center/contain no-repeat url("~@/assets/bigScreen/left.webp") !important;
    }
    .el-tabs__nav-next {
      background: center/contain no-repeat url("~@/assets/bigScreen/right.webp") !important;
    }
    .el-tabs__nav-prev,
    .el-tabs__nav-next {
      color: #fafafa;
      font-size: 16px;
      position: absolute;
      cursor: pointer;
      line-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 100%;
      z-index: 10;
    }

    .el-tabs__nav-prev {
      left: 0;
    }

    .el-tabs__nav-next {
      right: 0;
    }
  }
}
</style>
