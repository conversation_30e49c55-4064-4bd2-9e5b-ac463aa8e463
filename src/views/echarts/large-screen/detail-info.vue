<template>
  <div>
    <!-- <img> -->
    <!-- <div style="color:#fff"> 电击疗法就是独领风骚江东父老开始打架方式打发
    {{ detailInfoParams }}</div> -->
    <!-- 企业列表 -->
    <!--detailInfoParams.level === '5' 为企业节点，展示企业的详情，其余为主干节点，展示企业列表  -->
    <AtlasDetailAllEnterprise
      v-if="detailInfoParams.type === 'graph' && (detailInfoParams.level !== '5')"
      :search-value-data="searchValueData"
      class="rightDrawer"
      :detail-info-params="detailInfoParams"
      :firm-typedetail="firmTypedetail"
      :child-id="childId"
      :is-fullscreen="isFullscreen"
      @closeDetail="closeDetail"
    />
    <!-- 企业详情 -->
    <AtlasDetailCompany
      v-if="detailInfoParams.type === 'graph' && (detailInfoParams.level === '5')"
      class="rightDrawer"
      :child-id="childId"
      :firm-typedetail="firmTypedetail"
      :second-query-param="detailInfoParams.secondQueryParam"
      @closeDetail="closeDetail"
    />

    <!-- 地图-企业列表 -->
    <MapDetail
      v-if="detailInfoParams.type === 'map'"
      :detail-num="detailNum"
      :detail-info-params="detailInfoParams"
      :child-id="childId"
      :search-value-data="searchValueData"
      :search-value="searchValue"
      :city-data="cityData"
      :second-query-param="secondQueryParam"
      @closeDetail="closeDetail"
    />
    <Attract
      v-if="detailInfoParams.type === 'attract'"
      :detail-info-params="detailInfoParams"
      :child-id="childId"
      @closeDetail="closeDetail"
    />
  </div>
</template>

<script>
// import { chartApi, getDetail } from '../apiUrl'
import AtlasDetailAllEnterprise from './companyList.vue' // 图谱 -  企业统计  、全国 、本地 
import AtlasDetailCompany from './companyDetail.vue' // 图谱 - 企业详情
import MapDetail from './detail-map.vue' // 图谱 - 企业详情
import Attract from './detail-attract.vue'
// import img from './img.vue'

export default {

  components: {
    AtlasDetailAllEnterprise,
    AtlasDetailCompany,
    MapDetail,
    Attract
  },

  props: {
    detailNum: {
      type: Number,
      default: 0
    },
    isFullscreen:{
        type:Boolean,
        default:false,
   },
    cityData: {
      type: Array,
      default: function () {
        return []
      }
    },
    changeId: {
      type: String,
      default: ''
    },
    searchValueData:{
      type: String,
      default: ''
    },
    // 图谱
    detailInfoParams: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {
        titleName: '',
        type: 'graph',
        level: "2",
        paraimsId: null,
        childId: null,
        node:null
      }
    },
    searchValue: {
      type: String,
      default: null
    },
    firmTypedetail: {
      type: Object,
      default: (() => { })
    },
    secondQueryParam: {
      type: String,
      default: null
    },
  },
  data () {
    return {
      childId: ''
    }
  },
  watch: {
    'cityData' () {

    },
    'changeId' (val) {
      this.childId = val;
    },
  
    // 'detailInfoParams'(val){
    //   // console.log('=val=2=',val); 
    // }
  },
  mounted () {
    
    // console.log(this.searchValue,'info');
  },
  beforeDestroy () {

  },
  methods: {

    closeDetail () {
      this.$emit('closeDetail')
    }
  }
}
</script>
<style lang="scss" scoped>
.rightDrawer{
  // width: 100vw;
  // height: 100vh;
  // background:rgb(67 66 66 / 12%);
  // //  rgba(255, 255, 255, 0);
  // left: 0;
  // top:0;
  z-index: 9999;
  position: fixed;
}
.btn {
  position: relative;
  cursor: pointer;
  float: left;
  color: #fff;
  font-size: 1rem;
  height: 2.6rem;
  text-align: center;
  width: 6rem;

  img {
    position: absolute;
    z-index: 9;
    left: 0;
    top: -2px;
    width: 100%;
    height: 100%;
  }

  span {
    position: absolute;
    z-index: 99;
    text-align: center;
    line-height: 2.4rem;
    text-align: center;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
}

.detail-main {
  position: fixed;
  top: 1px;
z-index: 2147483648;
  right: 0px;
  width: 39rem;
  height: calc(100vh - 5rem);
  top: 5rem;
  color: #fff;
  min-width: 460px;
  background-image: linear-gradient(to right, #00112d00 1%, #00112da8 20%, #00112da8 99%);
  // padding-left: 4rem;
  padding-right: 1rem;
  // margin-top: 16px;
  .type-name {
    width: 100%;
    height: 1rem;

    img {
      position: absolute;
      z-index: 9;
      left: 0;
      top: 1.2rem;
      width: 100%;
      height: 100%;
    }

    span {
      text-align: left;
      margin-left: 1.8rem;
    }

    span::after {
      position: absolute;
      border: none;
      background: #fff;
      left: -1.1rem;
      top: 1rem;
      content: "";
      z-index: 999;
      width: 0.4rem;
      height: 0.4rem;
      border-radius: 50%;
    }
  }

  .detail-sel {
    height: 3rem;
    margin-top: 3rem
  }

  .sel-main {
    display: flex;

    .tab-main {
      overflow: hidden;

      .tab {
        float: left;
        //background-image: -webkit-gradient(linear, left top, left bottom, from(#3370FF), to(#c6c9df2e));
        //background-image: linear-gradient(#3370FF, #c6c9df2e);
        width: 3.8rem;
        text-align: center;
        height: 2.4rem;
        font-size: 1rem;
        line-height: 2.4rem;
        margin-right: 0.18rem;
        border-radius: 4px 4px 0px 0px;
        border-bottom: #00FFF0 2px solid;
      }

      .btn.tab img {
        top: 0px;
      }
    }

    .input-sel {
      margin-left: 1rem;
      width: 15rem;
      display: flex;

      .input-info {
        width: 12rem;
      }

      .sel-btn {
        background-image: radial-gradient(#c6c9df1f, #0659a7);
        width: 4rem;
        height: 2.2rem;
        font-size: 1rem;
        text-align: center;
        line-height: 2.2rem;
        border-radius: 10rem;
      }
    }

    .import-btn {
      width: 4rem;
      height: 2.2rem;
      font-size: 1rem;
      text-align: center;
      line-height: 2.1rem;
      border-radius: 10rem;
      border: 2px solid #3370FF;
      color: #fff;
      font-size: 1rem;
      text-align: center;
      margin-left: 0.8rem;
    }

  }

  .detail-list {
    max-height: calc(100vh - 11rem);
    overflow: scroll;

    .title {
      display: flex;
    }

    .detail {
      background-image: linear-gradient(to bottom right, #1b53a8, #00122e);
      opacity: 0.97;
      border-radius: 10px;
      padding: 16px;
      margin: 10px 0;

      .logo {
        display: flex;
        margin: auto;
        width: 60px;
        height: 60px;
        border-radius: 4px;
        background: #52abfb;
        border-radius: 4px;
        overflow: hidden;

        span {
          width: 32px;
          height: 36px;
          font-size: 16px;
          color: #FFFFFF;
          line-height: 20px;
          margin: auto;
          text-align: center;
          font-weight: 400;
        }
      }

      .info {
        flex: 1;
        margin-left: 10px;
        height: 17px;
        font-size: 18px;
        line-height: 30px;
        font-weight: 400;
        height: 60px;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        padding: 0.2rem 0;
        display: flex;

        .name {
          font-size: 16px;
          font-weight: 400;
          margin: auto 0;
          cursor: pointer;
          line-height: 20px;
        }

        .tag {
          line-height: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;

          span {
            float: left;
            height: 20px;
            font-size: 12px;
            background: #52abfb3d;
            padding: 5px 9px;
            margin: 2px;
            margin-right: 4px;
            border-radius: 2px;
            color: #ffffffd4;
            line-height: 10px;
          }
        }
      }

      .btn {
        line-height: 20px;

        img {
          height: auto;
        }

        span {
          line-height: 14px;
          font-size: 14px;
          text-align: left;
        }
      }

      .describe-info {
        margin-top: 20px;

        .describe-list {
          margin-top: 14px;
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          line-height: 14px;
          color: #ffffffb3;
        }

        .describe {}

        .time {}
      }
    }
  }

}

::v-deep {
  .input-info .el-input {
    font-size: 1rem;
    line-height: 2.4rem;
    margin-left: 1.2rem;
  }

  .el-input__inner {
    background: transparent;
    color: #fff;
    line-height: 2.5rem;
    border: none;
    padding: 0 0.2rem;
    height: 100%;
    font-size: 1rem;
  }
}
</style>
