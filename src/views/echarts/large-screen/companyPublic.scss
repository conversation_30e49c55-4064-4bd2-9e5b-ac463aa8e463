@import "@/styles/public.scss";

.type-name {
  width: 100%;
  height: 2.4rem;
  background: center / cover no-repeat url("~@/assets/screen/new/inventory-title.webp");
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 0 60px;
  position: relative;
  span {
    @include YouSheBiaoTi24();
  }
  // span::before {
  //   position: absolute;
  //   border: none;
  //   background: #fff;
  //   display: block;
  //   left: 44px;
  //   top: 16px;
  //   content: "";
  //   z-index: 999;
  //   width: 0.4rem;
  //   height: 0.4rem;
  //   border-radius: 50%;
  // }
  .close {
    width: 40px;
  }
}

.detailpage {
  width: 100%;
  height: 86%;
  overflow-y: scroll;
  // min-he7ight: 200px;
  padding-top: 10px;
  background: rgba(0, 24, 53, 0.8);
  border-radius: 10px;
  position: relative;
  z-index: 1000;
  display: flex;
  flex-wrap: wrap;
  // flex-direction: column;
  // justify-content: space-between;
}
.tabs {
  width: 100%;
  display: flex;
  border-radius: 10px 10px 0px 0px;
  cursor: pointer;
  overflow-x: scroll;
  height: 40px;
  > div:not(:first-child) {
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 14px;
      width: 1px;
      height: 12px;
      background: rgba(201, 205, 211, 0.8);
    }
  }
  .tab {
    width: 100%;
    @include Puhuiti(16px, rgba(201, 205, 211, 0.8), 500);
    position: relative;
    text-align: center;
    height: 2.5rem;
    line-height: 2.5rem;
    // white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
  }
  .tab6{
    width: 120%;
  }
  .opttab6{
    width: 160% !important;
  }

  .opttab {
    width: 100%;
    text-align: center;
    position: relative;
    height: 2.5rem;
    line-height: 2.5rem;
    @include PuhuitiGradient(18px, 600);
    &::after {
      content: "";
      position: absolute;
      left: 37%;
      top: calc(100% - 5px);
      width: 26%;
      height: 3px;
      background: linear-gradient(180deg, #09E0F3 0%, #09A1F3 100%);
      box-shadow: 0px 0px 10px 0px rgba(0, 201, 220, 0.79);
    }
  }
}
.sel-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px 0 0px;
  margin-top: 16px;
  .tab-main {
    overflow: hidden;

    //min-width: 8rem;
    //min-width: 10.5rem;
    .tab {
      float: left;
      //width: 2.4rem;
      width: 3rem;
      text-align: center;
      height: 1.8rem;
      font-size: 0.85rem;
      line-height: 1.6rem;
      margin-right: 0.12rem;
      border-radius: 4px 4px 0px 0px;
      border-bottom: #00fff094 2px solid;
      overflow: hidden;
    }

    .btn.tab.active {
      border-color: #00fff0;

      span {
        color: #fff;
      }
    }

    .btn.tab {
      img {
        top: 0px;
        height: 2.1rem;
      }

      span {
        font-size: 0.8rem;
        line-height: 2.1rem;
        color: #ffffffc4;
      }
    }
  }
  .import-btn {
    cursor: pointer;
    width: 3.6rem;
    padding-left: 5px;
    z-index: 999;
    color: #19cdff;
    font-size: 14px;
  }
  .input-info {
    width: 85%;
    background: rgba(72, 82, 115, 0.5);
    height: 2rem;
    border-radius: 2px;
  }
  
}
.detail {
  height: 105px;
  border-radius: 3px;
  margin-top: 16px;
  border: 1px solid transparent;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, #02274e, #001530),
    radial-gradient(
      circle at 0 50%,
      rgba(0, 165, 254, 0.54),
      rgba(0, 165, 254, 0.52) 20%,
      rgba(0, 165, 254, 0.3) 45%,
      rgba(0, 165, 254, 0.1) 75%,
      rgba(113, 161, 194, 0.04) 85%,
      rgba(113, 161, 194, 0.04) 100%
    ) !important;
  box-sizing: content-box;
  border-radius: 3px;
  cursor: default;
  position: relative;
}
::v-deep {
  .input-info .el-input {
    font-size: 1rem;
    line-height: 2.4rem;
    margin-left: 0;
    height: 32px;
    .el-input__inner {
      background: transparent;
      position: absolute;
      left: 0;
      top: 0;
      color: #fff;
      line-height: 2rem;
      border: none;
      padding: 0 0.625rem;
      height: 100%;
      height: 2rem;
      font-size: 0.75rem;
      margin-top: 0px !important;
    }
  }

  .el-input__inner {
    background: transparent;
    color: #fff;
    // line-height: 2.5rem;
    border: none;
    padding: 0 16px;
    height: 100%;
    font-size: 1rem;
  }
}