<!-- 产业图谱 -->
<template>
  <sceenMap>
    <span
      slot="chainName"
      class="name-info"
    >{{ chainName || '产业金脑' }}<span style="font-size: 0.9rem; font-weight: 400; padding-left: 0.5rem;">[{{ orgAreaName || '企业链' }}]</span></span>
    
    <DetailInfo
      slot="DetailInfo"
      :detail-num="detailNum"
      :change-id="changeId"
      :detail-info-params="detailInfoParams"
      :city-data="cityData"
      :search-value="searchValue"
      :second-query-param="secondQueryParam"
      :search-value-data="searchValueData"
      @closeDetail="closeDetail"
    />
    
    <MapEcharts
      slot="screenMain"
      ref="mapEcharts"
      class-name="chart-container"
      form-name="产业地图"
      height="78vh"
      width="100%"
      :sel-num="selNum"
      :get-map-relation="getMapRelation"
      :detail-info-params="detailInfoParams"
      @onGetChild="onGetChild"
      @setFetchParams="setFetchParams"
      @closeDetail="closeDetail"
      @OnSearch="OnSearch"
      @onSelType="onSelType"
    />
  </sceenMap>
</template>

<script>
import { getPathId } from '@/utils/utils'

import { chartApi, getList, loadByParent, 
  // pageTypeListm,
  industryChainNodeAPI,getMinAreaByTokenAPI } from "./apiUrl";
import DetailInfo from "./large-screen/detail-info.vue"; // 图谱 - 企业详情
import MapEcharts from "./components/charts-map.vue";
import sceenMap from "./large-screen/sceenMap.vue"; // 大屏 - 框
export default {
  name: "MixChart",
  components: { MapEcharts, sceenMap, DetailInfo },
  data() {
    return {
      selNum: 0,
      detailNum:0,
      changeId: null,
      chainName:'',
      orgAreaName:'',
      optionsList: [], // 产业链数据
      searchValue: null,
      searchValueData: null,
      dataList: { name: "222" },
      queryId:this.$route.query.id|| getPathId()|| null,
      fetchParams: null,
      cityData:null,
      detailInfoParams: {
        type: "",
        level: "",
        paraimsId: this.$route.query?.id || getPathId()|| null,
        childId: null,
      },
      // 省市区 - 级联加载
      props: (qid) => {
        return {
          lazy: true,
          multiple: true, // 多选
          checkStrictly: true, // 任意一级选中
          lazyLoad(node, resolve) {
            const { level, 
              // data = {} 
            } = node;
            // const {id = 0} = data;
            let params = { id: qid };
            let nodes = [];
            if (level <= 2) {
              loadByParent(params)
                .then((res) => {
                  res.forEach((city) => {
                    const { id, nodeName } = city;
                    nodes.push({
                      value: nodeName,
                      label: nodeName,
                      id: id,
                      leaf: level >= 2,
                    });
                  });
                  resolve(nodes);
                })
                .catch(() => {});
            } else {
              resolve([]);
            }
          },
        };
      },

      secondQueryParam: '', // 二级查询菜单字段
    };
  },
  watch: {
    searchValue(val) {
      this.searchValueData = val[val.length - 1];
    },
  },
  beforeCreate() {
    this.queryId =this.$route.query.id|| getPathId()|| null;
  },
  created() {
    this.getOptionsList();
    this.getMinAreaByToken()
  },
  mounted() {
    // this.getMapRelation();
    // this.getOptionsList();
  },

  methods: {
    async getMinAreaByToken(){
      const res =await getMinAreaByTokenAPI({  
      relationId:this.$route.query.id|| getPathId()|| null
      })
      this.chainName=res.chainName
      this.orgAreaName=res.minArea
    },
    async getOptionsList() {
      const params = { id: this.$route.query?.id || getPathId()|| null };
/*       loadByParent(params).then((res) => {
        this.chainName = res.nodeName;
        this.orgAreaName = res.orgAreaName;
        this.optionsList = this.formatWay(res.childNodes);
      }); */
       const res =  await industryChainNodeAPI(params)
       this.optionsList = res 
       //this.orgAreaName = res.orgAreaName;
    },
    
    // 产业链节点数据 - 数据格式化
    formatWay(childNodes){
      let nodeList = [];
      childNodes?.forEach((item)=>{
        let children = null;
        if(item?.childNodes && item.childNodes.length>=1){
          children = this.formatWay(item?.childNodes);
          nodeList.push({value:item.id, label:item.nodeName, children:children});
        }else{
          nodeList.push({value:item.id, label:item.nodeName});
        }
      });
      return nodeList;
    },

    // 点击节点 - 打开详情
    onGetChild(params) {
      this.detailNum = this.detailNum+1;
      this.detailInfoParams.type = "map";
      this.detailInfoParams.childId = params.name;
      this.changeId = params.name;
      this.cityData = params.cityData;
    },
    // 查询条件 - 传参 - 省/市/区
    setFetchParams(params) {
      this.fetchParams = params;
    },

    // 搜索
    OnSearch(val) {
      this.searchValue = val;
      // this.getMapRelation();
    },
    onSelType(val) {
      this.secondQueryParam = val;
      // this.getMapRelation();
      this.closeDetail();
    },

    // 查看舆情
    OnCheckOpinion() {},
    // 查看统计
    OnViewStatistics() {},

    // 获取地图数据
    getMapRelation() {
      const noData = ['重庆市','北京市','上海市','天津市']
      const id = this.$route.query?.id ||  getPathId()|| null;
      if (!id) {
        return;
      }
      const fetchParams = this.fetchParams;
      let province = null;
      let city = null;
      let area = null;
      if (fetchParams && fetchParams.length >= 2) {
        province = fetchParams[1].countArea;
        if(noData.includes(province)){
          city = fetchParams[1].countArea;
        }
         if(noData.includes(province) &&  fetchParams.length==4){
           area =  fetchParams[3].countArea;
        }
        if (fetchParams.length >= 3  && !noData.includes(province)) {
        city = fetchParams[2].countArea;
          if (fetchParams.length >= 4) {
            area = fetchParams[3].countArea;
          }
        }
      }
      let data = {
        id: parseInt(id),
        //code: this.searchValueData,
        chainNodeId: this.searchValue,
        province,
        city,
        area,
        secondQueryParam: this.secondQueryParam
      };
      return new Promise((resolve, reject) => {
        getList(chartApi.mapRelation, data)
          .then((res) => {
            this.dataList = res;
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 关闭详情
    closeDetail() {
      this.detailInfoParams.type = "";
      this.$refs.mapEcharts.showDetaial = false;
    },
  },
};
</script>
<style lang="scss">
::v-deep{
  .node-info-tree.el-popper{
    background: #00112D;
    border: blue 1px solid;
    .el-cascader-node__label{
      color: #fff;
    }
    .el-cascader__dropdown{
      border: red 1px solid;
    }
  }
}

.node-info-tree.el-popper{
  background: #00112D;
  border: blue 1px solid;
  .el-cascader-menu{
    border-right:blue 1px solid;
  }
  .el-cascader-node__label{
    color: #fff;
  }
  .el-cascader-node:not(.is-disabled):hover, 
  .el-cascader-node:not(.is-disabled):focus, 
  .el-cascader-node:not(.is-disabled):hover, 
  .el-cascader-node:not(.is-disabled):focus{
    background: #184290;
  }
  .el-radio__inner{
    border: #0066FF 1px solid;
    background: transparent;
  }
  .el-radio__input.is-checked .el-radio__inner{
    background: #0066FF;
  }
  .el-cascader__dropdown{
    border-color: #0066FF;
  }
}


</style>
<style scoped lang="scss">
.navigation {
  width: 8rem;
  .navigation-info {
  }
}

::v-deep {
  .el-input--suffix .el-input__inner {
    color: #fff;
  }
  .el-select > .el-input {
    width: 113px;
    font-weight: 700;
    background-color: rgba(12, 39, 83, 0.7);
    border-radius: 8px;
  }
  .el-input--suffix .el-input__inner {
    border: 1px solid #3695ff;
    font-weight: 700;
    background-color: rgba(12, 39, 83, 0.7);
    border-radius: 8px;
  }
  .el-select-dropdown__item .selected :hover{
    border: 1px solid #3695ff;
  }
  .el-input--suffix .el-input__inner:hover {
    border: 1px solid #3695ff;
  }
  .node-info.el-cascader{
    .el-input .el-input__inner{
      font-size: 0.8rem;
      background: transparent;
      border: none;
      height: 100%;
    }
    .el-input .el-icon-arrow-down{
      font-size: 0.8rem;
      height: 2rem;
      line-height: 2rem;
    }
    .el-input .el-icon-circle-close{
      font-size: 0.8rem;
      height: 2.2rem;
      line-height: 2rem;
    }
    .el-cascader-panel{
      position: absolute;
      background: #09132a;
      top: -16px;
    }
    .el-cascader-node__label{
      color: #fff;
    }
  }
  .navigation-select .el-input--suffix .el-input__inner{
    padding-left: 15px !important;
  }
}
.suffix {
  position: relative;
  font-size: 0.9rem;
  font-style: normal;
  margin-left: 1rem;
}
.chart-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 84px);
}
::v-deep {
  .el-cascader {
    width: calc(100% - 13rem);
    font-size: 14px;
    line-height: 2rem;
    position: absolute;
    left: 3rem;
    height: 2rem;
    margin-top: 0.3rem;
  }
  .el-cascader .el-input .el-input__inner {
    font-size: 0.8rem;
  }
  .el-cascader .el-input .el-icon-arrow-down {
    font-size: 0.8rem;
    height: 2.4rem;
    line-height: 2rem;
  }
  .el-cascader-panel {
    position: absolute;
    background: #09132a;
    top: -16px;
  }
  .el-cascader-node__label {
    color: #fff;
  }
  .el-cascader .el-input {
    width: 23rem;
  }
}
</style>

