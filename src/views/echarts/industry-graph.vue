<!-- 产业图谱 -->
<template>
  <div>
  <ScreenGraph>
    <ChartGraph
      slot="screenMain"
      ref="chart"
      x-if="!dataList"
      class-name="chart-container"
      height="100%"
      width="100%"
 
    />
  </ScreenGraph>
</div>
</template>

<script>
 
import ScreenGraph from "./large-screen/sceenGraph.vue"; // 大屏 - 框
import ChartGraph from "./components/vis-graph.vue"; // 图谱
export default {
  name: "MixChart",
  components: { ChartGraph, ScreenGraph, },
  data() {
    return {
    };
  },
  computed: {},
  watch: {
  },
 
  methods: {}
};
</script>

<style lang="scss">
::v-deep {
  .node-info-tree.el-popper {
    background: #00112d;
    border: blue 1px solid;
    .el-cascader-node__label {
      color: #fff;
    }
    .el-cascader__dropdown {
      border: red 1px solid;
    }
  }
}
.node-info-tree.el-popper {
  background: #00112d;
  border: blue 1px solid;
  .el-cascader-menu {
    border-right: blue 1px solid;
  }
  .el-cascader-node__label {
    color: #fff;
  }
  .el-cascader-node:not(.is-disabled):hover,
  .el-cascader-node:not(.is-disabled):focus,
  .el-cascader-node:not(.is-disabled):hover,
  .el-cascader-node:not(.is-disabled):focus {
    background: #184290;
  }
  .el-radio__inner {
    border: #0066ff 1px solid;
    background: transparent;
  }
  .el-radio__input.is-checked .el-radio__inner {
    background: #0066ff;
  }
  .el-cascader__dropdown {
    border-color: #0066ff;
  }
}
</style>
<style lang="scss" scoped>
.chainName {
  em {
    color: #fff;
  }
}
.navigation {
  width: 8rem;
  .navigation-info {
  }
}

::v-deep {
  .el-cascader {
    .el-input .el-input__inner {
      font-size: 0.8rem;
      background: transparent;
      border: none;
      height: 100%;
    }
    .el-input .el-icon-arrow-down {
      font-size: 0.8rem;
      height: 2rem;
      line-height: 2rem;
    }
    .el-input .el-icon-circle-close {
      font-size: 0.8rem;
      height: 2.2rem;
      line-height: 2rem;
    }
    .el-cascader-panel {
      position: absolute;
      background: #09132a;
      top: -16px;
    }
    .el-cascader-node__label {
      color: #fff;
    }
  }
  .navigation-select .el-input--suffix .el-input__inner {
    padding-left: 15px !important;
  }
  .navigation-select {
    .el-input--suffix .el-input__inner {
      background: #0f2ba9;
      border: #468ae7 1px solid;
      color: #fff;
      font-size: 0.8rem;
      height: 100%;
      height: 2.4rem;
      line-height: 2.4rem;
    }
    .el-select-dropdown__list {
      background-color: #091e76;
    }
    .el-popper[x-placement^="bottom"] {
      left: 0 !important;
      border: #3370FF 1px solid;
    }
    .el-input.is-focus {
      border: #468ae7 1px solid;
      border-radius: 3px;
    }
    .el-input__icon.el-icon-arrow-up {
      height: 2.4rem;
      line-height: 2.4rem;
    }
    .el-select-dropdown__item {
      color: #fff;
    }
    // 鼠标点击后移入的颜色
    .el-select-dropdown__item.selected {
      color: #fff;
    }
    // 下拉框移入的颜色
    .el-select-dropdown__item:hover {
      background-image: linear-gradient(to bottom right, #2484ef, #09217b);
      color: #fff;
    }
    // 移出不要丢失颜色
    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-image: linear-gradient(to bottom right, #2484ef, #09217b);
      color: #fff;
    }
  }
}
.suffix {
  position: relative;
  font-size: 0.9rem;
  font-style: normal;
  margin-left: 1rem;
}
.edit-btn {
  position: absolute;
  top: 64px;
  color: #fff;
  right: 32px;
  cursor: pointer;
  display: block;
  border: 1px solid #204697;
  // border-image: linear-gradient(to right, #204697, transparent) 1;
  padding: 8px;
  border-radius: 4px;
  z-index: 99;
}
.el-icon-back {
  position: absolute;
  top: 64px;
  color: #fff;
  font-size: 20px;
  left: 128px;
  cursor: pointer;
  display: block;
  padding: 8px;
  border-radius: 4px;
  z-index: 99;
}

::v-deep {
  .el-cascader {
    width: calc(100% - 13rem);
    font-size: 14px;
    line-height: 2rem;
    position: absolute;
    left: 3rem;
    height: 2rem;
    margin-top: 0.3rem;
  }
  .el-cascader .el-input .el-input__inner {
    font-size: 0.8rem;
  }
  .el-cascader .el-input .el-icon-arrow-down {
    font-size: 0.8rem;
    height: 2.4rem;
    line-height: 2rem;
  }
  .el-cascader-panel {
    position: absolute;
    background: #09132a;
    top: -16px;
  }
  .el-cascader-node__label {
    color: #fff;
  }



}

</style>

