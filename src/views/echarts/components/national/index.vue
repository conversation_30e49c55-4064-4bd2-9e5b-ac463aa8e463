<template>
  <div>
    <div
      class="nav-header"
      :class="{ showBg: isEdit }"
    >
      <div>
        <div
          v-if="$store.getters.user.accountType == '1'"
          class="save-btn"
          @click="beginEdit(isEdit)"
        >
          <!-- v-if="$store.getters.user.accountType == '1'" -->

          {{ isEdit ? "退出" : "修改" }}
        </div>
        <div
          class="reset"
          @click="reset"
        >
          重置
        </div>
      </div>
      <div class="nav-center">
        <div class="nav-center-item center1">
          <div class="leftIcon" />
          <div class="rightText">
            <div class="title">
              企业总量
            </div>
            <div class="number">
              {{ headerViewData?.count || 0 }}<span>家</span>
            </div>
          </div>
        </div>
        <div class="nav-center-item center2">
          <div class="leftIcon" />
          <div class="rightText">
            <div class="title">
              <span class="point red" />本地企业
            </div>
            <div class="number">
              {{ headerViewData?.localCount || 0 }}<span>家</span>
            </div>
          </div>
        </div>
        <div class="nav-center-item center3">
          <div class="leftIcon" />
          <div class="rightText">
            <div class="title">
              本地产业环节覆盖率
            </div>
            <div class="number">
              {{ headerViewData?.coverRadio || 0 }}<span>%</span>
            </div>
          </div>
        </div>
        <div class="nav-center-item center4">
          <div class="leftIcon" />
          <div class="rightText">
            <div class="title">
              <span class="point yellow" />招商推荐企业
            </div>
            <div class="number">
              {{ headerViewData?.investCount || 0 }}<span>家</span>
            </div>
          </div>
        </div>
      </div>
      <div>
        <Preview
          ref="previewRef"
          :network="!isEdit ? network : editNetwork"
          @changeNodePosition="changeNodePosition"
          @changeTwinkState="changeTwinkState"
        />
      </div>
      <!-- 产业导览 -->
    </div>
    <!-- :style="{ height: !isEdit ? '100vh' : '0' }" -->
    <div
      id="mynetwork"
      :class="{ sohwHeight: !isEdit, hideView: isEdit }"
    />
    <div
      id="myNetworkEdit"
      :class="{ sohwHeight: isEdit, hideView: !isEdit }"
    />
    <!--  :style="{ height: isEdit ? '100vh' : '0' }" -->

    <DetailInfo
      :is-fullscreen="isFullscreen"
      :change-id="changeId"
      :detail-info-params="detailInfoParams"
      :firm-typedetail="firmType"
      :search-value-data="searchValueData"
      @closeDetail="closeDetail"
    />
    <!--    current-state="true" -->
    <ShortcutBtm
      v-if="!isEdit"
      ref="cutbtm" 
   
      @onGetSecondQueryParam="onGetSecondQueryParam"
    />
    <div
      v-if="!isEdit"
      class="line-box"
    >
      <div
        v-for="(item, index) in lineList"
        :key="item.key"
        class="line-list"
        :class="[
          'line-list-' + (index + 1),

        ]"
      >
        <!--  item.key === treeState ? 'active' : '', -->
        <!-- @click="searchTreeType(item.key)" -->
        {{ item.name }}
      </div>
    </div>
    <!--  v-if="loading" -->
    <div
      v-if="loading"
      v-loading="loading"
      class="loading-info"
      customClass="loading-info-icon"
      element-loading-text=""
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgb(0 0 0 / 24%)"
      element-loading-color="#fff"
    />
    <!-- v-if="toolTips"  -->
    <div
      v-if="toolTips"
      class="toolTips"
    >
      <!-- 数据计算中。。。 -->
      <div class="el-loading-spinner">
        <i class="el-icon-loading" />
      </div>
    </div>
  </div>
</template>
<script>
import {
  // drawLine,
  getRange,
  getXandY,
  getRanges,
  getImageUtils,
  getImgs,
} from "./vis-graph-utils.js";
import { options, getTreeEdges, getPoints, drawLine } from "./configs.js";
import { getPathId } from '@/utils/utils'

// import { orNodes } from "./configsCopy.js";

import DetailInfo from "../../large-screen/detail-info.vue"; // 图谱 - 企业详情
require("vis-network/dist/dist/vis-network.min.css");
const vis = require("vis-network/dist/vis-network.min");
import Preview from "../preview.vue";
import ShortcutBtm from "../../large-screen/shortcut-btm.vue";
import { chartApi, getList, batchUpdateXY } from "../../apiUrl";
import { message } from "@/utils/MessageUtil";

import _ from "lodash";
export default {
  components: {
    Preview,
    ShortcutBtm,
    DetailInfo,
  },
  props:{
   isFullscreen:{
        type:Boolean,
        default:false,
   }
  },
  data() {
    return {
      originData: null,//tree初始数据
      lineList: [
        // { name: "强链环节", key: "强链" },
        // { name: "补链环节", key: "补链" },
        // { name: "延链环节", key: "延链" },
      ],
      toolTips: false,
      loading: false,
      changeId: null,
      isEdit: false, // 是否开启修改模式
      graphData: null, // 获取的数据
      // network: null,
      accountType: "2", // 1: 运营端，2：用户端
      detailInfoParams: {
        titleName: "",
        type: "", // graph/map
        level: "", // 1 2 3
        paraimsId: this.$route.query?.id || getPathId()|| null,
        // childId:"107"
        childId: null,
        secondQueryParam: null,
        enterpriseType: null,
        node: null,
        current: 'national'
      },
      firmType: {},
      nodes: new vis.DataSet([]),
      edges: new vis.DataSet([]),
      network: null,
      showImgs: {}, //需要转化为img的数据
      backboneNode: [], //次主节点node数据
      // blueAndRedData: [], //先渲染的企业节点，一圈内包含红色和绿色
      letterShowData: [], //最后渲染的企业节点，一圈内只有单个颜色
      letterShowNode: {},
      otherDataNodes: null,
      nodeLevel3Array: [], //所有 第二层级 node
      enterInfoObj: {}, //所有  包含父级的企业节点 obj
      nodeLevel2Array: {}, //所有 第一层级 node
      enterpriseArray: [], //所有  不包含父级的企业 信息 array
      updatedImgNode: [], //已更新的图片节点数据
      pointsEdges: [], //已有的折线连线数据
      // nodeLevel2ChildObj: {},//所有 第一层级下包含的child
      nodeData: {},
      editNetwork: null,
      enterpriseData: [],
      treeState: "",
      treeStateSave: '',
      size: 6,
      timer: null,
      searchValueData: '',
      // 
      headerViewData: null,
      heightArray: [],
      nodePoints: 0,//页面节点总数
      imgs: {}
    };
  },

  async mounted() {
    this.loading = true
    // this.toolTips=true
    if (localStorage.user) {
      let user_json = JSON.parse(localStorage.user);
      this.accountType = user_json.user.user.accountType;
    }
    await this.getImage1();
    let imgs = getImgs()
    this.imgs = imgs
    if (this.searchValueData === '' && !!this.$store.getters.visViewData) {

      this.getInfoNoAjax()
      this.loading = false

    }
  },

  created() {
    if (!(this.searchValueData === '' && !!this.$store.getters.visViewData)) {
      this.getInfo();
    }
  },
  // beforeRouteEnter(to,from,next)  {
  //   next(()=>{

  //   })
  // console.log(111)

  // 调用时机为首次挂载
  // 以及每次从缓存中被重新插入时
  // },

  // onDeactivated()  {
  //   // 在从 DOM 上移除、进入缓存
  //   // 以及组件卸载时调用
  //   console.log(222)
  // },
  methods: {
    reset() {
        // network.on("zoom", module.zoom);
          this.network.fit({
          animation: {
            duration: 300,
          },
        });
    },
    async getImage1() {
      try {
        await getImageUtils(this);
      } catch (e) {
        // console.log(e)
      }
    },

    drawingTree() {
      this.nodes = new vis.DataSet([]);
      this.edges = new vis.DataSet([]);
      const container = document.getElementById("mynetwork");
      const data = getTreeEdges(this.originData, {
        image_1: this.image_1,
        image_2: this.image_2,
        image_5: this.image_5,
        image_3: this.image_3,
        image_4: this.image_4,
        image_6: this.image_6,
        image_7: this.image_7,
        image_8: this.image_8,
      });
      let points = getPoints(this.originData);

      this.enterInfoObj = data.enterInfo;
      this.nodeLevel2Array = data.nodes;
      this.nodeLevel3Array = data.nodeLevel3Array;
      this.enterpriseArray = data.enterpriseArray;
      this.pointsEdges = points.pointsEdges
      // this.nodeLevel2ChildObj = data.nodeLevel2ChildObj
      let enterpriseData = this.drawingOrg(data.enterInfo);
      let addNode = this.getBlueAndRedNode();
      let info = {
        // 主干节点,
        // pointsEdges,pointsNode
        nodes: [
          // ...initialData,
          ...data.nodes,
          ...enterpriseData.addNodes,
          ...addNode,
          ...data.nodeLevel3Array,
          ...points.pointsNode,
        ],
        edges: [
          ...data.edges,
          ...enterpriseData.addEdges,
          ...points.pointsEdges,
        ],
      };

      let viewOptions = {
        ...options
      }
      this.nodes = new vis.DataSet(info.nodes);
      this.edges = new vis.DataSet(info.edges);
      viewOptions.interaction.dragNodes = false
      let network = new vis.Network(container, info, options);
      this.network = network;

      this.listeningEvent(network, this);

      // this.otherDataNodes = [...addNode, ...enterpriseData.addNodes];
      // if(this.nodePoints>30){
      //   this.otherDataNodes = [ ...enterpriseData.addNodes];
      // }else{
      // this.otherDataNodes = [...addNode, ...enterpriseData.addNodes];
      // }
      // this.otherDataNodes = [...enterpriseData.addNodes];
      // this.updateNode(network,this.otherDataNodes );
      network.once("afterDrawing", function () {
        network.fit({
          animation: {
            duration: 200,
          },
        });
      });

      // let that = this
      // if (that.treeStateSave && that.treeStateSave !== '') {
      //   that.setGlobalAlpha(0.1, that.treeStateSave);
      //   that.treeState = that.treeStateSave
      //   that.treeStateSave = ''
      // } else {
      //   that.heightLight();
      // }


    },
    drawingTreeEdit() {
      // this.loading = true;
      const container = document.getElementById("myNetworkEdit");
      const data = getTreeEdges(this.originData, {
        image_1: this.image_1,
        image_2: this.image_2,
        image_5: this.image_5,
        image_3: this.image_3,
        image_4: this.image_4,
      });
      this.enterInfoObj = data.enterInfo;
      this.nodeLevel2Array = data.nodes;
      this.nodeLevel3Array = data.nodeLevel3Array;
      this.enterpriseArray = data.enterpriseArray;
      let enterpriseData = this.drawingOrg(data.enterInfo);
      this.enterpriseData = enterpriseData.addNodes;
      let info = {
        // 主干节点,
        nodes: [
          ...data.nodes,
          ...this.nodeLevel3Array,
          ...enterpriseData.addNodes,
        ],

        edges: [
          ...data.edges,
          ...data.nodeLevel3Edges,
        ],
      };

      let editOptions = { ...options };
      editOptions.interaction.dragNodes = true
      let network = new vis.Network(container, info, editOptions);
      this.editNetwork = network;
      this.listeningEvent(network, this);
      this.editNetwork.fit({
        nodes: this.nodeLevel2Array.map((e) => e.id),
        animation: {
          duration: 200,
        },
      });
      this.otherDataNodes = [...enterpriseData.addNodes];

      // setTimeout(() => {
      // this.updateNode(network, this.otherDataNodes);
      network.once("afterDrawing", function () {
        network.fit({
          animation: {
            duration: 300,
          },
        });
      });
      // this.loading = false;
      // }, 1);

    },
    drawingOrg() {
      // console.time('qiye耗时==>')
      // // 添加企业节点
      let json = this.enterInfoObj;
      // let otherData = {}
      let PartNodesData = {
        addNodes: [],
        addEdges: [],
      };
      // let node = {
      //   492: {}
      // }
      for (let key in json) {
        const list = json[key].enterInfo;
        // 节点总数
        let length = list.length;
        let range = getRange(length);
        if (length === range?.max || length === range?.min || length < 10) {
          //  截取相应数据来渲染
          let newList = list.slice(range?.min, length);
          let { xvalue, yvalue, id } = json[key];
          let parentId = id;
          let data = { xvalue, yvalue, list: newList, range, parentId, nodeData: json[key] };
          let partNodesData = this.addPartNodes(data, { showLine: true });
          PartNodesData.addNodes = [
            ...PartNodesData.addNodes,
            ...partNodesData.addNodes,
          ];
          PartNodesData.addEdges = [
            ...PartNodesData.addEdges,
            ...partNodesData.addEdges,
          ];
          this.getOtherPartData(key, {
            xvalue,
            yvalue,
            trueLength: length,
            list: list.slice(0, range?.min),
          }, json[key]);
        } else {
          let secondRange = getRange(range.min - 1);
          // console.log(range.min - (range.max - length), length)
          let firstList = list.slice(range.min, length);
          let trueNum = 0;
          if (secondRange.num === range.num) {
            trueNum = range.min - (range.max - length);
          } else {
            trueNum = range.min - parseInt((range.max - length) / 2);
          }
          let { xvalue, yvalue, id } = json[key];

          let parentId = id;
          //   let secondRangeMin = trueNum < 0 ? secondRange.min : trueNum
          let secondList = list.slice(secondRange.min, secondRange.max);
          // 将第二圈数据分为两部分，secondList补剩余的线，thirdList正常展示不画线
          //   let thirdList = list.slice(secondRange.min, secondRangeMin)
          //   let thirdListDataInfo = { xvalue, yvalue, list: thirdList, range: secondRange,parentId }
          //   let thirdListData = this.addPartNodes(thirdListDataInfo,  { index: secondRangeMin - secondRange.min, isThird: true, showLine: false })
          //   PartNodesData.addNodes = [...PartNodesData.addNodes, ...thirdListData.addNodes]
          //   PartNodesData.addEdges = [...PartNodesData.addEdges, ...thirdListData.addEdges]

          let listData = [
            {
              list: firstList,
              range: range,
            },
            {
              list: secondList,
              range: secondRange,
            },
          ];

          listData.map((item, index) => {
            // index === 1 && console.log(item.list.length)
            let data = {
              xvalue,
              yvalue,
              list: item.list,
              range: item.range,
              parentId,
              nodeData: json[key]
            };
            let partNodesData =
              index === 1
                ? this.addPartNodes(data, { index, showLine: true })
                : this.addPartNodes(data, { showLine: true });
            PartNodesData.addNodes = [
              ...PartNodesData.addNodes,
              ...partNodesData.addNodes,
            ];
            PartNodesData.addEdges = [
              ...PartNodesData.addEdges,
              ...partNodesData.addEdges,
            ];
          });
          if (!this.isEdit) {
            this.getOtherPartData(key, {
              xvalue,
              yvalue,
              trueLength: length,
              list: list.slice(0, secondRange.min),
            }, json[key]);
          }
        }
      }
      // if (!this.isEdit) {
      //   this.getBackboneNode(this);
      // }

      // this.otherData = otherData
      return PartNodesData;
      // console.timeEnd('qiye耗时==>')
    },
    // 设置剩余节点组成形式
    getOtherPartData(parentId, node, parent) {
      let { list, ...others } = node;
      let ranges = getRanges();
      // enterType  3蓝色，1红色
      let imgs = [];
      let blueAndRedData = [];
      let blueDataArray = [];
      let redDataArray = [];
      // let showImgs = {}
      let allData = [];
      for (let key in ranges) {
        let range = ranges[key];
        if (range.max <= list.length) {
          let newList = list.slice(range.min, range.max);

          blueAndRedData.push({
            list: newList,
            range: range,
            ...others,
            parentId,
            nodeData: parent
          });
          allData = [...allData, ...newList]
        }
      }

      this.showImgs[parentId] = imgs;
      // this.blueAndRedData = [...this.blueAndRedData, ...blueAndRedData];
      this.letterShowData = [
        ...this.letterShowData,
        ...blueAndRedData,
      ];

      //  [...redDataArray, ...blueDataArray];
      // 开始绘制主干节点
      // 绘制主干节点+效果图
    },
    getBackboneNode(that) {
      let nodes = that.nodeLevel3Array;
      //   this.nodeLevel3Array
      let newData = nodes?.map((item) => {
        // if (item._node  ) {
        if (item._node) {
          let obj = {
            ...item,
          };

          return obj;
        }
      });
      this.backboneNode = newData;
    },
    // 添加部分节点
    addPartNodes(obj, indexShift, nodes) {

      let { xvalue, yvalue, list, range, parentId, nodeData } = obj;
      // console.log(nodeData)
      let { r, num } = range;
      let addEdges = [];
      let addNodes = [];
      list.map((item, index) => {
        // 最后区间的数据等分一下平均分布成一个圆
        let dx = 0;
        let dy = 0;
        if (indexShift.index) {
          // 特殊的一圈展示要反着来展示数据，有线的和最外层没数据的要对上
          if (indexShift.isThird) {
            dx =
              +xvalue +
              Math.cos(
                ((360 / num) * (num - index - 1 - indexShift.index) * Math.PI) /
                180
              ) *
              r;
            dy =
              +yvalue +
              Math.sin(
                ((360 / num) * (num - index - 1 - indexShift.index) * Math.PI) /
                180
              ) *
              r;
          } else {
            dx =
              +xvalue +
              Math.cos(((360 / num) * (num - index - 1) * Math.PI) / 180) * r;
            dy =
              +yvalue +
              Math.sin(((360 / num) * (num - index - 1) * Math.PI) / 180) * r;
          }
        } else {
          dx = +xvalue + Math.cos(((360 / num) * index * Math.PI) / 180) * r;
          dy = +yvalue + Math.sin(((360 / num) * index * Math.PI) / 180) * r;
        }
        //   enterType
        // id
        // labelType
        // name
        let { id, labelType, enterType } = item;
        // labelType大于9类型用默认
        let _enterpriseType = enterType || 0;
        let _enterpriseLabelType =labelType&& Number(labelType)>9? 0:labelType|| 0;
        let json = {
          borderWidth: 2,
          id: `${parentId}_${id}`,
          label: item.name,
          _name: item.name,
          _id: id,
          nodeId: id,
          x: dx,
          y: dy,
          widthConstraint: 0,
          physics: false,
          shape: "circle", // 设置节点形状为圆形
          size: 1,
          _enterpriseType,
          _enterpriseLabelType,
          _nodePropose: nodeData.nodePropose,
          opacity: 0,
          parentId,
        };
        // labelType
        let img = this.imgs[`${enterType ? enterType : 1}` + "-" + `${labelType || 0}`];
        // 以 DataURL 格式读取文件内容
        if (indexShift && indexShift.showImg) {
          let type = {
            // shape: "image", //标签类型image
            opacity: 1,
          };
          json = { ...type };
          json.image = img || '';
          json._image = img || '';
          const node = nodes[`${parentId}_${id}`];
          node.setOptions(json);
        } else {
          let type = {
            shape: "image", //标签类型image
            opacity: 1,
            // shape: "image", //标签类型
            size: 6,
            group: 4,
            widthConstraint: 13,
            font: {
              size: 2.5,
              color: "#fff",
              vadjust: -10, //垂直位置距离
            },
          };
          json = { ...json, ...type };
          json.image = img;
          json.image = img;
          json._image = img ? img : '';
          json.selectedImage = img ? img : '';
          // json.label = "";
          // const node = nodes[`${parentId}_${id}`];
          // node.setOptions(json);
        }
        addNodes.push(json);
        let edgeObj = {
          from: parentId,
          to: `${parentId}_${id}`,
          physics: false,
          color: {
            color: "#4E5966",
            hover: "#4E5966",
            highlight: "#4E5966",
          },
          width: 0.1,
          hoverWidth: 0,
          opacity: 1,
          selectionWidth: function (width) {
            return width * 2;
          },
          scaling: {
            min: 0.5,
          },
          chosen: false,
          shadow: false,
        };
        if (this.treeStateSave && this.treeStateSave !== '') {
          edgeObj.opacity = nodeData.nodePropose === this.treeStateSave ? 1 : 0.1
        }
        indexShift && indexShift?.showLine && addEdges.push(edgeObj);
      });
      return {
        addNodes,
        addEdges,
      };
    },
    getBlueAndRedNode() {
      let node = [...this.letterShowData];
      let newList = [];
      // 一圈内包含红和蓝两种数据
      node.map((item) => {
        // {
        // list: newList,
        //     range: range,
        //     ...others,
        //     parentId,
        //     nodeData: parent
        //   }
        let info = this.addPartNodes(item, {});
        newList = [...newList, ...info.addNodes];
        this.letterShowNode[item.parentId] = [...this.letterShowNode?.[item.parentId] || [], ...info.addNodes]
      });

      return newList;
    },

    // updateNode(network, showNode) {
    //   // let updatedNodeStyle = {
    //   //   // shape: "image", //标签类型
    //   //   opacity: 1,
    //   //   size: 6,
    //   //   group: 4,
    //   //   widthConstraint: 13,
    //   //   font: {
    //   //     size: 2.5,
    //   //     color: "#fff",
    //   //     vadjust: -10, //垂直位置距离
    //   //   },
    //   // };
    //   // const nodes = network.body.nodes;
    //   // // 更新节点样式和文本样式
    //   // showNode?.map((item) => {
    //   //   let { id } = item;
    //   //   const node = nodes[id];
    //   //   let options = node.options;
    //   //   if (options._name) {
    //   //     let { _enterpriseType, _enterpriseLabelType, _nodePropose } = options;
    //   //     updatedNodeStyle.label = options._name;
    //   //     if (this.treeStateSave && this.treeStateSave !== '') {
    //   //       updatedNodeStyle.opacity = _nodePropose === this.treeStateSave ? 1 : 0.1
    //   //     }
    //   //     if (!this.isEdit) {
    //   //       let img =this['image_'+(_enterpriseType === '4' ? '1' : _enterpriseType) + "_" + _enterpriseLabelType]
    //   //       console.log(img,'721')

    //   //       //  getImgs()[(_enterpriseType === '4' ? '1' : _enterpriseType) + "-" + _enterpriseLabelType];
    //   //       // console.log( [(_enterpriseType==='4'?'1':_enterpriseType )+ "-" + _enterpriseLabelType])
    //   //       updatedNodeStyle.shape = "image", //标签类型
    //   //         updatedNodeStyle.image = img;
    //   //     } else {
    //   //       updatedNodeStyle.size = 1
    //   //       updatedNodeStyle.color = {
    //   //         border: '#4690fe',
    //   //         background: '#0b3a66',
    //   //       }
    //   //       updatedNodeStyle.shape = "dot" //标签类型
    //   //       updatedNodeStyle.font.vadjust = -4 //垂直位置距
    //   //     }

    //   //     node.setOptions(updatedNodeStyle);
    //   //     // this.nodes.update([{ id, ...updatedNodeStyle }]);
    //   //   }
    //   // });

    // },
    // 
    listeningEvent: (network, that) => {
      const module = (function () {
        const handleClick = function (e) {
          //  let that = this;
          if (e?.nodes.length > 0 && !that.isEdit) {
            let allNodes = that.network.body.nodes;
            let paramsId = e.nodes[0].toString();
            let data = allNodes[paramsId]
            that.getDetail({
              x: data.options?.x,
              y: data.options?.y,
              name: data.options.label || data.options._label,
              paramsId: data.options.nodeId,
              level: data.options.nodeLevel ? data.options.nodeLevel : '5',
              type: "graph",
              node: data.options
            });
            // if(this.nodePoints>30){
            // this.changeImgNode({ network, paramsId })
            // }
          }

        }
        // const dragEnd = function (e) {
        //    let that = this;
        //   if (that.isEdit) {
        //     const allNodes = network.body.nodes;
        //     if (e.nodes.length > 0) {
        //       let id = e.nodes[0];
        //       let { x, y } = e.pointer.canvas;
        //       // 先拿到选中点的数据
        //       let dataArray = [...that.nodeLevel3Array, ...that.nodeLevel2Array];
        //       let nodePoint = _.find(dataArray, (datas) => datas.id === id);
        //       if (nodePoint) {
        //         let addX = nodePoint.x - x;
        //         let addY = nodePoint.y - y;
        //         // 企业+相对位置
        //         that.enterpriseData.map((es) => {
        //           if (es.parentId === id) {
        //             let esNode = {
        //               // ...es,
        //               x: es.x - addX,
        //               y: es.y - addY,
        //               color: {
        //                 border: '#4690fe',
        //                 background: '#0b3a66',
        //               }
        //             };
        //             const node = allNodes[es.id];
        //             node.setOptions(esNode);
        //           }
        //         });
        //         //   let ids=[`${nodePoint.parentId}-${id}-a0`,`${nodePoint.parentId}-${id}-a`,`${nodePoint.parentId}-${id}-b`,`${nodePoint.parentId}-${id}-b0`  ]
        //         // 折线信息+相对位置
        //         // id:`${findParent.id}-${e.id}-a0`,
        //         // id:`${findParent.id}-${e.id}-a`,
        //         // id:`${findParent.id}-${e.id}-b`,
        //         // id:`${findParent.id}-${e.id}-b0`,
        //         // const option = allNodes[id];
        //         batchUpdateXY([
        //           { id, abscissaValue: x, ordinateValue: y },
        //         ]).then(() => {
        //           message({
        //             showClose: false, // 可关闭
        //             message: '请求成功',
        //             type: 'success',
        //           })
        //         });
        //       }

        //     }
        //   }
        //   // else{
        //   //   if(this.nodePoints>30){
        //   //   let scale = network.getScale()
        //   //     if (scale > 0.3) {
        //   //       // 绘制图标
        //   //       this.showLastOrg(network, 'getImg')
        //   //     }
        //   // }}
        // }
        const zoom = function (e) {
          // if (!this.isEdit&&this.nodePoints>30) {
          //     // zoom>1.6渲染文字
          //     // console.log('www',e)
          //     if (e.scale > 0.5) {
          //       // 绘制图标
          //       // console.log(e)
          //       this.showLastOrg(network)
          //     }
          //   }
          if (e.scale > 7) {
            // console.log('scale==>',e)
            let currentCenter = network.getViewPosition();
            network.moveTo({ scale: 7, position: currentCenter, });
          }

        }
        const hoverNode = function (e) {

          if (!that.isEdit) {
            const allNodes = network.body.nodes;
            const mainNodes = [...that.nodeLevel3Array, ...that.nodeLevel2Array];
            const node = _.find(mainNodes, (item) => item.id === e.node);
            //  mainNodes[e.node]
            if (node) {
              let { nodeName, localCount, countryCount, investCount, isLeaf } =
                node.nodeHoverData;
              //   /
              let updatedNodeStyle = that.htmlTitle(`
          <div class='nodeTips'  >
            <div>
              <div 
              style="background: #00112D;
               border: 1px solid #0066FF;
                  height: ${that.tab == 1 ? 70 : 95}px; 
                  position:absolute;
                  left: -8px; top: -6px; 
                  width: 110%;"></div>
              <div style="position:relative; line-height: 24px;opacity: 0.8;">
                当前节点：${nodeName}</br>
              ${isLeaf == 1
                  ? that.tab == 1
                    ? `本地企业数(家)： ${localCount || 0}`
                    : `本地/全国/招商企业数(家)： </br>
        ${localCount || 0}/${countryCount || 0}/${investCount || 0}`
                  : that.tab == 1
                    ? `本地企业数(家)：${countryCount}`
                    : `企业总数(家)：${countryCount}`
                }
              </div>
            </div>
          </div>
        `);

              let nodeStyle = {
                title: updatedNodeStyle,
              };

              allNodes[e.node].setOptions(nodeStyle);
            }
            else {
              let findNode = allNodes[e.node].options
              if (findNode.label) {
                let nodeStyle = {
                  image: findNode._image,
                  widthConstraint: 25,
                  label: findNode._name,
                  title: '',
                  font: {
                    size: 4,
                    color: "#fff",
                    // multi: true,
                    // face: "SourceHanSansCN",
                    vadjust: -13, //垂直位置距离
                  },
                  size: 8
                }
                // console.log('nodeStyle==>',nodeStyle.label)
                allNodes[e.node].setOptions(nodeStyle);
              }

            }

            //单击
            network.on("click", handleClick)
          }

        }
        const blurNode = function (e) {
          if (!that.isEdit) {
            const allNodes = network.body.nodes;
            const mainNodes = [...that.nodeLevel3Array, ...that.nodeLevel2Array];
            const node = _.find(mainNodes, (item) => item.id === e.node);
            if (!node) {
              let findNode = allNodes[e.node].options
              //  _.filter(childNode, (item) => item.id ===  allNodes[e.node].options.nodeId)[0]
              if (findNode.label) {
                let nodeStyle = {
                  image: findNode._image,
                  widthConstraint: 13,
                  label: findNode._name,
                  size: 6,
                  font: {
                    size: 2.5,
                    color: "#fff",
                    // multi: true,
                    // face: "SourceHanSansCN",
                    vadjust: -10, //垂直位置距离
                  },
                }
                // console.log('nodeStyle22==>',nodeStyle.label)
                allNodes[e.node].setOptions(nodeStyle);
                // this.beginTwinkle(this.twinkleNode, this.size);
              }

            }
            network.off('click', handleClick);
          }
        }

        return {
          handleClick,
          // dragEnd,
          zoom,
          hoverNode,
          blurNode
        };
      })();
      if (!that.isEdit) {
        // 节点悬停
        network.on("hoverNode", module.hoverNode);
        // 节点失焦
        network.on("blurNode", module.blurNode);
      }
      network.on("dragEnd", (e)=>{
          //  let that = this;
          if (that.isEdit) {
            const allNodes = network.body.nodes;
            if (e.nodes.length > 0) {
              let id = e.nodes[0];
              let { x, y } = e.pointer.canvas;
              // 先拿到选中点的数据
              let dataArray = [...that.nodeLevel3Array, ...that.nodeLevel2Array];
              let nodePoint = _.find(dataArray, (datas) => datas.id === id);
              if (nodePoint) {
                let addX = nodePoint.x - x;
                let addY = nodePoint.y - y;
                // 企业+相对位置
                that.enterpriseData.map((es) => {
                  if (es.parentId === id) {
                    let esNode = {
                      // ...es,
                      x: es.x - addX,
                      y: es.y - addY,
                      color: {
                        border: '#4690fe',
                        background: '#0b3a66',
                      }
                    };
                    const node = allNodes[es.id];
                    node.setOptions(esNode);
                  }
                });
                //   let ids=[`${nodePoint.parentId}-${id}-a0`,`${nodePoint.parentId}-${id}-a`,`${nodePoint.parentId}-${id}-b`,`${nodePoint.parentId}-${id}-b0`  ]
                // 折线信息+相对位置
                // id:`${findParent.id}-${e.id}-a0`,
                // id:`${findParent.id}-${e.id}-a`,
                // id:`${findParent.id}-${e.id}-b`,
                // id:`${findParent.id}-${e.id}-b0`,
                // const option = allNodes[id];
                batchUpdateXY([
                  { id, abscissaValue: x, ordinateValue: y },
                ]).then(() => {
                  message({
                    showClose: false, // 可关闭
                    message: '修改成功',
                    type: 'success',
                  })
                });
              }

            }
          }
          // else{
          //   if(this.nodePoints>30){
          //   let scale = network.getScale()
          //     if (scale > 0.3) {
          //       // 绘制图标
          //       this.showLastOrg(network, 'getImg')
          //     }
          // }}
        });
      network.on("zoom", module.zoom);
    },
    /*********************************************  **********************************************/
    getInfoNoAjax() {
      // console.time('耗时1')
      let res = this.$store.getters.visViewData
      this.nodePoints = res.childNodes?.length
      this.originData = res.childNodes
      this.headerViewData = {
        count: res.count,
        coverRadio: res.coverRadio,
        investCount: res.investCount,
        localCount: res.localCount
      }

      if (!this.isEdit) {
        this.drawingTree();
      } else {
        this.drawingTreeEdit();
      }
      // console.timeEnd('耗时1')
    },
    getInfo() {
      const id = this.$route.query?.id || getPathId()|| null;
      if (!id) {
        return;
      }
      this.loading = true;
      let data = {
        id: parseInt(id),
        enterpriseType: 4, // 企业类型
        secondQueryParam: this.searchValueData // 企业名称
      };
      // console.time('耗时2')
      getList(chartApi.atlasRelation, data).then((res) => {
        this.$store.dispatch('visView/changeViewData', res)
        this.nodePoints = res.childNodes?.length
        this.originData = res.childNodes
        this.headerViewData = {
          count: res.count,
          coverRadio: res.coverRadio,
          investCount: res.investCount,
          localCount: res.localCount
        }
 
        if (!this.isEdit) {
          this.drawingTree();
        } else {
          this.drawingTreeEdit();
        }
        this.loading = false;
        // console.timeEnd('耗时2')
      }).catch(() => {
        this.loading = false;
      });

    },
    // // 关闭详情
    closeDetail() {
      this.detailInfoParams.type = "";
    },
    onGetSecondQueryParam(label) {
      this.changeTwinkState(true)
      this.closeDetail()
      this.onSearch();
      this.searchValueData = label
      this.getInfo()
      this.$refs.previewRef.showBody && this.$refs.previewRef.headClick()
    },
    beginEdit(state) {
      let that=this
      clearInterval(that.timer)
      that.timer = null
      that.isEdit = !state
      that.onSearch();
      that.getInfo();

    },
    // 全部/本地切换 - 底部选项 - 条件查询 - 重新渲染
    onSearch() {
      this.otherDataNodes = [];
      this.updatedImgNode = []; //已更新的图片节点数据
      this.showImgs = {}; //需要转化为img的数据
      // this.backboneNode = []; //次主节点node数据
      // this.blueAndRedData = []; //先渲染的企业节点，一圈内包含红色和绿色
      this.letterShowData = []; //最后渲染的企业节点，一圈内只有单个颜色
      this.letterShowNode = {};
      this.nodeLevel3Array = []; //所有 第二层级 node
      this.enterInfoObj = {}; //所有  包含父级的企业节点
      this.nodeLevel2Array = {}; //所有 第一层级 node
      this.enterpriseArray = []; //所有  不包含父级的企业 信息
      // this.updatedImgNode = []; //已更新的图片节点数据
      this.pointsEdges = []
      this.nodeData = {};
      this.network = null;
      this.editNetwork = null;
      this.searchValueData = ''
      this.treeState = ''

    },
    // 点击节点 - 打开详情
    getDetail(params, secondQueryParam) {
      this.$refs.previewRef.showBody && this.$refs.previewRef.headClick()
      // console.log("节点--详情", params, secondQueryParam, 'secondQueryParam');
      if (params.level >= 2) {
        this.detailInfoParams.secondQueryParam = this.searchValueData // 企业名称
        this.detailInfoParams.type = "graph";
        this.detailInfoParams.titleName = params.name;
        this.detailInfoParams.childId = params.paramsId;
        this.detailInfoParams.level = params.level;
        this.detailInfoParams.enterpriseType = params.enterpriseType;
        this.detailInfoParams.node = params.node
        this.changeId = params.paramsId;
      }
      return;
    },
    htmlTitle(html) {
      const container = document.createElement("div");
      container.innerHTML = html;
      return container;
    },
    searchTreeType(type) {
      this.$refs.previewRef.showBody && this.$refs.previewRef.headClick()
      this.changeTwinkState(true)
      this.treeStateSave = type;
      if (this.$refs.cutbtm.active !== '') {
        this.$refs.cutbtm.active = "";
      } else {
        this.onGetSecondQueryParam('')

      }
    },
    setGlobalAlpha(globalAlpha, type) {
      this.toolTips = true
      const list = [...this.nodeLevel3Array, ...this.nodeLevel2Array];
      const heightLightLevel2 = _.filter(
        this.nodeLevel2Array,
        (e) => e.nodePropose === type
      );
      const heightLightLevel3 = [];
      this.nodeLevel3Array.map((item) => {
        if (item.nodePropose === type) {
          if (item.nodeLevel === "4") {
            let firstFather = _.find(
              this.nodeLevel3Array,
              (father) => father.id === item.parentId
            );
            let secondFather = _.find(
              this.nodeLevel2Array,
              (father) => father.id === firstFather?.parentId
            );
            heightLightLevel3.push(
              ...[{ ...item }, { ...firstFather }, { ...secondFather }]
            );
          } else {
            let firstFather = _.find(
              this.nodeLevel2Array,
              (father) => father.id === item.parentId
            );
            heightLightLevel3.push(...[{ ...item }, { ...firstFather }]);
          }
        }
      });
      // 高亮的数据
      let uniHeightLights = _.uniqBy(
        [...heightLightLevel3, ...heightLightLevel2],
        "id"
      );
      // 置灰的数据
      let unHeightLights = _.xorBy(uniHeightLights, list, "id");
      let allNodes = this.network.body.nodes;
      uniHeightLights.map((es) => {
        this.setConfig(allNodes, es, 1);
      });
      unHeightLights.map((es) => {
        /************************  二级三级节点颜色置灰 *******************************/
        this.setConfig(allNodes, es, globalAlpha);
      });
      this.network.fit({
        animation: {
          duration: 200,
        },
      });
      this.toolTips = false
    },

    setConfig(allNodes, es, globalAlpha) {
      /************************  二级三级节点颜色置灰 *******************************/
      const node = allNodes[es.id];
      let updatedNodeStyle = {
        ...es,
        shape: "text",
        label: "text",
      };
      node.setOptions(updatedNodeStyle);
      updatedNodeStyle.shape = "custom";
      updatedNodeStyle.ctxRenderer = function ({
        ctx,
        x,
        y,
        state: { selected, hover },
        style,
      }) {
        // do some math here
        return {
          async drawNode() {
            let text =
              node.options?._label.length > 6
                ? `${node.options?._label.slice(0, 6)}...`
                : node.options?._label;
            let image = node.options._image || '';
            ctx.globalAlpha = globalAlpha;
            if (es.nodeLevel === "4" || es.nodeLevel === "3") {
              image && ctx.drawImage(image, x - 32, y - 9.75, 64, 19.5);
              ctx.font = "8px Arial";
              ctx.textAlign = "center";
              ctx.fillStyle = "#fff";
              ctx.fillText(text, x, y + 3);
              drawLine({ e: node.options, ctx, x: x - 20, y: y + 10, w: 40 });
            } else {
              image && ctx.drawImage(image, x - 120, y - 30, 240, 88);
              ctx.font = "25px Arial";
              ctx.textAlign = "center";
              ctx.fillStyle = "#fff";
              ctx.fillText(text, x, y + 7);
            }
          },
        };
      };
      allNodes[es.id].setOptions(updatedNodeStyle);
    },
    heightLight() {
      let allNodes = this.network?.body?.nodes;
      if (allNodes) {
        let twinkleNode = [];
        for (let key in allNodes) {
          let option = allNodes[key].options;
          if (option._enterpriseType && option._enterpriseType === "2") {
            twinkleNode.push(option);
          }
        }
        this.twinkleNode = twinkleNode
      }
    },
    beginTwinkle(twinkleNode) {
      // let allNodes = this.network?.body?.nodes;
      // if (!this.isEdit && allNodes) {
      //   twinkleNode.map((e) => {
      //     let node = allNodes[e.id];
      //     let twinkle = {
      //       ...e,
      //       size: 6,
      //       font: {
      //         size: 2.5,
      //         color: "white",
      //         vadjust: -10, //垂直位置距离
      //       },
      //     };
      //     let img =
      //       this.size === 8
      //         ? getImgs()[e._enterpriseType + "-" + e._enterpriseLabelType]
      //         : getImgs()[e._enterpriseType + "-" + e._enterpriseLabelType + "h"];
      //     twinkle.image = img;
      //     node?.setOptions(twinkle);
      //   });
      //   let currentCenter = this.network.getViewPosition();
      //   let newCenter = {
      //     x: currentCenter.x, // 在 x 轴上向右移动 50 个单位
      //     y: currentCenter.y, // 在 y 轴上向下移动 20 个单位
      //   };
      //   // 使用 moveTo 方法移动画布
      //   this.network.moveTo({
      //     position: newCenter,
      //     offset: { x: 0, y: 0 }, // 可选的偏移量
      //     scale: undefined, // 保持当前缩放级别
      //     animation: false, // 是否使用动画
      //   });
      //   this.timer = setTimeout(() => {
      //     this.size = this.size === 8 ? 6 : 8;
      //     this.beginTwinkle(twinkleNode, this.size);
      //   }, 2000);
      // }
    },
    changeTwinkState(state) {
      if (state) {
        clearInterval(this.timer)
        this.timer = null
      } else {
        this.beginTwinkle(this.twinkleNode);
      }

    },
    changeNodePosition(param,) {
      this.network.moveTo({
        scale: param.scale,
        position: { x: param.e.xvalue, y: param.e.yvalue },
      });
      // this.changeImgNode({ network:this.network, paramsId:param.e.id })
      //  animation: { duration: 200 }
    },
    /*********************************************** *******************************/
    // changeImgNode({ network, paramsId }){
    //       // 点击一个节点。渲染节点下数据
    //         // this.updatedImgNode 已更新的节点数据
    //         // 查找已更新的节点是否存在当前点击的节点，存在 return
    //         // console.log(this.updatedImgNode)
    //         let findData = null
    //         if (this.updatedImgNode && this.updatedImgNode.length > 0) {
    //           findData = _.filter(this.updatedImgNode, (item) => {
    //             return item === paramsId
    //           })[0] 
    //         }
    //         if (!findData) {
    //           this.updateNode(network,this.letterShowNode[paramsId]) 
    //           this.updatedImgNode.push(paramsId)
    //         }

    // },
    // showLastOrg (network) {
    //         // 1获取画布大小
    //         var canvas = document.getElementById("mynetwork");
    //         // 2 获取network 中心位置
    //         let networkCenter = network.getViewPosition();
    //         // 3获取当前画布缩放比
    //         let networkScale = network.getScale();
    //         // 4获取当前容器相对大小
    //         // 计算当前屏幕的边界框
    //         let position = {
    //           left: networkCenter.x - canvas.offsetWidth / networkScale / 2,
    //           right: networkCenter.x + canvas.offsetWidth / networkScale / 2,
    //           top: networkCenter.y - canvas.offsetHeight / networkScale / 2,
    //           bottom: networkCenter.y + canvas.offsetHeight / networkScale / 2,
    //         };

    //         for (let key in this.letterShowNode) {

    //           var nodesInScreen = network.getBoundingBox(key);
    //           let { bottom, left, right, top } = nodesInScreen;
    //           let { bottom: _bottom, left: _left, right: _right, top: _top } = position;

    //               // 5、对比位置bottom, left, right, top 值，在容器内的进行下一步操作
    //               if (
    //               right >= _left &&
    //               left <= _right &&
    //               bottom >= _top &&
    //               top <= _bottom
    //             ) {
    //               this.updateNode(network,this.letterShowNode[key]) 
    //           this.updatedImgNode.push(key)
    //             }




    //       }
    //       }


  },
};
</script>
<style lang="scss" scoped>
@import "./index.scss";

</style>
