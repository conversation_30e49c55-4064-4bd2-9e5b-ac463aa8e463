
import { findKey } from 'lodash';

export const getRanges = () => {
  return {
    "0-8": { min: 0, max: 8, num: "8", r: "40" },
    "8-24": { min: 8, max: 24, num: "16", r: "70" },
    "24-56": { min: 24, max: 56, num: "32", r: "105" },
    "56-88": { min: 56, max: 88, num: "32", r: "145" },
    "88-120": { min: 88, max: 120, num: "32", r: "195" },
    "120-184": { min: 120, max: 184, num: "64", r: "250" },
    "184-248": { min: 184, max: 248, num: "64", r: "305" },
    "248-376": { min: 248, max: 376, num: "128", r: "365" },
    "376-504": { min: 376, max: 504, num: "128", r: "430" },
  };
};

export const getRange = (length) => {
  const ranges = getRanges();
  let range;
  for (let key in ranges) {
    let r = ranges[key];
    if (length > r.min && length < r.max) {
      range = { ...r };
      break;
    }
    if (length === 0) {
      range = ranges["0-8"];
      break;
    } else if (length === r.min) {
      let newKey = findKey(ranges, function (o) {
        return o.max === length;
      });
      range = ranges[newKey];
      break;
    }
  }
  return range;
};

export const getXandY = (length, index, cx, cy) => {
  let range = getRange(length - 1);
  let dx = Math.cos(((360 / range.num) * index * Math.PI) / 180) * range.r;
  let dy = Math.sin(((360 / range.num) * index * Math.PI) / 180) * range.r;
  dx = +Number(cx) + dx;
  dy = +Number(cy) + dy;
  return {
    x: dx,
    y: dy,
  };
};
export const drawWrappedText = (context, text, x, y, maxWidth, lineHeight) => {
  let chunkSize = 6;

  // words
  let dividedArrays = splitArrayIntoChunks(chunkSize, text);
  let line = "";

  for (let i = 0; i < dividedArrays.length; i++) {
    let testLine = line + dividedArrays[i];
    let metrics = context.measureText(testLine);
    let testWidth = metrics.width;

    if (testWidth > maxWidth && i > 0) {
      context.fillText(line, x, y);
      line = dividedArrays[i];
      y += lineHeight;
    } else {
      line = testLine;
    }
  }

  context.fillText(line, x, y);
};
export const splitArrayIntoChunks = (chunkSize, text) => {
  let result = [];
  for (let i = 0; i < text.length; i += chunkSize) {
    result.push(text.slice(i, i + chunkSize));
  }
  return result;
};
 
export const getImageUtils = async (that) => {
  that.image_1 = await loadingImg({
    url: "https://static.idicc.cn/cdn/pangu/svg/tree-btn12.png",
  });
  that.image_2 = await loadingImg({
    url: "https://static.idicc.cn/cdn/pangu/svg/img14.png",
  });
  that.image_3 = await loadingImg({
    url: "https://static.idicc.cn/cdn/pangu/svg/img15.png",
  });
  that.image_4 = await loadingImg({
    url: "https://static.idicc.cn/cdn/pangu/svg/img16.png",
  });
  that.image_5 = await loadingImg({
    url: "https://static.idicc.cn/cdn/pangu/svg/btn-01.png",
  });
  that.image_6 = await loadingImg({
    url: "https://static.idicc.cn/cdn/pangu/svg/btn-011.png",
  });
  that.image_7 = await loadingImg({
    url: "https://static.idicc.cn/cdn/pangu/svg/btn-02.png",
  });
  that.image_8 = await loadingImg({
    url: "https://static.idicc.cn/cdn/pangu/svg/btn-03.png",
  });

};
async function loadingImg({ url }) {
  return await new Promise((resolve, reject) => {
    let img = new Image();
    img.src = url;
    img.onload = function () {
      resolve(img);
    };
    img.onerror = reject;
  });
}

export const getImgs = () => {
  return {
    "1-0": 'https://static.idicc.cn/cdn/pangu/svg/1-0.svg',
    "1-1": 'https://static.idicc.cn/cdn/pangu/svg/1-1.svg',
    "1-2": 'https://static.idicc.cn/cdn/pangu/svg/1-2.svg',
    "1-3": 'https://static.idicc.cn/cdn/pangu/svg/1-3.svg',
    "1-4": 'https://static.idicc.cn/cdn/pangu/svg/1-3.svg',
    "1-5": 'https://static.idicc.cn/cdn/pangu/svg/1-5.svg',
    "1-6": 'https://static.idicc.cn/cdn/pangu/svg/1-6.svg',
    "1-7": 'https://static.idicc.cn/cdn/pangu/svg/1-7.svg',
    "1-8": 'https://static.idicc.cn/cdn/pangu/svg/1-1.svg',
    "1-9": 'https://static.idicc.cn/cdn/pangu/svg/1-1.svg',
    "1-10": 'https://static.idicc.cn/cdn/pangu/svg/1-0.svg',
    "1-11": 'https://static.idicc.cn/cdn/pangu/svg/1-2.svg',
    "1-12": 'https://static.idicc.cn/cdn/pangu/svg/1-3.svg',
    "1-13": 'https://static.idicc.cn/cdn/pangu/svg/1-3.svg',
    "1-14": 'https://static.idicc.cn/cdn/pangu/svg/1-3.svg',
    "1-15": 'https://static.idicc.cn/cdn/pangu/svg/1-15.svg',
    "1-16": 'https://static.idicc.cn/cdn/pangu/svg/1-16.svg',
    "1-17": 'https://static.idicc.cn/cdn/pangu/svg/1-15.svg',
    "1-18": 'https://static.idicc.cn/cdn/pangu/svg/1-18.svg',
    "1-19": 'https://static.idicc.cn/cdn/pangu/svg/1-7.svg',
    "1-20": 'https://static.idicc.cn/cdn/pangu/svg/1-2.svg',
    "1-21": 'https://static.idicc.cn/cdn/pangu/svg/1-5.svg',
    "1-22": 'https://static.idicc.cn/cdn/pangu/svg/1-6.svg',

    "2-0": 'https://static.idicc.cn/cdn/pangu/svg/2-0.svg',
    "2-1": 'https://static.idicc.cn/cdn/pangu/svg/2-1.svg',
    "2-2": 'https://static.idicc.cn/cdn/pangu/svg/2-2.svg',
    "2-3": 'https://static.idicc.cn/cdn/pangu/svg/2-3.svg',
    "2-4": 'https://static.idicc.cn/cdn/pangu/svg/2-3.svg',
    "2-5": 'https://static.idicc.cn/cdn/pangu/svg/2-5.svg',
    "2-6": 'https://static.idicc.cn/cdn/pangu/svg/2-6.svg',
    "2-7": 'https://static.idicc.cn/cdn/pangu/svg/2-7.svg',
    "2-8": 'https://static.idicc.cn/cdn/pangu/svg/2-1.svg',
    "2-9": 'https://static.idicc.cn/cdn/pangu/svg/2-1.svg',
    "2-10": 'https://static.idicc.cn/cdn/pangu/svg/2-0.svg',
    "2-11": 'https://static.idicc.cn/cdn/pangu/svg/2-2.svg',
    "2-12": 'https://static.idicc.cn/cdn/pangu/svg/2-3.svg',
    "2-13": 'https://static.idicc.cn/cdn/pangu/svg/2-3.svg',
    "2-14": 'https://static.idicc.cn/cdn/pangu/svg/2-3.svg',
    "2-15": 'https://static.idicc.cn/cdn/pangu/svg/2-15.svg',
    "2-16": 'https://static.idicc.cn/cdn/pangu/svg/2-16.svg',
    "2-17": 'https://static.idicc.cn/cdn/pangu/svg/2-15.svg',
    "2-18": 'https://static.idicc.cn/cdn/pangu/svg/2-18.svg',
    "2-19": 'https://static.idicc.cn/cdn/pangu/svg/2-7.svg',
    "2-20": 'https://static.idicc.cn/cdn/pangu/svg/2-2.svg',
    "2-21": 'https://static.idicc.cn/cdn/pangu/svg/2-5.svg',
    "2-22": 'https://static.idicc.cn/cdn/pangu/svg/2-6.svg',

    "3-0": 'https://static.idicc.cn/cdn/pangu/svg/3-0.svg',
    "3-1": 'https://static.idicc.cn/cdn/pangu/svg/3-1.svg',
    "3-2": 'https://static.idicc.cn/cdn/pangu/svg/3-2.svg',
    "3-3": 'https://static.idicc.cn/cdn/pangu/svg/3-3.svg',
    "3-4": 'https://static.idicc.cn/cdn/pangu/svg/3-3.svg',
    "3-5": 'https://static.idicc.cn/cdn/pangu/svg/3-5.svg',
    "3-6": 'https://static.idicc.cn/cdn/pangu/svg/3-6.svg',
    "3-7": 'https://static.idicc.cn/cdn/pangu/svg/3-7.svg',
    "3-8": 'https://static.idicc.cn/cdn/pangu/svg/3-1.svg',
    "3-9": 'https://static.idicc.cn/cdn/pangu/svg/3-1.svg',
    "3-10": 'https://static.idicc.cn/cdn/pangu/svg/3-0.svg',
    "3-11": 'https://static.idicc.cn/cdn/pangu/svg/3-2.svg',
    "3-12": 'https://static.idicc.cn/cdn/pangu/svg/3-3.svg',
    "3-13": 'https://static.idicc.cn/cdn/pangu/svg/3-3.svg',
    "3-14": 'https://static.idicc.cn/cdn/pangu/svg/3-3.svg',
    "3-15": 'https://static.idicc.cn/cdn/pangu/svg/3-15.svg',
    "3-16": 'https://static.idicc.cn/cdn/pangu/svg/3-16.svg',
    "3-17": 'https://static.idicc.cn/cdn/pangu/svg/3-15.svg',
    "3-18": 'https://static.idicc.cn/cdn/pangu/svg/3-18.svg',
    "3-19": 'https://static.idicc.cn/cdn/pangu/svg/3-7.svg',
    "3-20": 'https://static.idicc.cn/cdn/pangu/svg/3-2.svg',
    "3-21": 'https://static.idicc.cn/cdn/pangu/svg/3-5.svg',
    "3-22": 'https://static.idicc.cn/cdn/pangu/svg/3-6.svg',
  };
};
