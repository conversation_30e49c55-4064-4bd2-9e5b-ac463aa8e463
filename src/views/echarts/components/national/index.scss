#mynetwork,
#myNetworkEdit {
  width: 100vw;

  &.sohwHeight {
    height: calc(100vh - 70px)
  }

  &.hideView {
    // height: 0;
    opacity: 0;
    display: none;
  }
}

.loading-info {
  position: inherit !important;
}

.line-box {
  display: flex;
  justify-content: space-between;
  position: absolute;
  right: 30px;
  bottom: 32px;
  width: 255px;

  .active.line-list {
    &-1 {
      background: center/contain no-repeat url(~@/assets/img/industry/bg/bgG.png);
    }

    &-2 {
      background: center/contain no-repeat url(~@/assets/img/industry/bg/bgY.png);
    }

    &-3 {
      background: center/contain no-repeat url(~@/assets/img/industry/bg/bgV.png);
    }
  }

  .line-list {
    width: 85px;
    height: 39px;
    border-radius: 10px 0px 10px 0px;
    font-size: 13px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 39px;
    opacity: 0.85;
    text-align: center;
    // cursor: pointer;
    // box-shadow: 0 0 7px 0 white inset;

    &-1 {
      background: center/contain no-repeat url(~@/assets/img/industry/bg/bgGn.png);
    }

    &-2 {
      background: center/contain no-repeat url(~@/assets/img/industry/bg/bgYn.png);
    }

    &-3 {
      background: center/contain no-repeat url(~@/assets/img/industry/bg/bgVn.png);
    }
  }
}

.save-btn {
  position: absolute;
  top: 55px;
  left: 221px;
  width: 90px;
  height: 32px;
  background: #0C2F66;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #58ABFF;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
}

.showBg.nav-header {
  background-image: linear-gradient(#021e46, #021e46);
}

.nav-header {
  width: 100%;
  height: 90px;
  position: fixed;
  // background: pink;
  padding-top: 5px;
  margin-top: 10px;
  // position: absolute;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  user-select: none;
  background: linear-gradient(0deg, rgb(0 0 0 / 23%) 0%, rgb(138 136 136 / 0%) 58.7646484375%);
}

.nav-center {
  width: 49%;
  padding: 0 35px 0 40px;
  background: left/contain no-repeat url(~@/assets/img/industry/bg/bg-left.png),
    23% 30px /120px 60px no-repeat url(~@/assets/img/industry/bg/bg-center.png),
    49% 30px /120px 60px no-repeat url(~@/assets/img/industry/bg/bg-center.png),
    78% 30px /120px 60px no-repeat url(~@/assets/img/industry/bg/bg-center.png),
    right/contain no-repeat url(~@/assets/img/industry/bg/bg-right.png);
  margin-top: -20px;
  height: 120px;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;

  // 
  .center1 {
    //   background:  167% 0/32px 46px  no-repeat url(~@/assets/img/industry/bg/bg-center.png);
    padding-left: 10px;
    // 27%;
    width: 25%;
  }

  .center3 {
    // background: 136%/115px 46px  no-repeat url(~@/assets/img/industry/bg/bg-center.png);
    // 27%;
    width: 23%;
  }

  .center2 {
    // background: 140%/117px 46px no-repeat url(~@/assets/img/industry/bg/bg-center.png);
    // 22%;
    width: 20%;
  }

  .center4 {
    padding-left: 10px;
    // 22%;
    width: 23%;
  }

  .center1 {
    .leftIcon {
      background: center/contain no-repeat url(~@/assets/img/industry/bg/icon_1.png);
    }
  }

  .center3 {
    .leftIcon {
      background: center/contain no-repeat url(~@/assets/img/industry/bg/icon_3.png);
    }

  }

  .center2 {
    .leftIcon {
      background: center/contain no-repeat url(~@/assets/img/industry/bg/icon_2.png);
    }
  }

  .center4 {
    .leftIcon {
      background: center/contain no-repeat url(~@/assets/img/industry/bg/icon_4.png);
    }
  }

  .nav-center-item {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;

    .leftIcon {
      width: 50px;
      height: 50px;

    }

    .rightText {
      width: calc(100% - 50px);
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;

      .title {
        width: 100%;
        font-size: 0.875rem;
        color: #FFFFFF;
        opacity: 0.85;
        display: flex;
        align-items: center;
      }

      .point {
        display: inline-block;
        width: 4px;
        height: 4px;
        border-radius: 4px;
        margin-right: 6px;

        &.red {
          background-color: #FF0000;
        }

        &.yellow {
          background-color: #C69F3A;
        }

      }

      .number {
        font-size: 1.56rem;
        font-family: YouSheBiaoTiHei;
        color: #10E9FC;

        span {
          font-size: 0.75rem;
          font-family: Source Han Sans CN;
          color: #10E9FC;
          opacity: 0.7;
        }
      }
    }
  }
}

.toolTips {
  width: 100%;
  font-size: 14px;
  text-align: center;
  top: 50%;
  position: absolute;
  color: #238fd1;
}

.reset{
  font-size: 0.875rem;
    color: #58ABFF;
    width: 5.625rem;
    height: 2rem;
    background: #0C2F66;
    border-radius: 0.25rem;
    top: 55px;
    left: 8rem;
    z-index: 11111;
    position: absolute;
    text-align: center;
    line-height: 2rem;
    cursor: pointer;
    &:hover{
      background: #0c2f6695;
    }
}