import _ from "lodash";
// import {
//   drawLine,
//   getRange,
//   getXandY,
//   getRanges,
//   getImageUtils,
//   getImgs,
// } from "./vis-graph-utils.js";
export const options = {
  interaction: {
    dragNodes: false,
    hideEdgesOnDrag: true, //拖动时隐藏边缘
    hideEdgesOnZoom: true, //缩放时隐藏边缘

    hoverConnectedEdges: false, // 当鼠标悬停在节点上时，与其连接的边将以高亮（高亮色）显示
    hover: true,
    // selectConnectedEdges: true, //选择节点后是否显示连接线
    // hoverConnectedEdges: true, //鼠标滑动节点后是否显示连接线
    // zoomView: true //是否能缩放画布
    // navigationButtons: true, // 如果为真，则在网络画布上绘制导航按钮。这些是HTML按钮，可以使用CSS完全自定义。
    // keyboard: {
    //   enabled: true,
    //   speed: {
    //     x: -1,
    //     y: -1,
    //     zoom: 0.02,
    //   },
    //   bindToWindow: true,
    // }, // 启用键盘快捷键
    selectConnectedEdges:false,
  },
  physics: {
    enabled: false,
  },
  //     manipulation: {
  // enabled: true
  // ,
  // initiallyActive: true,
  // addNode: true,
  // addEdge: true,
  // // editNode: undefined,
  // editEdge: true,
  // deleteNode: true,
  // deleteEdge: true,
  // controlNodeStyle:{
  //   // all node options are valid.
  // }
  // },
  // manipulation: {
  //     enabled: true
  //   },
 
  // chosen:{
  //         edge: function(values, id, selected, hovering){
  //             values.width = '0.3';
  //             values.color = 'red';
  //         },}
  // layout: {
  //   randomSeed: undefined,
  //   improvedLayout: true,
  //   clusterThreshold: 150,
  //   hierarchical: {
  //     enabled: false,
  //     levelSeparation: 150,
  //     nodeSpacing: 100,
  //     treeSpacing: 200,
  //     blockShifting: true,
  //     edgeMinimization: true,
  //     parentCentralization: true,
  //     direction: 'UD',        // UD, DU, LR, RL
  //     sortMethod: 'hubsize',  // hubsize, directed
  //     shakeTowards: 'leaves'  // roots, leaves
  //   }
  // },
};
// export const publicEdgesConfig = {
//   chosen: false,
//   smooth: {
//     type: "straightCross",
//   },
// };
export const getPoints = (node) => {
  let pointsNode = [];
  let pointsEdges = [];

  let points = node?.map((e) => {
    let findParent = _.find(node, (nodeItem) => nodeItem.id === e.nodeParent);
    let nodeLevel2Array = _.filter(
      node,
      (nodeItem) => nodeItem.nodeParent === e.nodeParent
    );
    let index =
      nodeLevel2Array && _.findIndex(nodeLevel2Array, (eid) => eid.id === e.id);
    // console.log(nodeLevel2Array,index)
    let width = e.nodeLevel === "2" ? 150 : 30;
    let width1 = findParent?.nodeLevel === "2" ? 150 : 30;
    // console.log(findParent)
    if (findParent) {
      let pointA = {
        x:
          nodeLevel2Array.length === 1
            ? Number(findParent?.xvalue)
            : index % 2 !== 0
            ? Number(findParent?.xvalue) +
              ((index + 1) * (width1 / nodeLevel2Array?.length)) / 2
            : Number(findParent?.xvalue) -
              ((index + 1) * (width1 / nodeLevel2Array?.length)) / 2,
        // x:findParent?.xvalue,
        y: Number(findParent?.yvalue),
      };
      let pointB = {
        x:
          nodeLevel2Array.length === 1
            ? Number(e.xvalue)
            : index % 2 !== 0
            ? Number(e.xvalue) +
              ((index + 1) * (width / nodeLevel2Array?.length)) / 2
            : Number(e.xvalue) -
              ((index + 1) * (width / nodeLevel2Array?.length)) / 2,
        // x:e.xvalue,
        y: Number(e.yvalue),
      };

      let nodes = [
        {
          shape: "image", //标签类型
          image: "",
          label: "",
          x: pointA.x,
          y: findParent?.yvalue,
          id: `${findParent.id}-${e.id}-a0`,
          //  hidden:true,
          size: 0,
        },
        {
          shape: "image", //标签类型
          image: "",
          label: "",
          x: pointA.x,
          y: pointB.y > 0 ? pointB.y - 40 : pointB.y + 40,
          id: `${findParent.id}-${e.id}-a`,
          //  hidden:true,
          size: 0,
        },
        {
          shape: "image", //标签类型
          image: "",
          label: "",
          x: e.xvalue,
          //  pointB.x,
          y: pointB.y > 0 ? pointB.y - 40 : pointB.y + 40,
          id: `${findParent.id}-${e.id}-b`,
          //  hidden:true,
          size: 0,
        },
        {
          shape: "image", //标签类型
          image: "",
          label: "",
          //  x:index%2!==0? (e.xvalue)+(index*(width/nodeLevel2Array?.length))/2:(pointB.x)-(index*(width/nodeLevel2Array?.length))/2,
          x: e.xvalue,
          //  pointB.x,
          y: pointB.y,
          id: `${findParent.id}-${e.id}-b0`,
          //  hidden:true,
          size: 0,
        },
      ];
    let pubConfig={
      width: e.nodeLevel === "3" ? 6 : 4,
      hoverWidth:0,
      //  e.nodeLevel === "3" ? 6 : 4,
      color: {
        color: e.nodeLevel === "3"?'#0d4273': "#093052",
        hover: e.nodeLevel === "3"?'#0d4273': "#093052",
        highlight: e.nodeLevel === "3"?'#0d4273': "#093052",
      },
 
        chosen: false,
        // smooth: false,
        smooth: {
          enabled:false,
          // type: "straightCross",
          // // forceDirection: ["horizontal", "vertical"],
          // roundness: 1,
        },
        points:true
    }
      let edges = [
        {
         ...pubConfig,
          from: `${findParent.id}-${e.id}-a0`,
          to: `${findParent.id}-${e.id}-a`,
        
        },
        {
          ...pubConfig,
          from: `${findParent.id}-${e.id}-a`,
          to: `${findParent.id}-${e.id}-b`,
         
        },
        {
          ...pubConfig,
          from: `${findParent.id}-${e.id}-b`,
          to: `${findParent.id}-${e.id}-b0`,
           
        },
      ];
      pointsNode = [...pointsNode, ...nodes];
      pointsEdges = [...pointsEdges, ...edges];
    }
  });
  // console.log(pointsEdges,pointsNode)
  return { pointsEdges, pointsNode };
};
export const getTreeEdges = (node, imgs) => {
  let configs = {
    chosen: false,
    // smooth: false,
    smooth: {
      enabled:false,
      // type: "straightCross",
      // // forceDirection: ["horizontal", "vertical"],
      // roundness: 1,
    },
  };
  let { image_1, image_2, image_5, image_3, image_4,image_6,image_7,image_8 } = imgs;
  // let that = this
  let nodes = [];
  let edges = [];
  let parentNodes = [];
  let enterInfo = {};
  let enterpriseArray = [];
  let nodeLevel3Array = [];
  // let nodeLevel2ChildObj={}
  let nodeLevel3Edges = [];

  node?.map((e) => {
    if (e.enterInfo && e.enterInfo.length > 0) {
      enterInfo[e.id] = e;
      enterpriseArray = [...enterpriseArray, ...e.enterInfo];
    }
    let obj = {
      shape: "custom",
      font: {
        color: "#fff",
      },
      size: 30, //图形大小
      physics: false,
      group: e.nodeLevel ? +e.nodeLevel : 4,
      id: e.id,
      _id: e.id,
      _label: e.nodeName,
      label: e.nodeName,
      x: +e.xvalue,
      y: +e.yvalue,
      isLeaf: e.isLeaf,
      parentId: e.nodeParent,
      nodeId:e.nodeId,
      nodeHoverData: {
        nodeName: e.nodeName,
        localCount: e.localCount,
        countryCount: e.countryCount,
        isLeaf: e.isLeaf,
        investCount:e.investCount,
      },
      nodePropose: e.nodePropose,
      nodeLevel: e.nodeLevel,
      opacity: 0.2,
    };

    // if(e.nodeLevel === '2'||e.nodeLevel === '3'){
    //     let child= _.filter(node,(item)=>item.nodeParent===e.id)
    //     nodeLevel2ChildObj[e.id]=child
    // }
    // 第一层级 产业类别
    if (e.nodeLevel === "2") {
      let image = image_1;
      if (e.nodePropose == "强链") {
        image = image_6;
      } else if (e.nodePropose == "补链") {
        image = image_7;
      } else if (e.nodePropose == "延链") {
        image = image_8;
      }
      obj._image = image;
      obj.ctxRenderer = function ({
        ctx,
        x,
        y,
        state: { selected, hover },
        style,
      }) {
        // do some math here
        return {
          async drawNode() {
         
            // console.log(image,e.nodePropose,e.nodeName,)
            let text =
              e.nodeName.length > 7
                ? `${e.nodeName.slice(0, 7)}...`
                : e.nodeName;
            image && ctx.drawImage(image, x - 120, y - 30, 240, 88);
            ctx.font = "25px Arial";
            ctx.textAlign = "center";
            ctx.fillStyle = "#fff";
            ctx.fillText(text, x, y+7);
          },
        };
      };
      nodes.push(obj);
      parentNodes.push(e);
    }
    // 第二层级 产业类别
    if (e.nodeLevel === "3" || e.nodeLevel === "4") {
      obj._node = e;
      let image = image_5;
      if (e.nodePropose == "强链") {
        image = image_2;
      } else if (e.nodePropose == "补链") {
        image = image_3;
      } else if (e.nodePropose == "延链") {
        image = image_4;
      }
      obj._image = image;
      obj.ctxRenderer = function ({
        ctx,
        x,
        y,
        state: { selected, hover },
        style,
      }) {
        // do some math here
        return {
          async drawNode() {
            let text =
              e.nodeName.length > 6
                ? `${e.nodeName.slice(0, 6)}...`
                : e.nodeName;
            // ctx.globalAlpha=0.2;
            image && ctx.drawImage(image, x - 32, y - 9.75, 64, 19.5);
            ctx.font = "8px Arial";
            ctx.textAlign = "center";
            ctx.fillStyle = "#fff";
            ctx.lineWidth = "3px";
            ctx.strokeStyle = "red";
            ctx.fillText(text, x, y+3);
            drawLine({ e: obj, ctx, x: x - 20, y: y + 10, w: 40 });
          },
        };
      };
      // nodes.push(obj)
      nodeLevel3Array.push(obj);
      nodeLevel3Edges.push({
        width: e.nodeLevel === "3" ? 6 : 4,
        hoverWidth:0,
        // hoverWidth: e.nodeLevel === "3" ? 6 : 4,
        color: {
          color: e.nodeLevel === "3"?'#0d4273': "#093052",
          hover: e.nodeLevel === "3"?'#0d4273': "#093052",
          highlight: e.nodeLevel === "3"?'#0d4273': "#093052",
        },
        from: e.id,
        to: e.nodeParent,
        ...configs,
      });
    }
  });
  parentNodes.map((edge, index) => {
    edges.push({
      width: 12,
      dashes: [36, 24],
      color: {
        opacity: 1,
        color: "#3370FF",
      },
      from: edge.id,
      to: parentNodes[index - 1]?.id,
      ...configs,
    });
  });

  return {
    nodes,
    edges,
    enterInfo,
    nodeLevel3Array,
    enterpriseArray,
    nodeLevel3Edges,
  };
};
export const drawLine = ({ e, ctx, x, y, w ,pointA,pointB}) => { 
  if (e?.isLeaf == 1) {
    ctx.beginPath();
    ctx.strokeStyle = "#3370FF";
    ctx.lineWidth = "1";
    ctx.rect(x, y, w, 1);
    ctx.arc(x - 0.5, y + 0.5, 0.5, 0.5 * Math.PI, 1.5 * Math.PI);
    ctx.arc(x + w + 0.5, y + 0.5, 0.5, 0, 2 * Math.PI);
    ctx.stroke();
    let p = e?.nodeHoverData?.localCount / e?.nodeHoverData?.countryCount;
    if (p && p < 0.05) {
      p = 0.05;
    }
    let red_w = p * w;
    if (red_w) {
      ctx.beginPath();
      ctx.strokeStyle = "#AE3718";
      ctx.lineWidth = "1";
      if (p == 1) {
        ctx.arc(x + w + 0.5, y + 0.5, 0.5, 0, 2 * Math.PI);
      }
      ctx.arc(x - 0.5, y + 0.5, 0.5, 0.5 * Math.PI, 1.5 * Math.PI);
      ctx.rect(x, y, red_w, 1);
      ctx.stroke();
    }
   let text=`本地/全国（家）：${e?.nodeHoverData?.localCount||0}/${e?.nodeHoverData?.countryCount||0}`
     // 绘制灰色背景矩形
     ctx.beginPath();
     ctx.fillStyle = "rgba(255,255,255,0.2)"; // 灰色
     ctx.fillRect(x, y+4,w, 7);
     // 写上相应颜色的文案
     ctx.font = "3px Arial";
     ctx.textAlign = "center";
     ctx.fillStyle = "#fff";
     ctx.fillText(text, x+20, y+8.5);
  }
};