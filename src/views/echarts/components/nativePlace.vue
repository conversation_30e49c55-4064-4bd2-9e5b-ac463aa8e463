<template>
  <div class="companyBox">
    <div class="comMount">
      企业总数（家）：<span>{{ enterpriseTotalCount }}</span>
    </div>
    <div class="NativePlace">
      <div
        :style="{
          'margin-left':
            (detailInfoParams.type !== 'graph') == 0
              ? minscreen
                ? '-24rem'
                : '-22rem'
              : '0px',
        }"
        class="img"
      >
        <img
          src="https://static.idicc.cn/cdn/pangu/top.png"
          class="i2"
        > 
        <img
          src="https://static.idicc.cn/cdn/pangu/bj.png"
          class="i1"
        >
        <span class="floating-text">
          {{ treeList.chainName?.replace('产业金脑·', '') }}·{{
            treeList.areaName
          }}
        </span>
        <img
          src="https://static.idicc.cn/cdn/pangu/buttom.png"
          class="i3"
        >
      </div>
    </div>
    <div
      v-loading="treeLoading"
      class="tree"
      customClass="loading-info-icon"
      element-loading-text=""
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgb(0 0 0 / 24%)"
      element-loading-color="#fff"
    >
      <industrydistribution
        ref="industrydistribution"
        :chain-list="conditionList"
        @Triggerparent="Triggerparent"
      />
    </div>
    <div v-if="screen">
      <ShortcutBtm
        ref="cutbtm"
        class="on"
        @onGetSecondQueryParam="cut"
      />
    </div>
  
    <DetailInfo
      :change-id="changeId"
      :detail-info-params="detailInfoParams"
      :second-query-param="secondQueryParam"
      :firm-typedetail="firmType"
      @closeDetail="closeDetail"
    />
  </div>
</template>

<script>
import { atlasAPI } from './../apiUrl';

import { queryAtlasSecondQueryParamAPI } from '../apiUrl';
import industrydistribution from './industrydistribution.vue';
import DetailInfo from '../large-screen/detail-info.vue'; //  - 企业详情
import { getPathId } from '@/utils/utils';
import ShortcutBtm from '../large-screen/shortcut-btm.vue';

export default {
  name: 'NativePlace',
  components: {
    industrydistribution,
    DetailInfo,
    ShortcutBtm,
  },
  data() {
    return {
      conditionList: [
        '上市企业',
        '新三板',
        '独角兽企业',
        '专精特新小巨人企业',
        '专精特新企业',
        '隐形冠军企业',
        '科技小巨人企业',
        '瞪羚企业',
      ],
      atpresent: '',
      treeLoading: false,
      enterpriseTotalCount: 0,
      screen: true,
      changeId: null, // 添加缺失的 changeId 属性
      firmType: null, // 添加缺失的 firmType 属性
      secondQueryParam: null, // 添加缺失的 secondQueryParam 属性
      minscreen: false, // 添加 minscreen 属性，因为在模板中使用了这个属性
      detailInfoParams: {
        titleName: '',
        type: '', // graph/map
        level: '', // 1 2 3
        paraimsId: this.$route.query?.id || getPathId() || null,
        // childId:"107"
        childId: null,
        secondQueryParam: null,
        enterpriseType: null,
        current: 'local',
        minscreen: false,
      },
      treeList: {},
    };
  },
  created() {
    if (window.innerHeight < 750) {
      this.minscreen = true;
    }
    this.getInfo();
    //this.getList();
  },

  methods: {
    getInfo() {
      this.treeLoading = true;
      atlasAPI({
        id: this.$route.query.id || getPathId() || null,
        enterpriseType: 1,
      }).then((res) => {
        this.treeLoading = false;
        this.treeList = res;
        this.enterpriseTotalCount = res.count;
        this.$refs.industrydistribution.init(res.childNodes);
      });
    },
    async getList() {
      const res = await queryAtlasSecondQueryParamAPI();
      this.conditionList = [...this.conditionList, ...res];
      //this.atpresent='全部'
      this.screen = true;
    },
    async cut(item) {
      this.closeDetail();
      // this.$emit('Closelist')
      try {
        this.treeLoading = true;
        if (item == this.atpresent) {
          this.atpresent = '';
        } else {
          this.atpresent = item;
        }
        const res = await atlasAPI({
          id: this.$route.query.id || getPathId() || null,
          enterpriseType: 1,
          secondQueryParam: this.atpresent == '全部' ? '' : this.atpresent,
        });
        this.$refs.industrydistribution.init(res.childNodes);
        this.enterpriseTotalCount = res.count;
      } finally {
        this.treeLoading = false;
      }
    },
    Triggerparent(it) {
      // this.$emit('Triggerparent', it, this.atpresent)
      this.onGetChild(it);
    },
    // 点击节点 - 打开详情
    onGetChild(params) {
      if (Number(params.level) >= 2) {
        // if (locality) {
        this.detailInfoParams.secondQueryParam =
          this.atpresent == '全部' ? '' : this.atpresent;
        // }

        this.detailInfoParams.type = 'graph';
        this.detailInfoParams.titleName = params.name;
        this.detailInfoParams.childId = params.paramsId;
        this.detailInfoParams.level = params.level;
        this.detailInfoParams.enterpriseType = params.enterpriseType;
        this.changeId = params.paramsId;
      }
      return;
    },
    // 关闭详情
    closeDetail() {
      this.detailInfoParams.type = '';
    },
  },
};
</script>

<style lang="scss" scoped>
@keyframes float {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateY(0);
  }
}

.comMount {
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #b0bbff;
  position: absolute;
  left: 40px;
  top: 65px;

  span {
    margin-left: 6px;
    font-size: 18px;
    font-family: Source Han Sans CN;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 0 15px #2097fb;
  }
}

.companyBox {
  display: flex;
}

.NativePlace {
  padding-top: 3%;
  margin-left: 5%;
  display: flex;

  .placeholder {
    width: 12rem;
  }

  .img {
    position: relative;

    //width: 630px;
    //height: 598px;
    //width: 504px; /* 630 * 0.8 = 504 */
    //height: 478.4px; /* 598 * 0.8 = 478.4 */
    .floating-text {
      position: absolute;
      //bottom: 352px;
      //right: 279px;
      bottom: 45vh;
      left: 10rem;
      font-size: 31px;
      font-family: PangMenZhengDao;
      font-weight: 400;
      color: #ffffff;
      background: linear-gradient(130deg, #53d8ff 0%, #ffffff 53.9306640625%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: float 2s ease-in-out infinite;
    }

    .i1 {
      position: relative;
      width: 41.6rem;
      height: 40rem;
    }

    .i2 {
      position: absolute;
      width: 12rem;
      height: 8.2rem;
      left: 24rem;
      top: 2rem;
    }

    .i3 {
      position: absolute;
      width: 12rem;
      height: 8.2rem;
      //left: 462px;
      //top: 630px;
      left: 24rem;
      top: 30rem;
    }
  }
}

.tree {
  margin-top: 2%;
}


 
</style>
