<template>
  <div
    :style="{ height: height, width: width }"
    class="mapCon"
  >
    <div
      v-if="isCount"
      style="
        width: 100px;
        height: 165px;
        position: absolute;
        bottom: 0px;
        z-index: 999;
        left: 50px;
      "
    />
    <div
      v-else
      style="
        width: 120px;
        height: 180px;
        position: absolute;
        bottom: 30px;
        z-index: 999;
        left: 15px;
      "
    />
    <div :class="formName == '产业洞察' ? 'mapSele' : 'mapSele2'">
      <el-select
        slot="screenTitleSearch"
        v-model="searchValue"
        clearable
        filterable
        class="cascader-screen"
        popper-class="select-hand"
        placeholder="产业环节"
        @change="onChange"
      >
        <el-option
          v-for="(item, index) in optionsList"
          :key="index"
          :label="item.nodeName"
          :value="item.id"
        />
      </el-select>
      <div
        :class="['left-btn', isCount ? 'is-count' : '']"
        style="z-index: 999999"
      >
        <span
          v-for="(city, idx) in cityData"
          v-show="
            city.countArea !==
              (cityData[idx + 1] ? cityData[idx + 1].countArea : undefined)
          "
          :key="idx"
          class="city-info"
          @click="mapReturn(city, idx)"
        >
          <em class="city-icon">{{ city.countArea !== '全国' ? '>' : '' }}</em>
          <em class="city-name">{{ city.countArea }}</em>
          <!--           <i
            v-if="idx==cityData.length-1"
            class="el-icon-arrow-up"
          /> -->
        </span>
      </div>
    </div>

    <div
      v-if="loading"
      v-loading="loading"
      class="loading-info"
      customClass="loading-info-icon"
      element-loading-text=""
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgb(0 0 0 / 14%)"
      element-loading-color="#fff"
    />
    <div
      class="mapBox"
      :class="isCount ? 'on' : ''"
    >
      <div
        :id="id"
        :class="className"
        :style="{ height: height, width: width }"
      />

      <div v-if="!isCount">
        <mapMix
          v-if="!showDetaial"
          :area-map-data="areaMapData"
          :showarea="showarea"
        />

        <ShortcutBtm
          ref="cutbtm"
          class="on"
          @onGetSecondQueryParam="onGetSecondQueryParam"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getPathId } from '@/utils/utils';
import * as echarts from 'echarts';
import { getCityJson, industryChainNodeAPI } from '../apiUrl';
import ShortcutBtm from '../large-screen/shortcut-btm.vue';
import mapMix from './mapMix.vue';
import { customerBatteryCityData, customerBatteryAreaData } from './mapData';

import {
  setMapData,
  OptionPrimeval,
  initEchart,
  geoCoordMapWay,
  batteryCityDataWay,
  fomatterData,
} from './nationalMapUtil';

export default {
  components: {
    mapMix,
    ShortcutBtm,
  },
  props: {
    className: {
      type: String,
      default: 'chart',
    },
    id: {
      type: String,
      default: 'chart',
    },
    width: {
      type: String,
      default: '200px',
    },
    height: {
      type: String,
      default: '200px',
    },
    getMapRelation: {
      type: Function,
      default: null,
    },

    selNum: {
      type: Number,
      default: null,
    },
    isCount: {
      type: Number,
      default: null,
    },
    searchNode: {
      type: String,
      default: '',
    },
    formName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: true,
      cityData: [{ countArea: '全国', areaCode: '100000' }],
      click_type: false, // 单双击 处理
      nowLevel: 1,
      areaType: '2',
      parentNodeData: {},
      requestDataOld: {}, // 保留原始数据
      chart: null,
      customerBatteryCityData: customerBatteryCityData, // 市 - 柱状图数据
      customerBatteryAreaData, // 区 - 柱状图数据
      areaParams: { areaType: 1, geoScope: [300, 0] }, // areaType:1, // 当前地图级别 1 市 2 区    2不可放大缩小
      geoScope: [300, 0], // 分段 - 最大值/最小值
      currentName: '', // 地图下钻 - 地图名称
      nowCityName: [], // ['晋中','介休']

      mapShowCity: '', // 地图所展示的省市区数据
      mapShowData: [], // 地图所展示的数据 - value

      areaJson: { nowNode: {}, childNode: {} },
      searchValueData: null,

      childObj: { areaCode: null, areaType: null },

      searchValue: null,
      optionsList: null,

      areaMapData: null,
      showarea: null,
      showDetaial: false,
    };
  },
  watch: {
    selNum(val) {
      if (!val) {
        return;
      }
    },
  },

  async mounted() {
    this.getOptionsList();
    
    this.nowLevel = 1;
    this.initChart();
  },

  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    onChange() {
      // console.log('this.searchValue', this.searchValue)
      this.$emit('OnSearch', this.searchValue);
      this.$emit('closeDetail');
      this.initChart();
      // this.cityData= [{countArea:'全国',areaCode:'100000'}];
    },

    async getOptionsList() {
      const params = { id: this.$route.query?.id || getPathId() || null };
      const res = await industryChainNodeAPI(params);
      this.optionsList = res;
    },

    onGetSecondQueryParam(secondQueryParam) {
      // console.log('secondQueryParam', secondQueryParam);
      this.$emit('onSelType', secondQueryParam);
      this.initChart();
    },


    // 城市json数据  通过接口请求 参数变量为code
    getCityJsonWay(area, areaCode, level, areaType) {
      return new Promise((resolve, reject) => {
        getCityJson(areaCode, areaType, this.cityData)
          .then((res) => {
            const cityData = JSON.parse(res);
            
            // const cityData = res;
            const newCity = [];
            cityData.features.forEach((city) => {
              let { type, geometry, properties } = city;
              if (properties.name == '甘肃省') {
                properties.cp = [95.823557, 40.058039]; // [103.823557, 36.058039]
              }
              if (properties.name == '河北省') {
                properties.cp = [115.502461, 38.045474]; // [114.502461, 38.045474]
              }
              if (properties.name == '香港特别行政区') {
                //properties.cp = [114.793355, 22.620048]; // [114.173355, 22.320048]
                properties.cp = [115.793355, 22.2]; // [114.173355, 22.320048]
              }
              if (properties.name == '澳门特别行政区') {
                //properties.cp = [113.05909, 21.95]; // [113.54909, 22.198951]
                properties.cp = [113.05909, 21.2]; // [113.54909, 22.198951]
              }
              if (properties.name == '上海市') {
                properties.cp = [121.492644, 31.231706]; // [121.472644, 31.231706]
              }
              if (properties.name == '天津市') {
                properties.cp = [117.490182, 39.125596]; // [117.190182, 39.125596]
              }
              if (properties.name == '内蒙古自治区') {
                properties.cp = [111.670801, 41.818311]; // [111.670801, 40.818311]
              }
              if (properties.name == '重庆市') {
                properties.cp = [106.504962, 29.533155]; // [106.504962, 29.533155]
              }
              // if(properties.name == '宁夏回族自治区'){
              //   properties.cp = [106.504962, 29.533155]; // [106.504962, 29.533155]
              // }
              // properties.cp = properties.center;
              newCity.push({ type, geometry, properties });
            });

            const geoCoordMap = geoCoordMapWay(
              this.nowLevel == 1
                ? { type: 'FeatureCollection', features: newCity }
                : cityData,
              level
            ); // 地图 名称/中心经纬度
            if (level === 1) {
              this.areaJson.nowNode = geoCoordMap;
            } else {
              this.areaJson.childNode = geoCoordMap;
            }
            resolve(cityData);
          })
          .catch((error) => {
            this.loading = false;
            reject(error);
          });
      });
    },

    // 后端接口请求
    mapRequestWay() {
      if (this.getMapRelation) {
        return new Promise((resolve, reject) => {
          this.getMapRelation()
            .then((res) => {
              this.requestDataOld = res;
              resolve(res);
            })
            .catch((error) => {
              this.loading = false;
              reject(error);
            });
        });
      }
    },

    // 后端初始化数据到echart  向后端请求数据的数据 进行处理echart所需要的
    setInitData(requestData, level) {
      const { areaType, chainName, maxNum, minNum } = requestData;
      this.areaParams = { areaType, chainName, geoScope: [maxNum, minNum] };
      if (level === 1) {
        this.nowCityName[0] = requestData.area;
      }
      this.mapShowCity = requestData.area; // 地图所展示的省市区数据
      this.mapShowData = fomatterData(requestData.areaEnterpriseCountDTOList); // 地图所展示的数据 - value
    },

    // 初始化 - 地图
    async initChart(areaCode, areaType) {
      this.loading = true;
      const level = this.nowLevel;
      
      try {
        const res = await this.mapRequestWay();
        let { area, areaEnterpriseCountDTOList, areaEnterpriseProportionDTOS } = res;
        
        this.areaMapData = areaEnterpriseProportionDTOS;
        this.showarea = area;
        this.areaType = areaType || res.areaType;
        this.setInitData(res, level);

        // 处理全国地图
        if (res.areaType === '1') {
          res.areaCode = '100000';
          // 加载中国地图数据
        }

        if (res.areaType == 4) {
          areaCode = res.areaEnterpriseCountDTOList[0].areaCode;
        }

        // 获取城市 JSON 数据
        const cityJson = await this.getCityJsonWay(
          res.area,
          areaCode || res.areaCode,
          level,
          areaType || res.areaType
        );

        if (res.areaType === '1') {
          area = 'china';
        }

        const options = this.setOptionWay({
          area,
          areaList: areaEnterpriseCountDTOList,
          cityJson,
        });

        // 初始化地图
        let myChart = echarts.init(document.getElementById(this.id));
        let obj = {};
        
        cityJson.features.map((e) => {
          if (e.properties.name === '三沙市') {
            obj = {
              三沙市: {
                left: 110,
                top: 17.9,
                width: 0.5,
              },
            };
            delete e.properties.center;
          }
          return e;
        });

        // 注册地图（除了中国地图，其他都在这里注册）
        // if (area !== 'china') {
          echarts.registerMap(area, cityJson, obj);
        // }

        if (this.chart) {
          this.chart.clear();
        }
        
        myChart.setOption(options);
        this.chart = myChart;

        this.chart.off('click');
        this.mapClick();

        this.chart.off('dblclick');
        this.dblclick();
        
      } catch (error) {
        console.error('初始化地图失败:', error);
      } finally {
        this.loading = false;
      }
    },

    // option 属性设置
    setOptionWay({ area, areaList, cityJson }) {
      // name/value 数据获取  用于展示悬浮数据量
      const batteryCityData = batteryCityDataWay(areaList);
      // 预留 - 格式化数据 - 进行数据设置   没处理逻辑
      const { mapName, mapData } = setMapData(area, batteryCityData);

      // cityName guangdong 城市名称 intervalList 计算左侧分段数据 - regionsData 公共 地图边框颜色
      let { cityName, intervalList, regionsData, regionsData1 } = initEchart(
        mapName,
        mapData,
        this.areaParams.geoScope,
        cityJson
      );
      let isCount = this.isCount;
      const Option = OptionPrimeval(
        cityName, // mapName 地图名称
        intervalList, // areaList?.length > 0 ? 0 : intervalList,   // intervalList 左下角侧数据分段
        regionsData, // regionsData 地图颜色描边
        regionsData1, // regionsData1 地图颜色描边
        this.areaParams.geoScope, // geoScope 左侧数据区间设置
        mapData, // 地图数据量
        this.nowCityName, // 当前所选城市级别
        this.cityData,
        isCount
      );
      return Option;
    },

    // 双击到下级
    dblclick() {
      this.chart.on('dblclick', (params) => {
        this.click_type = true;
        this.$emit('cityDataList', this.cityData);
        const administrative = ['澳门特别行政区', '台湾省', '香港特别行政区'];
        const noprovince = ['峪关市', '中山市', '东莞市', '儋州市'];
        if (administrative.includes(params.name)) {
          return this.$message({
            message: '暂无收录企业',
            type: 'error',
            customClass: 'admin-tips-message',
            duration: 3000,
            showClose: true,
            center: false, // 是否居中
          });
        }
        if (noprovince.includes(params.name) && this.cityData.length >= 2) {
          //return this.$message('不能再向下啦~');
          return this.$message({
            message: '无下级区县',
            type: 'error',
            customClass: 'admin-tips-message',
            duration: 3000,
            showClose: true,
            center: false, // 是否居中
          });
        }
        if (params.componentType === 'geo') {
          this.$emit('closeDetail');
          if (this.cityData.length >= 4) {
            return this.$message({
              message: '不能再向下啦~',
              type: 'error',
              customClass: 'admin-tips-message',
              duration: 3000,
              showClose: true,
              center: false, // 是否居中
            });
          } else {
            this.nowCityName[1] = params.name;
            const childArea =
              this.requestDataOld.areaEnterpriseCountDTOList.find(
                (childItem) => childItem.countArea === params.name
              );
            if (!childArea) {
              if (params.name == '台湾省') {
                return this.$message({
                  message: '不能再向下啦~',
                  type: 'error',
                  customClass: 'admin-tips-message',
                  duration: 3000,
                  showClose: true,
                  center: false, // 是否居中
                });
              }
              this.$message({
                type: 'info',
                message: `${params.name} - 暂无数据`,
                customClass: 'admin-tips-message',
                duration: 3000,
                showClose: true,
                center: false, // 是否居中
              });
              return;
            } else {
              this.nowLevel = this.nowLevel + 1;
              const noData = ['重庆市', '北京市', '上海市', '天津市'];
              if (noData.includes(childArea.countArea)) {
                this.cityData.push(childArea);
              }
              this.cityData.push(childArea);
              this.areaType = childArea.areaType;
              this.$emit('setFetchParams', this.cityData);
              this.initChart(childArea.areaCode, childArea.areaType);
              this.childObj.areaCode = childArea.areaCode;
              this.childObj.areaType = childArea.areaType;
              //this.$emit('initData')
            }
          }
        }
      });
    },

    // 地图点击
    mapClick() {
      this.chart.on('click', (params) => {
        const { componentType, componentIndex } = params;

        // 返回上级 - 点击
        if (componentType === 'graphic') {
          const cityData = this.cityData;
          if (componentIndex == 0) {
            // 找到上级数据
            this.nowLevel = this.nowLevel - 1;
            this.cityData.pop();
            const preArea = cityData[cityData.length - 1];
            this.areaType = preArea.areaType;
            this.$emit('setFetchParams', this.cityData);
            if (preArea.areaCode === '100000') {
              this.initChart();
            } else {
              this.initChart(preArea.areaCode, preArea.areaType);
            }
          }
        }

        // 详情点击
        if (componentType === 'geo') {
          this.click_type = false;
          setTimeout(() => {
            if (this.click_type != false) return;
            this.showDetaial = true;
            // 点击 - 打开详情
            this.$emit('onGetChild', {
              ...params,
              type: 'map',
              cityData: this.cityData,
            });
          }, 200);
        }
      });
    },

    // 地图 -  返回特定层级
    mapReturn(city, idx) {
      this.$emit('closeDetail');
      this.nowLevel = idx + 1;
      this.cityData = this.cityData.splice(0, idx + 1);
      this.$emit('cityDataList', this.cityData);
      this.$emit('setFetchParams', this.cityData);
      if (city.areaCode === '100000') {
        this.initChart();
      } else {
        this.initChart(city.areaCode, city.areaType);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-input--suffix .el-input__inner {
    display: inline-block !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    font-size: 13px;
  }

  .el-select > .el-input {
    //width: 7.8rem !important;
    width: 115px !important;
  }
}

.mapCon {
  position: relative;

  .mapSele {
    padding-top: 4.7rem;
  }
  .mapSele2 {
    padding-top: 2rem;
  }
  .mapSele,
  .mapSele2 {
    padding-left: 40px;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 999;
    width: 80%;
    top: 0px;

    .cascader-screen {
      margin-top: 2px;
    }

    &::v-deep {
      .el-input__inner {
        height: 32px;
        line-height: 32px;
        background: url('https://static.idicc.cn/cdn/pangu/assets/screen/new/chanyelian.webp') no-repeat;
        background-size: 100% 100%;
        border: 0;
        color: #fff;
      }
      .el-input__suffix {
        top: 4px;
      }
    }
  }

  .chart-container {
  }

  .mapBox {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    // background: url('https://static.idicc.cn/cdn/pangu/assets/img/HUD.png') no-repeat;
    // background-size: contain;
    // background-position-x: 36%;

    &.on {
      top: 52px;
      height: calc(100% - 52px);
    }
  }

  .left-btn.is-count {
    font-size: 16px;

    .city-info {
      font-size: 16px;
      .city-icon {
        color: rgba(255, 255, 255, 0.32) !important;
      }
      em {
        font-size: 16px;
      }
    }
  }

  .left-btn {
    color: #ffffffe3;
    font-size: 18px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    z-index: 9;
    cursor: pointer;

    .city-info {
      float: left;
      position: relative;
      .city-icon {
        color: rgba(255, 255, 255, 0.32) !important;
      }
      em {
        font-style: normal;
        margin: 3px;
        font-size: 18px;
        font-family: Source Han Sans CN;
        font-weight: 400;
      }
      &:not(:last-child) {
        color: rgba(255, 255, 255, 0.32);
      }
      // &:last-child {
      //   &::before {
      //     content: '';
      //     position: absolute;
      //     bottom: -15px;
      //     left: 50%;
      //     transform: translateX(-46%);
      //     width: 31px;
      //     height: 10px;
      //     background: url('https://static.idicc.cn/cdn/pangu/assets/img/icon06.png') no-repeat center;
      //   }
      // }
    }

    .city-info:hover {
      .city-name {
        color: #fff;
      }
    }
  }
}

.loading-info {
  position: fixed;
  top: 0rem;
  left: 0rem;
  color: #ffffffe3;
  font-size: 1.2rem;
  z-index: 99;
  width: 100%;
  height: 100%;
}

::v-deep {
  .el-loading-spinner i {
    display: none;
  }

  .el-loading-spinner {
    width: 100px;
    height: 100px;
    top: calc(50% - 50px);
    left: calc(50% - 50px);
    transform: translate(-130px, -130px);
    background: url('https://static.idicc.cn/cdn/pangu/assets/screen/loading.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    animation: load 2s linear infinite;
  }

  @keyframes load {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .loading-info .el-loading-spinner .el-loading-text {
    font-size: 3rem;
    color: #dce1ea;
  }

  .el-loading-mask {
    // background-color: #ffffff26 !important;
  }
}
</style>
