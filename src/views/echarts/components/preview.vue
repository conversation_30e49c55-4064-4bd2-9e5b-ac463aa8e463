<template>
  <!--      v-if="newGraphData && newGraphData.length > 0" -->
  <div
    class="preview"
    :class="!isThree ? 'on' : null"
  >
    <div
      class="preview-header"
      :class="showBody ? 'on' : null"
      @click="headClick()"
    >
      <img
        class="img1"
        src="https://static.idicc.cn/cdn/pangu/icon05.png"
        alt=""
      >
      <img
        class="daolan"
        src="https://static.idicc.cn/cdn/pangu/daolan.png"
        alt=""
      >
      <img
        class="img2"
        src="https://static.idicc.cn/cdn/pangu/icon03.png"
        alt=""
        srcset=""
      >
    </div>
    <!--  @change="search" -->
    <div class="previewSearch">
      <el-input
        v-model="searchKey"
        placeholder="请输入节点名称"
        prefix-icon="el-icon-search"
        @focus="focusInput"
        @input="inputChange"
      />
    </div>
   
    <transition name="slide-fade">
      <div
        v-if="showBody"
        class="preview-con"
      >
        <div class="preview-body">
          <div
            v-if="newGraphData && newGraphData?.length > 0"
            class="tree-box"
          >
            <!-- <div class="line0" :style="`height:${lineH + 5}px;`" /> -->
            <div
              v-for="(item, i) in newGraphData"
              :key="i"
              class="list"
            >
              <div
                class="line0"
                :style="`height:102%`"
              />
              <div
                class="btn btn-1"
                :class="active1 == item.id ? 'on' : ''"
                @click="eventClick(item, [item.id])"
              >
                <el-tooltip
                  effect="dark"
                  :visible-arrow="false"
                  popper-class="tag-popover"
                  :content="item.nodeName"
                  placement="left"
                >
                  <span
                    style="
                      white-space: nowrap;
                      display: block;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      margin-left: 2px;
                    "
                  >
                    {{ item.nodeName }}
                  </span>
                </el-tooltip>
              </div>
              <div
                class="line"
                :style="
                  'height:' +
                    (item.childNodes.length * 26 + +item.len * 26) / 2 +
                    'px;'
                "
              />
              <div class="list1">
                <div
                  class="line1"
                  :style="
                    'height:' +
                      (+(item.childNodes.length * 34 - 34) + +item.len * 26) +
                      'px;'
                  "
                />
                <div
                  v-for="(item_1, i_1) in item.childNodes"
                  :key="i_1"
                  class="list1-top"
                >
                  <div
                    :class="
                      item.childNodes.length > 1
                        ? ' list1-top-line'
                        : 'list1-top-lines'
                    "
                  />
                  <div
                    class="btn btn-2"
                    :class="active2 == item_1.id ? 'on' : null"
                    @click="eventClick(item_1, [item.id, item_1.id])"
                  >
                    <el-tooltip
                      effect="dark"
                      :visible-arrow="false"
                      popper-class="tag-popover"
                      :content="item_1.nodeName"
                      placement="left"
                    >
                      <span
                        style="
                          white-space: nowrap;
                          display: block;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          margin-left: 2px;
                        "
                      >
                        {{ item_1.nodeName }}
                      </span>
                    </el-tooltip>
                  </div>
                  <div class="list2">
                    <div
                      v-if="item_1.childNodes.length"
                      class="line2"
                      :style="
                        'height:' +
                          ((item_1.childNodes.length * 26) / 2 - 4) +
                          'px;'
                      "
                    />
                    <div
                      v-if="item_1.childNodes.length"
                      class="line2-1"
                      :style="
                        'height:' + (item_1.childNodes.length * 26 - 26) + 'px;'
                      "
                    />
                    <div
                      v-for="(item_2, i_2) in item_1.childNodes"
                      :key="i_2"
                      class="list2-top"
                    >
                      <div class="list2-top-line" />
                      <div
                        class="btn btn-3"
                        :class="active3 == item_2.id ? 'on' : null"
                        @click="
                          eventClick(item_2, [item.id, item_1.id, item_2.id])
                        "
                      >
                        <el-tooltip
                          effect="dark"
                          :visible-arrow="false"
                          popper-class="tag-popover"
                          :content="item_2.nodeName"
                          placement="left"
                        >
                          <span
                            style="
                              white-space: nowrap;
                              display: block;
                              overflow: hidden;
                              text-overflow: ellipsis;
                              margin-left: 2px;
                            "
                          >
                            {{ item_2.nodeName }}
                          </span>
                        </el-tooltip>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="!loading&&!(newGraphData && newGraphData?.length>0)"
            class="noData"
          >
            暂无数据
          </div>
          <!-- <div class="toolTips"> -->
          <!-- 数据计算中。。。 -->
          <div
            v-if="loading"
            class="el-loading-spinner"
          >
            <i class="el-icon-loading" />
          </div>
        </div>
        <!-- </div> -->
      </div>
    </transition>
  </div>  
</template>

<script>
import { getTree } from "../apiUrl";
import { debounce } from 'lodash';
import { getPathId } from '@/utils/utils'

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: "Preview",

  props: {
    // propName: {
    //   type: Number,
    //   default: 0,
    // },
    // graphData: {
    //   // eslint-disable-next-line vue/require-prop-type-constructor
    //   type: Object | Array,
    //   default: null,
    // },
    network: {
      // eslint-disable-next-line vue/require-prop-type-constructor
      type: Object | Function,
      default: null,
    },
  },
  data() {
    return {
      showBody: false,
      active1: "",
      active2: "",
      active3: "",
      isThree: false, // 是否有第三级
      lineH: 0,
      searchKey: "",
      allNodes:null,
      newGraphData:[],
      loading:false,
    };
  },
  watch:{
    showBody(val) {
      if(val){
        this.getTreeData();
      }
      }
  },
  mounted() {
    // this.getTreeData();
    this.$emit("changeTwinkState",true);
  },
  beforeUnmount() {
    this.searchKey=''
  },
  methods: {
    focusInput(){
      this.showBody = true
      this.$emit("changeTwinkState",true);
    },
    getTreeData() {
      this.loading=true
      const id = this.$route.query?.id || getPathId()|| null;
      if (!id) {
        return;
      }
      let data = {
        id: parseInt(id),
        key: this.searchKey,
      };
      this.newGraphData=[]
      getTree(data).then((res) => {
      
        this.allNodes=res.childNodes
        this.getNewGraphData(res.childNodes)
        this.loading=false
      }).catch(() => {
        this.loading=false
      });
      
    },
    getNewGraphData(childNodes) {
      let graphData = childNodes
     let newData=   graphData?.map((e, n) => {
          let len = 0,
            num = 0;
          e.childNodes.map((el, i) => {
            num = num + el.childNodes.length;
            if (e.childNodes.length - 1 !== i) {
              len = len + el.childNodes.length;
            }
          });
          e.num = num;
          e.len = len;
          if (n + 1 !== graphData.length) {
            this.lineH = this.lineH + e.childNodes.length * 34 + num * 26 + 24;
          }
          if (!this.isThree && num > 0) {
            this.isThree = true;
          }
          return e;
        });
       this.newGraphData=newData
    },
    // 放大
    eventClick(e, arr) {
      // this.network.fit({
      //   nodes: [e.id],
      //   // animation: {
      //   //   duration: 500,
      //   // },
      // });
      // let network = this.network;
      // window.network = this.network;
      // setTimeout(() => {
      //   network.focus(e.id,{scale:4,animation:{duration:200}})
      // }, 200);
      if (arr.length === 1) {
        this.active1 = e.id;
        this.active3 =''
        this.active2 =''
        this.$emit("changeNodePosition", {
          scale: 1.5,
          // network: this.network,
          paramsId: e.id,
          e
        });
        // this.network.focus(e.id, { scale: 1.5, animation: { duration: 200 } });
        return;
      } else if (arr.length === 2) {
        this.active1 = arr[0];
        this.active2 = arr[1];
        this.active3 =''
      } else if (arr.length === 3) {
        this.active1 = arr[0];
        this.active2 = arr[1];
        this.active3 = arr[2];
      }
      if (arr.length === 3 || arr.length === 2) {
        this.$emit("changeNodePosition", {
          scale: 4.2,
          // network: this.network,
          paramsId: e.id,
          e
        });
      }
      // this.network.focus(e.id, { scale: 4.2, animation: { duration: 200 } });
    },
    // 展示隐藏
    headClick() {
      this.active1=''
        this.active2=''
        this.active3=''
      this.showBody = !this.showBody;
      this.searchKey=''
      this.$emit("changeTwinkState",this.showBody);
      if(!this.showBody){
        this.loading=false
      }
    },
  
     // 定义一个防抖的函数进行请求接口
     onSearch: debounce(function () {
      this.getTreeData()
    }, 1000),
    inputChange(e){
this.searchKey=e
this.onSearch()
//       _.throttle(function(e) {
// }, 1000, {
//   leading: true,
//   trailing: false
// })
    }
  },
};
</script>

<style lang="scss" scoped>
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px;
  /* 对应横向滚动条的宽度 */
  height: 5px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #324156;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  display: none;
}
.preview-header.on {
  .img2 {
    transform: rotate(90deg);
  }
}
.preview {
  width: 350px;
  position: absolute;
  right: 30px;
  // top: 85px;
  background: url("~@/assets/img/img11.png") no-repeat;
  background-size: 347px 62px;

  &.on {
    width: 250px;
    background-size: 250px 62px;
  }

  &-header {
    width: 100%;
    height: 34px;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;

    .img1 {
      padding-left: 10px;
      margin-top: -3px;
    }

    .daolan {
      height: 13px;
      padding-left: 10px;
      margin-top: -3px;
    }

    .img2 {
      width: 15px;
      height: 20px;
      position: absolute;
      right: 30px;
      top: 5px;
    }
  }

  .slide-fade-enter-active {
    transition: all 0.3s ease;
  }

  .slide-fade-leave-active {
    transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter,
  .slide-fade-leave-to

  /* .slide-fade-leave-active for below version 2.1.8 */ {
    transform: translateY(-10px);
    opacity: 0;
  }

  &-con {
    margin-top: 15px;
    // background: url("./../../../assets/img/img07.png") rgba(0, 0, 0, 0.5);
    // background-size: 100% 100%;
    overflow: scroll;
    border: 1px solid;
    background: rgba(1 ,18, 38,0.9);
    border-image: linear-gradient(0deg, #0670B4, rgb(28 145 255 / 2%)) 1 1;
    border-radius: 10px;
  }

  &-body {
    //width: calc(100% + 20px);
    width: 100%;
    // max-height: 835px;
    // min-height: 100px;
    height: calc(100vh - 260px);
    overflow-y: auto;
    box-sizing: border-box;
    padding-top: 20px;
    padding-left: 24px;
    border-radius: 10px;

    .tree-box {
      position: relative;

      /* border-left: 2px dashed #0e4984; */
      .line0 {
        position: absolute;
        width: 2px;
        height: 100px;
        border-left: 2px dashed #0e4984;
        top: 10px;
        left: -3px;
      }

      .list {
        margin-bottom: 5px;
        position: relative;

        .line {
          position: absolute;
          width: 36px;
          height: 40px;
          border-left: 1px dashed #3370FF;
          border-bottom: 1px dashed #3370FF;
          left: 62px;
          top: 33px;
        }

        .list1 {
          margin-left: 120px;
          margin-bottom: 7px;
          position: relative;

          .line1 {
            position: absolute;
            width: 1px;
            border-left: 1px dashed #3370FF;
            left: -20px;
            top: 12px;
          }

          &-top {
            margin-bottom: 8px;
            position: relative;

            &-line {
              position: absolute;
              width: 13px;
              height: 1px;
              border-top: 1px dashed #3370FF;
              left: -20px;
              top: 11px;
            }

            &-lines {
              position: absolute;
              width: 13px;
              height: 1px;
              border-top: 1px dashed #3370FF;
              left: -20px;
              top: 19px;
            }
          }
        }

        .list2 {
          margin-left: 92px;
          position: relative;

          .line2 {
            position: absolute;
            left: -49px;
            top: 4px;
            width: 32px;
            height: 16px;
            border-left: 1px solid rgba(28, 145, 255, 0.4);
            border-bottom: 1px solid rgba(28, 145, 255, 0.4);
          }

          .line2-1 {
            position: absolute;
            width: 1px;
            border-left: 1px solid rgba(28, 145, 255, 0.4);
            left: -17px;
            top: 12px;
          }

          &-top {
            position: relative;
            padding-left: 6px;

            &-line {
              position: absolute;
              width: 17px;
              height: 1px;
              border-top: 1px solid rgba(28, 145, 255, 0.4);
              left: -17px;
              top: 12px;
            }
          }
        }
      }

      .btn {
        width: 96px;
        height: 26px;
        /* border: 1px solid rgba(47, 213, 255, 0.7); */
        line-height: 26px;
        text-align: center;
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #3370FF;
        cursor: pointer;

        &:hover {
          color: #00fff0;
        }

        &.btn-1 {
          margin-left: 10px;
          line-height: 24px;
          max-width: 96px;
          position: relative;
          background: url("~@/assets/img/img08.png") no-repeat;
          background-size: 96px 26px;

          &.on {
            color: #00fff0;
            background: url("~@/assets/img/img09.png") no-repeat;
          }

          &::before {
            position: absolute;
            content: "";
            width: 6px;
            height: 6px;
            background: #3370FF;
            border-radius: 50%;
            left: -15px;
            top: 8px;
          }
        }

        &.btn-2 {
          width: 84px;
          height: 26px;
          line-height: 26px;
          border: none;
          background: #0d2544;
          font-size: 12px;
          color: rgba(28, 145, 255, 0.85);
          background: #0d2544;

          &:hover {
            color: #00fff0;
          }

          &.on {
            color: #00fff0;
            background: url("~@/assets/img/img10.png") no-repeat;
            background-size: 100% 100%;
            /* height: 32px;
              width: 96px;
              margin-top: -3px;
              line-height: 32px;
              transform: translate(-6px, -3px); */
          }
        }

        &.btn-3 {
          width: auto;
          border: none;
          font-size: 12px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #3370FF;
          line-height: 26px;
          opacity: 0.85;
          text-align: left;

          &:hover {
            color: #00fff0;
          }

          &.on {
            color: #00fff0;
          }
        }
      }
    }
  }
}
::v-deep {
  .previewSearch {
    margin-top: 10px;
    .el-input__inner {
      //       width: 396px;
      // height: 32px;
      background: rgba(0, 0, 0, 0.2);
      border: 1px solid;
      border-image: linear-gradient(136deg, rgba(28, 145, 255, 0.3), rgba(28, 145, 255, 0.3), rgba(28, 145, 255, 0.3)) 1 1;
 color: white;
      border-radius: 2px;
      height: 32px;
    }
    .el-icon-search {
      margin-top: -3px;
    }
  }
}
.noData{
    font-size: 14px;
    color: rgba(181, 193, 209, 0.7);
    line-height:calc(100vh - 260px);
    width: 100%; 
    height: calc(100vh - 260px);
    text-align: center;
margin-left: -24px;

}
</style>
