/* eslint-disable no-unused-vars */
// const geoColor = [['#00174F','#001C60'],['#001F6B','#002E9E '],['#02439A','#3681E9'],['#2B8BFB','#6BE7FF'],['#3BAAFF','#C1E4FF']];
import { provinceList} from './mapData'
const geoColor = [['#3BAAFF','#C1E4FF'],['#2B8BFB','#6BE7FF'],['#02439A','#3681E9'],['#001F6B','#002E9E '],['#00174F','#001C60'],];

// const geoColor1 = ['#3BAAFF','#2B8BFB','#02439A',  '#001F6B', '#00123F' ];
// const geoColor2 = ['#C1E4FF','#6BE7FF','#3681E9','#002E9E ','#001C60'];

const geoColor1 = ['#00123F','#001F6B','#02439A',  '#2B8BFB', '#3BAAFF' ];
const geoColor2 = ['#001C60','#002E9E','#3681E9','#6BE7FF','#C1E4FF'];

const geoColor3 = [
  '#313695',
  '#4575b4',
  '#74add1',
  '#abd9e9',
  '#e0f3f8',
  '#ffffbf',
  '#fee090',
  '#fdae61',
  '#f46d43',
  '#d73027',
  '#a50026'
];

const geoColor4 = [
  ['#384DA0','#1A2281'],
  ['#2591ff','#005aff'],
  ['#32C5E5','#00A8A6'],
  ['#2CCB4A','#00B770'],
  ['#FFDF33','#BE8916'],
  ['#de8c32','#c44804'],
  ['#A50026','#5E091C'],
]

const replaceCity = [
  ['克孜勒苏柯尔克孜自治州','克州'],
  ['伊犁哈萨克自治州','伊犁州'],
  ['博尔塔拉蒙古自治州','博州'],
  ['昌吉回族自治州','昌吉州'],
  ['巴音郭楞蒙古自治州','巴州'],
  ['五家渠市',''],
  ['克拉玛依市',''],
  ['双河市',''],
  ['长宁区',''],
  ['静安区',''],
  ['虹口区',''],
  ['包头市',''], 
  ['乌海市',''],
  ['彭水苗族土家族自治县','彭水自治县'],
  ['渝中区',''],
  ['江北区',''],  
  ['璧山区',''],
  ['东城区',''],
  ['大渡口区',''],
  ['包头市',''], 
  ['河北区',''],
  ['河东区',''],
  ['南开区',''],
  ['怒江傈僳族自治州','怒江州'],
  ['大理白族自治州','大理州'],
  ['楚雄彝族自治州','楚雄州'],
  ['迪庆藏族自治州','迪庆州'],
  ['德宏傣族景颇族自治州','德宏州'],
  ['红河哈尼族彝族自治州','红河州'],
  ['文山壮族苗族自治州','文山州'],
  ['西双版纳傣族自治州','西双版纳州'],
]

	// 层级索引
	// var name = [opt.mapName];
	// var idx = 0;
	var pos = {
		leftPlus: 115,
		leftCur: 150,
		left: 108,
		top: 120
	};

	var line = [[0, 0], [8, 11], [0, 22]];
  //   // style
	var style = {
		font: '18px "Microsoft YaHei", sans-serif',
		textColor: '#eee',
		lineColor: 'rgba(147, 235, 248, .8)'
	};
  

// 地图 - 悬浮显示
function tooltipWay(nodeData){
  return {
    show: true,    // 是否显示提示框组件
        backgroundColor: 'rgb(3 21 60 / 80%)',    // 提示框浮层的背景颜色
    padding: 5,    // 提示框浮层内边距，单位px
    textStyle: {
        color: '#FFF',     // 文字的颜色
        fontStyle: 'normal',    // 文字字体的风格（'normal'，无样式；'italic'，斜体；'oblique'，倾斜字体） 
        fontWeight: 'normal',    // 文字字体的粗细（'normal'，无样式；'bold'，加粗；'bolder'，加粗的基础上再加粗；'lighter'，变细；数字定义粗细也可以，取值范围100至700）
        fontSize: '14',    // 文字字体大小
        lineHeight: '50',    // 行高 
    },
      formatter:(params,ticket, callback)=>{
        const node = nodeData?.find((item)=>item.name===params.name) || null;
        var dotHtml = ''
        if(!node){
          dotHtml=`<div><div>${params.name}</div><div>企业数量：暂无数据</div></div>`
        }else{
          dotHtml=`<div><div>${params.name}</div><div>企业数量：${node.value}</div></div>`
        }
        // var dotHtml = '<span style="display:inline-block;margin-right:5px;border-radius:2px;width:30px;height:30px;background-color:#F1E67F"></span>'    // 定义第一个数据前的圆点颜色
        // var dotHtml2 = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:30px;height:30px;background-color:#2BA8F1"></span>'    // 定义第二个数据前的圆点颜色
        // result += params[0].axisValue + "</br>" + dotHtml + ' 数据名称 ' + params[0].data + "</br>" + dotHtml2 + ' 数据名称 ' + params[1].data;
        return dotHtml
    }//数据格式化
  }
}



// 地图左上角城市显示
function showMapTitle2(nowCityName){
  // console.log('nowCityName=====',nowCityName)
  const names = []
  if(nowCityName && nowCityName.length >= 1){
    names.push(isClickCityName(nowCityName[0].countArea,0,20));
    if(nowCityName && nowCityName.length >= 2){
      names.push(isClickCityName(nowCityName[1].countArea,1,30));
    }
  }
  return names;
}


// 左上角可点击按钮
function isClickCityName (cityName){
  // const nowCityName =  10 * cityName.length + 50;
  // const leftLength = preNameLength + nowCityName
  // console.log('=======leftLength==', cityName, nowCityName, preNameLength, leftLength)
  if(!cityName){
    return null
  }
 return {
    cityName:cityName,
    id: cityName,
    type: 'group',
    left: 100,
    top: 100,
    children: [
              {
                  type: 'text',
                  left: 0,
                  top: 'middle',
                  style: {
                      text: cityName,
                      textAlign: 'center',
                      fill: style.textColor,
                      font: style.font
                  },
                  onclick: function(params){
                    // console.log('===左上角==params==',params)
                  }
              },
    ]
  }
}
function showMapTitle(nowCityName){
  if(nowCityName && nowCityName.length >= 2){
    // let namePreSumLength = 10;
    // let names = nowCityName.map((item,index)=>{
    //   let cityNameData = isClickCityName(item.countArea,index, namePreSumLength);
    //   namePreSumLength = cityNameData.cityNameLength
    //   return cityNameData;
    // })
    // return names
    let name = '';
    nowCityName.forEach((item,idx)=>{
      let fu = idx=== nowCityName.length-1 ? '' :' > ';
      name = name + `${item.countArea}`+fu;
    })
   return isClickCityName(name);
  }else{
    return null
  }
}
  // 初始化地图 - 地图边框颜色绘制 geo / 柱状绘制 series
  // mapName 地图名称
  // intervalList 左下角侧数据分段
  // regionsData 地图颜色描边
  // 是否为初始化地图 - 主要区分功能，不展示柱状图
  // geoCoordMap 经纬度
  // geoScope 左侧数据区间设置

  // 初始化地图 - 地图边框颜色绘制 geo / 柱状绘制 series
  // mapName 地图名称
  // intervalList 左下角侧数据分段
  // regionsData 地图颜色描边
  // geoScope 左侧数据区间设置
  // mapData 地图数据量
  // nowCityName 用于展示左上角城市名称
  export function OptionPrimeval(mapName, intervalList,regionsData,regionsData1,geoScope=[100,0], mapData, nowCityName, cityNameData, isCount) {
    const graphic = showMapTitle(cityNameData)
    // console.log('mapData', mapData)
    // console.log('regionsData', regionsData)
    // console.error('mapName', mapName);
    return {
      backgroundColor: '#ffffff00',
      tooltip: {
      },
      // 左侧数据范围标志 -  面积
      // visualMap: {
      //   type: 'continuous',
      //   text: ['', ''],
      //   showLabel: true,
      //   max: geoScope[0],
      //   min: geoScope[1],
      //   left: !isCount ? 26 : 50,
      //   bottom: !isCount ? 40 : 0,
      //   inRange: {
      //       color: ['#edfbfb', '#b7d6f3', '#40a9ed', '#3598c1', '#215096', ]
      //   },
      //   textStyle:{
      //     fontSize: 12,
      //     lineHeight:12,
      //     color: '#fff',         
      //   },
      //   splitNumber: 0
      // },
      // 左侧数据范围标志 - 线条
      visualMap: visualMapWay(geoScope,intervalList, isCount), // 左下角 - 数据分段设置
      // graphic:graphic, // 左上角 - 城市显示
      // graphic:showMapTitle2(cityNameData), // 左上角 - 城市显示
      // 地图
      geo: [
        {
          map:mapName, 
          // 在地图中对特定的区域配置样式
          regions:regionsData,
          tooltip:tooltipWay(mapData),
          top:200,
          aspectScale: 0.75, // 3d
          zoom: 1, // 默认显示级别
          zlevel: 12,
          roam: false, // 是否允许缩放
          layoutSize: mapName!=='china' ? (!isCount ? '90%' : '90%') : (!isCount ? '119%' : '130%'),
          layoutCenter: mapName!=='china' ? (!isCount ? ['45%', '50%'] : ['43%', '48%']):(!isCount ? ['45%', '65.5%'] : ['43%', '68%']),
          // 图形上的文本标签，可用于说明图形的一些数据信息，比如值，名称等。
          label: { 
            emphasis: {
                show: true,
                color: '#fff',
            },
            show: true, // 是否显示标签。
            fontSize: '12',
            color: '#e2e2e2',
            formatter:(params,ticket, callback)=>{
                if(params.name){
                  // return '澳门'
                  if(params.name === '香港特别行政区'){
                    return '香港'
                  }else if(params.name.includes('澳门特别行政区')){
                    return '澳门'
                  }else if(params.name === '台湾省'){
                    return '台湾'
                  }else{
                    const province =  provinceList.find((item )=>{
                      if(item[1] === params.name){
                        return true
                      }
                    })
                    if(province){
                      return province[0]
                    }else{
                      let name = params.name
                      replaceCity.map(e=>{
                        if(e[0]===name){
                          name = name.replace(e[0],e[1]);
                        }
                      })
                      return name;
                      // return ''
                    }
                  }
                }
            }
          },
          // 地图背景样式
          // itemStyle: {
          //   borderColor:'#19A8FF', // 图形的描边颜色。
          //   borderWidth: 1, // 描边线宽。为 0 时无描边。
          //   borderType: 'solid', // 描边类型。
          //   areaColor: {
          //       type: 'radial',
          //       x: 0.5,
          //       y: 0.5,
          //       r: 1,
          //       colorStops: [{
          //         offset: 0, 
          //         color: '#15417f' // 0% 处的颜色
          //       }, {
          //         offset: 1, 
          //         color: '#516b97' // 100% 处的颜色
          //       }],
          //       global: true, // 缺省为 false
          //       borderColor: 'red',
          //       borderWidth: 1
          //   },
          // },
          // backgroundColor: 'transparent',
          // 悬浮高亮样式 - 背景样式
          emphasis: {
            // disabled: !false,
            focus: 'none', //在高亮图形时，是否淡出其它数据的图形已达到聚焦的效果。'none' 不淡出其它图形，默认使用该配置。'self' 聚焦当前高亮图形，淡出其它图形。
            // 高亮状态下文本标签
            label: { 
                show: 1,
                color: '#ffffff',

            },
            // 高亮状态下图形样式
            itemStyle: {
              areaColor: '#2a5aaf', // 高亮区域的颜色
              // areaColor: '#00fff0', // 高亮区域的颜色
              shadowBlur: 3,
              borderWidth: 2, // 描边线宽。为 0 时无描边。
              shadowColor:'#19A8FF',
              shadowOffsetX: 0,
              shadowOffsetY: 0
            },
          },
          // selectedMode: false,
          //selectedMode: !isCount?'single': false, // 'single', 'multiple' // 选中模式，表示是否支持多个选中，默认关闭，支持布尔值和字符串，字符串取值可选'single'表示单选，或者'multiple'表示多选。 
          // 选中状态下的多边形和标签样式。
          
          select: {
            // disabled: false,
            disabled: isCount?false:true, // 是否可以被选中。在开启selectedMode的时候有效，可以用于关闭部分数据。
            // 选中区域文本标签
            label: { 
                show:true,
                color: '#fff',
            },
            // 高亮状态下图形样式
            itemStyle: {
              areaColor: '#2a5aaf',//'#2a5aaf', // 高亮区域的颜色。
              color: '#333', // 高亮区域的颜色。areaColor和color都设置，高亮区域渲染areaColor的值
              borderWidth: 1, // 描边线宽。为 0 时无描边。
              borderType: 'solid', // 描边类型。
            },
          }
          
        },
        {
          map:mapName, 
          // 在地图中对特定的区域配置样式
          regions:regionsData1,
          // tooltip:tooltipWay(mapData),
          tooltip: {
            show: !true,
          },
          silent: true,
          top:'500px',
          aspectScale: 0.75, // 3d
          zoom: 1, // 默认显示级别
          zlevel: 10,
          roam: false, // 是否允许缩放
          layoutSize: mapName!=='china' ? (!isCount ? '90%' : '90%') : (!isCount ? '119%' : '130%'),
          layoutCenter: mapName!=='china' ? (!isCount ? ['45%', '50%'] : ['43%', '48%']):(!isCount ? ['45%', '67%'] : ['43%', '70%']),
          // 图形上的文本标签，可用于说明图形的一些数据信息，比如值，名称等。
          label: { 
            emphasis: {
                show: true,
                color: '#fff',
            },
            show: true, // 是否显示标签。
            fontSize: '12',
            color: '#e2e2e2',
            formatter:'',
          },
          // 地图背景样式
          itemStyle: {
            borderColor:'red', // 图形的描边颜色。
            borderWidth: 0, // 描边线宽。为 0 时无描边。
            borderType: 'solid', // 描边类型。
            areaColor: '',
            shadowBlur: 10,
            shadowColor:'rgba(0,138,249,0.8)',
            shadowOffsetX: 0,
            shadowOffsetY: 12
          },
          // 悬浮高亮样式 - 背景样式
          emphasis: {
            focus: 'none', //在高亮图形时，是否淡出其它数据的图形已达到聚焦的效果。'none' 不淡出其它图形，默认使用该配置。'self' 聚焦当前高亮图形，淡出其它图形。
            // 高亮状态下文本标签
            label: { 
                show: 1,
                color: '#ffffff',
            },
            // 高亮状态下图形样式
            itemStyle: {
                // areaColor: '#2a5aaf', // 高亮区域的颜色。
                areaColor: '#000E57', // 高亮区域的颜色。
                color: '#333', // 高亮区域的颜色。areaColor和color都设置，高亮区域渲染areaColor的值
                // borderWidth: 0, // 描边线宽。为 0 时无描边。
                borderType: 'solid', // 描边类型。
            },
          },
          ...[ isCount?{selectedMode:''}: {selectedMode:'single'} ],
          // selectedMode:!isCount?'single':'none', // 'single', 'multiple' // 选中模式，表示是否支持多个选中，默认关闭，支持布尔值和字符串，字符串取值可选'single'表示单选，或者'multiple'表示多选。 
          // 选中状态下的多边形和标签样式。
          ...[!isCount?
          {select: {
              disabled: true, // 是否可以被选中。在开启selectedMode的时候有效，可以用于关闭部分数据。
              // 选中区域文本标签
              label: { 
                  show:true,
                  color: '#fff',
              },
              // 高亮状态下图形样式
              itemStyle: {
                areaColor: '#3d6cb5',//'#2a5aaf', // 高亮区域的颜色。
                color: '#333', // 高亮区域的颜色。areaColor和color都设置，高亮区域渲染areaColor的值
                borderWidth: 3, // 描边线宽。为 0 时无描边。
                borderType: 'solid', // 描边类型。
              },
          }}:{}
          ]
        },
      ],
    }
  }

  // 左下角 - 分段数据 - 内容显示
  function visualMapWay( geoScope,intervalList, isCount){
    // console.log('geoScope', geoScope)
    return {
      max: geoScope[0],
      min: geoScope[1],
      left: !isCount ? 26 : 50,
      bottom: !isCount ? 40 : 0,
      // showLabel: !0,
      // type: 'continuous', // 分段型 - 自定分段  // viusalMap continuous   
      type: 'piecewise', // 分段型 - 自定分段  // viusalMap continuous   
      // text:['企业数量'], //["高", "低"],
   
      // itemWidth:8,                           //图形的宽度，即长条的宽度。
      // itemHeight:8,                         //图形的高度，即长条的高度。
      // // textGap:10,                              //两端文字主体之间的距离，单位为px
      // dimension:100,                            //指定用数据的『哪个维度』，映射到视觉元素上。『数据』即 series.data。 可以把 series.data 理解成一个二维数组,其中每个列是一个维度,默认取 data 中最后一个维度
      // seriesIndex:1,                          //指定取哪个系列的数据，即哪个系列的 series.data,默认取所有系列
      hoverLink:true,                         //鼠标悬浮到 visualMap 组件上时，鼠标位置对应的数值 在 图表中对应的图形元素，会高亮
      // z:2,                                         //所属组件的z分层，z值小的图形会被z值大的图形覆盖
      // // bottom:"10%",                                   //组件离容器上侧的距离,'top', 'middle', 'bottom','20%
      // orient:"vertical",                         //图例排列方向
      // padding:2,                                   //图例内边距，单位px  5  [5, 10]  [5,10,5,10]
      // backgroundColor:"transparent",            //标题背景色
      // // borderColor:"#fff",                         //边框颜色
      // // borderWidth:1,                               //边框线宽
      textStyle:{
        fontSize: 12,
        lineHeight:12,
        color: '#fff',         
      },                      //文本样式
      formatter: function (value) {                 //标签的格式化工具。
          return value;                    // 范围标签显示内容。
      },
      pieces:intervalList, 
      show: true,
      inRange: {
        color: geoColor4.map(e=>{
          e=e[0]
          return e;
        })
      },
      realtime: !false,
      calculable: !true,
      selectedMode: false,
    }
  }

  // 左下角 - 分段颜色
  function  getGeoColor(item,regionsData){
    let singleColor = 0;
    const { value = 0 } = item;
      if(regionsData.length >= 5){
        if(value >= regionsData[0].lte &&  value <= regionsData[0].gte){
          singleColor = 0;
        }
        if(value >= regionsData[1].lte && value <= regionsData[1].gte){
          singleColor = 1;
        }
        if(value >= regionsData[2].lte && value <= regionsData[2].gte){
          singleColor = 2;
        }
        if(value >= regionsData[3].lte && value <= regionsData[3].gte){
          singleColor = 3;
        }
        if(value >= regionsData[4].lte && value <= regionsData[4].gte){
          singleColor = 4;
        }
        if(value >= regionsData[5].lte && value <= regionsData[5].gte){
          singleColor = 5;
        }
        if(value >= regionsData[6].lte && value <= regionsData[6].gte){
          singleColor = 6;
        }
        // if(value >= regionsData[7].lte){
        //   singleColor = 7;
        // }
      }
    return singleColor;
  }

  // 计算地图左侧分段数据 + 分段颜色
  export function getIntervalList(geoScope){
    let max = parseInt(geoScope[0]);
    let min = parseInt(geoScope[1]);
    let intervalList = []; // 对应的描边颜色
    let baseNum = 7;
    if(max > 10 && max != min){
      const interval = (max - 0) / baseNum;
      ['1','2','3','4','5',6,7]?.forEach((num, idx)=>{
        let gte = parseInt((idx+1)*interval);
        let lte = parseInt(idx*interval);
       // let label = `${lte} ~ ${gte + 1}`
       //let label = gte >= max ? `${lte} ~ ${max}`:  `${lte} ~ ${gte}`
        lte =  Math.ceil(lte/100) * 100 
        gte =  Math.ceil(gte/100) * 100 
        let label =  `${lte} ~ ${gte}`
        intervalList.push({
          // gte,lte,label, color:geoColor1[idx]}
          gte,lte,label, color:geoColor4[idx][0]}
        );
      })
    }else if (min == max && max !=0){
      intervalList = [
        {gte:10,lte:10,label:`0-${max}`,color:geoColor1[1]},
        // {gte:1, lte:1,label:'0',color:geoColor1[0]},
      ]
    } else if(max == 0){
      intervalList = [
        {gte:1, lte:1,label:'0',color:geoColor1[0]},
      ]
    } else{
      intervalList = [
        {gte:10,lte:10,label:'0-10',color:geoColor1[1]},
       // {gte:1, lte:1,label:'0',color:geoColor1[0]},
      ]
    }
    return intervalList;
  }

  // 地图边框颜色
  export function getRegionsData(intervalList,geoData,cityJson){
    // console.log('cityJson', cityJson)
    const regionsData = []; // 对应的描边颜色
    geoData?.forEach((item)=>{
      const singleColor = getGeoColor(item,intervalList);
      // console.log('singleColor', singleColor)
      // console.log('item.name', item.name)
      let new_obj = {
        name: item.name, // 地图区域的名称
        itemStyle: {
          // backgroundColor:singleColor,
          // backgroundColor: "rgb(1 33 86)",
          // borderColor: "rgb(161 210 255)",
          // borderColor: singleColor, // 图形的描边颜色。
          // borderWidth: 2, // 描边线宽。为 0 时无描边。
          // borderType: 'solid', // 描边类型。
          borderColor:'rgba(219,223,225,0.5)', // 图形的描边颜色。
          borderWidth: 1, // 描边线宽。为 0 时无描边。
          borderType: 'solid', // 描边类型。
          areaColor: {
            type: 'radial', //  radial
            x: 0.2,
            y: 0.8,
            r: 1,
            colorStops: [{
              offset: 0, 
              color: geoColor4[singleColor][1] // 0% 处的颜色
            }, 
            {
              offset: 1, 
              color: geoColor4[singleColor][0] // 100% 处的颜色
            }],
            // globalCoord: false, // 缺省为 false
            // global: true, // 缺省为 false
            borderColor: '#78DCE5',
            borderWidth: 1.2,
            color:'#fff'
          },
        }
      }
      regionsData.push(new_obj)
    });
    cityJson.features?.forEach((item)=>{
      let status = false;
      // console.log('item.properties.name', item.properties.name)
      geoData.map(e=>{
        if(e.name == item.properties.name){
          status = true;
        }
      });
      if(!status){
        let new_obj = {
          name: item.properties.name, // 地图区域的名称
          itemStyle: {
            borderColor:'rgba(219,223,225,0.5)', // 图形的描边颜色。
            borderWidth: 1, // 描边线宽。为 0 时无描边。
            borderType: 'solid', // 描边类型。
            areaColor: 'rgb(1 33 86)',
          }
        }
        regionsData.push(new_obj)
      }
    });
// 南海诸岛变色
    regionsData.push({
      name: '南海诸岛', // 地图区域的名称
      itemStyle: {
        borderColor:'rgba(219,223,225,0.5)', // 图形的描边颜色。
        borderWidth: 1, // 描边线宽。为 0 时无描边。
        borderType: 'solid', // 描边类型。
        areaColor: 'rgb(1 33 86)',
      }
    });

    return regionsData;
  }

  // 地图边框颜色 第二层底色
  export function getRegionsData1(intervalList,geoData){
    const regionsData = []; // 对应的描边颜色
    geoData?.forEach((item)=>{
      const singleColor = getGeoColor(item,intervalList);
      regionsData.push({
          name: item.name, // 地图区域的名称
          itemStyle: {
              // backgroundColor:singleColor,
              // backgroundColor: "rgb(1 33 86)",
              // borderColor: "rgb(161 210 255)",
              // borderColor: singleColor, // 图形的描边颜色。
              // borderWidth: 2, // 描边线宽。为 0 时无描边。
              // borderType: 'solid', // 描边类型。
              borderColor:'#fff ', // 图形的描边颜色。
              borderWidth: 0, // 描边线宽。为 0 时无描边。
              borderType: 'solid', // 描边类型。
              areaColor: '#000E57',
        },
      })
    });
    return regionsData;
  }

  // 地图JSON -  中心点 经纬度 数据提取
  export function geoCoordMapWay(cityJson){
    let cityXYList = []; //  广州市: [113.507649675, 23.3200491021],
    cityJson?.features.forEach(city => {
      const cityName = city.properties.name;
      const centroid = city.properties.centroid;
      cityXYList.push({[cityName]:centroid});
    });
    return {...cityXYList};
  }

  // 显示数据 - 对name value 数据大小 进行格式化
  export function batteryCityDataWay(childList){
    if(!childList || childList.length == 0){
      return
    }
      const cityData = childList?.map((areaItem)=>{
        const { countArea, enterpriseNum } = areaItem
        return {name:countArea,value: enterpriseNum}
      })
      return cityData
  }

  // 动态计算柱形图的高度（定一个max）
  export function lineMaxHeight(customerBatteryCityData) {
      const maxValue = Math.max(...customerBatteryCityData.map((item) => item.value));
      return 0.9 / maxValue;
  }

   // 柱状图设置
  export function setLineWay(geoCoordMap,customerBatteryCityData){
    // 高度
    const maxValue = Math.max(...customerBatteryCityData?.map((item) => item.value));
    const lineMaxHeight = 0.9 / maxValue;

    // 主干
    const line = customerBatteryCityData?.map((item) => {
      return {
        coords: [
          geoCoordMap?.[item.name] || '',
          [geoCoordMap?.[item.name]?.[0], geoCoordMap?.[item.name]?.[1] + item.value * lineMaxHeight],
        ],
      };
    });
    // 顶部
    const scatterData = customerBatteryCityData?.map((item) => {
      return [geoCoordMap?.[item.name]?.[0], geoCoordMap?.[item.name]?.[1] + item.value * lineMaxHeight];
    });
    const scatterData2 =  customerBatteryCityData?.map((item) => {
      return {
        name: item.name,
        value: geoCoordMap?.[item.name],
      };
    })
    return {line,scatterData, scatterData2}
  }

  // 设置地图数据 - 
  export function  setMapData(mapName,mapData){
    return {mapName,mapData};
  } 

  // 设置 左侧分段 / 地图边框
  export function initEchart(cityName,geoData,geoScope,cityJson){
    const intervalList = getIntervalList(geoScope); // 计算左侧分段数据 - 公共
    const regionsData = getRegionsData(intervalList,geoData,cityJson); // 地图边框颜色
    const regionsData1 = getRegionsData1(intervalList,geoData); // 地图边框颜色
    // regionsData 左侧分段数据
    // regionsData 地图对应边框颜色
    return {cityName,intervalList,regionsData,regionsData1}
  }

  // 格式化地图数量数据
  export function fomatterData(areaData){
    const newArea = []
    areaData?.forEach(area => {
      newArea.push({ name: area.countArea+'22', value: parseInt(area.enterpriseNum)},)
    });
    return newArea;
  }

    // 地图放大缩小 - 下钻/返回
    // 放大/缩小范围值问题 -  添加开关字段
  export function goDown(){
    this.chart.on("georoam", params => {     //这里是bmaproam方法，其余都一样
      let {zoom,componentType} = params;
      if(componentType === "geo" && this.areaParams.type !=='2'){
        let mapNameSet = this.nowCityName[1];
        let mapJson = this.areaJson.childNode;
        let zoomSet = 1;
        let layoutCenterSet = ['55%', '50%'];
        // 放大 - 打开子集
        // if(zoom >= 1.5){
        //   console.log('==进行放大===',params);
        //   mapNameSet = this.nowCityName[1];
        //   mapJson = this.areaJson.childNode;
        // }
        // 缩小 - 返回上级
        // if(zoom < 1){
          // console.log('==进行缩小===',params);
          mapNameSet = this.nowCityName[0];
          mapJson = this.areaJson.nowNode;
          zoomSet = 1.23;
          // this.initChart()
        // }
        // this.$echarts.init(document.getElementById("bar")) //实例
        this.chart = this.$echarts.init(document.getElementById(this.id));
        this.$echarts.registerMap(mapNameSet, mapJson);
        this.chart.clear();
        const testData = [{areaType: "2",countArea: "哈密市",enterpriseNum: "30"}];
        const options = this.setOptionWay({area:mapNameSet,areaList:testData});
        options.geo[0].zoom = zoomSet;
        options.geo[0].layoutCenter = layoutCenterSet;
        options.geo[1].zoom = zoomSet;
        options.geo[1].layoutCenter = layoutCenterSet;
        options.geo[2].zoom = zoomSet;
        options.geo[2].layoutCenter = layoutCenterSet;
        
        this.chart.setOption(options);
      }
    });
  }

     // 放大
function shrinkWay(){
      // let layoutCenterSet = ['55%', '50%'];
      // const  mapNameSet = this.nowCityName[2];
      // const  mapJson = this.areaJson.childNode;
      // const  zoomSet = 1;

      // this.chart = echarts.init(document.getElementById(this.id));
      // echarts.registerMap(mapNameSet, mapJson);
      // this.chart.clear();
      // const testData = [{areaType: "2",countArea: "哈密市",enterpriseNum: "30"}];
      // const options = this.setOptionWay({area:mapNameSet,areaList:testData});
      // options.geo[0].zoom = zoomSet;
      // options.geo[0].layoutCenter = layoutCenterSet;
      // options.geo[1].zoom = zoomSet;
      // options.geo[1].layoutCenter = layoutCenterSet;
      // options.geo[2].zoom = zoomSet;
      // options.geo[2].layoutCenter = layoutCenterSet;
      
      // this.chart.setOption(options);
}
  // // 柱状体的主干
  // export function lineData(geoCoordMap,customerBatteryCityData) {
  //   return customerBatteryCityData.map((item) => {
  //     return {
  //       coords: [
  //         geoCoordMap[item.name],
  //         [geoCoordMap[item.name][0], geoCoordMap[item.name][1] + item.value * lineMaxHeight()],
  //       ],
  //     };
  //   });
  // }

  
  // // 柱状体的顶部
  // export function scatterData(geoCoordMap,customerBatteryCityData) {
  //   return customerBatteryCityData.map((item) => {
  //     return [geoCoordMap[item.name][0], geoCoordMap[item.name][1] + item.value * lineMaxHeight()];
  //   });
  // }
  
  // // 柱状体的底部
  // export function scatterData2(geoCoordMap,customerBatteryCityData) {
  //   return customerBatteryCityData.map((item) => {
  //     return {
  //       name: item.name,
  //       value: geoCoordMap[item.name],
  //     };
  //   });
  // }