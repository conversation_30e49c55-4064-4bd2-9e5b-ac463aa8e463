<template>
  <div
    ref="screenRef"
    class="chart-container"
  >
    <div class="tab">
      <div
        class="tab-list tab-list-1"
        :class="tab == 4 ? 'on' : null"
        @click="tabClick(4)"
      >
        全国
      </div>
      <div
        class="tab-list tab-list-2"
        :class="tab == 1 ? 'on' : null"
        @click="tabClick(1)"
      >
        本地
      </div>
    </div>
    <div
      v-if="tab == 4"
      class="screenFull"
    >
      <ScreenFull
        id="screenfull"
        class="right-menu-item hover-effect"
        :elements="elements"
        :show-text="true"
        @isFullscreen="isFullscreenFn"
      />
    </div>
    <nativePlace v-if="showLocality" />
    <div v-if="$store.getters.user.accountType !== '1'">
      <keep-alive>
        <nationalPlace
          v-if="!showLocality"
          :is-fullscreen="isFullscreen"
        />
      </keep-alive>
    </div>
    <div v-else>
      <nationalPlace
        v-if="!showLocality"
        :is-fullscreen="isFullscreen"
      />
    </div>
  </div>
</template>
<script>
import ScreenFull from '@/components/Screenfull'
import nativePlace from './nativePlace.vue'
import nationalPlace from './national/index.vue'
import screenfull from 'screenfull'
export default {
  components: {
    nativePlace,
    nationalPlace,
    ScreenFull
    // DetailInfo
  },
  props: {

    id: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "200px",
    },
    height: {
      type: String,
      default: "200px",
    },
    detailInfoParams: {
      type: Object,
      default: () => { }
    },
    // 本地 / 全部切换
    selNum: {
      type: Number,
      default: 0,
    },
    // getAtlasRelation: {
    //   type: Function,
    //   default: null,
    // },
    searchBtn: {
      type: Number,
      default: 0,
    },
    searchValue: {
      type: String,
      default: "",
    },
    // 节点是否可拖拽
    draggable: {
      type: Boolean,
      default: false,
    },
    // 企业类型
    selTypeList: {
      type: Number,
      default: 4,
    },

  },

  data () {
    return {
      showLocality: false,
      tab: 4,
      elements: null,
      isFullscreen: false
    };
  },

  watch: {

  },
  async beforeMount () {

  },
  mounted () {
    this.elements = this.$refs.screenRef
  },
  methods: {
    isFullscreenFn (isFullscreen) {
      this.isFullscreen = isFullscreen
    },

    // 全国本地企业切换
    tabClick (val) {
      if (val == 1)
      {
        screenfull.exit(this.elements)
      }
      this.showLocality = val === 1
      this.tab = val;
    },



  },
};
</script>
<style>
.vis-network .vis-tooltip {
  background: #00112D;
  border: 0px solid;
  padding: 0px
}

.vis-network .vis-tooltip .nodeTips {
  min-width: 195px;
  border: 1px solid #0066FF;
  background: #00112D;
  padding: 5px 10px;
  position: relative;
  color: #fff;
}

@font-face {
  font-family: 'SourceHanSansCN';
  src: url(../../../assets/font/SourceHanSansCN-Normal.otf);
  /* src: url(https://static.idicc.cn/cdn/pangu/assets/font/SourceHanSansCN-Normal.otf); */
}

.vis-edit-mode {
  top: 25px !important;
  z-index: 99999;
}

.vis-manipulation {
  top: 50px !important;
  z-index: 99999;
}

.icon-text .el-loading-spinner {
  width: 100px;
  height: 120px;
  top: calc(50% - 50px);
  left: calc(50% - 102px);
  transform: translate(-130px, -130px);
  background: url('https://static.idicc.cn/cdn/pangu/assets/screen/loading.png');
  background-size: 100px 100px;
  background-repeat: no-repeat;
  animation: load 2s linear infinite;
  position: relative
}

.icon-text i {
  display: none;
}

.icon-text .el-loading-text {
  font-size: 3rem;
  color: #dce1ea;
  position: absolute;
  bottom: 0
}

@keyframes load {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
<style lang='scss' scoped>
@import './vis-graph.scss';

.screenFull {

  font-size: 14px;
  color: #58ABFF;
  width: 90px;
  height: 32px;
  background: #0C2F66;
  border-radius: 4px;
  top: 65px;
  left: 35px;
  z-index: 11111;
  position: absolute;
  text-align: center;
  line-height: 32px;
  cursor: pointer;

  span {
    padding-right: 10px;
  }
}

.chart-container {
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
}
</style>
 