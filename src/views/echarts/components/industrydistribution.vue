<template>
  <div class="box">
    <div
      class="radar2"
      :style="{ 'height': '71.3vh'}"
    />
    <div
      class="radar"
      :style="{ 'height': '71vh'}"
    >
      <div class="radar-con">
        <div class="radar-item">
          <div
            class="tree"
            :style="`height: ${treeH};`"
          >
            <div class="dashed" />
            <div class="dashed2" />
            <div
              v-for="(item, i) in detail"
              :key="i"
              class="tree-list"
            >
              <div
                class="btn-icon"
                :style="{ 'margin-top': i == 0 ? '-16px' : '0px' }"
              >
                <i
                  :class="
                    item.show ? 'el-icon-caret-bottom' : 'el-icon-caret-top'
                  "
                  @click="isshow(i)"
                />
              </div>
              <div
                :style="{ 'margin-top': i == 0 ? '-16px' : '0px' }"
                class="tree-btn"
                :class="{
                  green: item.nodePropose == '强链',
                  orange: item.nodePropose == '补链',
                  purple: item.nodePropose == '延链',
                }"
                @click="enterpriseList(item)"
              >
                <el-tooltip
                  effect="dark"
                  :visible-arrow="false"
                  placement="right"
                  popper-class="tag-popover"
                >
                  <div slot="content">
                    当前节点：{{ item.nodeName }}
                    <br>
                    本地企业数(家)：{{ item.countryCount }}
                  </div>
                  <div class="boxcount">
                    <span class="names">{{ item.nodeName }}</span>
                  </div>
                </el-tooltip>
              </div>
              <div
                v-show="item.show"
                :style="{ 'margin-top': i == 0 ? '-16px' : '0px' }"
                class="tree-item"
              >
                <div
                  v-for="(item1, j) in item.childNodes"
                  :key="j"
                  class="tree-btn a1"
                  :class="{
                    mountnode: item1.nodePropose == null && item1.localCount > 0,
                    green: item1.nodePropose == '强链',
                    orange: item1.nodePropose == '补链',
                    purple: item1.nodePropose == '延链',
                  }"
                  :style="`margin-bottom: ${
                    (item1.childNodes && item1.childNodes.length - 1) * 52 + 20
                  }px`"
                  @click="enterpriseList(item1)"
                >
                  <el-tooltip
                    effect="dark"
                    :visible-arrow="false"
                    placement="right"
                    popper-class="tag-popover"
                  >
                    <div slot="content">
                      当前节点：{{ item1.nodeName }}
                      <br>
                      本地企业数(家)：{{
                        item1.isLeaf == 1
                          ? item1.localCount
                          : item1.countryCount || 0
                      }}
                    </div>
                    <div class="boxcount">
                      <span class="NodeNametext">{{ item1.nodeName }}</span>
                      <span v-if="item1.isLeaf == 1">({{ item1.localCount || 0 }})</span>
                    </div>
                  </el-tooltip>
                </div>
                <div
                  class="list 111"
                  :style="`height: ${item.len * 52}px;`"
                />
              </div>
              <div
                v-show="item.show"
                :style="{ 'margin-top': i == 0 ? '-16px' : '0px' }"
                class="tree-item"
              >
                <div
                  v-for="(item2, j) in item.childNodes"
                  :key="j"
                  class="item-box"
                  :style="!item2.childNodes ? 'height: 52px;' : ''"
                >
                  <div
                    v-for="(item3, n) in item2.childNodes"
                    :key="n"
                    class="tree-btn a2 a1"
                    :class="{
                      mountnode: item3.nodePropose == null && item3.localCount > 0,
                      green: item3.nodePropose == '强链',
                      orange: item3.nodePropose == '补链',
                      purple: item3.nodePropose == '延链',
                    }"
                    @click="enterpriseList(item3)"
                  >
                    <el-tooltip
                      effect="dark"
                      :visible-arrow="false"
                      placement="right"
                      popper-class="tag-popover"
                    >
                      <div slot="content">
                        当前节点：{{ item3.nodeName }}
                        <br>
                        本地企业数(家)：{{
                          item3.isLeaf == 1
                            ? item3.localCount
                            : item3.countryCount
                        }}
                      </div>
                      <div class="boxcount">
                        <span class="NodeNametext">{{ item3.nodeName }}</span>
                        <span v-if="item3.isLeaf == 1">({{ item3.localCount }})</span>
                      </div>
                    </el-tooltip>
                  </div>
                  <div
                    v-if="item2 && item2.childNodes"
                    class="list 222"
                    :style="`height: ${(item2.childNodes.length - 1) * 52}px;`"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
export default {
  name: "RaDar",
  props: {
    chainList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      detail: null,
      treeH: "auto",
      minscreen:false,
    };
  },
  created() {
    if(window.innerHeight<750){
      this.minscreen=true
    }
    //this.init(this.chainList.childNodes)
  },
  methods: {
    filterNodes(arr) {
      const filteredArr = arr.filter((node) => {
        if (node.relationEnterpriseCount > 0) {
          return true;
        } else if (node.childNodes) {
          node.childNodes = this.filterNodes(node.childNodes);
          return node.childNodes.length > 0;
        }
        return false;
      });
      return filteredArr;
    },
    isshow(i) {
      this.$set(this.detail[i], "show", !this.detail[i].show);
      this.$forceUpdate();
    },
    init(chainList) {
      // console.log("???++++", chainList);
      //this.detail = chainList
      this.detail = chainList;
      this.detail?.map((e) => {
        let len = 0;
        e.show = true;
        e.childNodes?.map((e1, n) => {
          if (e.childNodes?.length > 1) {
            if (n != e.childNodes?.length - 1) {
              len = len + (e1.childNodes?.length || 0);
              if (
                e.childNodes.length > 1 &&
                (e1.childNodes == null || e1.childNodes.length == 0)
              ) {
                len++;
              }
            }
            e1.childNodes?.map((e2) => {
              len = len + (e2.childNodes?.length || 0);
            });
          }
        });
        e.len = len;
      });
      this.detail?.map((item) => {
        if (item.childNodes && item.childNodes.length < 1) {
          item.childNodes = null;
        }
        item.childNodes?.map((e) => {
          if (e.childNodes && e.childNodes.length < 1) {
            e.childNodes = null;
          }
        });
      });
    },
    enterpriseList(it) {
      // console.log(it.isLeaf, "是否为挂载节点");
    /*   if (it.isLeaf == 0) {
        return;
      } */
      // console.log(it, "?e");
      let data = {
        level: "4",
        name: it.nodeName,
        paramsId: it.nodeId,
        type: "graph",
        enterpriseType: 1,
      };
      this.$emit("Triggerparent", data);
    },
    showEvent() {
      if (this.treeH == "auto") {
        this.treeH = "266px";
      } else {
        this.treeH = "auto";
      }
    },
  },
};
</script>
  
<style lang="scss" scoped>
.boxcount{
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
.NodeNametext{
  max-width: 110px;
  white-space: nowrap;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}
.names{
  max-width: 120px;
  white-space: nowrap;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}
}

   ::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px;
  /* 对应横向滚动条的宽度 */
  height: 0px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #354151;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  display: none;
}
.btn-icon {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-right: 14px;
  cursor: pointer;
  color: #3370FF;
}
.btn-icon::before {
  content: "";
  position: absolute;
  left: -22.7px;
  width: 6px;
  height: 6px;
  background: #f2fbff;
  border-radius: 50%;
  z-index: 99;
}
.green {
  background: linear-gradient(33deg, #00b769 0%, #5eca9c 100%)
    rgba(0, 0, 0, 0.2) !important;
  box-shadow: inset 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  border: 0px !important;
}
.orange {
  background: linear-gradient(33deg, #f87700 0%, #f59841 100%) !important;
  color: #ffffff !important;
  border: 0px !important;
}
.purple {
  background: linear-gradient(33deg, #7f39ef 0%, #995ef6 100%) !important;
  color: #ffffff !important;
  border: 0px !important;
}
.mountnode {
  background: linear-gradient(33deg, #3975ff 0%, #51abff 100%) !important;
  color: #ffffff !important;
  border: 0px !important;
}
.box{
  position: relative;
  opacity: 1;
}
.radar2{
  width: 60.2rem;
  height: 73.3vh;
	position: absolute;
	border-radius: 10px;
	// box-shadow: 0 0 1px 1px #001e38;
	// background-image: linear-gradient(130deg, #031d3b, #48515e);
	// top: 0px;
	// left: 0px;
}
.radar {
  padding-top: 60px;
  position: absolute;
  width: 60rem;
  height: 73vh;
  overflow-x: hidden;
  background-color: #021326;
  border-radius: 8px;
  position: relative;
  outline: 5px solid transparent;
  top: 1px;
  left: 1px;

  .title {
    height: 55px;
    line-height: 55px;
    padding-left: 24px;
    border-bottom: 1px solid #e9e9e9;
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }
  &-con {
    display: flex;
  }
  &-item {
    width: 100%;
    position: relative;
    .show {
      line-height: 32px;
      text-align: center;
      color: #3975ff;
      position: absolute;
      bottom: 15px;
      width: 100%;
      cursor: pointer;
    }
    .p1 {
      padding-left: 66px;
      padding-top: 34px;
      padding-bottom: 30px;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(29, 33, 41, 0.85);
      line-height: 22px;
      img {
        margin-right: 8px;
      }
    }
    .tree {
      //border-left: 1px dashed #000;
      //border-image: linear-gradient(to bottom, #97a0a9, #031425) 1 100%;
      //padding: 0rem 1rem 0rem 1rem;
      //box-sizing: border-box;
      //border-left: 1px dashed transparent;
      //background-image: linear-gradient(rgba(2, 24, 51), rgba(2, 24, 51)),
      //  linear-gradient(to bottom, rgba(5, 84, 138, 0.1), rgba(5, 84, 138, 1));
      //background-origin: border-box;
      //background-clip: padding-box, border-box;
      //border-radius: 10px;
      margin-left: 66px;
      margin-bottom: 50px;
      //overflow: hidden;
      .dashed {
        position: absolute;
        width: 1px;
        height: 100%;
        background-image: repeating-linear-gradient(
          to bottom,
          #020f1f,
          #020f1f 4px,
          transparent 4px,
          transparent 8px,
          transparent 8px,
          transparent 10px
        );
        z-index: 11;
      }

      .dashed2 {
        top: 0;
        left: 66px;
        position: absolute;
        width: 1px;
        height: 100%;
        background-image: linear-gradient(to bottom, #9db0c5, #051527);
      }
      &-list {
        display: flex;
        padding-bottom: 30px;
        padding-left: 20px;
      }
      &-item {
        position: relative;
        .list {
          width: 1px;
          background: #0083ff;
          opacity: 0.4;
          position: absolute;
          top: 15px;
          left: -60px;
        }
        .tree-btn {
          margin-bottom: 20px;
        }
        .item-box {
          position: relative;
        }
      }
      &-btn {
        cursor: pointer;
        min-width: 135px;
        width: 135px;
        padding: 0 5px;
        height: 32px;
        //background: linear-gradient(33deg, #3975FF 0%, #51ABFF 100%);
        background: #0b1d35;
        border: 1px solid rgba(253, 253, 253, 0.2);
        border-radius: 17px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #F4F9FF;
        //color: #FFFFFF;
        //color: #3370FF;
        line-height: 32px;
        text-align: center;
        margin-right: 6rem;
        position: relative;
        &.a1 {
          &::before {
            content: "";
            width: 60px;
            height: 1px;
            background: #0083ff;
            opacity: 0.4;
            position: absolute;
            left: -60px;
            top: 15px;
          }
          &:first-child {
            &::before {
              width: 6rem;
              left: -6rem;
            }
          }
        }
        .a2 {
          &::before {
            content: "";
            width: 60px;
            height: 1px;
            background: #0083ff;
            opacity: 0.4;
            position: absolute;
            left: -60px;
            top: 15px;
          }
          &:first-child {
            &::before {
              width: 6rem;
              left: -6rem;
            }
          }
        }
        &.on1 {
          background: linear-gradient(33deg, #00b769 0%, #5eca9c 100%)
            rgba(0, 0, 0, 0.2);
          box-shadow: inset 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
        }
        &.on2 {
          // background: linear-gradient(33deg, #00B769 0%, #5ECA9C 100%);
          background: linear-gradient(33deg, #f87700 0%, #f59841 100%);
        }
        &.on3 {
          // background: linear-gradient(33deg, #F87700 0%, #F59841 100%);
          background: linear-gradient(33deg, #7f39ef 0%, #995ef6 100%);
        }
        &.on4 {
          background: linear-gradient(33deg, #f87700 0%, #eca96b 100%)
            linear-gradient(33deg, #f87700 0%, #f59841 100%);
        }
      }
    }
    .radar-dom {
      height: 378px;
      padding-top: 30px;
      box-sizing: border-box;
    }
  }

&::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px;
  /* 对应横向滚动条的宽度 */
  height: 0px;
background: rgba(53, 66, 82, 1);
}

/* 滚动条上的滚动滑块 */
&::-webkit-scrollbar-thumb {
  background-color: rgba(175, 231, 253, 0.56);
  border-radius: 32px;
}

/* 滚动条轨道 */
&::-webkit-scrollbar-track {
  display: none;
}
}
</style>