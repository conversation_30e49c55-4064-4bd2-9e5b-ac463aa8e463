.tab {
  width: 210px;
  height: 50px;
  position: absolute;
  top: 20px;
  left: 30px;
  display: flex;
  z-index: 111;

  &-list {
    width: 88px;
    height: 33px;
    line-height: 33px;
    background: rgba(21, 47, 80, 0.7);
    border-radius: 0px 4px 4px 0px;
    box-sizing: border-box;
    padding-left: 39px;
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    position: relative;
    cursor: pointer;

    &::before {
      content: '';
      position: absolute;
      width: 4px;
      height: 4px;
      left: 21px;
      top: 15px;
    }

    &-1 {
      &::before {
        background: #4bccee;
      }
    }

    &-2 {
      &::before {
        background: #d7431c;
      }
    }

    &.on {
      width: 104px;
      height: 47px;
      background: url('~@/assets/img/img12.png') no-repeat;
      margin-top: -5px;
      padding-left: 42px;
      padding-top: 5px;

      &::before {
        left: 23px;
        top: 20px;
      }
    }
  }
}

.line-box {
  display: flex;
  justify-content: space-between;
  position: absolute;
  right: 30px;
  bottom: 32px;
  width: 240px;
  opacity: 0.8;

  .line-list {
    width: 71px;
    height: 27px;
    border-radius: 10px 0px 10px 0px;
    font-size: 13px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 27px;
    opacity: 0.85;
    text-align: center;

    &-1 {
      background: #12975d;
    }

    &-2 {
      background: #e75900;
    }

    &-3 {
      background: #6a25d1;
    }
  }
}

.comMount {
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #b0bbff;
  position: absolute;
  left: 30px;
  top: 140px;

  span {
    margin-left: 6px;
    font-size: 18px;
    font-family: Source Han Sans CN;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 0 15px #2097fb;
  }
}

.chart-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

.save-btn {
  position: absolute;
  right: 20px;
  top: 130px;
  cursor: pointer;
  background: #00112d;
  border: 1px solid #0066ff;
  color: #fff;
}

.test-btn {
  position: absolute;
  right: 20px;
  top: 160px;
  cursor: pointer;
  background: #00112d;
  border: 1px solid #0066ff;
  color: #fff;

  &.line-btn {
    top: 130px;
    right: 70px;
  }

  &.del-btn {
    right: 80px;
  }
}

.loading-info {
  position: fixed;
  top: 0rem;
  left: 0rem;
  color: #ffffffe3;
  font-size: 1.2rem;
  z-index: 99;
  width: 100%;
  height: 100%;
}

::v-deep {
  .el-loading-spinner i {
    display: none;
  }



  .el-loading-spinner {
    width: 100px;
    height: 100px;
    top: calc(50% - 50px);
    left: calc(50% - 50px);
    transform: translate(-130px, -130px);
    background: url('../../../assets/screen/loading.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    animation: load 2s linear infinite;
  }

  @keyframes load {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .loading-info .el-loading-spinner .el-loading-text {
    font-size: 3rem;
    color: #dce1ea;
  }

  .el-loading-mask {}
}