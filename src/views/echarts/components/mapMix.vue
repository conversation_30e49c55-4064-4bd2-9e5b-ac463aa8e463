<template>
  <div class="mapProvenceShow">
    <div class="type-name">
      <span>企业占比</span>
    </div>

    <div class="mapMix">
      <div class="mapMix-header">
        <div class="th th-1">
          {{ showarea || '全国' }}
        </div>
        <div class="th th-2">
          企业数
        </div>
        <div class="th th-3">
          占比
        </div>
      </div>
      <div class="mapMix-body">
        <div
          v-if="newData?.length > 0"
          class="box"
        >
          <div
            v-for="(item, key) in newData"
            :key="key"
            class="tr"
          >
            <div class="td td-1">
              <el-tooltip
                effect="dark"
                :visible-arrow="false"
                placement="right"
                :content="item.countArea"
                popper-class="tag-popover"
              >
                <div>{{ item.countArea }}</div>
              </el-tooltip>
            </div>

            <div class="td td-2">
              {{ item.enterpriseNum }}
            </div>
            <div class="td td-3">
              <div class="mix">
                <div
                  class="mixs"
                  :style="`width: ${item.proportion || 0};`"
                />
              </div>
              {{ item.proportion }}
            </div>
          </div>
        </div>
        <div
          v-if="newData?.length > 0"
          class="pageNation"
        >
          <el-pagination
            small
            layout="prev, pager, next"
            :total="total"
            :current-page="page.pageCurrent"
            :page-size="page.pageSize"
            @current-change="currentChange"
          />
        </div>
        <div
          v-else
          class="nodata"
        >
          <div class="noDataBg" />
          <NoData />
          <!-- <h2>暂无数据</h2> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NoData from '@/views/overview/components/component/noData.vue';
export default {
  components: {
    NoData,
  },
  props: {
    areaMapData: {
      type: Array,
      default: null,
    },
    showarea: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      mapData: [],
      page: {
        pageSize: 11,
        pageCurrent: 1,
      },
    };
  },
  computed: {
    total() {
      return this.areaMapData && this.areaMapData?.length;
    },
    newData() {
      let { pageCurrent, pageSize } = this.page;
      return (
        this.areaMapData &&
        this.areaMapData?.slice(
          (pageCurrent - 1) * pageSize,
          pageCurrent * pageSize
        )
      );
    },
  },
  watch: {
    areaMapData() {
      this.page.pageCurrent = 1;
    },
  },
  mounted() {
    if (window.innerHeight < 800) {
      this.page.pageSize = 6;
    }
  },
  methods: {
    currentChange(val) {
      this.page.pageCurrent = val;
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.pageNation {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.nodata {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #f5f5f5;
}
.mapMix {
  background: rgba(0, 24, 53, 0.8);
  height: calc(100% - 50px);
  padding: 16px;
  font-family: puhuiti;
  font-weight: 500;

  // position: absolute;
  // right: 30px;
  // top: 20px;
  // background: url('~@/assets/img/img13.png') no-repeat top;
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 2.6875rem;
    background: #1b4786;
    text-align: left;
    padding: 0 0.5rem;
    // padding: 0 5.5rem 0 1.2rem;
    .th {
      font-size: 16px;
      color: #ffffff;
    }
    .th-1 {
      width: 180px;
    }
    .th-3 {
      width: 140px;
    }
    .th-2 {
      width: 100px;
    }
  }
  &-body {
    width: 100%;
    height: calc(100% - 40px);
    text-align: left;
    position: relative;

    .box {
      .tr {
        display: flex;
        padding: 0 8px;
        justify-content: space-between;
        &:nth-child(2n) {
          background: rgba(153, 178, 188, 0.1);
        } // border-bottom: 1px dashed rgba(28, 145, 255, 0.4);
        .td {
          padding: 16px 0;
          font-size: 14px;
          font-family: puhuiti;
          font-weight: 400;
          color: #cafeff;
          position: relative;
          &-1 {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 180px;
            position: relative;
          }
          &-2 {
            width: 100px;
            @include YouSheBiaoTi28(18px);
          }
          &-3 {
            display: flex;
            align-items: center;
            width: 140px;
            justify-content: space-between;
            .mix {
              width: 63px;
              height: 4px;
              background: rgba(52, 100, 185, 0.4);
              border-radius: 2px;
              margin-right: 6px;
              position: relative;
              .mixs {
                height: 4px;
                position: absolute;
                background: linear-gradient(270deg, #1478ff);
                border-radius: 2px;
              }
            }
          }
        }
      }
    }
    ::v-deep {
      .el-pagination {
        text-align: center !important;
        background: none !important;
        bottom: 0 !important;
        button:disabled {
          background-color: transparent !important;
          color: rgba(255, 255, 255, 0.9) !important;
        }
        .btn-prev,
        .btn-next {
          background: transparent !important;
          color: rgba(255, 255, 255, 0.9) !important;
        }
      }
      .el-pager li {
        background: transparent !important;
        &.active {
          background: linear-gradient(
            180deg,
            #1cceff 0%,
            rgba(34, 69, 243, 0.88) 100%
          ) !important;
          border-radius: 50%;
          color: rgba(255, 255, 255, 0.9) !important;
        }
      }
    }
  }
}
.mapProvenceShow {
  width: 500px;
  height: 90vh;
  position: absolute;
  right: 32px;
  top: 16px;
}
.type-name {
  width: 100%;
  height: 2.4rem;
  background: center / cover no-repeat
    url('~@/assets/screen/new/inventory-title.webp');
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 0 60px;
  position: relative;
  span {
    @include YouSheBiaoTi24();
  }
  .close {
    width: 40px;
  }
}
</style>
