.homePageContainer {
  width: 100%;
  height: calc(100vh - 60px);
  padding: 6rem 7rem;
  // margin-left: 10px;
  overflow: scroll;
}

.contain {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 32% 68%;
  grid-column-gap: 20px;

  .left {
    width: 100%;
    height: 100%;
    background: url("~@/assets/screen/new/module_bg5.png") center/cover no-repeat;
    background-size: 100% 100%;
    padding: 1.5rem 2.5rem;

    .des {
      font-size: 1rem;
      color: #f7faff;
      padding: 3rem 2rem 4rem 2rem;
    }

    .text {
      padding: 0rem 2rem;

      .leftDesName {
        font-size: 1rem;
        font-family: PingFang SC;
        font-weight: 600;
        // background: linear-gradient(180deg, rgba(1, 174, 255, 0.99) 0%, rgba(5, 177, 254, 0.5) 0%, white 70%);
        background: linear-gradient(
          180deg,
          rgba(0, 200, 241, 1) 0%,
          rgba(0, 193, 255, 1) 0%,
          rgba(236, 253, 254, 1) 58.7646484375%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .leftDesItem {
        padding: 1rem 0 2rem 0;
        font-size: 0.8rem;
        font-family: PingFang SC;
        color: #adb9c2;
      }
    }
  }

  .right {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-rows: 52% 5% 36%;
    grid-row-gap: 3%;


    .rightTop {
      width: 100%;
      height: 100%;
      background: url("~@/assets/screen/new/module_bg4.png") center/cover no-repeat;
      background-size: 100% 100%;
      padding: 1.5rem 1.8rem 0;

      .tabs {
        width: 100%;
        height: 100%;

        .tabsDes {
          font-size: 0.88rem;
          color: rgba(247, 250, 255, 0.8);
          padding: 0.5rem 4.5rem 0;
        }

        .tabsTitle {
          width: 100%;
          position: relative;
        }
      }

      .text {
        height: calc(100% - 5rem);
      }

      .viewAll {
        width: 13.06rem;
        height: 3.19rem;
        background: url("~@/assets/screen/new/title_bg5.png") center/cover no-repeat;
        font-size: 1rem;
        font-weight: 500;
        color: #ffffff;
        line-height: 3.19rem;
        text-align: center;
        top: -5px;
        left: 10rem;
      }
    }

    .rightMiddle {
      // display: grid;
      // grid-template-columns: 1fr 1fr 1fr 1fr;
      // grid-column-gap: 20px;

      // .text {
      //   width: 100%;
      //   height: 100%;
      //   background: url("~@/assets/screen/new/module_bg2.png") center/cover no-repeat;
      //   background-size: 100% 100%;
      //   padding: 0.5rem 4.6rem 0;
      //   font-size: 1.25rem;
      //   font-family: PingFang SC;
      //   font-weight: 600;
      //   color: #ffffff;
      //   // cursor: pointer;
      //   display: flex;
      //   justify-content: center;
      //   // align-items: flex-end;
      //   flex-wrap: wrap;
      //   align-content: center;

      //   // &:hover {
      //   //   background: url('~@/assets/screen/new/module_bg6.png') center/cover no-repeat;
      //   //   background-size: 100% 100%;
      //   // }
      // }

      // .textInner {
      //   width: 7rem;
      //   height: 7.5rem;
      //   margin-top: -1.5rem;
      // }

      // .textInnerName {
      //   width: 100%;
      //   text-align: center;
      //   // margin-top: -0.8rem;
      // }
    }

    .rightBottom {
      display: grid;
      grid-template-columns: 2fr 1fr;
      grid-column-gap: 20px;

      .rightBottomLeft,
      .rightBottomRight {
        // module_bg3
        width: 100%;
        height: 100%;
        background: url("~@/assets/screen/new/module_bg4.png") center/cover no-repeat;
        background-size: 100% 100%;
            display: flex;
  flex-wrap: wrap;
    align-items: stretch;
    justify-content: center;
    padding: 2rem 1.8rem 4rem 2rem;
      }

      .rightBottomLeftMain {
        width: 100%;

        display: flex;
        justify-content: space-between;

        .text {
          width: 50%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem 2rem 0;
        }

        .bgSize {
          width: 5.88rem;
          height: 5.38rem;
        }

        .rightText {
          width: calc(100% - 5.88rem);
          padding-left: 1.5rem;

          .firstChild {
            font-size: 1.13rem;
            font-weight: 500;
            background: linear-gradient(0deg, #7cd0ff 0%, #feffff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          .lastChild {
            padding-top: 1rem;
            font-size: 1.25rem;
            font-family: YouSheBiaoTiHei;
            background: linear-gradient(0deg, #0064f8 0.1953125%, #70fffa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }

      .rightBottomRight {
        .text {
          position: relative;
          width: 17.88rem;
          height: 7.31rem;
          margin: -1rem auto 0;
          cursor: pointer;
          background: url("~@/assets/screen/new/title_bg6.png") center/cover no-repeat;
          background-size: 100% 100%;

          span {
            position: absolute;
            top: 3rem;
            right: 3rem;
            font-size: 1.13rem;
            font-weight: 500;
            background: linear-gradient(0deg, #7cd0ff 0%, #feffff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          &:hover {
            background: url("~@/assets/screen/new/title_bg3.png") center/cover no-repeat;

            span {
              background: linear-gradient(0deg, #7cffdf 0%, #feffff 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }
      }
    }
  }
}

.screen-main {
  background-color: #070d61;
  color: white;
  width: 100vw;
  height: 100vh;
  position: relative;
  min-width: 1080px;
}

// .text {
//   color: white;
//   padding: 10px;
//   cursor: pointer;
// }

// .textBg {
//   width: 7rem;
//   height: 7.5rem;
// }

.text1 {
  background: url("~@/assets/screen/new/icon17.png") center/cover no-repeat;
}

.text2 {
  background: url("~@/assets/screen/new/icon18.png") center/cover no-repeat;
}

.text3 {
  background: url("~@/assets/screen/new/icon19.png") center/cover no-repeat;
}

.text4 {
  background: url("~@/assets/screen/new/icon20.png") center/cover no-repeat;
}

.leftBg1 {
  background: url("~@/assets/screen/new/icon16.png") center/cover no-repeat;
}

.leftBg2 {
  background: url("~@/assets/screen/new/icon15.png") center/cover no-repeat;
}
