<template>
  <div class="layouts">
    <div
      v-if="showList.length > 0"
      class="layout"
    >
      <!-- <div v-if="list.length > 5"> -->
      <div>
        <img
          v-if="list.length > 5"
          src="https://static.idicc.cn/cdn/pangu/right-left.png"
          class="icon-left"
          @click="toleft"
        >
      </div>
      <div class="container">
        <!-- </div> -->
        <!-- <el-row type="flex" justify="space-around" :gutter="20" class="layoutMiddle"> -->
        <!-- <el-col  :span="4.8"> -->
        <div
          v-for="(item, index) in showList"
          v-show="index < 5"
          :key="index"
          class="purple"
          @click="checkedFn(item)"
        >
          <div
            v-if="item?.icon"
            class="chainBg"
            :style="{ backgroundImage: `url(${item?.icon || ''})` }"
          />
          <div
            v-else
            class="chainBg"
          />
          <div class="chainName">
            <span>{{
              item?.chainName ? item?.chainName.replace("产业金脑·", "") : ""
            }}</span>
          </div>
        </div>
      </div>
      <!-- </el-col> -->
      <!-- </el-row> -->
      <div>
        <img
          v-if="list.length > 5"
          src="https://static.idicc.cn/cdn/pangu/right-icon.png"
          class="icon-right"
          @click="toright"
        >
      </div>
    </div>
    <div
      v-else
      class="no-data"
    >
      <NoData title="暂无产业链权限" />
    </div>
  </div>
</template>
<script>
import { orgIndustryChainRelationAPI } from "@/api/user";
import NoData from "@/views/overview/components/component/noData";

export default {
  components: {
    NoData,
  },
  setup() {
    return {};
  },
  data() {
    return {
      showList: [],
      list: [],
      num: 0,
      defaultIcon: "https://static.idicc.cn/cdn/pangu/default.png",
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      // 获取列表
      this.orgIndustryChainRelation();
    },
    async orgIndustryChainRelation() {
      try {
        // this.showondata = false;
        let data = {
          isAll: 1,
        };
        const res = await orgIndustryChainRelationAPI(data);
        if (res.result == null) {
          res.result = [];
        }
        this.list = [...res.result];
        this.showList =
          this.list?.length > 5 ? this.list.slice(0, 5) : this.list;
        //  res?.result?.length > 5 ? this.list.slice(0, 5) : res.result
      } catch (error) {
        console.log(error);
      } finally {
        // this.showondata = true;
      }
    },
    // 点击
    checkedFn(item) {
      if (!item.id) {
        this.$message({
          message: "正在开发中，敬请期待",
          type: "warning",
          customClass: "admin-tips-message",
          duration: 3000,
          showClose: true,
          center: false, // 是否居中
        });
        return;
      }
      if (!item.status) {
        this.$message({
          message: "暂无该产业链权限，具体事项可联系运营人员",
          type: "warning",
          customClass: "admin-tips-message",
          duration: 3000,
          showClose: true,
          center: false, // 是否居中
        });
        return;
      }
      const baseURL = process.env.VUE_APP_PORT_URL || "https://localhost:9999/";
      let orgCode = localStorage.getItem("orgCode");
      let zhumadianCode = process.env.VUE_APP_ZHU_ORG_CODE;
      let newPath =
        zhumadianCode === orgCode
          ? `${baseURL}#/parkOverView?id=${item.id}`
          : `${baseURL}#/overview?id=${item.id}`;
      localStorage.setItem("routerQuery", item.id);

      window.open(newPath);
    },
    // 左滑
    toleft() {
      this.num = (this.num - 1 + this.list.length) % this.list.length;
      this.showList = this.list
        .slice(this.num, this.num + 5)
        .concat(
          this.list.slice(0, Math.max(0, 5 - (this.list.length - this.num)))
        );
    },
    // 右滑
    toright() {
      this.num = (this.num + 1) % this.list.length;
      this.showList = this.list
        .slice(this.num, this.num + 5)
        .concat(
          this.list.slice(0, Math.max(0, 5 - (this.list.length - this.num)))
        );
    },
  },
};
</script>
<style scoped lang="scss">
// .industry-bg {
//   width: 7.75rem;
//   height: 7.75rem;
// }
.layouts {
  height: 100%;
}

.no-data {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.layout {
  display: grid;
  grid-template-columns: 1.69rem 1fr 1.69rem;
  width: 100%;
  height: 100%;
  align-items: center;
  padding: 1rem 5rem;
}

.container {
  display: flex;
  justify-content: space-around;
}

.layoutMiddle {
  padding: 0.5rem 1rem 0;
}

.icon-left,
.icon-right {
  width: 1.69rem;
  height: 3.2rem;

  &:hover {
    transform: scale(1.1);
  }
}

.chainBg {
  width: 6rem;
  height: 6rem;
  background-size: 100%;
  background-repeat: no-repeat;
  position: relative;
  background-image: url("https://static.idicc.cn/cdn/pangu/default.png");
}

.purple {
  display: flex;
  justify-content: center;
  align-items: stretch;
  flex-wrap: wrap;
  cursor: pointer;

  // background-color: #f5f5f5;
  // 放大1.2倍，向下移动1.2倍
  &:hover {
    // background-color: #f5f5f5;
    // 放大1.2倍，向下移动1.2倍
    transform: scale(1.1);
    // translate(0px, -10px);
  }
}

.chainName {
  width: 100%;
  text-align: center;
  bottom: -3px;
  font-size: 1.13rem;
  font-weight: 500;
  background: linear-gradient(0deg, #7cd0ff 0%, #feffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
