<template>
  <div class="title">
    <span> {{ title }}</span>
  </div>
</template>
<script>
export default {
  name: 'TitleView',
  components: {
  },
  props: {
    title: {
      type: String,
      default: ''
    },

  },
  data () {
    return {
    }
  },
  created () {
  },
  mounted () {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
.title {
  width: 100%;
  height: 40px;
  background: url('https://static.idicc.cn/cdn/pangu/assets/screen/new/title_bg.png') center/cover no-repeat;
  background-size: 6.3rem 2.2rem;
  background-position: left;

  span {
    font-size: 1.38rem;
    font-family: Alibaba PuHuiTi;
    font-weight: bold;
    // font-style: italic;
    color: #F6FAFF;
    line-height: 40px;
    padding-left: 2.5rem;
    padding-right: 20px;
    // text-shadow: 0px 2px 8px rgba(5, 28, 55, 0.42);
    // background: linear-gradient(180deg, rgb(1 175 255 / 50%) 0%, rgb(108 223 248 / 50%) 0%, rgb(255 255 255 / 100%) 50%);
    // -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
  }
}

// @media screen and (max-width: 1450px) {
//   .title {
//     span {
//       font-size: 1.25rem;
//     }
//   }
// }
</style>