<template>
  <div class="screen-main">
    <!-- 首页 -->
    <Headers state="homePage">
      <div
        slot="view-container"
        class="homePageContainer"
      >
        <!-- <div>
          <div class="top">
            <div>
              哈密高新技术产业园·产业金脑
            </div>
            <div>
              您好，欢迎来到哈密高新技术产业园产业金脑
            </div>
          </div>
          <div>
            今日是{{ days.today }}
            产业金脑为您服务第 xx 天！
            {{ days.todayWeek }} / {{ days.todayTime }}
          </div>
        </div> -->
        <div class="contain">
          <div class="left">
            <div class="title">
              <Titles title="产业金脑简介" />
            </div>
            <div class="des">
              依托人工智能大数据和先进算法，构建产业研究、产业决策、产业招商、产业运营、产业服务等五位一体产业赋能体系，助力地方产业链强链补链高质量发展。
            </div>
            <div class="text">
              <div
                v-for="item in textList"
                :key="item.id"
              >
                <div class="leftDesName">
                  {{ item.name }}
                </div>
                <div class="leftDesItem">
                  {{ item.des }}
                </div>
              </div>
              <!-- 查看详情 -->
            </div>
            <!-- <div class="img" /> -->
          </div>
          <div class="right">
            <div class="rightTop">
              <div class="tabs">
                <div class="tabsTitle">
                  <Titles title=" 产业驾驶舱" />
                  <!-- <div class="viewAll" @click="toPage('')">
                    产业概览 >
                  </div> -->
                </div>
                <div class="tabsDes">
                  精确定位，实现企业与产业链细分环节精准匹配，打造产业全景一张图；以驾驶舱视角观趋势、研形势，系统化洞察区域数字交通产业发展态势。
                </div>
                <div class="text">
                  <!-- 产业tab -->
                  <Industry />
                </div>
              </div>
            </div>
            <div class="rightMiddle" />
            <!-- <div class="rightMiddle">
              <div
                v-for="textItem in jumpList"
                :key="textItem.id"
                class="text "
              >
                <div :class="['textInner', 'text' + textItem.id]" />
                <div class="textInnerName">
                  {{ textItem.name }}
                </div>
              </div>
            </div> -->
            <div class="rightBottom">
              <div class="rightBottomLeft">
                <Titles title="产业数字底座" />
                <div class="rightBottomLeftMain">
                  <!-- @click="toPage('')" -->
                  <div class="text">
                    <div class="leftBg1 bgSize" />
                    <div class="rightText">
                      <div class="firstChild">
                        产业专题库
                      </div>
                      <div class="lastChild">
                        150+产业链
                      </div>
                    </div>
                  </div>
                  <!--  @click="toPage('')" -->
                  <div class="text">
                    <div class="leftBg2 bgSize" />
                    <div class="rightText">
                      <div class="firstChild">
                        数据管理平台
                      </div>
                      <div class="lastChild">
                        2.3亿条主体信息
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="rightBottomRight">
                <Titles title="控制台" />
                <div
                  class="text rightBg"
                  @click="toPage('/')"
                >
                  <!-- -->
                  <span>管理后台</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Headers>
  </div>
</template>

<script>
import Headers from "@/components/header.vue";
// import logout from "@/components/logout.vue";
import Industry from "./components/index.vue";
import Titles from "./components/titleHeader.vue";
import dayjs from "dayjs";
export default {
  name: "HomePage",
  components: {
    // logout,
    Industry,
    Headers,
    Titles,
  },
  data() {
    return {
      days: {
        today: "",
        todayWeek: "",
        todayTime: "",
      },
      textList: [
        {
          name: "·1套数据底座",
          id: 1,
          des: "链接企业、机构、人才、技术、产品、资金等数据资源，构建大数据数字底座。",
        },
        {
          name: "·2种服务模式",
          id: 2,
          des: "提供线上产业分析研判、线下招商委托互动的融合服务模式",
        },
        {
          name: "·3个数字终端",
          id: 3,
          des: "打造大屏端+PC端+移动端3个数字终端，全方位适配多种工作场景。",
        },
        {
          name: "·4大数据赋能",
          id: 4,
          des: "构建产业洞察、产业全景、产业地图、产业招商4大数据智能应用。",
        },
        {
          name: "·5类场景应用",
          id: 5,
          des: "赋能产业研究、产业决策、产业招商、产业运营、产业服务等5类场景应用。",
        },
      ],
      jumpList: [
        {
          name: "产业洞察",
          id: 1,
          path: "/idicc/view",
        },
        {
          name: "数智招商",
          id: 2,
          path: "/attract/intelligentSearch",
        },
        {
          name: "招商智管",
          id: 3,
          path: "/attract/manage",
        },
        {
          name: "智能产服",
          id: 4,
          path: "",
        },
      ],
    };
  },
  computed: {},
  created() {},
  mounted() {
    // 更改浏览器标签名称
    // document.title = '首页-产业金脑';
    // this.init()
  },
  methods: {
    init() {
      // dayjs获取当天年月日
      this.days.today = dayjs().format("YYYY年MM月DD日");
      // dayjs获取当天星期几
      this.days.todayWeek = dayjs().format("dddd");
      // dayjs获取当天星期几转中文
      switch (this.days.todayWeek) {
        case "Sunday":
          this.days.todayWeek = "周日";
          break;
        case "Monday":
          this.days.todayWeek = "周一";
          break;
        case "Tuesday":
          this.days.todayWeek = "周二";
          break;
        case "Wednesday":
          this.days.todayWeek = "周三";
          break;
        case "Thursday":
          this.days.todayWeek = "周四";
      }
      // dayjs获取当时间
      this.days.todayTime = dayjs().format("HH:mm");
    },
    toPage(path) {
      if (path === "") {
        this.$message({
          message: "正在开发中，敬请期待",
          type: "warning",
          customClass: "admin-tips-message",
          duration: 3000,
          showClose: true,
          center: false, // 是否居中
        });
        return;
      }
      this.$router.push(path);

      // window.$wujie?.bus.$emit('newRouter', { path: path });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>