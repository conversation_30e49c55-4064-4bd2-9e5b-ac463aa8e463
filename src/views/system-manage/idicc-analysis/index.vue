<!--
 * @Author: jhy
 * @Date: 2023-05-24 10:34:31
 * @LastEditTime: 2023-06-05 15:31:41
 * @LastEditors: jhy
 * @Description: 
 * @FilePath: /QT-SASS-PC/src/views/system-manage/idicc-analysis/index.vue
-->
<template>
  <div v-if="examine">
    <div v-if="showcontent">
      <div class="analysis">
        <div class="analysis-top">
          <!--       <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">
          产业洞察
        </el-breadcrumb-item>
        <el-breadcrumb-item>产业分析</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="analysis-title" /> -->
          <div class="analysis-tab-con">
            <div class="analysis-tab">
              <div
                class="analysis-tab-list"
                :class="tabCheck == 1 ? 'on' : ''"
                @click="tabEvent(1)"
              >
                地区对比
              </div>
              <!-- <div
                class="analysis-tab-list"
                :class="tabCheck == 2 ? 'on' : ''"
                @click="tabEvent(2)"
              >
                强链补链延链分析
              </div> -->
            </div>
          </div>
        </div>

        <div class="analysis-box">
          <AddressC
            v-if="tabCheck == 1"
            ref="AddressC"
            :user-del="userDel"
          />
          <Analysis
            v-if="tabCheck == 2"
            ref="Analysis"
            :user-del="userDel"
          />
        </div>
      </div>
    </div>
    <div
      v-else
      class="nodata"
    >
      <div class="con">
        <img src="https://static.idicc.cn/cdn/SSO/zhaoshangnodata.png">
        <span class="p1">暂无产业链权限</span>
        <span class="p2">请联系机构管理员配置权限功能</span>
      </div>
    </div>
  </div>
</template>

<script>
import { homeListAPI } from "@/views/system-manage/attract-investment/apiUrl";
import { defaultUserAuthInfo } from "@/views/system-manage/idicc-scan/apiUrl";
import AddressC from "./component/address.vue";
import Analysis from "./component/analysis.vue";
export default {
  name: "IdiccAnalysis",
  components: {
    AddressC,
    Analysis,
  },
  data() {
    return {
      tabCheck: 1,
      examine: false,
      showcontent: false,
      userDel: {},
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    this.getuserDel();
    //this.tabEvent(1)
  },
  methods: {
    tabEvent(val) {
      this.tabCheck = val;
      this.$nextTick(() => {
        if (val == 1) {
          this.$refs.AddressC.updata();
        } else {
          this.$refs.Analysis.updata();
        }
      });
    },
    async getuserDel() {
      const res = await defaultUserAuthInfo();
      this.userDel = res;
      setTimeout(() => {
        this.$nextTick(() => {
          this.$refs.AddressC.updata();
        });
      }, 1000);
    },
    async getList() {
      try {
        const res = await homeListAPI();
        if (res.result == null || res.result.length < 1) {
          this.showcontent = false;
        } else {
          this.showcontent = true;
        }
      } finally {
        this.examine = true;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.nodata {
  min-width: 88.5vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  .con {
    margin-top: 10%;
    width: 170px;
    height: 300px;
    display: flex;
    align-items: center;
    flex-direction: column;
    img {
      width: 160px;
      height: 165.03px;
    }
    .p1 {
      margin-top: 38px;
      font-size: 14px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
    .p2 {
      margin-top: 16px;
      font-size: 12px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
.analysis {
  // background: #f5f6f7;
  min-height: 100vh;
  box-sizing: border-box;
  // min-width: 1240px;
  &-top {
    padding: 16px 0px;
    padding-bottom: 12px;
  }
  &-box {
    margin: 16px 0;
  }
  &-title {
    padding-bottom: 31px;
    display: flex;
    .sp1 {
      font-size: 20px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 28px;
      margin-right: 8px;
    }
    .sp2 {
      font-size: 14px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.45);
      line-height: 28px;
    }
  }
  &-tab {
    display: flex;
    padding-left: 16px;
    &-con {
      display: flex;
      justify-content: space-between;
    }
    &-select {
      display: flex;
      .el-select {
        margin-left: 8px;
      }
    }
    &-list {
      margin-right: 64px;
      font-size: 16px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      cursor: pointer;
      &.on {
        font-weight: bold;
        // color: #3370ff;
        position: relative;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.85);
        // &::after {
        //   content: "";
        //   width: 28px;
        //   height: 2px;
        //   background: #3370ff;
        //   position: absolute;
        //   left: 18px;
        //   bottom: -8px;
        // }
      }
      &:last-child {
        &.on {
          &::after {
            left: 48px;
          }
        }
      }
    }
  }
}
</style>