<template>
  <div class="radar">
    <div class="title">
      {{ info.name }}
    </div>
    <div class="radar-con">
      <div class="radar-item">
        <div class="p1">
          <img
            width="25"
            src="https://static.idicc.cn/cdn/pangu/icon12.png"
            alt=""
          >
          基于产业研判分析，发现
          {{ 
            info.name=='强链环节'? dataInfo.numberOfStrongNode:
            info.name=='补链环节'? dataInfo.numberOfComplementNode:
            info.name=='延链环节'? dataInfo.numberOfExtendedNode:0
          }}
          个{{ info.name }}
        </div>
        <div
          class="tree"
          :style="`height: ${treeH};`"
        >
          <div
            v-for="(item,i) in detail"
            :key="i"
            class="tree-list"
          >
            <div
              class="tree-btn"
              :class="item.isStrong?
                (info.name=='强链环节'?'on1':
                  info.name=='补链环节'?'on2':
                  info.name=='延链环节'?'on3':''
                )
                :''"
            >
              {{ item.nodeName }}
            </div>
            <div class="tree-item">
              <div
                v-for="(item1,j) in item.childNodes"
                :key="j"
                class="tree-btn a1"
                :class="item1.isStrong?
                  (info.name=='强链环节'?'on1':
                    info.name=='补链环节'?'on2':
                    info.name=='延链环节'?'on3':''
                  )
                  :''"
                :style="`margin-bottom: ${((item1.childNodes && item1.childNodes.length -1) * 52)+20}px`"
              >
                {{ item1.nodeName }}
              </div>
              <div
                class="list 111"
                :style="`height: ${item.len * 52}px;`"
              />
            </div>
            <div class="tree-item">
              <div
                v-for="(item2,j) in item.childNodes"
                :key="j"
                class="item-box"
                :style="!item2.childNodes?'height: 52px;':''"
              >
                <div 
                  v-for="(item3, n) in item2.childNodes" 
                  :key="n"
                  class="tree-btn a2 a1" 
                  :class="item3.isStrong?
                    (info.name=='强链环节'?'on1':
                      info.name=='补链环节'?'on2':
                      info.name=='延链环节'?'on3':''
                    )
                    :''"
                >
                  {{ item3.nodeName }}
                </div>
                <div
                  v-if="item2 && item2.childNodes"
                  class="list 222"
                  :style="`height: ${(item2.childNodes.length-1)*52}px;`"
                />
              </div>
            </div>
          </div>
        </div>
        <div
          class="show"
          @click="showEvent"
        >
          {{ treeH=='206px'?'展开':'收起' }}
        </div>
      </div>
      <div class="radar-item">
        <div
          :id="'radarId' + info.id"
          class="radar-dom"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import * as echarts from 'echarts';
  export default {
    name: 'RaDar',
    props: {
      info: {
        type: Object,
        default: null
      },
      dataInfo: {
        type: Object,
        default: ()=>{
          return {};
        }
      }
    },
    data() {
      return {
        detail: null,
        treeH: '206px',
      }
    },
    mounted () {
      this.init();
    },
    methods: {
      init() {
        let arr = this.dataInfo.strongNodesRank;
        if(this.info.name=='强链环节'){
          arr = this.dataInfo.strongNodesRank;
          this.detail = this.dataInfo.strongNodes
        }
        if(this.info.name=='补链环节'){
          arr = this.dataInfo.complementNodesRank;
          this.detail = this.dataInfo.complementNodes
        }
        if(this.info.name=='延链环节'){
          arr = this.dataInfo.extendedNodesRank;
          this.detail = this.dataInfo.extendedNodes
        }
        let _len=0
        this.detail && this.detail.map(e=>{
          let len= 0;
          e.childNodes?.map((e1,n)=>{
            if(e.childNodes?.length>1){
              // len= (e.childNodes?.length || 0);
              if(n!=e.childNodes?.length-1){
                len = len + (e1.childNodes?.length || 0);
                if(e.childNodes.length>1 && (!e1.childNodes || e1.childNodes.length==0)){
                  len++
                }
              }
              e1.childNodes?.map((e2)=>{
                len = len + (e2.childNodes?.length || 0);
              })
            }
          })
          _len = _len + len;
          e.len = len;
        });
        
        // if(_len>6){
        //   this.treeH= '338px';
        // }else{
        //   this.treeH= 'auto';
        // }
        let nameList=[];
        let list = [];
        let maxList=[];
        let max=0;
        // let nameList=['规模力排名','引领力排名','创新力排名','成长力排名','融资力排名'];

        arr.map(e=>{
          let nodeName
          this.detail.map(el=>{
            if(e.nodeId == el.nodeId){
              nodeName=el.nodeName
              nameList.push(el.nodeName)
            }else{
              el.childNodes?.map(el1=>{
                if(e.nodeId == el1.nodeId){
                  nameList.push(el1.nodeName)
                  nodeName=el1.nodeName
                }else{
                  el1.childNodes?.map(el2=>{
                    if(e.nodeId == el2.nodeId){
                      nameList.push(el2.nodeName)
                      nodeName=el2.nodeName
                    }else {
                      el1.childNodes?.map(el3=>{
                        if(e.nodeId == el3.nodeId){
                          nameList.push(el3.nodeName)
                          nodeName=el3.nodeName
                        }
                      })
                    }
                  })
                }
              })
            }
          })

          let val = [
              e.rankOfScale || 0,
              e.rankOfGuidance || 0,
              e.rankOfCreative || 0,
              e.rankOfGrowth || 0,
              e.rankOfFinancing || 0,
            ]
            maxList=[...maxList,...val];
            list.push({
              value: val,
              name: nodeName
            })
        })
        max = Math.max(...maxList);

        let chartDom = document.getElementById('radarId'+this.info.id);
        let myChart = echarts.init(chartDom);
        let option;

        option = {
          // title: {
          //   text: 'Basic Radar Chart'
          // },
          tooltip: {
            trigger: 'item'
          },
          legend: {
            type:'scroll',
            pageIconInactiveColor: 'rgba(44,132,251,0.40)',
            pageIconColor: '#2C86FF', 
            pageIconSize: 10, //翻页按钮大小
            itemWidth: 12,  
            // data: ['规模力排名','引领力排名','创新力排名','成长力排名','融资力排名'],
            textStyle: {
              color: "rgba(0,0,0,0.65)",
              fontSize: 12,
              opacity:0.8,
              lineHeight: 33, // 设置文字之间的上下间距
            },
            left:"70%",    
            orient: 'vertical',
            bottom: 'middle',
            top: '0%', //调整图例位置
            itemHeight: 7, //修改icon图形大小
            icon: "circle", //图例前面的图标形状
          },
          radar: {
            // shape: 'circle',
            indicator: [
              { name: '规模力排名', max: max },
              { name: '引领力排名', max: max },
              { name: '创新力排名', max: max },
              { name: '成长力排名', max: max },
              { name: '融资力排名', max: max }
            ],
            center: ['30%', '50%'],
            radius: '55%',
          },
          series: [
            {
              type: 'radar',
              tooltip: {
                trigger: 'item'
              },
              left: "-50%",
              top: "10%",
              data: list
            }
          ]
        };

        option && myChart.setOption(option);
      },
      showEvent() {
        if(this.treeH=='auto'){
          this.treeH='206px';
        }else{
          this.treeH='auto';
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
.green{
  background: linear-gradient(33deg, #00B769 0%, #5ECA9C 100%) rgba(0,0,0,0.2) !important;
  box-shadow: 0px 4px 12px 0px #EEF1F8;
}
.orange{
  background: linear-gradient(33deg, #F87700 0%, #F59841 100%) !important;
}
.purple{
  background: linear-gradient(33deg, #7F39EF 0%, #995EF6 100%) !important;
}
  .radar{
    .title{
      height: 55px;
      line-height: 55px;
      padding-left: 24px;
      border-bottom: 1px solid #E9E9E9;
      font-size: 16px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0,0,0,0.85);
    }
    &-con{
      display: flex;
    }
    &-item{
      width: 49%;
      position: relative;
      .show{
        line-height: 32px;
        text-align: center;
        color: #3975FF;
        position: absolute;
        bottom: 15px;
        font-size: 14px;
        width: 100%;
        cursor: pointer;
      }
      .p1{
        padding-left: 66px;
        padding-top: 34px;
        padding-bottom: 30px;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: rgba(29,33,41,0.85);
        line-height: 22px;
        img{
          margin-right: 8px;
        }
      }
      .tree{
        padding-left: 66px;
        margin-bottom: 50px;
        overflow: hidden;
        &-list{
          display: flex;
          padding-bottom: 30px;
        }
        &-item{
          position: relative;
          .list{
            width: 1px;
            background: #BADEFF;
            position: absolute;
            top: 15px;
            left: -24px;
          }
          .tree-btn{
            margin-bottom: 20px;
          }
          .item-box{
            position: relative;
          }
        }
        &-btn{
          cursor: pointer;
          width: 109px;
          height: 32px;
          background: linear-gradient(33deg, #3975FF 0%, #51ABFF 100%);
          font-size: 14px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 32px;
          text-align: center;
          border-radius: 5px;
          margin-right: 48px;
          position: relative;
          &.a1{

            &::before{
              content: '';
              width: 24px;
              height: 1px;
              background: #BADEFF;
              position: absolute;
              left: -24px;
              top: 15px;
            }
            &:first-child{
              &::before{
                width: 48px;
                left: -48px;
              }
            }
          }
          .a2{
            &::before{
              content: '';
              width: 24px;
              height: 1px;
              background: #BADEFF;
              position: absolute;
              left: -24px;
              top: 15px;
            }
            &:first-child{
              &::before{
                width: 48px;
                left: -48px;
              }
            }
          }
          &.on1{
            background: linear-gradient(33deg, #00B769 0%, #5ECA9C 100%) rgba(0,0,0,0.2);
            box-shadow: 0px 4px 12px 0px #EEF1F8;
            
          }
          &.on2{
            // background: linear-gradient(33deg, #00B769 0%, #5ECA9C 100%);
            background: linear-gradient(33deg, #F87700 0%, #F59841 100%);
          }
          &.on3{
            // background: linear-gradient(33deg, #F87700 0%, #F59841 100%);
            background: linear-gradient(33deg, #7F39EF 0%, #995EF6 100%);
            
          }
          &.on4{
            background: linear-gradient(33deg, #F87700 0%, #ECA96B 100%) linear-gradient(33deg, #F87700 0%, #F59841 100%);
          }
        }
      }
      .radar-dom{
        height: 378px;
        padding-top: 30px;
        box-sizing: border-box;
      }
    }
  }
</style>