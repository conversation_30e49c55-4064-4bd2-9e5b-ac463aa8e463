<!--
 * @Author: jhy
 * @Date: 2023-05-24 17:36:28
 * @LastEditTime: 2023-06-06 10:02:41
 * @LastEditors: jhy
 * @Description: 
 * @FilePath: /QT-SASS-PC/src/views/system-manage/idicc-analysis/component/analysis.vue
-->
<template>
  <div class="analysis">
    <div class="search">
      <div class="search-list">
        <div class="label">
          产业链：
        </div>
        <el-select
          ref="select"
          v-model="chainId"
          size="mini"
          placeholder="请选择"
          :popper-append-to-body="false"
          @change="changeEvent"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="search-list on">
        <div class="label">
          分析地区：
        </div>
        <div class="add-list">
          <div class="item appendToBodyFalse">
            <el-cascader
              ref="cascader"
              v-model="regionVal"
              size="mini"
              :props="regionProp"
              @change="check"
            />
          </div>
        </div>
      </div>
      <div class="btn">
        <div
          class="btn-list"
          @click="reset"
        >
          重置
        </div>
        <div
          class="btn-list on"
          :class="disable?'disable':''"
          @click="search"
        >
          查询
        </div>
      </div>
    </div>

    <div
      v-if="dataInfo"
      class="analysis-con"
    >
      <div class="list">
        <div class="title">
          产业链基本信息
        </div>
        <div class="list-con">
          <div class="item">
            <div class="li li1">
              <div class="div1">
                <img
                  src="https://static.idicc.cn/cdn/pangu/icon10.png"
                  alt=""
                >
              </div>
              <div class="div2">
                <div class="p1">
                  <span class="sp1">{{ val2 }}</span>
                  <span class="sp2">[{{ val1 }}]</span>
                </div>
                <div class="p2">
                  产业链名称
                </div>
              </div>
              <div class="div2">
                <div class="p1">
                  {{ dataInfo.appraise || 0 }}
                </div>
                <div class="p2">
                  产业链整体评价
                </div>
              </div>
            </div>
            <div class="li li2">
              <div class="div1">
                <img
                  src="https://static.idicc.cn/cdn/pangu/icon11.png"
                  alt=""
                >
              </div>
              <div class="div2">
                <div class="p1">
                  {{ dataInfo.numberOfStrongNode || 0 }}
                </div>
                <div class="p2 on1">
                  强链环节
                </div>
              </div>
              <div class="div2">
                <div class="p1">
                  {{ dataInfo.numberOfComplementNode }}
                </div>
                <div class="p2 on2">
                  补链环节
                </div>
              </div>
              <div class="div2">
                <div class="p1">
                  {{ dataInfo.numberOfExtendedNode || 0 }}
                </div>
                <div class="p2 on3">
                  延链环节
                </div>
              </div>
            </div>
          </div>
          <div class="item">
            <ul>
              <li>
                <span class="name">规模力排名</span>
                <span class="sp1">
                  <el-progress
                    color="#3370FF"
                    :percentage="(dataInfo.industryNodeAttributeRankingDTO.topPercentOfScale || 0) | fnName"
                    :format="format"
                  />
                </span>
                <span class="sp2">NO.{{ dataInfo.industryNodeAttributeRankingDTO.rankOfScale || 0 }}  前{{ (dataInfo.industryNodeAttributeRankingDTO.topPercentOfScale || 0) | fnName }}%</span>
              </li>
              <li>
                <span class="name">引领力排名</span>
                <span class="sp1">
                  <el-progress
                    color="#3370FF"
                    :percentage="(dataInfo.industryNodeAttributeRankingDTO.topPercentOfGuidance||0) | fnName"
                    :format="format"
                  />
                </span>
                <span class="sp2">NO.{{ dataInfo.industryNodeAttributeRankingDTO.rankOfGuidance||0 }}  前{{ (dataInfo.industryNodeAttributeRankingDTO.topPercentOfGuidance||0) | fnName }}%</span>
              </li>
              <li>
                <span class="name">创新力排名</span>
                <span class="sp1">
                  <el-progress
                    color="#3370FF"
                    :percentage="(dataInfo.industryNodeAttributeRankingDTO.topPercentOfCreative||0) | fnName"
                    :format="format"
                  />
                </span>
                <span class="sp2">NO.{{ dataInfo.industryNodeAttributeRankingDTO.rankOfCreative||0 }}  前{{ (dataInfo.industryNodeAttributeRankingDTO.topPercentOfCreative||0) | fnName }}%</span>
              </li>
              <li>
                <span class="name">成长力排名</span>
                <span class="sp1">
                  <el-progress
                    color="#3370FF"
                    :percentage="(dataInfo.industryNodeAttributeRankingDTO.topPercentOfGrowth||0) | fnName"
                    :format="format"
                  />
                </span>
                <span class="sp2">NO.{{ dataInfo.industryNodeAttributeRankingDTO.rankOfGrowth||0 }}  前{{ (dataInfo.industryNodeAttributeRankingDTO.topPercentOfGrowth||0) | fnName }}%</span>
              </li>
              <li>
                <span class="name">融资力排名</span>
                <span class="sp1">
                  <el-progress
                    color="#3370FF"
                    :percentage="(dataInfo.industryNodeAttributeRankingDTO.topPercentOfFinancing||0) | fnName"
                    :format="format"
                  />
                </span>
                <span class="sp2">NO.{{ dataInfo.industryNodeAttributeRankingDTO.rankOfFinancing||0 }}  前{{ (dataInfo.industryNodeAttributeRankingDTO.topPercentOfFinancing||0) | fnName }}%</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div
        v-for="(item, i) in linkList"
        :key="i"
        class="list"
      >
        <Radar 
          :info="item" 
          :data-info="dataInfo"
        />
      </div>
    </div>
    <custom-loading :is-show="loading" />
  </div>
</template>

<script>
  import Radar from './radar.vue';
  import { verificationAPI } from "@/api/user";
  import CustomLoading from "@/components/loading.vue";
  import { orgChainList, getRegion, industryNodeAttribute } from './../../idicc-scan/apiUrl'
  export default {
    name: 'AnalysisC',
    components: {
      Radar,
      CustomLoading
    },
    filters: {
      fnName: function(value) {
        return +(value*100).toFixed(0);
      }
    },
    props:{
      userDel:{
        type:Object,
        default:()=>{}
      }
    },
    data() {
      return {
        chainId: '',
        options: [],
        regionProp:{ 
          checkStrictly: true,
          lazy: true,
          lazyLoad (node, resolve) {
            const { level, data } = node;
            if(level>2){
              resolve([]);
              return
            }
            let type = level + 1;
            let param = {type}
            if(data && data.id){
              param.parentId= data.id
            }
            getRegion(param).then(res=>{
              res.map(e=>{
                e.value = e.code;
                e.parentId = e.id;
                e.label = e.name;
                return e;
              })
              resolve(res)
            })
          }
        },
        regionVal: [],

        linkList: [
          {name: '强链环节', id: 1, count: 5},
          {name: '补链环节', id: 2, count: 3},
          {name: '延链环节', id: 3, count: 2},
        ],
        dataInfo: null,
        val1:'',
        val2:'',
        disable: true,
        loading:true
      }
    },
    watch:{
      userDel(){
        this.updata()
      }
    },
    created () {
      this.orgChainList();
    },
    methods: {
      updata(){
        this.chainId=this.userDel.chainId
        this.regionVal = [this.userDel.provinceCode];
        this.userDel.cityCode && (this.regionVal = [this.userDel.provinceCode, this.userDel.cityCode]);
        this.changeEvent()
        setTimeout(()=>{
          this.$nextTick(()=>{
          this.search()
        })
        },1500)
      },
      format(){
        return ''
      },
      check(){
        if(this.regionVal.length>0 && this.chainId){
          this.disable = false
        }else{
          this.disable = true
        }
      },
      changeEvent(){
        //let val1 = this.$refs.cascader.$children[0].getInput().value;
        //let val2 = this.$refs.select.$children[0].getInput().value;
        this.check();
      },
      orgChainList(){
        orgChainList().then(res=>{
          if(!res || res.length==0){
            this.$message.error('暂无内容，请联系机构管理员配置功能权限')
            this.loading=false
            return 
          }
          res.map(e=>{
            this.options.push({
              label: e.chainName.replace('产业金脑·', ''),
              value: e.industryChainId
            });
          });
        });
      },
     async industryNodeAttribute(){
        this.loading=true
        let param = {
          regionCode: this.regionVal[this.regionVal.length-1],
          industryId: this.chainId,
          divisionLevel: (this.regionVal.length),
        }
       /*  const is = await verificationAPI({
        industryChainId:this.chainId,
      }) 
      if(is){ */
        industryNodeAttribute(param).then(res=>{
          res.industryNodeAttributeRankingDTO = res.industryNodeAttributeRankingDTO || {};
          this.dataInfo = res;
          this.loading=false
        }).catch(()=>{
          this.loading=false
        })
    /*   }else{
        this.$message.error('暂无该产业链权限')
        setTimeout(()=>{
          location.reload()
        },500)
      } */
      },
      search() {
        if(this.disable){
          return false;
        }
        let val1 = this.$refs.cascader.$children[0].getInput().value;
        let val2 = this.$refs.select.$children[0].getInput().value;
        val1 = val1.split('/');
        val1 = val1[val1.length-1];
        this.val1 = val1;
        this.val2 = val2;
        this.dataInfo=null;
        this.$nextTick(()=>{
          this.industryNodeAttribute();
        })
      },
      reset() {
        this.loading=true
        this.chainId = '';
        this.disable= true,
        this.regionVal = '';
        this.updata()
      }
    },
  }
</script>

<style lang="scss" scoped>
  .analysis{
    padding-bottom: 30px;
    .search{
      background: #FFFFFF;
      box-shadow: 0px 4px 12px 0px #EEF1F8;
      border-radius: 10px;
      padding: 22px 24px;
      &-list{
        display: flex;
        align-items: center;
        padding-bottom: 20px;
        &.on{
          align-items: baseline;
        }
        .label{
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0,0,0,0.85);
          width: 90px;
        }
        .add-list{
          .item{
            display: flex;
            align-items: center;
            padding-bottom: 12px;
            i{
              color: #3370FF;
              font-weight: bold;
              margin-left: 20px;
              font-size: 15px;
              cursor: pointer;
            }
          }
        }
        .ul{
          display: flex;
          align-items: center;
          .li{
            font-size: 14px;
            font-family: Abel-Regular, Abel;
            font-weight: 400;
            color: rgba(0,0,0,0.65);
            line-height: 22px;
            margin-left: 32px;
            &.on{
              background: #3370FF;
              border-radius: 2px;
              color: #FFFFFF;
              padding: 0 8px;
            }
            &.zdy{
              color: #3370FF;
            }
          }
        }
      }
      .btn{
        display: flex;
        justify-content: center;
        &-list{
          width: 65px;
          height: 32px;
          background: #FFFFFF;
          border-radius: 5px;
          opacity: 1;
          border: 1px solid #D9D9D9;
          text-align: center;
          line-height: 32px;
          margin-right: 10px;
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0,0,0,0.65);
          cursor: pointer;
          &.on{
            background: #3370FF;
            border: 0px solid #D9D9D9;
            color: #FFFFFF;
          }
          &.disable{
            background: #D9D9D9;
            border: 0px solid #D9D9D9;
            color: #FFFFFF;
            cursor: no-drop;
          }
        }
      }
    }
    &-con{
      .list{
        margin-top: 25px;
        background: #fff;
        box-shadow: 0px 4px 12px 0px #EEF1F8;
        border-radius: 10px;
        .title{
          height: 55px;
          line-height: 55px;
          padding-left: 24px;
          border-bottom: 1px solid #E9E9E9;
          font-size: 16px;
          font-family: Source Han Sans CN-Medium, Source Han Sans CN;
          font-weight: 500;
          color: rgba(0,0,0,0.85);
        }
        &-con{
          background: #FFFFFF;
          box-shadow: 0px 4px 12px 0px #EEF1F8;
          border-radius: 10px;
          display: flex;
        }
        .item{
          width: 49%;
          &:last-child{
            border-left: 1px solid #E9E9E9;
          }
          .li{
            height: 134px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            &:first-child{
              border-bottom: 1px solid #E9E9E9;
            }
            .div1{
              img{
                width: 50px;
                height: 47.45px;
              }
            }
            .div2{
              text-align: center;
              .p1{
                height: 38px;
                font-size: 24px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: rgba(0,0,0,0.85);
                line-height: 38px;
                .sp2{
                  font-size: 14px;
                  margin-left: 8px;
                }
              }
              .p2{
                height: 22px;
                font-size: 14px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: rgba(0,0,0,0.45);
                line-height: 22px;
                padding-top: 7px;
                position: relative;
                &::before{
                  content: "";
                  width: 5px;
                  height: 5px;
                  border-radius: 5px;
                  position: absolute;
                  top: 16px;
                  left: -12px;
                }
                &.on1{
                  &::before{
                    background: #00B769;
                  }
                }
                &.on2{
                  &::before{
                    background: #F87700;
                  }
                }
                &.on3{
                  &::before{
                    background: #7F39EF;
                  }
                }
              }
            }
          }
          ul{
            padding-top: 30px;
            li{
              display: flex;
              align-items: center;
              height: 22px;
              margin-bottom: 23px;
              .name{
                font-size: 14px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: rgba(0,0,0,0.45);
                line-height: 22px;
                margin-right: 17px;
              }
              .sp1{
                width: 57%;
                ::v-deep{
                  .el-progress-bar{
                    padding-right: 0;
                  }
                }
              }
              .sp2{
                font-size: 14px;
                font-family: Abel-Regular, Abel;
                font-weight: 400;
                color: rgba(0,0,0,0.45);
                line-height: 22px;
                margin-left: 17px;
              }
            }
          }
        }
      }
    }
  }
</style>