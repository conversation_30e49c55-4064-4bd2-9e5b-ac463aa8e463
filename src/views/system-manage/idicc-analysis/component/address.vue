<template>
  <div class="address">
    <div class="search">
      <div class="search-list appendToBodyFalse">
        <div class="label">
          产业环节：
        </div>
        <el-cascader
          v-model="chainId"
          size="mini"
          :options="optChain"
          :props="{checkStrictly: true,}"
          @change="check"
        />
      </div>
      <div class="search-list on">
        <div class="label">
          对比地区：
        </div>
        <div class="add-list">
          <div class="item appendToBodyFalse">
            <el-cascader
              :ref="'arr_0'"
              v-model="arr[0]"
              size="mini"
              :options="areaList"
              :props="regionProp"
            />
          </div>
          <div
            v-for="(item,i) in addressList"
            :key="i"
            class="item appendToBodyFalse"
          >
            <el-cascader
              :ref="'arr_'+(i+1)"
              v-model="arr[i+1]"
              size="mini"
              :options="areaList"
              :props="regionProp"
            />
            <i
              v-if="addressList.length<4"
              class="el-icon-plus"
              @click="addEvent(i)"
            />
            <i
              v-if="addressList.length!=1"
              class="el-icon-minus"
              @click="removeEvent(item,i)"
            />
          </div>
        </div>
      </div>
      <div class="btn">
        <div
          class="btn-list"
          @click="reset"
        >
          重置
        </div>
        <div
          class="btn-list on"
          :class="disable?'disable':''"
          @click="search"
        >
          查询
        </div>
      </div>
    </div>

    <div
      v-if="dataInfo"
      class="address-con"
    >
      <div class="list list1">
        <div class="item">
          <div
            class="title"
            style="display: flex; justify-content: space-between;"
          >
            企业数量
            <div style="padding-right: 24px; padding-top: 14px;">
              <div class="invent">
                <div
                  v-for="(item, index) in patenttable"
                  :key="index"
                  :class="patent == index ? 'xzoption' : 'option'"
                  @click="cutpatent(index)"
                >
                  {{ item }}
                </div>
              </div>
            </div>
          </div>
          <div class="item-con">
            <div class="item-title">
              ·企业数量对比
            </div>
            <div
              id="bar1"
              class="bar"
            />
          </div>
        </div>
        <div class="item">
          <div class="title">
            优质企业
          </div>
          <div class="item-con">
            <div class="item-title">
              ·优质企业对比
            </div>
            <div
              id="bar2"
              class="bar"
            />
          </div>
        </div>
      </div>
      <div class="list list2">
        <div class="item">
          <div class="title">
            注册资本
          </div>
          <div class="item-con">
            <div class="item-title">
              ·企业注册资本分布对比
            </div>
            <div
              id="bar3"
              class="bar"
            />
          </div>
        </div>
        <div class="item">
          <div class="title">
            成立年限
          </div>
          <div class="item-con">
            <div class="item-title">
              ·企业成立年限分布对比
            </div>
            <div
              id="bar4"
              class="bar"
            />
          </div>
        </div>
      </div>
      <div class="list list4">
        <div class="item">
          <div class="title">
            产业投资
          </div>
          <div class="item-con">
            <div class="item-title">
              ·对外投资企业环节分布对比
            </div>
            <div
              id="bar7"
              class="bar"
            />
          </div>
        </div>
        <div class="item">
          <div class="title" />
          <div class="item-con">
            <div class="item-title">
              ·投向企业环节分布对比
            </div>
            <div
              id="bar8"
              class="bar"
            />
          </div>
        </div>
      </div>
      <div class="list list3">
        <div class="item">
          <div class="title">
            创新能力
          </div>
          <div class="item-con">
            <div class="item-title">
              ·产业专利总量对比
            </div>
            <div
              id="bar5"
              class="bar"
            />
          </div>
        </div>
        <!-- <div class="item">
          <div class="title" />
          <div class="item-con">
            <div class="item-title">
              ·研发投入占比对比
            </div>
            <div
              id="bar6"
              class="bar"
            />
          </div>
        </div> -->
      </div>
    </div>
    <custom-loading :is-show="loading" />
  </div>
</template>

<script>
  import * as echarts from "echarts"
  import { verificationAPI } from "@/api/user";
  import { getAllAddress, orgChainList, linkPlaceList, regionalComparison } from './../../idicc-scan/apiUrl'
  import CustomLoading from "@/components/loading.vue";
  export default {
    name: 'AddressC',
    components: {
      CustomLoading
    },
    props:{
      userDel:{
        type:Object,
        default:()=>{}
      }
    },
    data() {
      return {
        chainId: [],
        arr: [],

        tabCheck: 1,
        addressList: [],
        optChain: [],
        regionProp:{ 
          checkStrictly: true
        },
        patenttable:['企业总量','新增企业'],
        patent:0,        
        dataInfo: null,
        loading: true,
        areaList: [],
        disable: true,
      }
    },
   /*  watch:{
      userDel(){
        this.chainId=this.userDel.chainId
        this.check()
        this.search()
      }
    }, */
    mounted () {
      this.getAllAddress();
      this.addressList=[+new Date()];
      this.getLinkPlaceList();
    },
    methods: {
      updata(){
        this.chainId=this.userDel.chainId
        this.check()
        this.search()
      },
      cutpatent(i){
         this.patent=i
         if(i ==1){
          this.bar('bar1', this.dataInfo.diffEnterpriseIncrement);
         }else{
          this.bar('bar1', this.dataInfo.diffEnterpriseCount);
         }
      },
      check(){
        if(this.chainId){
          this.disable = false;
        }
      },
      getAllAddress(){
        getAllAddress().then(res=>{
          let addressList=[];
          let areaList = [];
          // 省
          res.map(e=>{
            if(e.parentId==0){
              e.label = e.province;
              e.value = e.code;
              addressList.push(e)
            }else{
              e.label = e.province;
              e.value = e.code;
              areaList.push(e)
            }
          });
          // 市
          addressList.map(el=>{
            res.map(e=>{
              if(el.id == e.parentId){
                e.label = e.city;
                e.value = e.code;
                if(el.children){
                  el.children.push(e);
                }else{
                  el.children=[];
                  el.children.push(e);
                }
              }
            })
            return el;
          })
          // 区
          addressList.map(el=>{
            if(el.children && el.children.length>0){
              for(let i=0; i<el.children.length; i++){
                let el1 = el.children[i];
                areaList.map(e=>{
                  if(e.parentId == el1.id){
                    e.label = e.area;
                    e.value = e.code;
                    if(el1.children){
                      el1.children.push(e);
                    }else{
                      el1.children=[];
                      el1.children.push(e);
                    }
                  }
                });
              }
            }
            return el
          })


          this.areaList = addressList;

        });
      },
      // 产业链列表获取
      getLinkPlaceList(){
        linkPlaceList().then(res=>{
          orgChainList().then(res1=>{
            if(!res1 || res1.length==0){
              this.$message.error('暂无内容，请联系机构管理员配置功能权限')
              return 
            }
            res.map(e=>{
              res1.map(e1=>{
                if(e1.industryChainId == e.chainId){
                  
                  let data = {};
                  data.value = e.chainId;
                  data.label = e.chainName.replace('产业金脑·','');
                  data.children = e.nodes.map(e2=>{
                    return {
                      value: e2.nodeId,
                      label: e2.nodeName,
                      children: e2.nodes?.map(e3=>{
                        return {
                          value: e3.nodeId,
                          label: e3.nodeName,
                        }
                      })
                    }
                  })

                  this.optChain.push(data);
                }

              });
            });
          })
        })
      },
      tabEvent(val) {
        this.tabEvent = val;
      },
      addEvent(){
        let list = new Set(this.addressList);
        list.add(+new Date());
        this.$set(this, 'addressList', [...list]);
      },
      removeEvent(item, i){
        let list = new Set(this.addressList);
        list.delete(item);
        this.$set(this, 'addressList', [...list]);
        this.arr.splice(i+1, 1);
      },
      reset(){
        //this.chainId= '';
        this.disable = true;
        this.arr= [];
        this.addressList= [+new Date()];
        this.chainId=this.userDel.chainId
        this.check()
        this.search()
      },
      search(){
        if(this.disable){
          return false;
        }
        this.patent=0,       
        this.arr =  this.arr.filter(item=>{
          return item !== undefined 
        })
        let comparisonRegionCodes=[];
        let that = this;
        this.arr.map((e,i)=>{
          let list1 = null;
          if(i==0){
            list1 = that.$refs['arr_0'].$children[0].getInput().value.split('/')
          }else{
            list1 = that.$refs['arr_'+i][0].$children[0].getInput().value.split('/')
          }
          let data = {
            regionCode: e[e.length-1],
            regionName: list1[list1.length-1],
            divisionLevel: list1.length,
          }
          comparisonRegionCodes.push(data);
        });
        this.dataInfo=null;
       /*  this.loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        }); */
        this.loading=true
        setTimeout(() => {
          this.loading=false
        }, 10000);
        this.$nextTick(()=>{
          this.regionalComparison(comparisonRegionCodes);
        })
      },
     async regionalComparison(comparisonRegionCodes){
      let data ={}
      let type = typeof  this.chainId
      if(type=='string'){
         data = {
          industryId: this.chainId,
          comparisonRegionCodes
        };
      }else{
         data = {
          industryId: this.chainId[0],
          chainNodeId: this.chainId[1],
          comparisonRegionCodes
        };
      }
 
      /*   const is = await verificationAPI({
        industryChainId:this.chainId[0],
      }) 
      if(is){ */
        regionalComparison(data).then(res=>{
          this.dataInfo=res;
          this.$nextTick(()=>{
            this.loading=false
            this.bar('bar1', res.diffEnterpriseCount);
            this.bar('bar2', res.diffQualityEnterprise);
            this.bar('bar3', res.diffRegisterCap);
            this.bar('bar4', res.diffRegisterYear);
            this.bar('bar5', res.diffInnovation);
            this.bar('bar7', res.diffOverseasInvestmentIndustryNode);
            this.bar('bar8', res.diffInvestInvestmentIndustryNode);
            this.line(res.diffRDInvestment);
          })
        }).catch((err)=>{
          console.error(err);
          this.loading=false
        });
        setTimeout(() => {
          this.loading=false
        }, 5000);
    /*   }else{
        this.$message.error('暂无该产业链权限')
        setTimeout(()=>{
          location.reload()
        },500)
      } */
        

      },
      bar(id, arr){
        let source=[];
        let series = [];
        let colorList = ['#165DFF','#14C9C9', '#F7BA1E', '#469AFA', '#9FDB1D']
        arr.map((e,j)=>{
          if(!source[0]){
            source[0] = ['product'];
          }
          source[0].push(e.regionName);
          let list = e.numberCompareResultDTOS || e.enterpriseCompareResultDTOS || e.enterpriseCompareResultDTOS;
          list.map((e1,i)=>{
            if(!source[i+1]){
              source[i+1] = [];
            }
            if(j==0){
              source[i+1].push(e1.compareX.replace("数量", ""));
            }
            source[i+1].push(e1.compareY);
          });
          let barWidth=16;
          if(arr.length>4){
            barWidth = 8
          }else if(arr.length>3){
            barWidth = 10
          }else if(arr.length>2){
            barWidth = 12
          }
          series.push({ 
            type: 'bar',
            barWidth,
            itemStyle: {
              normal: {
                barBorderRadius: 4,
                type: 'linear',
                color: colorList[j],
              }
            },
            barMinHeight: 2,
          })
        });
        // colorList.map(e=>{
          
        // })
        let chartDom = document.getElementById(id);
        let myChart = echarts.init(chartDom);
        let option;

        option = {
          legend: {
            right: 0,
            itemWidth: 15,
            itemHeight: 7, //修改icon图形大小
            //icon: "circle", //图例前面的图标形状
          },
          tooltip: {},
          dataset: {
            source: source
          },
          xAxis: { 
            type: 'category',
            axisLabel: {
              interval: 0,
              rotate: -20, //倾斜的程度
            }, 
          },
          yAxis: {

            splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed",
                },
              },
          },
          series
        };

        option && myChart.setOption(option);
      },
      line(arr){
        if(!arr || arr.length==0){
          return
        }
        let xData=[];
        let source=[];
        let nameList=[];
        let series = [];
        arr.map((e,j)=>{
          source[j]=[];
          nameList.push(e.regionName);
          let list = e.enterpriseCompareResultDTOS;
          list.map((e1)=>{
            if(j==0){
              xData.push(e1.compareX);
            }
            source[j].push(e1.compareY);
          });
        });
        
        let colorList = ['#165DFF','#14C9C9', '#F7BA1E', '#469AFA', '#9FDB1D']
        source.map((el,i)=>{
          series.push({
            name: nameList[i],
            type: "line",
            yAxisIndex: 1,
            symbol: "none", //取消折点圆圈
            tooltip: {
              valueFormatter: function (value) {
                return value + "%";
              },
            },
            itemStyle: {
              normal: {
                color: colorList[i],
              },
            },
            data: el,
          })
        })

        // colorList.map(e=>{
        //   series.push({ type: 'bar',
        //     barWidth: 16,
        //     itemStyle: {
        //       normal: {
        //         barBorderRadius: 4,
        //         type: 'linear',
        //         color: e,
        //       }
        //     },
        //     barMinHeight: 2,
        //   });
        // });

        let chartDom = document.getElementById("bar6");
        let myChart = echarts.init(chartDom);
        let option;

        option = {
          grid: {
            left: "5%",
            right: "5%",
            bottom: "10%",
            top: "18%",
            containLabel: true,
          },
          tooltip: {
            trigger: "axis",
          },
          axisLabel: {
            show: true,
            fontSize: 12,
            color: "#86909C",
          },
          legend: {
            data: nameList,
            right: 0,
          },
          xAxis: [
            {
              type: "category",
              data: xData,
              axisTick: {
                show: false,
              },
              axisPointer: {
                type: "shadow",
              },
            },
          ],
          yAxis: [
          {
              type: "value",
              name: "%",
              interval: 5,
              nameTextStyle: {
                padding: [0, 0, 0, -15],
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed",
                },
              },
              axisLabel: {
                formatter: "{value} ",
              },
            },
            {
              type: "value",
              name: "",
              interval: 50,
              nameTextStyle: {
                padding: [0, 28, 0, 0],
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed",
                },
              },
              axisLabel: {
                formatter: "{value}",
              },
            },
          ],
          series: series,
          position: 'left'
        };

        option && myChart.setOption(option);
      }
    },
  }
</script>

<style lang="scss" scoped>
 .invent {
        display: flex;
        .xzoption {
          width: 80px;
          height: 25px;
          color: #3370FF;
          background: #ffffff;
          border-radius: 2px 0px 0px 2px;
          opacity: 1;
          border: 1px solid #3370FF;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          font-size: 14px;
        }
        .option {
          width: 80px;
          height: 25px;
          background: #ffffff;
          border-radius: 2px 0px 0px 2px;
          opacity: 1;
          border: 1px solid #d9d9d9;
          display: flex;
          justify-content: center;
          font-size: 14px;
          align-items: center;
          cursor: pointer;
        }
      }
  .address{
    padding-bottom: 10px;
    .search{
      background: #FFFFFF;
      box-shadow: 0px 4px 12px 0px #EEF1F8;
      border-radius: 10px;
      padding: 22px 24px;
      &-list{
        display: flex;
        align-items: center;
        padding-bottom: 20px;
        &.on{
          align-items: baseline;
        }
        .label{
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0,0,0,0.85);
        }
        .add-list{
          .item{
            display: flex;
            align-items: center;
            padding-bottom: 12px;
            i{
              color: #3370FF;
              font-weight: bold;
              margin-left: 20px;
              font-size: 15px;
              cursor: pointer;
            }
          }
        }
        .ul{
          display: flex;
          align-items: center;
          .li{
            font-size: 14px;
            font-family: Abel-Regular, Abel;
            font-weight: 400;
            color: rgba(0,0,0,0.65);
            line-height: 22px;
            margin-left: 32px;
            &.on{
              background: #3370FF;
              border-radius: 2px;
              color: #FFFFFF;
              padding: 0 8px;
            }
            &.zdy{
              color: #3370FF;
            }
          }
        }
      }
      .btn{
        display: flex;
        justify-content: center;
        &-list{
          width: 65px;
          height: 32px;
          background: #FFFFFF;
          border-radius: 5px;
          opacity: 1;
          border: 1px solid #D9D9D9;
          text-align: center;
          line-height: 32px;
          margin-right: 10px;
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0,0,0,0.65);
          cursor: pointer;
          &.on{
            background: #3370FF;
            border: 0px solid #D9D9D9;
            color: #FFFFFF;
          }
          &.disable{
            background: #D9D9D9;
            border: 0px solid #D9D9D9;
            color: #FFFFFF;
            cursor: no-drop;
          }
        }
      }
    }
    &-con{
      margin: 16px 0;
      .list{
        display: flex;
        justify-content: space-between;
        margin-bottom: 24px;
        .item{
          width: 49%;
          height: 410px;
          background: #FFFFFF;
          box-shadow: 0px 4px 12px 0px #EEF1F8;
          border-radius: 10px;
          .title{
            height: 55px;
            line-height: 55px;
            padding-left: 24px;
            border-bottom: 1px solid #E9E9E9;
            font-size: 16px;
            font-family: Source Han Sans CN-Medium, Source Han Sans CN;
            font-weight: 500;
            color: rgba(0,0,0,0.85);
          }
          &-con{
            padding: 12px 24px;
            position: relative;
            height: calc(100% - 56px);
            box-sizing: border-box;
            .item-title{
              font-size: 14px;
              font-family: Source Han Sans CN-Regular, Source Han Sans CN;
              font-weight: 400;
              color: rgba(29,33,41,0.85);
              line-height: 28px;
              position: absolute;
              left: 20px;
              top: 20px;
            }
            .bar{
              width: 100%;
              height: 354px;
            }
          }
        }
      }
    }
  }
</style>