<template>
  <div class="info">
    <div class="close">
      <i
        class="el-icon-circle-close"
        @click="close"
      />
    </div>
    <div class="title">
      <div
        v-if="state === 'edit'"
        class="label"
      >
        报告编辑
      </div>
      <div
        v-else
        class="label"
      >
        报告上传
      </div>
    </div>
    <Upload
      :info-data="infoData"
      :business-list="businessList"
      :product-list="productList"
      :state="state"
      @getEntrustList="getEntrustList"
    />
  </div>
</template>

<script>
import { formatDate } from '@/utils/utils';
import Upload from './upload.vue'
export default {
  name: 'InfoDrawer',
  filters: {
    time: function (value) {
      return formatDate('yyyy-MM-dd HH:mm:ss', new Date(+value));
    },
    timeStamp: function (val) {
      let date = val / (60 * 60 * 1000 * 24)
      let d = parseInt(val / (60 * 60 * 1000 * 24))
      let h = parseInt((date % 1) * 24)
      let s = parseInt((((date % 1) * 24) % 1) * 60);
      let str = d + '天' + Math.abs(h) + '时' + Math.abs(s) + '分'
      if (d < 0 || h < 0 || s < 0) {
        str = '-' + Math.abs(d) + '天' + Math.abs(h) + '时' + Math.abs(s) + '分'
      }
      return str;
    }
  },
  components: {
    Upload,
  },
  props: {
    id: {
      type: String,
      default: null
    },
    state: {
      type: String,
      default: null
    },
    infoData: {
      type: Object,
      default: null
    },
    businessList: {
      type: Array,
      default: null
    }, productList: {
      type: Array,
      default: null
    }
  },
  data () {
    return {
    }
  },
  created () {
    // this.getEntrustDetail();
  },
  methods: {

    followUp () {
      this.$emit('followUp', this.infoData)
    },
    getEntrustList () {
      this.$emit('getEntrustList');
      this.close()
    },
    close () {
      this.$emit('close');
    },
  },
}
</script>

<style lang="scss" scoped>
.info {
  padding: 0 12px;
  position: relative;

  .close {
    width: 30px;
    height: 30px;
    position: absolute;
    right: 10px;
    top: 7px;
    z-index: 999999;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    .el-icon-circle-close {
      font-size: 24px;
    }
  }

  .title {
    position: relative;
    padding-left: 35px;
    border-bottom: 1px solid #cacaca;
    line-height: 45px;
    font-size: 14px;
    color: #101010;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &::before {
      content: '';
      width: 5px;
      height: 18px;
      background: rgba(20, 66, 119, 0.98);
      position: absolute;
      top: 15px;
      left: 15px;
    }

    .el-button--mini {}
  }

  &-con {
    padding-top: 15px;

    &-list {
      display: flex;
      font-size: 12px;
      line-height: 42px;

      .label {
        width: 100px;
        padding-left: 60px;
        box-sizing: content-box;
      }

      .txt {
        color: #212121;
      }
    }
  }
}
</style>