<!--
 * @Author: jhy
 * @Date: 2023-06-05 09:36:46
 * @LastEditTime: 2023-06-05 18:04:47
 * @LastEditors: jhy
 * @Description: 
 * @FilePath: /QT-SASS-PC/src/views/system-manage/idicc-report/upload.vue
-->
<template>
  <div class="report-upload">
    <div class="report-upload-con">
      <div class="list">
        <div class="label">
          报告封面
        </div>
        <div class="list-con">
          <el-upload
            ref="upload"
            :action="chartApi.zstjUpload" 
            class="avatar-uploader"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
            accept=".png,.PNG,.JPG,.jpg,.jpeg,.JPEG"
            :on-error="handleAvatarError"
            :on-success="handleAvatarSuccess"
            :headers="{
              token
            }"
          >
            <!-- :action="chartApi.zstjUpload  :on-change="handleImgChange"" 
           
          -->

            <img
              v-if="imageUrl"
              :src="imageUrl"
              class="avatar"
            >
            <i
              v-else
              class="el-icon-plus avatar-uploader-icon"
            />
          </el-upload>
        </div>
      </div>


      <div class="list">
        <div class="label">
          报告标题
        </div>
        <div class="list-con">
          <el-input
            v-model="infoDataForm.title"
            size="mini"
            placeholder="请输入标题"
          />
        </div>
      </div>
      <div class="list">
        <div class="label">
          报告类型
        </div>
        <div class="list-con appendToBodyFalse">
          <el-select
            ref="select"
            v-model="infoDataForm.type"
            :popper-append-to-body="false"
            size="mini"
            placeholder="请选择"
          >
            <el-option
              v-for="item in productList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <div class="list">
        <div class="label">
          产业链
        </div>
        <div class="list-con appendToBodyFalse">
          <el-select
            ref="select"
            v-model="infoDataForm.industryId"
            :popper-append-to-body="false"
            size="mini"
            placeholder="请选择"
          >
            <el-option
              v-for="item in businessList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <div class="list fileLists">
        <div class="label">
          文件
        </div>
        <div class="list-con">
          <el-upload 
            class="upload-demo"
            action="#"
            :on-change="handleChange"
            :file-list="fileList"
            :multiple="multiple"
            :limit="limit"
            :on-remove="onRemove"
            :on-exceed="handleExceed"
            accept=".doc,.docx,.pdf"
            :headers="{
              token
            }"
            :auto-upload="false"
          >
            <el-button
              size="small"
              type="primary"
            >
              点击上传
            </el-button>
          </el-upload>
        </div>
      </div>
      <div class="btn">
        <el-button
          size="small"
          type="primary"
          @click="submit"
        >
          提交
        </el-button>
        <!-- <div class="btn-list" @click="submit" /> -->
      </div>
    </div>
  </div>
</template>

<script>
import { industryReportUpload, industryChainGetAll } from '../../../idicc-scan/apiUrl';
import { reportEdit } from '../../apiUrl'
import { getToken } from "@/utils/auth"; // get token from cookie
import { chartApi,} from '@/admin/apiUrl'
export default {
  name: 'ReportUpload',
  props: {
    infoData: {
      type: Object,
      default: null
    },
    businessList: {
      type: Array,
      default: null
    }, productList: {
      type: Array,
      default: null
    },
    state: {
      type: String,
      default: null
    },

  },
  data () {
    return {
      options: [],
      imageUrl: '',
      infoDataForm: {},
      fileList: [],
      multiple: false,
      limit: 1,
      token:'',
      chartApi,
    }
  },
  created () {
    this.token =  getToken()
    if (this.state === 'edit') {
      this.infoDataForm = this.infoData
      this.infoDataForm.type = Number(this.infoData.type)
      this.imageUrl = this.infoData.imagePath
      this.fileUrl = this.infoData.reportUrl
      this.fileList = [{
        name: this.infoData.reportUrl.split('/').reverse()[0],
        url: this.infoData.reportUrl,
        isEditState: true
      }]
    }

  },
  methods: {
    orgChainList () {
      industryChainGetAll().then(res => {
        res.map(e => {
          this.options.push({
            label: e.chainName.replace('产业金脑·', ''),
            value: e.id
          });
        });
      });
    },
    submit () {
      if (!this.imageUrl) {
        this.$message.error("请上传报告封面");
        return;
      }
      if (!this.infoDataForm.title) {
        this.$message.error("请填写报告标题");
        return;
      }
      if (!this.infoDataForm.type) {
        this.$message.error("请选择报告类型");
        return;
      }
      if (!this.infoDataForm.industryId) {
        this.$message.error("请选择产业链");
        return;
      }
      if (!this.fileUrl) {
        this.$message.error("请选择需要上传的文件");
        return;
      }

      var formData = new FormData();
      formData.append('title', this.infoDataForm.title);
      formData.append('industryId', this.infoDataForm.industryId);//产业id
      this.imageUrlFile && formData.append('fileImage', this.imageUrlFile);
      formData.append('type', this.infoDataForm.type);//报告类型：1产业洞察
      formData.append('dateTimestamp', Date.now());//报告类型：1产业洞察
      if (this.state === 'edit') {
        // !Array.isArray(this.fileList)
        !this.fileList?.[0]?.isEditState && formData.append('filePdf', this.fileList[0].raw);
        formData.append('id', this.infoDataForm.id);//报告类型：1产业洞察
        reportEdit(formData).then(res => {
          if (res.code === 'SUCCESS') {
            this.getEntrustList()
            this.$message.success('编辑成功！');
          }
        })
      } else {
        this.fileList && formData.append('filePdf', this.fileList[0].raw);
        industryReportUpload(formData).then((res) => {
          if (res) {
            this.getEntrustList()
            this.$message.success('上传成功！');
          }
        }) 
      }
    },
    getEntrustList () {
      this.$emit('getEntrustList');
    },
    onRemove () {
      this.fileUrl = '';
      this.fileList = []
    },
    handleImgChange(file, fileList) {
   this.imageUrl = URL.createObjectURL(file.raw);
      this.imageUrlFile = file.raw;
      //  console.log(file, fileList)
    },
    handleChange (file, fileList) {
 
        this.fileUrl = fileList[0].raw.uid;
      this.fileList = fileList;
   
      
    },
    handleAvatarError(res,file){
      // console.log(res,file)
      // if(this.isLt2M){
      // this.imageUrl = URL.createObjectURL(file.raw);
      // this.imageUrlFile = file.raw;
      // }
    },
    handleAvatarSuccess (res, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
      this.imageUrlFile = file.raw;
    },
    beforeAvatarUpload (file) {
      // console.log(file,)
      // const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 5;

      // if (!isJPG) {
      //   this.$message.error('上传头像图片只能是 JPG 格式!');
      // }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 5MB!');
      }
      // this.isLt2M=isLt2M
      return isLt2M;
      // return isJPG && isLt2M;
    },
    handleExceed (files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
  },
}
</script>

<style lang="scss" scoped>
.report-upload {
  // padding: 30px;
  background: #f7f7f7;

  .fileLists {
    height: 130px;
  }

  &-con {
    background: #fff;
    min-height: 80vh;
    padding-top: 30px;
    box-sizing: border-box;

    .list {
      display: flex;
      align-items: center;
      padding: 15px 0;

      .label {
        width: 120px;
        text-align: right;
        box-sizing: border-box;
        padding-right: 20px;
      }
    }

    .btn {
      display: flex;
      justify-content: center;


    }
  }
}
</style>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #3370FF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px !important;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
