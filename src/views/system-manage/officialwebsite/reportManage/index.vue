<template>
  <div class="entrust">
    <div class="entrust-con">
      <table-layout :tab-name-list="['报告管理']">
        <el-form
          slot="elForm"
          :inline="true"
          :model="form"
          class="entrust-form"
          label-width="80px"
          size="mini"
        >
          <el-form-item label="报告类型">
            <el-select
              v-model="form.type"
              placeholder="报告类型"
            >
              <el-option
                v-for="(item, k) in productList "
                :key="k"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="产业链">
            <el-select
              v-model="form.industryIds"
              placeholder="产业链"
            >
              <el-option
                v-for="(item, k) in businessList"
                :key="k"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button @click="resetForm('form')">
              重置
            </el-button>

            <el-button
              type="primary"
              class="btn"
              @click="onSubmit"
            >
              查询
            </el-button>
          </el-form-item>

          <el-form-item class="uploadBtn">
            <el-button
              type="primary"
              @click="upload('form')"
            >
              上传
            </el-button>
          </el-form-item>
        </el-form>
        <div
          slot="selTable"
          class="entrust-table"
        >
          <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
            size="mini"
          >
            <el-table-column
              prop="imagePath"
              label="封面"
              align="center"
            >
              <template slot-scope="scope">
                <el-image
                  style="width: 85px; height: 120px"
                  :src="scope.row.imagePath ? `${scope.row.imagePath}` : reportBook"
                  fit="fill"
                />
              </template>
            </el-table-column>

            <el-table-column
              prop="title"
              label="标题"
              align="center"
            />
            <el-table-column
              prop="type"
              label="类型"
              align="center"
            >
              <template slot-scope="scope">
                {{ ['', '产业洞察', '专题报告', "智能研报"][scope.row.type] }}
              </template>
            </el-table-column>

            <el-table-column
              prop="industryName"
              label="产业链"
              align="center"
            />
            <el-table-column
              prop="gmtModify"
              label="更新日期"
              align="center"
            />

            <el-table-column
              align="center"
              width="180"
              label="操作"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="edit(scope.row)"
                >
                  编辑
                </el-button>
                <!-- <el-divider direction="vertical" /> -->
                <!-- <el-button type="text" @click="followUp(scope.row)">
                  跟进
                </el-button> -->
                <el-divider direction="vertical" />
                <el-button
                  v-if="scope.row.followUpStatus == 2"
                  type="text"
                  disabled
                >
                  删除
                </el-button>
                <el-popconfirm
                  v-else
                  title="确定删除？"
                  @confirm="endTask(scope.row)"
                >
                  <el-button
                    slot="reference"
                    type="text"
                  >
                    删除
                  </el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </table-layout>
      <div class="ye">
        <el-pagination
          :current-page.sync="page.pageNum"
          :page-sizes="[5, 10, 20, 50]"
          :page-size.sync="page.pageSize"
          :total="+total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="getEntrustList"
          @current-change="getEntrustList"
        />
      </div>

      <!-- drawer start -->
      <el-drawer
        :visible.sync="drawer"
        :with-header="false"
        :wrapper-closable="false"
      >
        <info
          v-if="drawerStatus == 'followIn'"
          :id="infoData.id"
          :info-data="infoData"
          :business-list="businessList"
          :product-list="productList"
          :state="state"
          @close="close"
          @getEntrustList="getEntrustList"
        />
      </el-drawer>
      <!-- drawer end -->
    </div>
  </div>
</template>

<script>
import TableLayout from "@/common/components/table-layout";
import { reportDelete, reportList, } from "../apiUrl";
import { industryChainGetAll } from '../../idicc-scan/apiUrl'
// import { filterData } from "@/utils/utils";
import info from "./component/info.vue";
// import followUp from "./component/followUp.vue";
export default {
  name: "OfficialComponents",
  components: {
    info,
    TableLayout,
  },
  props: {
  },
  data () {
    return {
      reportBook:  'https://static.idicc.cn/cdn/pangu/assets/img/report_book.png',
      productList: [
        {
          label: "产业洞察",
          value: 1,
        },
        {
          label: "专题报告",
          value: 2,
        },
        {
          label: "智能研报",
          value: 3,
        },
      ],
      businessList: [],
      drawer: false,
      drawerStatus: "",
      loading: false,
      form: {
        type: "", //体验产品
        industryIds: ''
      },
      page: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      tableData: [],
      titles: {},
      state: ''
    };
  },
  created () {
    this.titles = {
      report: {
        key: 'report',
        title: '获取报告',
      },
      experience: {
        key: 'experience',
        title: '预约体验',
      },
      business: {
        key: 'business',
        title: '业务咨询',
      }
    }
    this.getEntrustList();
    this.getIndustry()
  },
  methods: {
    getIndustry () {
      //获取下拉框数据
      industryChainGetAll().then(res => {
        let data = res.map(e => {
          return {
            label: e.formerName,
            value: e.id
          }
        });
        this.businessList = data
      });
    },
    // 获取列表
    async getEntrustList () {
      let { type,
        industryIds } = this.form;
      let data = type === '' ? { ...this.page, industryIds: industryIds === '' ? [] : [industryIds] } : { ...this.page, type, industryIds: industryIds === '' ? [] : [industryIds] };
      try {

        // filterData(data);
        this.loading = true;
        let res = await reportList(data);
        if (res.code === 'SUCCESS') {
          this.tableData = res.result.records;
          this.total = res.result.totalNum;
        }

      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false;
      }
    },
    // 详情
    edit (e) {
      this.drawer = true;
      this.infoData = e;
      this.drawerStatus = "followIn";
      this.state = 'edit'
    },
    // 上传
    upload () {
      this.drawer = true;
      this.state = 'upload';
      this.drawerStatus = "followIn";

      this.infoData = {};
    },
    // 删除
    //  reportFinish,businessFinish,productFinish
    endTask (e) {
      reportDelete({ id: e.id }).then(() => {
        this.$message.success("删除成功！");
        this.getEntrustList();
      });
    },

    // 查询
    onSubmit () {
      this.getEntrustList();
    },
    // 重置
    resetForm () {

      this.form = {
        industryIds: '',
        type: ''
      };
      (this.page = {
        pageNum: 1,
        pageSize: 10,
      }),
        this.getEntrustList();
    },
    close () {
      this.drawer = false;
      this.drawerStatus = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.entrust {
  // padding: 8px 20px;
  // background: #f7f8fa;
  min-height: calc(100vh - 84px);

  &-form {
    padding: 20px;
    background: #fff;
    box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
    border-radius: 10px;

    ::v-deep {
      .el-form-item {
        width: auto !important;
      }

      .el-date-editor--daterange.el-input__inner {
        width: 250px !important;
      }

      .el-select__tags {
        max-width: 165px !important;
      }
    }
  }

  &-table {
    margin: 20px 0;
  }

  .ye {
    position: relative;
    height: 50px;
    background: #fff;

    .el-pagination {
      padding-right: 20px;
      bottom: 15px !important;
    }
  }

  .uploadBtn {
    position: absolute;
    right: 20px;
  }
}
</style>