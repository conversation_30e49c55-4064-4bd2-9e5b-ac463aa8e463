<template>
  <div class="info">
    <div class="close">
      <i
        class="el-icon-circle-close"
        @click="close"
      />
    </div>
    <div class="title">
      <div class="label">
        {{ titles[state].title }}
      </div>
      <el-button
        type="primary"
        size="mini"
        style="margin-right:60px;"
        @click="followUp"
      >
        跟进
      </el-button>
    </div>
    <div class="info-con">
      <div class="info-con-list">
        <div class="label">
          申请日期：
        </div>
        <div class="txt">
          {{ infoData.gmtCreate }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          {{ titles[state].name }}
        </div>
        <div
          v-if="state === 'report'"
          class="txt"
        >
          {{ infoData.title }}
        </div>
        <div
          v-else
          class="txt"
        >
          {{ infoData.keywordName }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          姓名：
        </div>
        <div class="txt">
          {{ infoData.name }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          手机号：
        </div>
        <div class="txt">
          {{ infoData.mobile }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          单位类型：
        </div>
        <div class="txt">
          {{ infoData.unitTypeName }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          单位名称：
        </div>
        <div class="txt">
          {{ infoData.unitName }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          职务：
        </div>
        <div class="txt">
          {{ infoData.position }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          邮箱：
        </div>
        <div class="txt">
          {{ infoData.email }}
        </div>
      </div>
      <div class="info-con-list">
        <div class="label">
          备注：
        </div>
        <div class="txt">
          {{ infoData.remark }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getExperienceDetail, getBusinessDetail, getReportDetail } from '../../apiUrl'
import { formatDate } from '@/utils/utils'
export default {
  name: 'InfoDrawer',
  filters: {
    time: function (value) {
      return formatDate('yyyy-MM-dd HH:mm:ss', new Date(+value));
    },
    timeStamp: function (val) {
      let date = val / (60 * 60 * 1000 * 24)
      let d = parseInt(val / (60 * 60 * 1000 * 24))
      let h = parseInt((date % 1) * 24)
      let s = parseInt((((date % 1) * 24) % 1) * 60);
      let str = d + '天' + Math.abs(h) + '时' + Math.abs(s) + '分'
      if (d < 0 || h < 0 || s < 0) {
        str = '-' + Math.abs(d) + '天' + Math.abs(h) + '时' + Math.abs(s) + '分'
      }
      return str;
    }
  },
  props: {
    id: {
      type: String,
      default: null
    },
    state: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      infoData: {},
      titles: {
        report: {
          key: 'report',
          title: '获取报告详情',
          name: '获取报告'
        },
        experience: {
          key: 'experience',
          title: '预约体验详情',
          name: '预约体验'
        },
        business: {
          key: 'business',
          title: '业务咨询详情',
          name: '业务咨询'
        }
      }
    }
  },
  created () {
    this.getEntrustDetail();
  },
  methods: {
    async getEntrustDetail () {
      let res = null;
      let data = {
        id: this.id
      }
      switch (this.state) {
        case 'business':
          res = await getBusinessDetail(data);
          break;
        case 'experience':
          res = await getExperienceDetail(data);
          break;
        case 'report':
          res = await getReportDetail(data);
          break;
      }
      if (res.code === 'SUCCESS') {
        this.infoData = res.result;
      }
    },
    followUp () {
      this.$emit('followUp', this.infoData)
    },
    close () {
      this.$emit('close');
    },
  },
}
</script>

<style lang="scss" scoped>
.info {
  padding: 0 12px;
  position: relative;

  .close {
    width: 30px;
    height: 30px;
    position: absolute;
    right: 10px;
    top: 7px;
    z-index: 999999;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    .el-icon-circle-close {
      font-size: 24px;
    }
  }

  .title {
    position: relative;
    padding-left: 35px;
    border-bottom: 1px solid #cacaca;
    line-height: 45px;
    font-size: 14px;
    color: #101010;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &::before {
      content: '';
      width: 5px;
      height: 18px;
      background: rgba(20, 66, 119, 0.98);
      position: absolute;
      top: 15px;
      left: 15px;
    }

    .el-button--mini {}
  }

  &-con {
    padding-top: 15px;

    &-list {
      display: flex;
      font-size: 12px;
      line-height: 42px;

      .label {
        width: 100px;
        padding-left: 60px;
        box-sizing: content-box;
      }

      .txt {
        color: #212121;
      }
    }
  }
}
</style>