<template>
  <div class="addFllowReload">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      label-width="105px"
      label-position="left"
      size="mini"
      class="demo-ruleForm"
    >
      <el-form-item
        label="跟进日期："
        prop="followData"
        class="appendToBodyFalse"
      >
        <el-date-picker
        :append-to-body="false"
          v-model="ruleForm.followData"
          type="date"
          placeholder="选择日期"
          :picker-options="pickerOptions"
        />
      </el-form-item>
      <el-form-item
        label="跟进人："
        prop="followPerson"
      >
        <el-input v-model="ruleForm.followPerson" />
      </el-form-item>
      <!-- <el-form-item label="企业对接人：" prop="enterpriseContactPerson">
        <el-input v-model="ruleForm.enterpriseContactPerson" />
      </el-form-item>
      <el-form-item label="联系方式：" prop="enterpriseContactInformation">
        <el-input v-model="ruleForm.enterpriseContactInformation" />
      </el-form-item> -->
      <el-form-item
        label="跟进概况："
        prop="remark"
      >
        <el-input
          v-model="ruleForm.remark"
          type="textarea"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="submitForm('ruleForm')"
        >
          保存
        </el-button>
        <el-button @click="resetForm('ruleForm')">
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { recordFlowUp, businessFlowUp, experienceFlowUp } from '../../apiUrl'
export default {
  name: 'AddFllowReload',
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: null
    },
    state: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      ruleForm: {
        id: '',
        followData: '',
        followPerson: '',
        // enterpriseContactPerson: '',
        // enterpriseContactInformation: '',
        remark: '',
      },
      rules: {
        followData: [
          { type: 'date', required: true, message: '请选择日期', trigger: 'change' }
        ],
        followPerson: [
          { required: true, message: '请输入跟进人名称', trigger: 'blur' },
        ],
        // enterpriseContactPerson: [
        //   { required: true, message: '请输入企业对接人', trigger: 'blur' },
        // ],
        // enterpriseContactInformation: [
        //   { required: true, message: '请输入联系方式', trigger: 'blur' },
        // ],
        remark: [
          { required: true, message: '请输入跟进概况', trigger: 'blur' },
        ]
      },
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() > Date.now();
        },
      }
    }
  },
  created () {
    let entrustTimeStamp = this.info.entrustTimeStamp;
    let oneDay = 24 * 60 * 60 * 1000;
    this.pickerOptions = {
      disabledDate (time) {
        return time.getTime() > Date.now() || time.getTime() < entrustTimeStamp - oneDay;
      },
    };
  },
  methods: {
    // 添加记录
    async addRecord () {
      let data = this.ruleForm;
      data.id = this.id;
      data.followData = +new Date(data.followData);
      let res;
      switch (this.state) {
        case 'business':
          res = await businessFlowUp(data);
          break;
        case 'experience':
          res = await experienceFlowUp(data);
          break;
        case 'report':
          res = await recordFlowUp(data);
          break;
      }
      if (res.code === 'SUCCESS') {
        this.$message.success('添加跟进记录成功！')
        this.$emit('init')
      }
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.addRecord()
        } else {
          return false;
        }
      });
    },
    resetForm (formName) {
      this.$refs[formName].resetFields();
    }
  },
}
</script>

<style lang="scss" scoped>
.addFllowReload {
  border: 1px solid #bbb;
  padding: 15px 25px;

  ::v-deep {
    .el-form-item__label {
      font-size: 12px;
    }

    .el-input--mini,
    .el-date-editor.el-input {
      width: 180px;
    }
  }
}
</style>