<template>
  <div class="entrust">
    <div class="entrust-con">
      <table-layout :tab-name-list="[titles[state].title]">
        <el-form
          slot="elForm"
          :inline="true"
          :model="form"
          class="entrust-form"
          label-width="80px"
          size="mini"
        >
          <el-form-item
            v-if="state !== 'report'"
            :label="titles[state].title"
          >
            <el-select
              v-model="form.keywordId"
              :placeholder="titles[state].title"
            >
              <el-option
                v-for="(item, k) in productList"
                :key="k"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            v-else
            :label="titles[state].title"
          >
            <el-select
              v-model="value"
              filterable
              remote
              reserve-keyword
              placeholder="请输入关键词"
              :remote-method="remoteMethod"
              :loading="searchLoading"
            >
              <el-option
                v-for="item in options"
                :key="item.title"
                :label="item.title"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              @click="onSubmit"
            >
              查询
            </el-button>
            <el-button @click="resetForm('form')">
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <div
          slot="selTable"
          class="entrust-table"
        >
          <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
            size="mini"
          >
            <el-table-column
              v-if="state === 'experience'"
              prop="keywordName"
              :label="titles[state].title"
              align="center"
            />
            <el-table-column
              v-else-if="state === 'report'"
              prop="title"
              :label="titles[state].title"
              align="center"
            />
            <el-table-column
              v-else
              prop="keywordName"
              :label="titles[state].title"
              align="center"
            />
            <el-table-column
              prop="name"
              label="姓名"
              align="center"
            />
            <el-table-column
              prop="mobile"
              label="手机号"
              align="center"
            />
            <el-table-column
              prop="unitTypeName"
              label="单位类型"
              align="center"
            />
            <el-table-column
              prop="unitName"
              label="单位名称"
              align="center"
            />
            <el-table-column
              prop="gmtCreate"
              label="申请日期"
              align="center"
            />
            <el-table-column
              align="center"
              label="跟进状态"
            >
              <template slot-scope="{ row }">
                <span v-if="row.dealState == 0">待处理</span>
                <span v-if="row.dealState == 1">处理中</span>
                <span v-if="row.dealState == 2">已完成</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              width="180"
              label="操作"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="detail(scope.row)"
                >
                  详情
                </el-button>
                <el-divider direction="vertical" />
                <el-button
                  type="text"
                  @click="followUp(scope.row)"
                >
                  跟进
                </el-button>
                <el-divider direction="vertical" />
                <el-button
            
                  v-if="scope.row.dealState == 2"
                  type="text"
                  disabled
                >
                  结束
                </el-button>
                <el-popconfirm
                  v-else
                  title="确定结束？"
                  @confirm="endTask(scope.row)"
                >
                  <el-button
                    slot="reference"
                    type="text"
                  >
                    结束
                  </el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </table-layout>
      <div class="ye">
        <el-pagination
          :current-page.sync="page.pageNum"
          :page-sizes="[5, 10, 20, 50]"
          :page-size.sync="page.pageSize"
          :total="+total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="getEntrustList"
          @current-change="getEntrustList"
        />
      </div>

      <!-- drawer start -->
      <el-drawer
        :visible.sync="drawer"
        :with-header="false"
        :wrapper-closable="false"
      >
        <info
          v-if="drawerStatus == 'followIn'"
          :id="infoData.id"
          :state="state"
          @followUp="followUp"
          @close="close"
        />
        <followUp
          v-if="drawerStatus == 'followUp'"
          :id="infoData.id"
          :info="infoData"
          :state="state"
          @close="close"
        />
      </el-drawer>
      <!-- drawer end -->
    </div>
  </div>
</template>

<script>
import TableLayout from "@/common/components/table-layout";
import { getExperience, getBusiness, getReportList, listByTypeAPI, reportFinish, businessFinish, productFinish, likeSearch } from "../apiUrl";
import info from "./component/info.vue";
import followUp from "./component/followUp.vue";
export default {
  name: "OfficialComponents",
  components: {
    info,
    followUp,
    TableLayout,
  },
  props: {
    state: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      productList: [],
      options: [],
      value: '',
      searchLoading: false,
      drawer: false,
      drawerStatus: "",
      loading: false,
      form: {
        keywordId: "", //体验产品
      },
      page: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      tableData: [],
      titles: {}
    };
  },
  created () {
    // 0未知 1招商资讯新闻主题 2招商情报推荐理由 3资讯来源 4产品体验关键字 5咨询业务关键字
    this.titles = {
      report: {
        key: 'report',
        title: '获取报告',
        keywordType: null,
      },
      experience: {
        key: 'experience',
        title: '预约体验',
        keywordType: 4,
      },
      business: {
        key: 'business',
        title: '业务咨询',
        keywordType: 5,
      }
    }
    this.getOptions();
    this.getEntrustList();
  },
  methods: {
    async getOptions () {
      if (!this.titles[this.state].keywordType) return
      //获取下拉框数据
      let res = await listByTypeAPI({
        keywordType: this.titles[this.state].keywordType
      });
      if (res.code === 'SUCCESS') {
        this.productList = res.result;
      }
    },
    // 获取列表
    async getEntrustList () {
      let { keywordId } = this.form;
      let data = this.state === 'report' ? { ...this.page, reportId: this.value } : { ...this.page, keywordId };
      try {
        // filterData(data);
        this.loading = true;
        let res;
        switch (this.state) {
          case 'business':
            res = await getBusiness(data);
            break;
          case 'experience':
            res = await getExperience(data);
            break;
          case 'report':
            res = await getReportList(data);
            break;
        }
        if (res.code === 'SUCCESS') {
          this.tableData = res.result.records;
          this.total = res.result.total;
        }

      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false;
      }
    },
    // 详情
    detail (e) {
      this.drawer = true;
      this.infoData = e;
      this.drawerStatus = "followIn";
    },
    // 结束任务
    //  reportFinish,businessFinish,productFinish
    async endTask (e) {
      let data = {
        id: e.id
      }
      let res;
      switch (this.state) {
        case 'business':
          res = await businessFinish(data);
          break;
        case 'experience':
          res = await productFinish(data);
          break;
        case 'report':
          res = await reportFinish(data);
          break;
      }
      if (res.code === 'SUCCESS') {
        this.$message.success("结束任务成功！");
      }
    },
    // 跟进
    followUp (e) {
      this.drawer = true;
      this.infoData = e;
      this.drawerStatus = "followUp";
    },
    // 查询
    onSubmit () {
      this.getEntrustList();
    },
    // 重置
    resetForm () {
      this.form = {
        product: "",
      };
      (this.page = {
        pageNum: 1,
        pageSize: 10,
      }),
        this.value = ''
      this.getEntrustList();
    },
    close () {
      this.drawer = false;
      this.drawerStatus = "";
      this.getEntrustList()
    },
    remoteMethod (query) {
      this.searchLoading = true
      likeSearch({ keyword: query, count: 10 }).then((res) => {
        if (res.code === "SUCCESS") {
          this.options = res.result;
          this.searchLoading = false
        }
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.entrust {
  // padding: 8px 20px;
  background: #f7f8fa;
  min-height: calc(100vh - 84px);

  &-form {
    padding: 20px;
    background: #fff;
    box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
    border-radius: 10px;

    ::v-deep {
      .el-form-item {
        width: auto !important;
      }

      .el-date-editor--daterange.el-input__inner {
        width: 250px !important;
      }

      .el-select__tags {
        max-width: 165px !important;
      }
    }
  }

  &-table {
    margin: 20px 0;
  }

  .ye {
    position: relative;
    height: 50px;
    background: #fff;

    .el-pagination {
      padding-right: 20px;
      bottom: 15px !important;
    }
  }
}
</style>