<template>
  <div class="followUp">
    <div class="close">
      <i
        class="el-icon-circle-close"
        @click="close"
      />
    </div>
    <div class="title">
      <div class="label">
        委托任务跟进
      </div>
    </div>
    <div class="followUp-con">
      <div class="top">
        当前共{{ infoData.length }}条跟进记录
      </div>
      <div class="followUp-con-main">
        <div class="addReload">
          <el-button
            icon="el-icon-circle-plus-outline"
            type="text"
            size="mini"
            @click="addReload"
          >
            新增跟进记录
          </el-button>
        </div>
        <addFllowReload
          v-if="isShowAdd"
          :id="id"
          :info="info"
          @init="init"
        />
        <el-collapse
          v-model="activeNames"
          @change="handleChange"
        >
          <el-collapse-item
            v-for="(item, k) in infoData"
            :key="k"
            :name="k"
          >
            <template slot="title">
              <div class="dis_flex">
                <span>
                  {{ item.followUpDate }}
                </span>
                <span class="ml_100">
                  {{ item.followUpPerson }}
                </span>
              </div>
            </template>
            <div class="list">
              <div class="label">
                企业对接人：
              </div>
              <div class="txt">
                {{ item.enterpriseContactPerson }}
              </div>
            </div>
            <div class="list">
              <div class="label">
                对接人联系方式：
              </div>
              <div class="txt">
                {{ item.enterpriseContactInformation }}（电话/微信）
              </div>
            </div>
            <div class="list">
              <div class="label">
                跟进概况：
              </div>
            </div>
            <div class="list">
              <div class="txt">
                {{ item.followUpOverview }}
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script>
import { entrustRecordList } from '../../apiUrl'
import { formatDate } from '@/utils/utils'
import addFllowReload from './addFllowReload.vue';
export default {
  name: 'FollowUp',
  filters: {
    time: function (value) {
      return formatDate('yyyy MM-dd', new Date(+value));
    }
  },
  components: {
    addFllowReload,
  },
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      activeNames: ['1'],
      isShowAdd: false,
      infoData: []
    }
  },
  watch: {
    id () {
      this.getEntrustRecordList();
    }
  },
  created () {
    this.getEntrustRecordList();
  },
  methods: {
    init () {
      this.isShowAdd = false;
      this.getEntrustRecordList();
    },
    // 添加记录按钮点击
    addReload () {
      this.isShowAdd = true;
    },
    getEntrustRecordList () {
      entrustRecordList({ id: this.id }).then(res => {
        this.infoData = res;
      });
    },
    handleChange () {
    },
    close () {
      this.$emit('close');
    },
  },
}
</script>

<style lang="scss" scoped>
.followUp {
  padding: 0 12px;
  position: relative;

  .close {
    width: 30px;
    height: 30px;
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 999999;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    .el-icon-circle-close {
      font-size: 24px;
    }
  }

  .title {
    position: relative;
    padding-left: 35px;
    line-height: 45px;
    font-size: 14px;
    color: #101010;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &::before {
      content: '';
      width: 5px;
      height: 18px;
      background: rgba(20, 66, 119, 0.98);
      position: absolute;
      top: 15px;
      left: 15px;
    }
  }

  &-con {
    padding-left: 40px;

    .top {
      line-height: 24px;
      padding-bottom: 10px;
    }

    &-main {
      .dis_flex {
        display: flex;
      }

      .ml_100 {
        margin-left: 100px;
      }
    }

    .list {
      display: flex;
      font-size: 12px;

      .label {
        width: 120px;
        font-family: SourceHanSansSC;
        font-weight: 400;
        color: rgb(16, 16, 16);
        font-style: normal;
        line-height: 30px;
      }

      .txt {
        font-family: SourceHanSansSC;
        font-weight: 300;
        color: rgb(16, 16, 16);
        font-style: normal;
        line-height: 30px;
      }
    }
  }
}
</style>