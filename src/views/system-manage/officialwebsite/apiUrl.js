import request from "@/utils/request";
/**
 * 我的产业链
 * @returns
 */

//  预约体验
export function getExperience(params) {
  return request({
    url: "/admin/product/experience/page",
    method: "POST",
    data: params,
  });
}
//  预约 体验 跟进记录 新增
export function experienceFlowUp(params) {
  return request({
    url: "/admin/product/experience/followUp",
    method: "POST",
    data: params,
  });
}
//   预约 体验  详情
export function getExperienceDetail(params) {
  return request({
    url: "/admin/product/experience/detail",
    method: "GET",
    params,
  });
}

//  咨询业务
export function getBusiness(params) {
  return request({
    url: "/admin/consult/business/page",
    method: "POST",
    data: params,
  });
}

// 咨询业务  跟进记录  新增
export function businessFlowUp(params) {
  return request({
    url: "/admin/consult/business/followUp",
    method: "POST",
    data: params,
  });
}
//   咨询业务  详情
export function getBusinessDetail(params) {
  return request({
    url: "/admin/consult/business/detail",
    method: "GET",
    params,
  });
}

//  产业报告
export function getReportList(params) {
  return request({
    url: "/admin/industry/report/record/page",
    method: "POST",
    data: params,
  });
}
//  产业报告名称校验
export function getReportName(params) {
  return request({
    url: "/admin/industry/report/name",
    method: "POST",
    data: params,
  });
}

// 产业报告 新增 跟进记录
export function recordFlowUp(params) {
  return request({
    url: "/admin/industry/report/record/followUp",
    method: "POST",
    data: params,
  });
}

//   产业报告  详情
export function getReportDetail(params) {
  return request({
    url: "/admin/industry/report/record/detail",
    method: "GET",
    params,
  });
}

// 预约产品体验记录 结束
export function productFinish(params) {
  return request({
    url: "/admin/product/experience/finish",
    method: "GET",
    params,
  });
}

// 咨询业务 结束
export function businessFinish(params) {
  return request({
    url: "/admin/consult/business/finish",
    method: "GET",
    params,
  });
}
//产业报告 结束
export function reportFinish(params) {
  return request({
    url: "/admin/industry/report/record/finish",
    method: "GET",
    params,
  });
}
// 产业报告 列表 跟进记录
export function recordFollowPage(params) {
  return request({
    url: "/admin/industry/report/record/followList",
    method: "GET",
    params,
  });
}

// 咨询业务 列表 跟进记录
export function businessFollowPage(params) {
  return request({
    url: "/admin/consult/business/followList",
    method: "GET",
    params,
  });
}

// 预约产品 列表 跟进记录
export function experienceFollowPage(params) {
  return request({
    url: "/admin/product/experience/followList",
    method: "GET",
    params,
  });
}

//  产业报告 编辑
export function reportEdit(params) {
  return request({
    url: "/dpar/industryReport/update",
    method: "POST",
    data: params,
  });
}
// 产业报告 列表
export function reportList(params) {
  return request({
    url: "/dpar/industryReport/pageByAdmin",
    method: "POST",
    data: params,
  });
}

// 产业报告 详情
export function reportDetail(params) {
  return request({
    url: "/dpar/industryReport/detail",
    method: "POST",
    data: params,
  });
}

//  产业报告 上传
export function reportUpload(params) {
  return request({
    url: "/dpar/industryReport/upload",
    method: "POST",
    data: params,
  });
}
//  产业报告 删除
export function reportDelete(params) {
  return request({
    url: "/dpar/industryReport/delete",
    method: "GET",
    params,
  });
}

// 下拉框数据

/**
 * 获取指定关键字类型的关键字词典集合 关键字类型 0未知 1招商资讯新闻主题 2招商情报推荐理由
 *下拉框数据
 * @returns
 *
 */
export function listByTypeAPI(params) {
  return request({
    url: "/admin/keyword/dictionary/listByType",
    method: "GET",
    params,
  });
}
// 单位类型下拉框查询
export function listUnitType(params) {
  return request({
    url: "/admin/keyword/dictionary/listUnitType",
    method: "GET",
    params,
  });
}

export function getList(params) {
  return request({
    url: "/dpar/industryReport/pageByAdmin",
    method: "POST",
    data: params,
  });
}

// 报告关键字查询
export function likeSearch(params) {
  return request({
    url: "/dpar/industryReport/likeSearch",
    method: "GET",
    params,
  });
}
