<template>
  <div class="deptTree">
    <div
      class="tree"
      :class="type == 1 ? 'on' : ''"
    >
      {{ type ? "" : "请选择新增部门:" }}
      <div class="bmtree">
        <el-tree
          v-loading="treeLoading"
          :data="deptTreeData"
          node-key="id"
          :props="defaultProps"
          :expand-on-click-node="false"
          default-expand-all="true"
          @node-click="gainList"
        >
          <span
            slot-scope="{ node, data }"
            class="custom-tree-node"
          >
            <el-input
              v-if="inputName === node.id || !node.label"
              v-model="deptName"
              size="mini"
              placeholder="请输入部门名称"
              @keyup.enter.native="blurEvent"
            />
            <span
              v-else
              @dblclick="clickEvent(node)"
            >{{ node.label }}</span>
            <div class="content">
              <i
                class="el-icon-plus"
                @click.stop="add(data)"
              />
              <i
                v-if="node.data.parentId!=0"
                class="el-icon-delete"
                @click.stop="deleteFn(node, data)"
              />
            </div>
          </span>
        </el-tree>
      </div>
    </div>
  </div>
</template>

<script>
import { deptTree, deptSave, deptDelete, deptEdit ,orgDeptTreeAPI} from "../apiUrl";
export default {
  name: "DeptTree",
  props: {
    addindustry: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "",
    },
    orgCode:{
      type: String,
      default: "",
    }
  },
  data() {
    return {
      defaultProps: {
        children: "childDeptList",
        label: "userDeptName",
      },
      deptTreeData: [], //部门树
      inputName: "", // 选中那个编辑
      deptName: "", //
      deptOpt: "", // 选中节点数据
      treeLoading:false,
      userId: "", // 用户ID -- 
      
      status: '', // 当前状态
      userDeptLevel:0
    };
  },
  created() {
    this.getDeptTree();
  },
  methods: {
    // 根据部门获取人员列表
    async gainList(data) {
      this.$emit("gaindeptId", data.id);
    },
    // 获取部门树
    getDeptTree() {
      if(this.type==''){
        orgDeptTreeAPI({
          orgCode:this?.orgCode
        }).then((res) => {
         // console.log(res);
        this.treeLoading=true
        res.noDel = true;
        let treeData = res;
        this.deptTreeData = treeData;
      }).finally(()=>{
        this.treeLoading=false
      });
      }else{
        deptTree().then((res) => {
        this.treeLoading=true
        res.noDel = true;
        let treeData = res;
        this.deptTreeData = treeData;
      }).finally(()=>{
        this.treeLoading=false
      });
      }
     
    },
    clickEvent(node) {
      if(this.status == 'add'){
        this.$message.error("请修改完成后再添加");
        return false
      }
      this.inputName = node.id;
      this.deptName = node.label;
      this.userId = node.data.id;
      this.status = 'add';
    },
    blurEvent(e) {
      let val = e.target.value;
      this.inputName = "";
      let data = {
        id: this.userId,
        userDeptName: val,
        parentId: this.deptOpt.id,
        userDeptLevel: +this.userDeptLevel + 1
      };
      if (!data.userDeptName) {
        this.$message.error("部门名称不能为空");
        return;
      }
      if (data.id) {
        // 修改部门
        deptEdit(data).then(() => {
          this.$message.success("修改成功！");
          this.getDeptTree();
          this.status = '';
          this.userDeptLevel=0
        }).catch(()=>{
          this.status = '';
        });
      } else {
        // 新增部门
        deptSave(data).then(() => {
          this.$message.success("新增成功！");
          this.getDeptTree();
          this.status = '';
          this.userDeptLevel=0
        });
      }
    },
    add(data) {
      if(this.status == 'add'){
        this.$message.error("请修改完成后再添加");
        return false
      }
      this.deptOpt = data;
      this.userDeptLevel=data.userDeptLevel          
      const newChild = { id: +new Date(), type: 1, label: "name", childDeptList: [] };
      if (!data.childDeptList) {
        this.$set(data, "childDeptList", []);
      }
      data.childDeptList.push(newChild);
      this.status = 'add';
      this.deptName = "";
      this.userId = "";
    },
    // eslint-disable-next-line no-unused-vars
    deleteFn(node, data) {
      if(data.type==1){
        this.inputName = ''
        this.deptName = ""
        const parent = node.parent;
        const children = parent.data.childDeptList || parent.data;
        const index = children.findIndex(d => d.id === data.id);
        children.splice(index, 1);
        this.status = '';
        return
      }
      this.$confirm("确定删除该部门？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deptDelete({ id: node.data.id }).then(() => {
          this.getDeptTree();
          this.$message({
          type: "success",
          message: "删除部门成功!",
        });
        });
        this.status = '';
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.deptTree {
  .tree {
    width: 80%;
    margin-left: 10%;
    &.on {
      margin-left: 0;
      .bmtree {
        padding-top: 20px;
      }
    }
    .bmtree {
      width: 100%;
      border: 1px solid #ededed;
      margin-top: 20px;
      height: 450px;
      display: block;
      overflow-y: scroll;
    }
  }
  ::v-deep {
    .custom-tree-node {
      .el-input--mini {
        width: 120px;
        .el-input__inner {
          height: 20px;
          line-height: 20px;
        }
      }
    }
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 50px;
    .el-icon-plus {
      margin-right: 10px;
    }
  }
}
</style>