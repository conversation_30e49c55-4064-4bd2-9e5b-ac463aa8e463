import interfaceRequest from '@/utils/interfaceRequest'
import { 
  apiUrl, 
} from '@/api/user'

const apiDomain = apiUrl;
export const chartApi = {
  deptTree: apiDomain + '/admin/user/dept/deptTree', // 部门树
  deptSave: apiDomain + '/admin/user/dept/save', // 新增部门
  deptDelete: apiDomain + '/admin/user/dept/delete', // 删除部门
  deptEdit: apiDomain + '/admin/user/dept/edit', // 编辑部门
  listUserByDept: apiDomain + '/admin/user/listUserByDept', // 查询部门关联的用户
  deptUserList: apiDomain + '/admin/user/dept/deptUserList', // 部门列表
  pageUserByDept: apiDomain + '/admin/user/pageUserByDept', // 人员列表 
  userDetail: apiDomain + '/admin/user/userDetail', // 获取用户详情 
  batchUpdateDept: apiDomain + '/admin/user/batchUpdateDept', // 获取用户详情 
  listDeptByOrgCode: apiDomain + '/admin/user/dept/listDeptByOrgCode', // 获取用户详情 
  orgDeptTree: apiDomain + '/admin/user/dept/orgDeptTree', // 获取用户详情 
  listRolesByOrgCode: apiDomain + '/admin/role/listRolesByOrgCode', // 根据机构获取角色 
  getAll: apiDomain + '/admin/institution/getAll', // 获取所有入驻机构 
  queryByDeptId: apiDomain + '/admin/user/dept/queryByDeptId', // 根据部门id查询部门及子部门
}
// 查询部门关联的用户
export function listUserByDept(data) {
  return interfaceRequest({
    url: chartApi.listUserByDept,
    method: 'GET',
    params: { ...data }
  })
}
// 查询部门关联的用户
export function userDetailAPI(data) {
  return interfaceRequest({
    url: chartApi.userDetail,
    method: 'GET',
    params: { ...data }
  })
}
// 删除部门
export function deptDelete(data) {
  return interfaceRequest({
    url: chartApi.deptDelete,
    method: 'GET',
    params: { ...data }
  })
}
// 部门树
export function deptTree(data) {
  return interfaceRequest({
    url: chartApi.deptTree,
    method: 'POST',
    data
  })
}
// 新增部门
export function deptSave(data) {
  return interfaceRequest({
    url: chartApi.deptSave,
    method: 'POST',
    data
  })
}

// 编辑部门
export function deptEdit(data) {
  return interfaceRequest({
    url: chartApi.deptEdit,
    method: 'POST',
    data
  })
}


// 部门list
export function deptUserList(data) {
  return interfaceRequest({
    url: chartApi.deptUserList,
    method: 'POST',
    data
  })
}
// 查询部门关联的用户
export function pageUserByDeptAPI(data) {
  return interfaceRequest({
    url: chartApi.pageUserByDept,
    method: 'POST',
    data
  })
}
// 批量移除部门
export function batchUpdateDeptAPI(data) {
  return interfaceRequest({
    url: chartApi.batchUpdateDept,
    method: 'POST',
    data
  })
}
// 根据机构获取部门
export function listDeptByOrgCodeAPI(data) {
  return interfaceRequest({
    url: chartApi.listDeptByOrgCode,
    method: 'GET',
    params: { ...data }
  })
}
// 根据机构获取角色列表
export function listRolesByOrgCodeAPI(data) {
  return interfaceRequest({
    url: chartApi.listRolesByOrgCode,
    method: 'GET',
    params: { ...data }
  })
}
// 根据机构code 获取树
export function orgDeptTreeAPI(data) {
  return interfaceRequest({
    url: chartApi.orgDeptTree,
    method: 'GET',
    params: { ...data }
  })
}
// 获取所有入驻机构
export function getAllAPI(data) {
  return interfaceRequest({
    url: chartApi.getAll,
    method: 'GET',
    params: { ...data }
  })
}
// 根据部门id查询部门及子部门
export function queryByDeptIdAPI(data) {
  return interfaceRequest({
    url: chartApi.queryByDeptId,
    method: 'GET',
    params: { ...data }
  })
}
export function findPathById(id, arr) {
  for (var i = 0; i < arr.length; i++) {
    if (arr[i].id === id) {
      return [arr[i].id]; // 找到了目标节点，返回它的id
    } else {
      var childDeptList = arr[i].childDeptList; // 获取当前节点的子节点列表
      if (childDeptList?.length > 0) {
        // 如果当前节点有子节点，则递归查找子节点并返回子节点的路径
        var path = findPathById(id, childDeptList);
        if (path) {
          return [arr[i].id].concat(path); // 将当前节点id和子节点路径合并为完整路径
        }
      }
    }
  }
  return null; // 没有找到目标节点，返回null
}