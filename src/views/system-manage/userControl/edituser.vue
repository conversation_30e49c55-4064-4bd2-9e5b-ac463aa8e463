<template>
  <div>
    <el-drawer
      width="50%"
      title="编辑用户"
      :visible="editUser"
      :close-on-click-modal="false"
      :wrapper-closable="false"
      @close="cancel"
    >
      <div class="tabs">
        <el-form
          ref="form"
          v-loading="formLoading"
          label-position="left"
          :model="form"
          class="form"
          :rules="rules"
          label-width="100px"
        >
          <el-form-item
            prop="orgName"
            label="机构名称"
          >
            <el-input
              v-model="form.orgName"
              disabled
              placeholder="请输入机构名称"
            />
          </el-form-item>
          <el-form-item
            label="部门名称"
            prop="deptId"
            class="appendToBodyFalse"
          >
            <!--             <el-select
              v-model="form.deptId"
              placeholder="请选择部门"
            >
              <el-option
                v-for="item in deptListFn"
                :key="item.id"
                :label="item.userDeptName"
                :value="item.id"
              />
            </el-select> -->
            <el-cascader
              v-model="form.deptId"
              placeholder="请选择部门"
              style="width: 180px"
              :options="deptListFn"
              :show-all-levels="false"
              :props="optionProps" 
            />
          </el-form-item>
          <el-form-item
            prop="realName"
            label="姓名"
          >
            <el-input
              v-model="form.realName"
              placeholder="请输入姓名"
            />
          </el-form-item>
          <el-form-item
            prop="username"
            label="手机(账号)"
          >
            <el-input
              v-model="form.username"
              placeholder="请输入联系人手机号码"
              class="miniInput"
            />
          </el-form-item>
          <el-form-item
            label="职位"
            prop="position"
          >
            <el-input
              v-model="form.position"
              placeholder="请输入职位"
            />
          </el-form-item>
          <el-form-item
            label="邮箱"
            prop="email"
          >
            <el-input
              v-model="form.email"
              placeholder="请输入邮箱"
            />
          </el-form-item>
          <el-form-item
            label="角色"
            prop="roleIds"
          >
            <el-select
              v-model="form.roleIds"
              multiple
              placeholder="请选择"
            >
              <el-option
                v-for="item in rolesList"
                :key="item.id"
                :label="item.roleName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="dialog-footer">
          <el-button
            @click="cancel"
          >
            取 消
          </el-button>
          <el-button
            class="btn"
            @click="preserve"
          >
            确 认
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
    
    <script>
import { usereditAPI } from "@/api/userset";
import { userDetailAPI,listDeptByOrgCodeAPI,listRolesByOrgCodeAPI } from "./apiUrl";
export default {
  name: "AddUser",
  props: {
    editUser: {
      type: Boolean,
      default: false,
    },
    compileEcho: {
      type: Object,
      default: () => {},
    },
/*     rolesList: {
      type: Array,
      default: null,
    }, */
    rowId: {
      type: String,
      default: null,
    },
    editInfo: {
      type: Object,
      default: null,
    },
    deptList: {
      type: Array,
      default: null,
    },
  },
  data() {
    return {
      form: {
        orgName: "", //机构名称
        realName: "", // 姓名
        deptId: "", // 部门id
        username: "", //手机号/账号
        position: "", //职位
        email: "", //邮箱
        roleIds: [], //角色
      },
      activeName: "first",
      formLoading:false,
      deptListFn:[],
      rolesList:[],
      optionProps:{
        value: "id",
        label: "userDeptName",
        children: "childDeptList",
        checkStrictly: true
      },
      rules: {
        /*  orgCode: [
              { required: true, message: "机构名称必选", trigger: "blur" },
            ], */
        deptId: [{ required: true, message: "部门名称必选", trigger: "blur" }],
        //position: [{ required: true, message: "职位必填", trigger: "blur" }],
        realName: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        roleIds: [{ required: true, message: "角色必选", trigger: "blur" }],
        email: [
          {
            pattern:
              /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
            message: "邮箱格式不符合规则",
            trigger: "blur",
          },
        ],
        username: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/,
            message: "手机号不符合规则",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.editFn();
  },
  methods: {
    async editFn() {
      try {
        this.formLoading = true;
        const res = await userDetailAPI({
          userId: this.rowId,
        });
        (this.form.orgName = res.orgName), //机构名称
          (this.form.realName = res.realName), // 姓名
          (this.form.deptId = res.deptId), // 部门id
          (this.form.username = res.username), //手机号/账号
          (this.form.position = res.position), //职位
          (this.form.email = res.email), //邮箱
          (this.form.roleIds = res.roleIds); //角色 */
          (this.form.id = res.id); //角色 */
        this.departmentList(res?.orgCode)
      } catch (error) {
        console.log(error);
      } finally {
        this.formLoading = false;
      }
    },
    getTreeData(data){
      for(var i=0;i<data.length;i++){
        if(data[i].childDeptList.length<1){
          data[i].childDeptList=undefined;
        }else {
          this.getTreeData(data[i].childDeptList);
        }
      }
      return data;
    },
    async departmentList(val){
        const res=  await listDeptByOrgCodeAPI({
            orgCode:val
          })
         const arr =await listRolesByOrgCodeAPI({
            orgCode:val
         })
          //this.deptListFn=res          
          this.deptListFn=this.getTreeData(res);
          //this.form.deptId =  findPathById(this.form.deptId,this.deptListFn)
          this.rolesList =arr
      },
    cancel() {
      this.$emit("update:editUser", false);
    },
    async preserve() {
      await this.$refs.form.validate();
      try {
        this.form.mobile=this.form.username
        if(Array.isArray(this.form.deptId)){
          this.form.deptId = this.form.deptId.pop(); 
        }
        await usereditAPI(this.form);
        this.cancel();
        this.$message.success("编辑用户成功");
        this.$emit("industryList");
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>
    
    <style scoped lang="scss">
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #3370FF;
  color: rgba(255, 255, 255, 0.85);
}
::v-deep {
  .el-drawer__header {
    font-size: 20px;
    color: #000;
  }
}
.tabs {
  width: 95%;
  margin-left: 2.5%;
  .form {
    width: 80%;
    margin-left: 10%;
  }
}
.dialog-footer {
  //width: 100%;
  margin-left: 55%;
}
</style>