<template>
  <div>
    <el-card class="box-card">
      <div
        slot="header"
        class="clearfix"
      >
        <span style="font-size: 20px; margin-left: 20px">部门设置</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
        >
          <i
            class="el-icon-close"
            @click="close"
          />
        </el-button>
      </div>
      <div class="jurisdiction">
        <div class="menu">
          <span style="font-size: 15px;">组织架构</span>
          <DeptTree
            type="1"
            @gaindeptId="gaindeptId"
          />
        </div>
        <div class="operation">
          <span style="font-size: 15px;">人员列表</span>
          <el-table
            v-loading="listLoading"
            :data="tableData"
            :row-key="getRowKeys"
            tooltip-effect="dark"
            style="width: 100%, overflow-y: scroll;  height: 520px;display: block;margin-top: 10px;"
            @selection-change="handleSelectStockChange"
          >
            <!--             <el-table-column
              type="selection"
              align="center"
              :reserve-selection="true"
              width="55"
            /> -->
            <el-table-column
              prop="realName"
              align="center"
              label="姓名"
              width="120"
            />
            <el-table-column
              prop="username"
              label="账号"
              align="center"
              width="120"
            />
            <el-table-column
              prop="position"
              label="职位"
              align="center"
              width="140"
            />
            <el-table-column
              prop="roleName"
              width="140"
              align="center"
              label="角色"
            />
            <el-table-column>
              <template slot-scope="{ row }">
                <el-button
                  type="text"
                  @click="adjust(row.id)"
                >
                  <span style="color: #1885fc">调整部门</span>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="ye">
            <el-pagination
              :current-page.sync="pageSize.pageNum"
              :page-sizes="[5, 10, 20, 50]"
              :page-size.sync="pageSize.pageSize"
              :total="+total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="staffListFn"
              @current-change="staffListFn"
            />
          </div>
        </div>
      </div>
      <el-dialog
        v-if="centerDialogVisible"
        :title="multipleSelection.length >= 2 ? '调整部门' : '调整部门'"
        :visible.sync="centerDialogVisible"
        :close-on-click-modal="false"
        width="25%"
        center
      >
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="80px"
        >
          <el-form-item
            label="移至部门"
            prop="value"
            class="appendToBodyFalse"
          >
            <!--            <el-select
              v-model="form.value"
              placeholder="请选择部门"
            >
              <el-option
                v-for="item in deptList"
                :key="item.id"
                :label="item.userDeptName"
                :value="item.id"
              />
            </el-select> -->
            <el-cascader
              v-model="form.value"
              :options="deptList"
              :show-all-levels="false"
              :props="optionProps" 
              clearable
            />
          </el-form-item>
          <form>
            <span
              slot="footer"
              class="dialog-footer"
              style="margin-left: 28%;"
            >
              <el-button @click="cancel">取 消</el-button>
              <el-button @click="transfer">确 定</el-button>
            </span>
          </form>
        </el-form>
      </el-dialog>
    </el-card>
  </div>
</template>
  
<script>
import DeptTree from "./component/deptTree.vue";
import { pageUserByDeptAPI,batchUpdateDeptAPI,queryByDeptIdAPI } from "./apiUrl";
export default {
  name: "JurisDiction",
  components: {
    DeptTree,
  },
  props:{
   /*  deptList:{
        type: Array,
        default: null
      } */
  },
  data() {
    return {
      options: [],
      tableData: [],
      deptList:[],
      optionProps:{
        value: "id",
        label: "userDeptName",
        children: "childDeptList",
        checkStrictly: true
      },
      type: 1,
      pageSize: {
        pageNum: 1,
        pageSize: 10,
      },
      rules: {
        value: [{ required: true, message: "目标部门必选", trigger: "blur" }],
      },
      total: 0,
      multipleSelection: [],
      centerDialogVisible: false,
      form: {
        value: [],
      },
      listLoading: false,
      treeId:""
    };
  },
  watch:{
   async 'centerDialogVisible'(value){
      if(value){
      const res= await queryByDeptIdAPI({
        deptId: this.treeId,
       })
       this.deptList=res
      }else{
        this.deptList=[]
      }
    }
  },
  created() {
    //this.staffListFn();
  },
  methods: {
    getRowKeys(row) {
      return row.id;
    },
    async gaindeptId(value) {
      try {
        this.treeId=value
        this.listLoading=true
        this.pageSize.pageNum = 1;
        this.pageSize.pageSize = 10;
        this.multipleSelection=[]
        const res = await pageUserByDeptAPI({
        deptId: value,
        pageNum: this.pageSize.pageNum,
        pageSize: this.pageSize.pageSize,
      });
      this.tableData = res.records;
      this.total = res.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.listLoading=false
      }
    },
    handleSelectStockChange(val) {
      let stockSelectlist = [];
      val.forEach((el) => {
        stockSelectlist.push(el.id);
      });
      this.multipleSelection = stockSelectlist;
    },
    close() {
      this.$emit("close", 1);
    },
    // 确认调整部门
    async transfer() {
      await this.$refs.form.validate();
      let arr =[]
      arr[0]= this.multipleSelection
      let fromValue = this.form.value.pop(); 
      await batchUpdateDeptAPI({
        deptId:fromValue,
        userIds:arr
      })
      this.$message.success("调整部门成功")
      this.cancel()
      this.gaindeptId(this.treeId)
    },
    // 取消调整部门
    cancel(){
      this.form.value=[]
      this.centerDialogVisible = false
    },
    // 调整部门
    adjust(row) {
      this.multipleSelection=row
      this.centerDialogVisible = true;
    },
   async staffListFn() {
      try {
        this.listLoading=true
        this.multipleSelection=[]
        const res = await pageUserByDeptAPI({
        deptId: this.treeId,
        pageNum: this.pageSize.pageNum,
        pageSize: this.pageSize.pageSize,
      });
      this.tableData = res.records;
      this.total = res.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.listLoading=false
      }
    },
  },
};
</script>
  
  <style scoped lang="scss">
.ye {
  position: relative;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 20px;
  .el-icon-plus {
    margin-right: 10px;
  }
}
.jurisdiction {
  display: flex;
  width: 95%;
  margin-left: 2.5%;
  .menu {
    width: 40%;
  }
  .operation {
    width: 60%;
  }
}
.box-card {
  height: 700px;
}

.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #3370FF;
  color: rgba(255, 255, 255, 0.85);
}
</style>