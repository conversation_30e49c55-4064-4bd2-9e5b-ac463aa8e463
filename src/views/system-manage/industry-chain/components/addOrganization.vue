<template>
  <div>
    <el-dialog
      width="60%"
      title="添加产业链"
      :visible="dialogFormVisible"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="form"
        label-position="top"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item
          prop="chainCode"
          label="产业链代码"
        >
          <el-input
            v-model="form.chainCode"
            placeholder="请输入产业链代码"
          />
        </el-form-item>
        <el-form-item
          prop="chainName"
          label="产业链名称"
        >
          <el-input
            v-model="form.chainName"
            placeholder="请输入产业链名称"
          />
        </el-form-item>
        <el-form-item
          prop="categoryId"
          label="产业链类型"
        >
          <el-select
            v-model="form.categoryId"
            style="width:100%"
            placeholder="请选择产业链类型"
            :popper-append-to-body="false"
          >
            <el-option
              v-for="item in chainTypes"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="form.notes"
            placeholder="请输入备注"
            type="textarea"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="cancel">
          取 消
        </el-button>
        <el-button
          v-loading="loading"
          type="primary"
          @click="preserve"
        >
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
import { saveAPI,industryTypeListAPI } from "@/api/industryChain";
//import {  addIndustryAPI } from '@/api/industry'
export default {
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      form: {},
      chainTypes:[],
      rules: {
        chainCode: [
          { required: true, message: "产业链代码不能为空", trigger: "blur" },
          { max: 10, message: "产业链代码应为10字符以内", trigger: "blur" },
        ],
        chainName: [
          { required: true, message: "产业链名称不能为空", trigger: "blur" },
          { max: 20, message: "产业链名称应为20字符以内", trigger: "blur" },
        ],
        categoryId: [
          { required: true, message: "产业链类型不能为空", trigger: "change" },
        ]
      },
    };
  },
  created(){
    this.chainTypeList()
  },
  methods: {
    async chainTypeList(){
      let res = await industryTypeListAPI()
      this.chainTypes=res.result
    },
    cancel() {
      this.$emit("update:dialogFormVisible", false);
    },
    async preserve() {
      await this.$refs.form.validate();
      this.loading = true;
      try {
        await saveAPI(this.form);
        // 关闭弹层
        this.cancel();
        // 提示
        this.$message.success("新增产业链成功");
        // 调用列表接口刷新页面
        this.$emit('refresh')
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-select-dropdown.el-popper {
  position: absolute !important;
}
</style>