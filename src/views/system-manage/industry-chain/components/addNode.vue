<template>
  <div>
    <el-dialog
      width="40%"
      title="添加产业链节点"
      :visible="dialogFormVisible"
      :close-on-click-modal="false"
      @close="cancel"
    > 
      <span class="refer">添加参考：</span>
      <br>
      父节点名称:{{ addrefer.nodeName }}
      <br>
      父节点x轴:{{ addrefer.abscissaValue }}
      <br>
      父节点y轴:{{ addrefer.ordinateValue }}
      <el-form
        ref="form"
        :rules="rules"
        label-position="top"
        :model="form"
        label-width="80px"
      >
        <el-form-item
          label="节点名称"
          prop="nodeName"
        >
          <el-input
            v-model="form.nodeName"
            placeholder="请输入产业链节点名称"
          />
        </el-form-item>
        <el-form-item
          prop="nodeOrder"
          label="节点顺序"
        >
          <el-input
            v-model="form.nodeOrder"
            placeholder="请输入节点顺序"
          />
        </el-form-item>
        <div class="xyz">
          <el-form-item
            label="X坐标值"
            prop="abscissaValue"
          >
            <el-input
              v-model="form.abscissaValue"
              placeholder="请输入X坐标值"
            />
          </el-form-item>        
          <el-form-item
            label="Y坐标值"
            prop="ordinateValue"
            class="yValue"
          >
            <el-input
              v-model="form.ordinateValue"
              placeholder="请输入Y坐标值"
            />
          </el-form-item>
        </div>
        <el-form-item
          prop="lineInfo"
          label="节点间线坐标json串"
        >
          <el-input v-model="form.lineInfo" />
        </el-form-item>
      
        <el-form-item
          prop="isLeaf"
          label="节点类型"
        >
          <el-radio
            v-model="form.isLeaf"
            label="0"
          >
            目录企业
          </el-radio>
          <el-radio
            v-model="form.isLeaf"
            label="1"
          >
            挂载企业
          </el-radio>
        </el-form-item>
        <el-form-item
          v-if="form.isLeaf == '1'"
          prop="showMaxNumber"
          label="最大显示个数"
        >
          <el-input v-model="form.showMaxNumber" />
        </el-form-item>
        <el-form-item
          v-if="form.isLeaf == '1'"
          prop="dynamicTags"
          label="产业链标签"
          class="appendToBodyFalse"

        >
          <el-select
          :popper-append-to-body="false"
            v-model="labelRequests"
            class="select"
            multiple
            filterable
            placeholder="请选择"
          >
            <div class="addtag">
              <span>
                <el-input
                  v-model="input"
                  placeholder="请输入内容"
                />
              </span>
              <span><el-button
                style="margin-left: 20px;"
                class="btn"
                @click="addtag"
              >添加</el-button></span>
            </div>   
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.labelName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="cancel">
          取 消
        </el-button>
        <el-button
          v-loading="loading"
          type="primary"
          :disabled="loading"
          @click="preserve"
        >
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
    
    <script>
import { addNode, industrylabelAPI, addTagAPI } from "@/api/industryChain";
import { getPathId } from '@/utils/utils'

export default {
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
    chainId: {
      type: String,
      default: "",
    },
    nodeLevel: {
      type: String,
      default: "",
    },
    nodeParent: {
      type: String,
      default: "",
    },
    addrefer:{
      type:Object,
      default:()=>{
      }
    }
  },
  data() {
    return {
      input:"",
      loading: false,
      isform: false,
      // 产业链列表
      options: [],
      // 标签
      dynamicTags: [],
      inputVisible: false,
      inputValue: "",
      // 表单
      form: {
        ordinateValue:'',
        abscissaValue:''
      },
      // 用于选中下拉
      labelRequests: [],
      // 选中的标签列表
      labelIds: [],
      rules: {
        nodeName: [
          { required: true, message: "节点名称不能为空", trigger: "blur" },
          { max: 15, message: "节点名称应在十五字符以内", trigger: "blur" },
        ],
        nodeOrder: [
          { required: true, message: "节点顺序不能为空", trigger: "blur" },
          {
            pattern: /^[+]{0,1}(\d+)$/,
            message: "节点顺序需为正整数",
            trigger: "blur",
          },
        ],
        isLeaf: [{ required: true, message: "节点类型必选", trigger: "blur" }],
        showMaxNumber: [
          { required: true, message: "最大显示个数必填", trigger: "blur" },
          {
/*             pattern: /^([1-9][0-9]{0,1}|100)$/,
            message: "最大显示个数需为正整数并且在1到100之间", */
            pattern: /^[+]{0,1}(\d+)$/,
            message: "最大显示个数需为正整数",
            trigger: "blur",
          },
        ],
        abscissaValue:[
       // { required: true, message: "X坐标值必填", trigger: "blur" },
          {
            pattern: /(^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:.\d{1,2})?$)/,
            message: "坐标要求是数字且最多保留两位小数",
            trigger: "blur",
          }, 
        ],
        ordinateValue:[
       // { required: true, message: "Y坐标值必填", trigger: "blur" },
           {
            pattern: /(^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:.\d{1,2})?$)/,
            message: "坐标要求是数字且最多保留两位小数",
            trigger: "blur",
          }, 
        ]
        /* dynamicTags: [
          { required: true, message: "产业链标签必填", trigger: "blur" },
        ], */
      },
    };
  },
  created() {
    this.tagListAP();
    this.form.ordinateValue=this.addrefer.ordinateValue
    this.form.abscissaValue=this.addrefer.abscissaValue
  },
  methods: {
    async addtag(){
    let input = this.input;
      if (input) {
        /* this.dynamicTags.push(inputValue); */
        // 新增产业链标签
        await addTagAPI({
          labelName: this.input,
          chainId:this.$route.query.id|| getPathId()|| null,
        });
        this.tagListAP()
        this.$message.success("新增产业链标签成功");
      } else {
        this.$message.error("产业链标签不能为空");
      }
      this.input = "";   
    },
    cancel() {
      this.$emit("update:dialogFormVisible", false);
    },
    async tagListAP() {
      const res = await industrylabelAPI();
      this.options = res.result;
    },
    async preserve() {
      await this.$refs.form.validate();
      this.loading = true;
      try {
        this.form.chainId = this.chainId;
        // 节点层级
        this.form.nodeLevel = this.nodeLevel;
        // 父节点id
        this.form.nodeParent = this.nodeParent;
        this.form.labelIds = this.labelRequests;
        await addNode(this.form);
        // 关闭弹层
        this.cancel();
        // 提示
        this.$message.success("新增产业链成功");
        // 调用列表接口刷新页面
        this.$emit("refresh");
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep{
  .el-tag.el-tag--info{
    color: #3370FF;
    background-color: #fff;
    border:0.4px solid #3370FF;
  }
  .el-select .el-tag__close.el-icon-close{
    background-color: #fff;
  }
}
.btn{
  color: rgba(255,255,255,0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover{
 background: #1765AD;;
 color: rgba(255,255,255,0.85);
}
.btn:active{
 background: #52ABFF;
 color: rgba(255,255,255,0.85);
}
.btn:focus{
 background: #3370FF;
 color: rgba(255,255,255,0.85);
}
.addtag{
  display: flex;
  width: 100%;
  justify-content: space-between;
  .el-input{
    margin-left: 5%;
    width: 22rem;
  }
  .el-button{
    margin-right: 20px;
  }
}
.select{
  width: 400px;
}
.refer{
  font-size: 16px;
}
.xyz{
  display: flex;
 .yValue{
  margin-left: 4rem;
 }
}
</style>

<style lang="scss">
.appendToBodyFalse .el-select-dropdown {
  position: absolute !important;
  top: 30px !important;
  left: 0 !important;
}
.appendToBodyFalse  .el-cascader__dropdown {
  position: absolute !important;
  top: 30px !important;
  left: -10 !important;
    // max-width: 450px;
    // min-width: 350px;
  overflow-x: scroll;
  overflow-y: scroll;

}
.appendToBodyFalse  .el-picker-panel__body-wrapper {
 
}
</style>