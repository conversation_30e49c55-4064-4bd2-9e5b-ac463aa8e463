<!-- Created by JiangHM on 2022/12/19. 入驻机构管理 -->
<template>
  <div
    ref="msMain"
    class="vue-ms-main vue-ms-main-sel"
  >
    <table-layout
      :tab-name-list="['产业链管理']"
    >
      <!-- 查询 -->
      <template slot="selBtn">
        <!--         <el-button
          type="primary"
          plain
          size="small"
          :loading="listLoading"
          icon="el-icon-search"
          @click="search"
        >
          搜索
        </el-button>
        <el-button
          size="small"
          @click="reset"
        >
          重置
        </el-button> -->
        <el-button
          class="btn"
          @click="dialogFormVisible = true"
        >
          添加
        </el-button>
      </template>
      <!-- 查询内容 -->
      <el-form
        slot="elForm"
        ref="params"
        label-width="82px"
        class="demo-form-inline"
        :model="params"
      >
        <el-form-item label="产业链类型">
          <el-select
            v-model="pageSize.categoryId"
            placeholder="请选择产业链类型"
          >
            <el-option
              v-for="item in chainTypes"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产业链名称">
          <el-input
            v-model="pageSize.chainName"
            placeholder="请输入产业链名称"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="pageSize.notes"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item style="width: 330px;">
          <el-button
            v-loading="listLoading"
            class="btn"
            @click="search"
          >
            查询
          </el-button>
          <el-button
            class="btn"
            @click="reset"
          >
            重置
          </el-button>
          <el-button
            class="btn"
            @click="dialogFormVisible = true"
          >
            添加
          </el-button>
        </el-form-item>
      </el-form>
      <div slot="selTable">
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column
            prop="chainCode"
            align="center"
            label="产业链代码"
            width="100"
          />
          <el-table-column
            prop="categoryName"
            label="产业链类型"
            align="center"
            width="180"
          />
          <el-table-column
            prop="chainName"
            label="产业链名称"
            align="center"
            width="180"
          />
          <el-table-column
            prop="relevanceOrganizeNumber"
            align="center"
            width="120"
            label="关联机构个数"
          />
          <el-table-column
            prop="relevanceEnterpriseNumber"
            align="center"
            label="关联企业个数"
            width="120"
          />
          <el-table-column
            prop="notes"
            align="center"
            show-overflow-tooltip="true"
            width="300"
            label="备注"
          />
          <el-table-column
            prop="gmtModify"
            label="最新编辑时间"
            align="center"
            width="160"
          />
          <el-table-column
            prop="gmtCreate"
            align="center"
            label="创建时间"
            width="160"
          />
          <el-table-column
            align="center"
            label="操作"
          >
            <template slot-scope="{row}">
              <el-button
                type="text"
                @click="allocation(row.id)"
              >
                配置
              </el-button>
              <el-divider direction="vertical" />
              <el-button
                type="text"
                @click="createindustry(row.id)"
              >
                初始化排列
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </table-layout>
    <div class="ye">
      <el-pagination
        :current-page.sync="pageSize.pageNum"
        :page-sizes="[5, 10, 20, 50]"
        :page-size.sync="pageSize.pageSize"      
        :total="+total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="industryList"
        @current-change="industryList"
      />
    </div>
    <addOrganization
      v-if="dialogFormVisible"
      :dialog-form-visible.sync="dialogFormVisible"
      @refresh="industryList()"
    />
  </div>
</template>
  
  <script>
import { industryAPI,initNodeXYValueAPI,industryTypeListAPI  } from "@/api/industryChain";
import addOrganization from "./components/addOrganization.vue";
import TableLayout from "@/common/components/table-layout";

export default {
  name: "IndustryChain",
  components: {
    TableLayout,
    addOrganization,
  },
  data() {
    return {
      listLoading:false,
      loading: false,
      // 弹层
      dialogFormVisible: false,
      // 联系人名称
      linkman: "",
      //列表
      tableData: [
      ],
      chainTypes:[],
      //默认页码
      pageSize: {
        pageSize: 10,
        pageNum: 1,
        categoryId:"",
        chainName: "",// 产业链名称
        notes:"" // 备注
      },
      //总数
      total: '',
    };
  },
  computed: {
  },
  watch: {},
  created() {
    this.industryList()
    this.chainTypeList()
  },
  methods: {
    createindustry(row){
      this.$confirm("初始化排列将覆盖人工调整的数据，请谨慎操作，确认将产业链排列初始化吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await initNodeXYValueAPI({
          chainId:row
        }); 
        // 刷新
        //this.SettledList();
        this.$message.success("操作成功!")
      });
    },
    allocation(id) {
      this.$router.push({
      path:'/business/configuration',
        query: {
        id,
      }
})
    },
    async chainTypeList(){
      let res = await industryTypeListAPI()
      this.chainTypes=res.result
    },
    //获取产业链列表
    async industryList() {
      try {
      this.loading =true
      const res = await industryAPI(this.pageSize);
      this.tableData = res.result.records
      this.total = res.result.total
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false
      }

    },
    reset() {
     this.pageSize= {
        pageSize: 10,
        pageNum: 1,
        categoryId:"",
        chainName: "",// 产业链名称
        notes:"" // 备注
      }
      this.industryList()
    },
    async search(){
    try {
      this.pageSize.pageNum = 1
      this.listLoading=true
      await this.industryList()
    } catch (error) {
      console.log(error);
    }finally{
      this.listLoading=false
    }
    }
  },
};
</script>
<style lang="scss">
.el-tooltip__popper {
 max-width: 900px;
}
</style>
<style scoped lang="scss">
.ye {
  margin-bottom: 50px;
}
.btn{
  color: rgba(255,255,255,0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover{
 background: #1765AD;;
 color: rgba(255,255,255,0.85);
}
.btn:active{
 background: #52ABFF;
 color: rgba(255,255,255,0.85);
}
.btn:focus{
 background: #3370FF;
 color: rgba(255,255,255,0.85);
}
</style>
  