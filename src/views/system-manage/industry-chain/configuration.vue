<template>
  <div>
    <el-button
      icon="el-icon-arrow-left"
      type="text"
      @click="$router.back()"
    >
      返回
    </el-button>
    <div class="allocation">
      <div class="down-tree">
        <el-tree
          class="tree"
          :data="data"
          :expand-on-click-node="false"
          default-expand-all="true"
          :props="defaultProps"
          @node-click="handleNodeClick"
        >
          <span
            slot-scope="{ node, data }"
            class="custom-tree-node"
            @mouseenter="mouseenter(data)"
            @mouseleave="mouseleave(data)"
          >
            <span style="display: flex;">
              <span 
                :style="{ 'max-width': data.nodeLevel== 1 ? '120px' : (data.nodeLevel== 2 ? '180px' : (data.nodeLevel== 3 ? '160px' : '110px') ) }"
                class="nodeNames"
              >{{ node.label }}</span><span v-if="data.leafNodeCount">(挂载企业节点：{{ data.leafNodeCount }})</span><span v-if="data.isLeaf == '1'">({{ node.data.relevanceEnterpriseNumber }})</span></span>
            <div class="content">
              <i
                v-show="data.show"
                v-if="data.isLeaf !== '1'"
                class="el-icon-plus"
                @click.stop="add(node,data)"
              />
              <i
                v-show="data.show"
                v-if="data.nodeLevel!== '1'"
                class="el-icon-delete"
                @click.stop="deleteFn(node)"
              />
            </div>
          </span>
        </el-tree>
      </div>
      
      <div class="from">
        <div v-if="Primarynode">
          <el-form
            ref="rootForm"
            v-loading="echo"
            :rules="rootRules"
            label-position="top"
            :model="rootForm"
            label-width="80px"
          >
            <el-form-item
              label="产业链图标(只支持png格式)"
              prop="icon"
            >
              <div class="list">
                <div class="list-con">
                  <el-upload
                    class="avatar-uploader"
                    :action="chartApi.zstjUpload" 
                    :show-file-list="false"
                    :on-success="handleAvatarSuccess"
                    :before-upload="beforeAvatarUpload"
                    accept=".png"
                    :headers="{
                  token
                }"
                  >
                    <img
                      v-if="rootForm.icon"
                      :src="rootForm.icon"
                      class="avatar"
                    >
                    <i
                      v-else
                      class="el-icon-plus avatar-uploader-icon"
                    />
                  </el-upload>
                </div>
              </div>
            </el-form-item>
            <el-form-item
              label="产业链名称"
              prop="chainName"
            >
              <el-input
                v-model="rootForm.chainName"
                placeholder="请输入产业链节点名称"
              />
            </el-form-item>
            <!--             <el-form-item
              label="产业链简称"
              prop="formerName"
            >
              <el-input
                v-model="rootForm.formerName"
                placeholder="请输入产业链节点简称"
              />
            </el-form-item> -->
            <el-form-item>
              <el-button
                type="primary"
                @click="reset"
              >
                重置
              </el-button>
              <el-button
                v-loading="loading"
                @click="onSubmitstair"
              >
                保存
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-form
          v-else-if="isform==true"
          ref="form"
          v-loading="echo"
          :rules="rules"
          label-position="top"
          :model="form"
          label-width="80px"
        >
          <el-form-item
            label="节点名称"
            prop="nodeName"
          >
            <el-input
              v-model="form.nodeName"
              placeholder="请输入产业链节点名称"
            />
          </el-form-item>
          <el-form-item
            prop="nodeOrder"
            label="节点顺序"
          >
            <el-input
              v-model="form.nodeOrder"
              placeholder="请输入节点顺序"
            />
          </el-form-item>
          <el-form-item
            prop="isLeaf"
            label="节点类型"
          >
            <el-radio
              v-model="form.isLeaf"
              :disabled="nodecut"
              label="0"
            >
              目录节点
            </el-radio>
            <el-radio
              v-model="form.isLeaf"
              :disabled="nodecut"
              label="1"
            >
              挂载企业
            </el-radio>
          </el-form-item>
          <el-form-item
            v-if="form.isLeaf == '1'"
            prop="showMaxNumber"
            label="最大显示个数"
          >
            <el-input v-model="form.showMaxNumber" />
          </el-form-item>
          <el-form-item
            v-if="form.isLeaf == '1'"
            prop="dynamicTags"
            label="产业链标签"
            class="appendToBodyFalse"
          >
            <el-select
              v-model="labelRequests"
              :popper-append-to-body="false"
              class="select"
              multiple
              filterable
              placeholder="请选择"
            >
              <div class="addtag">
                <span>
                  <el-input
                    v-model="input"
                    placeholder="请输入标签名称"
                  />
                </span>
                <span><el-button
                  class="btn"
                  style="margin-left: 20px;"
                  @click="addtag"
                >添加</el-button></span>
              </div>   
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.labelName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="X坐标值"
            prop="abscissaValue"
          >
            <el-input
              v-model="form.abscissaValue"
              placeholder="请输入X坐标值"
            />
          </el-form-item>        
          <el-form-item
            label="Y坐标值"
            prop="ordinateValue"
          >
            <el-input
              v-model="form.ordinateValue"
              placeholder="请输入Y坐标值"
            />
          </el-form-item>
          <el-form-item
            prop="lineInfo"
            label="节点间线坐标json串"
          >
            <el-input v-model="form.lineInfo" />
          </el-form-item>
      
          <el-form-item>
            <el-button
              type="primary"
              @click="reset"
            >
              重置
            </el-button>
            <el-button
              v-loading="loading"
              @click="onSubmit"
            >
              保存
            </el-button>
          </el-form-item>
        </el-form>
        <p v-else>
          请选择或新增节点进行编辑
        </p>
      </div>
    </div>
    <addNodedialog
      v-if="dialogFormVisible"
      :dialog-form-visible.sync="dialogFormVisible"
      :chain-id="form.chainID"
      :node-level="form.nodeLevel"
      :node-parent="form.nodeParent"
      :addrefer="addrefer"
      @refresh="treeList()"
    />
  </div>
</template>

<script>
import addNodedialog from "./components/addNode.vue"
import { getPathId } from '@/utils/utils'

import {
  treeListAPI,
  nodeDetailsAPI,
  industrylabelAPI,
  deleteAPI,
  addTagAPI,
  addNode,
  updateindustryChainAPI,
  indetailAPI
} from "@/api/industryChain";
import { chartApi,} from '@/admin/apiUrl'
import { getToken } from "@/utils/auth"; // get token from cookie
export default {
  name: "ConfiGuration",
  components: {
    addNodedialog
  },
  data() {
    return {
      token:'',
      chartApi,
       actionUrl:"",
      Primarynode:false,//是否一级节点
      // 添加参考
      addrefer:{},
      nodecut:false,
      treeData:{},
      echo:false,
      // 添加标签
      input:'',
      // 添加节点弹层
      dialogFormVisible:false,
      // 提交loading
      loading:false,
      // 刚开始进页面不显示表单
      isform: false,
      rootForm:{
        chainName:"",
        formerName:'',
        icon:"",
        imageUrlFile:'',
      },
      rootId:'',
      rootRules:{
        chainName: [
          { required: true, message: "节点名称不能为空", trigger: "blur" },
          { max: 15, message: "节点名称应在十五字符以内", trigger: "blur" },
        ],
        icon: [
          { required: true, message: "产业链图标必填", trigger: "blur" },
        ],
        formerName: [
          { required: true, message: "节点简称必填", trigger: "blur" },
          { max: 15, message: "节点简称应在十五字符以内", trigger: "blur" },
        ],
      },
      // 产业链列表
      options: [],
      // 路由跳转传过来的id
      chainId: "",
      // 表单
      form: {},
      // 选中的标签列表
      labelRequests: [],
      // 树
      tagList: [],
      data: [],
      defaultProps: {
        children: "childNodes",
        label: "nodeName",
      },
      rules: {
        nodeName: [
          { required: true, message: "节点名称不能为空", trigger: "blur" },
          { max: 15, message: "节点名称应在十五字符以内", trigger: "blur" },
        ],
        icon: [
          { required: true, message: "产业链图标必填", trigger: "blur" },
        ],
        nodeOrder: [
          { required: true, message: "节点顺序不能为空", trigger: "blur" },
          {
            pattern: /^[+]{0,1}(\d+)$/,
            message: "节点顺序需为正整数",
            trigger: "blur",
          },
        ],
        isLeaf: [{ required: true, message: "节点类型必选", trigger: "blur" }],
        showMaxNumber: [
          { required: true, message: "最大显示个数必填", trigger: "blur" },
          {
            /* pattern: /^([1-9][0-9]{0,1}|100)$/,
            message: "最大显示个数需为正整数并且在1到100之间",
            trigger: "blur", */
            pattern: /^[+]{0,1}(\d+)$/,
            message: "最大显示个数需为正整数",
            trigger: "blur",
          },
        ],
        /* dynamicTags: [
          { required: true, message: "产业链标签必填", trigger: "blur" },
        ], */
        abscissaValue:[
        //{ required: true, message: "X坐标值必填", trigger: "blur" },
        {
            pattern: /(^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:.\d{1,2})?$)/,
            message: "坐标要求是数字且最多保留两位小数",
            trigger: "blur",
          }, 
        ],
        ordinateValue:[
        //{ required: true, message: "Y坐标值必填", trigger: "blur" },
        {
            pattern: /(^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:.\d{1,2})?$)/,
            message: "坐标要求是数字且最多保留两位小数",
            trigger: "blur",
          }, 
        ]     
      },
    };
  },
  created() {
    this.token =  getToken()
    this.actionUrl=localStorage.getItem('originPath')
    this.treeList();
  },
  methods: {
    beforeAvatarUpload (file) {
      const isJPG = file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 5;
      
       if (!isJPG) {
         this.$message.error('产业链图标只能是 png 格式!');
         return false
       }
      if (!isLt2M) {
        this.$message.error('产业链图标大小不能超过 5MB!');
        return false
      }
      return isLt2M;
      // return isJPG && isLt2M;
    },
    handleAvatarSuccess (res, file) {
      this.rootForm.icon = URL.createObjectURL(file.raw);
      this.rootForm.imageUrlFile = file.raw;
    },
    // 获取产业链列表
   async tagListAP() {
      const res = await industrylabelAPI()
      this.options =res.result
    },
    // 新增产业链标签
    async addtag() {
      let input = this.input;
      if (input) {
        await addTagAPI({
          labelName: this.input,
          chainId:this.$route.query.id|| getPathId()|| null,
        });
        this.tagListAP()
        this.$message.success("新增产业链标签成功");
      }else{
        this.$message.error("标签名称不能为空");
      }
      this.input = "";
    },
    // 获取树状图
    async treeList() {
      this.chainId =this.$route.query.id|| getPathId()|| null;
      const res = await treeListAPI({
        chainId: this.chainId,
        countEnterpriseNum:1,
      });
      this.data = [];
      this.data.push(res.result);
      this.tagListAP()
    },
    // 点击树状图
    async handleNodeClick(data) {
      this.rootId=''
      if(data.nodeLevel==1){
        this.rootForm.imageUrlFile=''
        this.Primarynode=true
        this.rootId=data.id
        this.treeData = data
        try {
          this.echo =true
          const res = await indetailAPI({
            chainId:this.chainId
          })
          this.rootForm = res.result;
        } finally{
          this.echo =false
        }
      }else{
      this.Primarynode=false
      this.treeData = data
      try {
        this.echo =true
        const res = await nodeDetailsAPI({
         id: data.id,
        });
      // 展示form 清空节点
      this.isform=true
      this.labelRequests=[]
      // 回显
      this.form = res.result;
      // 挂载节点的企业标签
      if(this.form.isLeaf == '1'){
        res.result.industryLabelDTOS.filter(item => {
        return  this.labelRequests.push(item.id)
      }) 
      }
      } finally {
        this.echo =false
      }
      }

    },
    // 提交
    async onSubmit() {
      try {
        this.loading =true
        this.form.labelIds = this.labelRequests;
        //this.form.industryLabelDTOS=''
        await this.$refs.form.validate();
        await addNode({
          chainId:this.form.chainId,
          nodeName:this.form.nodeName,
          //icon:this.form.icon,
          labelIds:this.form.labelIds,
          nodeLevel:this.form.nodeLevel,
          nodeOrder:this.form.nodeOrder,
          nodeParent:this.form.nodeParent,
          isLeaf:this.form.isLeaf,
          showMaxNumber:this.form.showMaxNumber,
          chainNodeId:this.form.id,  
          abscissaValue:this.form.abscissaValue,
          ordinateValue:this.form.ordinateValue,
          lineInfo:this.form.lineInfo,
        });
        this.$message.success("编辑产业链节点成功");
        this.isform=false
        this.Primarynode=false
        this.form = {};
        this.labelRequests=[]
        //this.data = []
        this.treeList();
      } finally {
        this.loading =false
      
      }
    },
    // 根节点提交
    async onSubmitstair(){
      try {
        await this.$refs.rootForm.validate();
        let formData = new FormData();
        this.loading =true
        formData.append('nodeId', this.rootId);
        formData.append('chainName', this.rootForm.chainName);
        formData.append('formerName', this.rootForm.formerName);
        if(this.rootForm.imageUrlFile){
          formData.append('icon', this.rootForm.imageUrlFile);
        }
        await updateindustryChainAPI(formData)
        this.$message.success("编辑产业链节点成功");
        this.isform=false
        this.Primarynode=false
        this.form = {};
        this.labelRequests=[]
        //this.data = []
        this.treeList();
      } finally{
        this.loading =false
      }

    },
    // 移入移出
    mouseenter(data) {
      this.$set(data, "show", true);
    },
    mouseleave(data) {
      this.$set(data, "show", false);
    },
    // 添加子节点
    add(node,data) {
      if(data.nodeLevel==4){
        return this.$message.error("最多只能添加三级节点")
      }
      this.dialogFormVisible=true
      this.form = {};
      this.addrefer=data
      this.form.chainID = this.$route.query.id|| getPathId()|| null;
      // 节点层级
      this.form.nodeLevel = +node.data.nodeLevel + 1;
      // 父节点id
      this.form.nodeParent = node.data.id;
    },
    // 删除节点
    async deleteFn(node) {
      this.$confirm("确认删除该节点吗?(该节点下的子节点将被一并删除)", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await deleteAPI({
          id: node.data.id,
        });
        // 刷新产业链
        this.treeList();
        this.$message({
          type: "success",
          message: "删除产业链节点成功!",
        });
      });
    },
    // 重置
    async reset() {
      this.handleNodeClick(this.treeData)
    },
  },
};
</script>

<style scoped lang="scss">
.nodeNames{
  white-space: nowrap;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}
::v-deep{
  .avatar-uploader .el-upload{
    background-color: #f5f5f5;
  }
  .el-tag.el-tag--info{
    color: #3370FF;
    background-color: #fff;
    border:0.4px solid #3370FF;
  }
  .el-select .el-tag__close.el-icon-close{
    background-color: #fff;
  }
}
.down-tree{
 height: 60rem;
 display: block;
 overflow-y: scroll;
 margin-right: 30px;
}
.btn{
  color: rgba(255,255,255,0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover{
 background: #1765AD;;
 color: rgba(255,255,255,0.85);
}
.btn:active{
 background: #52ABFF;
 color: rgba(255,255,255,0.85);
}
.btn:focus{
 background: #3370FF;
 color: rgba(255,255,255,0.85);
}
.addtag{
  display: flex;
  width: 100%;
  justify-content: space-between;
  .el-input{
    margin-left: 5%;
    width: 22rem;
  }
  .el-button{
    margin-right: 20px;
  }
}
.select{
  width: 400px;
}
.allocation {
  overflow-y: scroll;
  display: flex;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.tree {
  width: 260px;
  margin-top: 20px;
}
.from {
  width: 400px;
  margin-top: 30px;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 200px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>