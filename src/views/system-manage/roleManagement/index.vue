<template>
  <div
    ref="msMain"
    class="vue-ms-main vue-ms-main-sel"
  >
    <div v-if="isList==1">
      <table-layout
        :tab-name-list="['角色管理']"
      >
        <!-- 查询内容 -->
        <el-form
          slot="elForm"
          ref="params"
          label-width="82px"
          class="demo-form-inline"
          :model="staform"
        >
          <el-form-item label="角色">
            <el-select
              v-model="staform.roleId"
              clearable 
              placeholder="请选择"
            >
              <el-option
                label="全部"
                value=""
              />
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.roleName"
                :value="item.id"
              />
            </el-select>
          </el-form-item> 
          <el-form-item
            v-if="$store.getters.user.accountType == '1'"
            label="角色类型"
          >
            <el-select
              v-model="staform.type"
              placeholder="请选择"
              clearable 
            >
              <el-option
                v-for="item in roleType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item> 
          <el-form-item
            v-if="$store.getters.user.accountType == '1'"
            label="机构名称"
          >
            <el-autocomplete
              v-model="staform.orgName"
              class="inline-input"
              :fetch-suggestions="querySearch"
              :trigger-on-focus="false"
              placeholder="请输入内容"
            />
          </el-form-item>
          <el-form-item style="width: 500px;">
            <el-button
              v-loading="loading"
              class="btn"
              @click="search"
            >
              查询
            </el-button>
            <el-button
              class="btn"
              @click="reset"
            >
              重置
            </el-button>
            <el-button
              class="btn"
              @click="addMemberFn"
            >
              新增角色
            </el-button>
            <el-button
              v-if="$store.getters.user.accountType == '1'"
              class="btn"
              @click="authorityManagement"
            >
              权限管理
            </el-button>
          </el-form-item>
        </el-form>
        <div slot="selTable">
          <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
          >
            <el-table-column
              prop="roleName"
              align="center"
              label="角色"
              width="300"
            />
            <el-table-column
              prop="orgName"
              label="创建机构"
              align="center"
              width="200"
            />
            <el-table-column
              align="center"
              width="140"
              label="角色类型"
            > 
              <template slot-scope="{row}">
                {{ row.type | type }}
              </template>
            </el-table-column>
            <el-table-column
              prop="aclNum"
              align="center"
              label="功能权限"
              width="120"
            />
            <el-table-column
              prop="userNum"
              align="center"
              width="120"
              label="成员数"
            />
            <el-table-column
              align="center"
              label="操作"
            >
              <template slot-scope="{row}">
                <el-button
                  type="text"
                  @click="allocation(row)"
                >
                  编辑
                </el-button>
                <el-divider direction="vertical" />
                <el-button
                  type="text"
                  @click="addMembers(row.id)"
                >
                  添加成员
                </el-button>
                <el-divider direction="vertical" />
                <el-button
                  type="text"
                  @click="ischeckMemberFn(row.id)"
                >
                  查看成员
                </el-button>
                <el-divider direction="vertical" />
                <el-button
                  type="text"
                  @click="deleteFn(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </table-layout>
      <div class="ye">
        <el-pagination
          :current-page.sync="staform.pageNum"
          :page-sizes="[5, 10, 20, 50]"
          :page-size.sync="staform.pageSize"      
          :total="+total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="industryList"
          @current-change="industryList"
        />
      </div>
    </div>
    <div v-if="isList==2">
      <table-layout
        :tab-name-list="['角色管理']"
      />
      <jurisdiction @close="close" />
    </div>
    <addRole
      v-if="isDrawer"
      :is-drawer.sync="isDrawer"
      :compileindustry-id="compileindustryId"
      :echo-obj="echoObj"
      @industryList="industryList"
    />
    <addMember
      v-if="isMember"
      :is-member.sync="isMember"
      :compileindustry-id="compileindustryId"
      @industryList="industryList"
    />
    <checkMember
      v-if="ischeckMember"
      :ischeck-member.sync="ischeckMember"
      :compileindustry-id="compileindustryId"
      @addition="addition"
    />
  </div>
</template>

<script>
import addMember from  './components/addMember.vue'
import jurisdiction from './components/jurisdiction.vue' //权限管理
import addRole from './components/addRole.vue' //添加/编辑成员
import checkMember from './components/checkMember.vue'
import TableLayout from "@/common/components/table-layout";
import { rolesAPI ,rolesDeleteAPI,rolepageAPI} from "@/api/role";
import { autoSearchAPI } from "@/api/Settled";
export default {
 name:"RoleManagement",
 components: {
    TableLayout,
    addRole,
    jurisdiction,
    addMember,
    checkMember
  },
  filters:{
    type(value){
     if(value==1){
      return '运营端'
     }else if (value==2){
      return '用户端'
     }else {
      return ''
     }
    }
  },
  data(){
   return{
      isList:1,
      // 添加成员/编辑成员抽屉
      isDrawer:false,
      //添加角色抽屉
      isMember:false,
      // 查看抽屉
      ischeckMember:false,
      echoObj:{
        type:"",
        name:""
      },
      // 列表
      tableData:[
      ],
      // 表单提交
      staform:{
         pageNum:1,
         pageSize:10,
         roleId:"",
         type:"",
         orgName:""
      },
      // 总数
      total:0,
      // 编辑角色所需id
      compileindustryId:"",
      // 列表loading
      loading:false,
      // 按钮loading
      listLoading:false,
      // 机构名称列表
      organizationName:[],
      // 角色列表
      options:[],
      // 角色类型
      roleType:[
        {
         label:"全部",
         value:""
        },
        {
         label:"运营端",
         value:"1"
        },
        {
         label:"用户端",
         value:"2"
        }
      ]
   }
  },
  created(){
   this.industryList()
   this.roleList()
  },
  methods:{
    async querySearch(queryString, cb) {
      if (this.staform.orgName !== "") {
        const res = await autoSearchAPI({
          orgName: this.staform.orgName
        });
        var results = res.result.searchHits;
        let dataList = [];
        for (let i = 0; i <= results.length - 1; i++) {
          dataList[i] = {
            value: results[i].content.orgName,
          };
        }
        cb(dataList);
      }
    },
    authorityManagement(){
     this.isList=2
    },
    addition(value){
     this.compileindustryId=value
     this.ischeckMember=false
     this.isMember=true
    },
    // 编辑角色
    allocation(row){
    this.compileindustryId = row.id
    this.echoObj.type=row.type
    this.echoObj.name=row.roleName
    this.isDrawer=true
    },
    // 获取可选角色列表
    async roleList(){
     const res =  await rolesAPI()
     this.options=res.result
   },
   // 获取角色列表
  async industryList(){
    try {
      this.loading=true
      const res =  await rolepageAPI(
      this.staform
     )
     this.tableData=res.result.records
     this.total=res.result.total
    } catch (error) {
      console.log(error);
    } finally {
      this.loading=false
    }

   },
   close(value){
     this.isList =value
   },
   // 删除角色
   deleteFn(row){
    this.$confirm("确定删除该角色？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await rolesDeleteAPI({
          id: row.id,
        });
        // 刷新产业链
        this.staform.pageNum=1
        this.industryList();
        this.$message({
          type: "success",
          message: "删除角色成功!",
        });
      });
   },
   // 查询列表
   search(){
     this.staform.pageNum=1
     this.industryList()
   },
   // 重置列表
   reset(){
    this.staform={
      pageNum:1,
      pageSize:10,
      roleId:"",
      type:"",
      orgName:""
    }
    this.industryList()
   },
   // 添加角色
   addMemberFn(){
    this.isDrawer=true
    this.compileindustryId=null
   },
   // 添加成员
   addMembers(id){
   this.compileindustryId=id
   this.isMember=true
   },
   // 查看成员
   ischeckMemberFn(id){
    this.compileindustryId = id
    this.ischeckMember=true
   }
  }
}
</script>

<style scoped lang="scss">

  .ye {
  margin-bottom: 50px;
}
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #3370FF;
  color: rgba(255, 255, 255, 0.85);
}
</style>