<template>
  <div
    class="topTable"
  >
    <div class="table-con">
      <div class="table-head">
        <div
          style="padding-left: 10px;"
          class="li li2"
        >
          企业名称
        </div>
        <div
          v-if="liststate!==4"
          class="li li4"
        >
          所在环节
        </div>
        <div
          v-else
          class="li li4"
        >
          行业分类
        </div>
        <div class="li li4">
          注册资本
        </div>
        <div class="li li5">
          成立日期
        </div>
      </div>
      <div
        v-if="enterpriseList.length>0"
        class="table-body"
      >
        <div
          v-for="(item,i) in enterpriseList"
          :key="i"
          class="ul"
        >
          <div
            style="padding-left: 10px"
            :class="liststate!==4 ? 'custom' : ''"
            class="li li2"
            @click="goDetailsenterpriseDel(item.id)"
          >
            <div>
              <div class="txt1">
                {{ item.enterpriseName }}
              </div>
            </div>
          </div>
          <div
            v-if="liststate!==4"
            class="li li4"
          >
            <el-tooltip
              effect="dark"
              :visible-arrow="false"
              placement="top"
              :content="item.linkPlace"
            >
              <span
                style="max-height: 63px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;"
              >{{ item.linkPlace }}
              </span>
            </el-tooltip>
          </div>
          <div
            v-else
            class="li li4"
          >
            {{ item.nationalStandardIndustry }}
          </div>
          <div class="li li4">
            {{ item.registeredCapital }}
          </div>
          <div
            v-if="liststate==1 || liststate==2"
            class="li li5"
          >
            {{ item.registerDate }}
          </div>
          <div
            v-else
            class="li li5"
          >
            {{ item.registerDate }}
          </div>
        </div>
      </div>
      <div
        v-else
        class="table-body"
      >
        <div
          v-if="!enterpriselistLoading"
          class="nodata"
        >
          <div class="con">
            <img src="https://static.idicc.cn/cdn/pangu/vacancy.png">
            <span class="p1">暂无数据</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { formatDate } from '@/utils/utils'
export default {
  name: "EnterpriselistD",
  filters: {
      time: function(value) {
          return formatDate('yyyy-MM-dd', new Date(+value));
      },
    },
  props:{
    enterpriseList:{
      type:Array,
      default:()=>[]
    },
    liststate:{
      type: Number,
      default:null
    },
    enterpriselistLoading:{
      type: Boolean,
      default:false
    }
  },
  data() {
    return {};
  },
  methods:{
    goDetailsenterpriseDel(id){
      this.$emit('DetaiDel',id)
    }
  }

};
</script>

<style lang="scss" scoped>
.custom{
  cursor: pointer;
}
.nodata {
  width:100%;
  height: 315px;
  display: flex;
  justify-content: center;
  .con {
    margin-top: 5%;
    width: 170px;
    height: 200px;
    display: flex;
    align-items: center;
    flex-direction: column;
    img {
      width: 216px;
      height: 177px;
    }
    .p1 {
      margin-top: 38px;
      font-size: 14px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
    .p2 {
      margin-top: 16px;
      font-size: 12px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
  .topTable{
    background: #fff;
    padding-right: 12px;
    padding-bottom: 20px;
    border-radius: 10px;
    .title{
      line-height: 50px;
      padding-left: 24px;
      font-size: 16px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0,0,0,0.85);
      border-bottom: 1px solid #E9E9E9;
    }
    .table{
      &-con{
        padding-left: 12px;
        //padding-top: 24px;
        .li{
          box-sizing: border-box;
          display: flex;
          align-items: center;
        }
        .li1{
          width: 9.196428571428571%;
          padding-left: 16px;
        }
        .li2{
          // width: 29.107142857142858%;
          width: 35.107142857142858%;
        }
        .li3{
          width: 16.25%;
        }
        .li4{
          // width: 16.964285714285715%;
          width: 26.964285714285715%;
        }
        .li5{
          width: 16.16071428571429%;
        }
        .li6{width: 12.321428571428573%;}
      }
      &-head{
        height: 54px;
        background: linear-gradient(180deg, rgba(230, 237, 255,0.5) 0%,rgba(178, 201, 255,0.5) 100%);
        border-radius: 0rpx 0rpx 0rpx 0rpx;
        display: flex;
        align-content: center;
        font-size: 14px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        color: rgba(0,0,0,0.85);
        line-height: 22px;
      }
      &-body{
        height: 315px;
        .ul{
          // padding-top: 16px;
          border-bottom: 1px solid #E9E9E9;
          display: flex;
          .li{
            min-height: 63px;
            font-size: 14px;
            font-family: Source Han Sans CN-Normal, Source Han Sans CN;
            font-weight: 400;
            color: rgba(0,0,0,0.65);
            line-height: 22px;
            &.li2{
              flex-wrap: wrap;
              .txt1{
                width: 100%;
              }
              .tag{
                display: flex;
                width: 100%;
                &-list{
                  padding: 0 6px;
                  background: #FFF2E6;
                  font-size: 11px;
                  font-family: PingFang SC-Medium, PingFang SC;
                  font-weight: 500;
                  color: #FF7D00;
                  line-height: 18px;
                  margin-right: 8px;
                  border-radius: 2px;
                }
              }
            }
          }
        }
      }
    }
  }
</style>