<template>
  <div v-if="examine">
    <div v-if="showcontent">
      <div
        v-show="!enterprisedel"
        class="scan"
      >
        <div class="scan-top">
          <!--       <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">
          产业洞察
        </el-breadcrumb-item>
        <el-breadcrumb-item>产业速览</el-breadcrumb-item>
      </el-breadcrumb> -->
          <div class="scan-title">
            <span class="sp1">{{ defaultInfo.chainName }}产业链</span>
            <span class="sp2">[{{ defaultInfo.areaName }}]</span>
          </div>
          <div class="scan-tab-con">
            <div class="scan-tab">
              <div
                class="scan-tab-list"
                :class="tabCheck == 1 ? 'on' : ''"
                @click="tabEvent(1)"
              >
                产业企业
              </div>
              <div
                class="scan-tab-list"
                :class="tabCheck == 2 ? 'on' : ''"
                @click="tabEvent(2)"
              >
                产业创新
              </div>
              <!-- <div
                class="scan-tab-list"
                :class="tabCheck==3?'on':''"
                @click="tabEvent(3)"
              >
                产业投资
              </div> -->
              <div
                class="scan-tab-list"
                :class="tabCheck == 4 ? 'on' : ''"
                @click="tabEvent(4)"
              >
                产业排行
              </div>
            </div>
            <div class="scan-tab-select appendToBodyFalse">
              <el-cascader
                ref="cascader"
                :key="cascaderKey"
                v-model="checkArea"
                :options="areaList"
                size="mini"
                :props="regionProp"
                @change="changeEvent"
              />
              <el-select
                ref="chain"
                v-model="chainId"
                size="mini"
                :popper-append-to-body="false"
                placeholder="请选择"
                @change="changeEvent"
              >
                <el-option
                  v-for="item in optChain"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
        </div>
        <div class="scan-con">
          <Enterprise
            v-show="tabCheck == 1"
            ref="enterprise"
            :data-info="dataInfo"
            @digenterprise="digenterprise"
            @DetaiDel="DetaiDel"
          />
          <innovate
            v-if="tabCheck == 2"
            ref="innovate"
            @diginnovate="diginnovate"
          />
          <Invest
            v-if="tabCheck == 3"
            ref="invest"
            @digfinancing="digfinancing"
            @diggetexternal="diggetexternal"
            @diginvest="diginvest"
          />
          <Rank
            v-if="tabCheck == 4"
            ref="rank"
            :default-info="defaultInfo"
          />
        </div>
      </div>
      <div v-if="enterprisedel">
        <Detailsenterprise
          :enterprise-i-d="enterpriseID"
          :chain-id="chainId"
          :company-detial="companyDetial"
          @goback="goback"
        />
      </div>
      <el-dialog
        style="position: absolute"
        :title="title"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        width="75%"
        :before-close="handleClose"
      >
        <template slot="title">
          <div style="display: flex; align-items: center">
            <span class="headtitle">{{ title }}</span>
            <el-tooltip
              effect="dark"
              content="默认导出前5000条数据"
              placement="top"
            >
              <div
                v-loading="xzloading"
                class="export"
                @click="exportex"
              >
                <img
                  src="https://static.idicc.cn/cdn/pangu/interactive.png"
                  class="up"
                >导出数据
              </div>
            </el-tooltip>
          </div>
        </template>
        <enterpriselist
          v-loading="enterpriselistLoading"
          :enterpriselist-loading="enterpriselistLoading"
          :enterprise-list="enterpriselist"
          :liststate="liststate"
          @DetaiDel="DetaiDel"
        />
        <div class="ye">
          <span class="total">共<span class="to">{{ total }}</span>条数据</span>
          <el-pagination
            :current-page.sync="pageNum"
            :page-sizes="[5, 10, 20, 50]"
            background
            :page-size.sync="pageSize"
            :total="+total"
            layout=" prev, pager, next, jumper"
            @size-change="getEntrustList"
            @current-change="getEntrustList"
          />
        </div>
      </el-dialog>
    </div>
    <div
      v-else
      class="nodata"
    >
      <div class="con">
        <img src="https://static.idicc.cn/cdn/SSO/zhaoshangnodata.png">
        <span class="p1">暂无产业链权限</span>
        <span class="p2">请联系机构管理员配置权限功能</span>
      </div>
    </div>
    <custom-loading :is-show="loading" />
  </div>
</template>

<script>
import { findNodeByCode } from '@/utils/dateFormat';
import { homeListAPI } from '@/views/system-manage/attract-investment/apiUrl';
import CustomLoading from '@/components/loading.vue';
import Enterprise from './enterprise/index.vue';
import Invest from './invest/index.vue';
import innovate from './innovate/index.vue';
import enterpriselist from './enterpriselist.vue';
import Detailsenterprise from '@/views/system-manage/attract-investment/IntelligentSearch/components/Detailsenterprise.vue';
import Rank from './rank/index.vue';
import { getPathId } from '@/utils/utils';
import {
  listIndustryEnterprisedownloadAPI,
  listIndustryInnovateEnterprisedownloadAPI,
  investEnterprisePagedownloadAPI,
  investedEnterprisePagedownloadAPI,
  financingPagedownloadAPI,
} from '@/api/export';
import {
  industryEnterprise,
  defaultUserAuthInfo,
  industryInnovate,
  overview,
  getAllAddress,
  listIndustryEnterpriseAPI,
  listIndustryInnovateEnterpriseAPI,
  financingPageAPI,
  InvestEnterprisePageAPI,
  InvestedEnterprisePageAPI,
} from './../idicc-scan/apiUrl';
export default {
  name: 'IdiccScan',
  components: {
    Rank,
    Invest,
    Enterprise,
    innovate,
    CustomLoading,
    enterpriselist,
    Detailsenterprise,
  },
  data() {
    return {
      defaultInfo: {
        chainName: '',
        areaName: '',
      },
      cascaderKey: 0,
      regionProp: {
        checkStrictly: true,
      },
      areaList: [],
      dialogVisible: false,
      pageNum: 1,
      pageSize: 5,
      liststate: null,
      is: true,
      title: '',
      enterpriselist: [],
      total: 0,
      optChain: [],
      checkArea: [], // 选择地区
      checkAreaId: [], // 选择地区Id
      chainId: '', // 选择产业链

      tabCheck: 1, //

      dataInfo: {},
      examine: false,
      showcontent: false,
      loading: false,
      enterpriselistLoading: false,
      enterpriseID: '',
      enterprisedel: false,
      scrollTop: 0,
      scrollTopecho: false,
      xzloading: false,
      companyDetial: {},
    };
  },
  watch: {
    dialogVisible(e) {
      if (e) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = 'auto';
      }
    },
  },
  mounted() {
    this.initializeData();
  },
  methods: {
    // 优化点1: 合并初始化数据获取
    async initializeData() {
      try {
        // 同时检查权限和获取产业链列表
        const res = await homeListAPI();

        if (res.result == null || res.result.length < 1) {
          this.showcontent = false;
        } else {
          this.loading = true;
          document.body.style.overflow = 'hidden';
          this.showcontent = true;
          console.log(res.result, 'res.result');
          // 并行获取地址数据和产业链数据
          await Promise.all([this.getAllAddressData(res.result)]);

          this.cascaderKey = +new Date();
        }
      } catch (error) {
        console.error('初始化数据失败:', error);
      } finally {
        this.examine = true;
      }
    },

    // 优化点1: 合并产业链列表获取和初始化
    async getChainListAndInit(res) {
      console.log(res, 'rewd');
      try {
        if (!res || res.length == 0) {
          localStorage.noData = 0;
          return;
        } else {
          localStorage.noData = 1;
        }

        // 处理产业链数据
        this.optChain = res.map((e) => ({
          value: e.industryChainId,
          label: e.chainName.replace('产业金脑·', ''),
          id: e.id,
        }));
        // 获取用户权限信息并初始化
        this.getDefaultUserAuthInfo();
      } catch (error) {
        console.error('获取产业链列表失败:', error);
        setTimeout(() => {
          this.loading = false;
          document.body.style.overflow = 'auto';
        }, 1000);
      }
    },

    // 优化点2: 智能设置chainId默认值
    setSelectedChainId(id) {
      // localStorage获取默认产业链ID
      const redirectId = getPathId();
      if (redirectId && redirectId !== 'null') {
        // 如果有路由参数，尝试获取对应的chainId，设置为选中的产业链
        // 检查这个chainId是否在optChain中存在
        const foundChain = this.optChain.find((item) => item.id === redirectId);
        if (foundChain) {
          this.chainId = foundChain.value;
        } else {
          // 如果不存在，使用第一个可用的选项
          this.setDefaultChainId(id);
        }
      } else {
        console.log('没有路由参数');
        this.setDefaultChainId(id);
      }
    },
    setDefaultChainId(chainId) {
      // 如果默认用户信息中有chainId且在列表中存在，则使用它
      if (chainId && this.optChain.find((item) => item.value === chainId)) {
        this.chainId = chainId;
      } else {
        // 否则使用第一个可用选项
        this.chainId = this.optChain.length > 0 ? this.optChain[0].value : '';
      }
    },
    // 简化用户权限信息获取
    getDefaultUserAuthInfo() {
      defaultUserAuthInfo()
        .then((res) => {
          // 设置地区选择
          this.checkArea = [res.provinceCode];
          res.cityCode && (this.checkArea = [res.provinceCode, res.cityCode]);
          res.areaCode &&
            (this.checkArea = [res.provinceCode, res.cityCode, res.areaCode]);
          this.cascaderKey = +new Date();

          // 优化点2: 智能设置chainId默认值
          this.setSelectedChainId(res.chainId);

          this.init();

          setTimeout(() => {
            this.loading = false;
            document.body.style.overflow = 'auto';
          }, 1000);
        })
        .catch((error) => {
          console.error('获取用户权限信息失败:', error);
          this.loading = false;
          document.body.style.overflow = 'auto';
        });
    },

    // 优化后的地址数据获取方法
    async getAllAddressData(chainListData) {
      try {
        const addressResponse = await getAllAddress();
        this.areaList = this.buildAddressHierarchy(addressResponse);
        console.log(this.areaList, 'this.areaList');
        await this.getChainListAndInit(chainListData);
      } catch (error) {
        console.error('获取地址数据失败:', error);
        throw error;
      }
    },
    // 数据分类 - 一次遍历完成分类
    categorizeAddressData(addressData) {
      const provinces = [];
      const cities = [];
      const areas = [];
      // console.log(addressData, 'addressData');
      addressData.forEach((item) => {
        // 统一数据格式
        const formattedItem = { ...item };

        if (!item.city && !item.area && item.province) {
          // 省级数据
          formattedItem.label = item.province;
          formattedItem.value = item.code;
          formattedItem.children = [];
          provinces.push(formattedItem);
        } else if (item.city && item.province && !item.area) {
          // 市级数据
          formattedItem.label = item.city;
          formattedItem.value = item.code;
          formattedItem.children = [];
          cities.push(formattedItem);
        } else if (item.area && item.city && item.province) {
          // 区/县级数据
          formattedItem.label = item.area;
          formattedItem.value = item.code;
          areas.push(formattedItem);
        }
      });
      //  console.log(provinces,  cities, areas ,'provinces');
      return { provinces, cities, areas };
    },
    // 构建地址层级结构
    buildAddressHierarchy(addressData) {
      // 1. 数据分类 - 使用更高效的分类方式
      const { provinces, cities, areas } =
        this.categorizeAddressData(addressData);

      // 2. 构建层级关系
      this.attachCitiesToProvinces(provinces, cities);
      this.attachAreasToCities(cities, areas);

      return provinces;
    },

    // 将城市关联到省份
    attachCitiesToProvinces(provinces, cities) {
      // 创建省份映射以提高查找效率
      const provinceMap = new Map();
      provinces.forEach((province) => {
        provinceMap.set(province.id, province);
      });

      cities.forEach((city) => {
        const parentProvince = provinceMap.get(city.parentId);
        if (parentProvince) {
          parentProvince.children.push(city);
        }
      });
    },

    // 将区县关联到城市
    attachAreasToCities(cities, areas) {
      // 创建城市映射以提高查找效率
      const cityMap = new Map();
      cities.forEach((city) => {
        cityMap.set(city.id, city);
      });

      areas.forEach((area) => {
        const parentCity = cityMap.get(area.parentId);
        if (parentCity) {
          parentCity.children.push(area);
        }
      });
    },

    async exportex() {
      if (this.xzloading) {
        return this.$message.warning('正在导出中，请耐心等待');
      }
      if (this.total == 0) {
        return this.$message.warning('这里还什么都没有~');
      }
      let data = this.getParam();
      let data3 = {
        chainId: data.industryId,
        code: data.regionCode,
        pageNum: 1,
        pageSize: 5000,
      };
      try {
        this.xzloading = true;
        let res = {};
        if (this.liststate == 1) {
          let data1 = {
            chainId: data.industryId,
            regionCode: data.regionCode,
            addThisYear: this.is,
            pageNum: 1,
            pageSize: 5000,
          };
          res = await listIndustryEnterprisedownloadAPI(data1); //产业企业
        } else if (this.liststate == 2) {
          let data2 = {
            chainId: data.industryId,
            regionCode: data.regionCode,
            allChildAreaCode: data.allChildAreaCode,
            addThisYear: this.is,
            pageNum: 1,
            pageSize: 5000,
          };
          res = await listIndustryInnovateEnterprisedownloadAPI(data2); //产业创新
        } else if (this.liststate == 3) {
          res = await investEnterprisePagedownloadAPI(data3); //对外投资
        } else if (this.liststate == 4) {
          res = await investedEnterprisePagedownloadAPI(data3); //投向企业
        } else if (this.liststate == 5) {
          res = await financingPagedownloadAPI(data3); //融资企业
        }
        if (res.msg) {
          return this.$message.error(res.msg);
        }
        let blob = new Blob([res], {
          type: 'text/csv,charset=UTF-8',
        });
        let objectUrl = URL.createObjectURL(blob);
        const fileName = `企业列表.xlsx`;
        const downloadLink = document.createElement('a');
        downloadLink.href = objectUrl;
        downloadLink.download = fileName;
        downloadLink.click();
      } finally {
        this.xzloading = false;
      }
    },
    handleClose() {
      (this.pageNum = 1), (this.pageSize = 5), (this.total = 0);
      this.enterpriselist = [];
      this.dialogVisible = false;
    },
    goback() {
      this.enterprisedel = false;
      if (this.scrollTopecho) {
        this.$nextTick(() => {
          document.body.scrollTop = this.scrollTop;
        });
      } else {
        this.dialogVisible = true;
      }
    },
    DetaiDel(id, top) {
      if (this.liststate == 4) {
        return;
      }
      this.enterpriseID = id;
      this.enterprisedel = true;
      this.dialogVisible = false;
      if (top) {
        this.liststate == 66;
        this.scrollTop = document.body.scrollTop;
        console.log(document.body.scrollTop, 'document.body.scrollTop');
        this.scrollTopecho = true;
      } else {
        this.scrollTopecho = false;
      }
    },
    //分页
    getEntrustList() {
      if (this.liststate == 1) {
        this.digenterpriseFn(this.is);
      } else if (this.liststate == 2) {
        this.diginnovateFn(this.is);
      } else if (this.liststate == 3) {
        this.diggetexternalFn();
      } else if (this.liststate == 4) {
        this.diginvestFn();
      } else if (this.liststate == 5) {
        this.digfinancingFn();
      }
    },
    //企业列表
    async digenterprise(is) {
      this.is = is;
      this.dialogVisible = true;
      this.title = is ? '今年新增企业' : '企业列表';
      this.liststate = 1;
      this.digenterpriseFn(is);
    },
    async digenterpriseFn(is) {
      try {
        this.enterpriselistLoading = true;
        let data = this.getParam();
        const res = await listIndustryEnterpriseAPI({
          chainId: data.industryId,
          regionCode: data.regionCode,
          addThisYear: is,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        });
        this.enterpriselist = res.records;
        this.total = res.total;
      } finally {
        this.enterpriselistLoading = false;
      }
    },
    //专利企业列表
    async diginnovate(is) {
      this.is = is;
      this.dialogVisible = true;
      this.title = is ? '今年累计新增专利企业' : '拥有专利企业';
      this.liststate = 2;
      this.diginnovateFn(is);
    },
    async diginnovateFn(is) {
      try {
        this.enterpriselistLoading = true;
        let data = this.getParam();
        const res = await listIndustryInnovateEnterpriseAPI({
          chainId: data.industryId,
          regionCode: data.regionCode,
          allChildAreaCode: data.allChildAreaCode,
          yearAdd: is,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        });
        this.enterpriselist = res.records;
        this.total = res.total;
      } finally {
        this.enterpriselistLoading = false;
      }
    },
    //产业融资企业列表
    async digfinancing() {
      this.dialogVisible = true;
      this.title = '融资企业';
      this.liststate = 5;
      this.digfinancingFn();
    },
    async digfinancingFn() {
      try {
        this.enterpriselistLoading = true;
        let data = this.getParam();
        const res = await financingPageAPI({
          chainId: data.industryId,
          code: data.regionCode,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        });
        this.enterpriselist = res.records;
        this.total = res.total;
      } finally {
        this.enterpriselistLoading = false;
      }
    },
    // 对外投资企业列表
    async diggetexternal() {
      this.dialogVisible = true;
      this.title = '对外投资企业';
      this.liststate = 3;
      this.diggetexternalFn();
    },
    async diggetexternalFn() {
      try {
        this.enterpriselistLoading = true;
        let data = this.getParam();
        const res = await InvestEnterprisePageAPI({
          chainId: data.industryId,
          code: data.regionCode,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        });
        this.enterpriselist = res.records;
        this.total = res.total;
      } finally {
        this.enterpriselistLoading = false;
      }
    },
    // 被投资企业列表
    async diginvest() {
      this.dialogVisible = true;
      this.title = '投向企业';
      this.liststate = 4;
      this.diginvestFn();
    },
    async diginvestFn() {
      try {
        this.enterpriselistLoading = true;
        let data = this.getParam();
        const res = await InvestedEnterprisePageAPI({
          chainId: data.industryId,
          code: data.regionCode,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        });
        this.enterpriselist = res.records;
        this.total = res.total;
      } finally {
        this.enterpriselistLoading = false;
      }
    },
    tabEvent(val) {
      if (localStorage.noData == 0) {
        //this.$message.error('暂无内容，请联系机构管理员配置功能权限')
        return false;
      }
      this.tabCheck = val;
      this.init();
    },
    updateName() {
      let defaultChain = this.optChain.find(
        (item) => item.value === this.chainId
      );
      this.defaultInfo.chainName = defaultChain.label;
      let arrVal = this.$refs.cascader.$children[0].getInput().value.split('/');
      this.defaultInfo.areaName = arrVal[arrVal.length - 1];
    },

    async changeEvent() {
      console.log('changeEvent', this.areaList);
      this.init();
    },
    init() {
      this.loading = true;
      if (this.tabCheck == 1) {
        this.industryEnterprise();
      } else if (this.tabCheck == 2) {
        this.industryInnovate();
      } else if (this.tabCheck == 3) {
        this.overview();
      } else if (this.tabCheck == 4) {
        this.$nextTick(() => {
          let data = {
            checkArea: this.checkArea,
            chainId: this.chainId,
            divisionLevel: this.checkArea.length,
          };
          this.$refs.rank.init(data);
        });
      }
      let that = this;
      setTimeout(() => {
        that.updateName();
        this.loading = false;
      }, 800);
    },
    // tab栏默认选中
    defaultUserAuthInfo() {
      defaultUserAuthInfo().then((res) => {
        this.checkArea = [res.provinceCode];
        res.cityCode && (this.checkArea = [res.provinceCode, res.cityCode]);
        res.areaCode &&
          (this.checkArea = [res.provinceCode, res.cityCode, res.areaCode]);
        this.cascaderKey = +new Date();
        // 如果用户权限中有chainId且在列表中存在，则使用它
        if (
          res.chainId &&
          this.optChain.find((item) => item.value === res.chainId)
        ) {
          this.chainId = res.chainId;
        }
        // 否则保持之前setDefaultChainId中设置的值

        this.init();

        setTimeout(() => {
          this.loading = false;
          document.body.style.overflow = 'auto';
        }, 1000);
      });
    },

    getParam() {
      let data = {
        // regionCode: "330000", // 区域code
        // divisionLevel: 1, // 区域类型 1省 2市 3区
        industryId: this.chainId, // 产业id
        allChildAreaCode: [],
      };
      if (this.checkArea) {
        if (this.checkArea[2]) {
          data.divisionLevel = 3;
          data.regionCode = this.checkArea[2];
        } else if (this.checkArea[1]) {
          data.divisionLevel = 2;
          data.regionCode = this.checkArea[1];
        } else if (this.checkArea[0]) {
          data.divisionLevel = 1;
          data.regionCode = this.checkArea[0];
        }
      }
      if (data.regionCode) {
        let result = findNodeByCode(this.areaList, data.regionCode);
        data.allChildAreaCode = result;
        data.allChildAreaCode?.push(data.regionCode);
      }
      return data;
    },
    getParam2() {
      let data = {
        // regionCode: "330000", // 区域code
        // divisionLevel: 1, // 区域类型 1省 2市 3区
        industryId: this.chainId, // 产业id
      };
      if (this.checkArea) {
        if (this.checkArea[2]) {
          data.divisionLevel = 3;
          data.regionCode = this.checkArea[2];
        } else if (this.checkArea[1]) {
          data.divisionLevel = 2;
          data.regionCode = this.checkArea[1];
        } else if (this.checkArea[0]) {
          data.divisionLevel = 1;
          data.regionCode = this.checkArea[0];
        }
      }
      return data;
    },
    // 产业企业
    industryEnterprise() {
      let data = this.getParam();
      industryEnterprise(data).then(async (res) => {
        this.dataInfo = res;
        let mapName = res.mapName;
        data.mapName = mapName;
        data.checkArea = this.checkArea;
        this.$refs?.enterprise.init(res, data);
      });
    },
    // 产业创新
    industryInnovate() {
      let data = this.getParam();
      industryInnovate(data).then((res) => {
        this.$refs.innovate.init(res, data);
      });
    },
    // 产业投资信息总览
    overview() {
      let data = this.getParam2();
      data.industryChainId = data.industryId;
      overview(data).then((res) => {
        res && this.$refs.invest.init(res, data);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.total {
  margin-left: 12px;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #86909c;
  .to {
    color: #3370ff;
    padding: 0px 5px;
  }
}
.ye {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep {
  .el-pagination {
    width: 65% !important;
    height: 50px;
    padding-top: 10px;
  }
}
.headtitle {
  font-size: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #1d2129;
  margin-left: 12px;
}
.export {
  cursor: pointer;
  margin-left: 16px;
  width: 82px;
  height: 24px;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px solid #ced4db;
  font-size: 12px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #3f4a59;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  .up {
    width: 14px;
    height: 14px;
    margin-right: 5px;
  }
}
::v-deep {
  .el-dialog {
    border-radius: 10px;
  }
  .el-dialog__header {
    background: #fff !important;
    border-bottom: 1px solid #e9e9e9 !important;
  }
  .el-dialog__title {
    font-size: 16px !important;
  }
}

.nodata {
  min-width: 88.5vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  .con {
    margin-top: 10%;
    width: 170px;
    height: 300px;
    display: flex;
    align-items: center;
    flex-direction: column;
    img {
      width: 160px;
      height: 165.03px;
    }
    .p1 {
      margin-top: 38px;
      font-size: 14px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
    .p2 {
      margin-top: 16px;
      font-size: 12px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
.scan {
  // background: #fafcff;
  min-height: 100vh;
  box-sizing: border-box;
  min-width: 1110px;
  padding-bottom: 50px;
  &-top {
    padding: 16px 0px;
    // background: #fff;
    padding-bottom: 12px;
  }
  &-title {
    //padding-top: 16px;
    padding-left: 16px;
    padding-bottom: 31px;
    display: flex;
    .sp1 {
      font-size: 20px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      line-height: 28px;
      margin-right: 8px;
    }
    .sp2 {
      font-size: 14px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.45);
      line-height: 28px;
    }
  }
  &-tab {
    display: flex;
    padding-left: 16px;
    &-con {
      display: flex;
      justify-content: space-between;
    }
    &-select {
      display: flex;
      .el-select {
        margin-left: 8px;
      }
    }
    &-list {
      margin-right: 64px;
      font-size: 16px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      cursor: pointer;
      &.on {
        font-weight: bold;
        color: #3370ff;
        position: relative;
        &::after {
          content: '';
          width: 28px;
          height: 2px;
          background: #3370ff;
          position: absolute;
          left: 18px;
          bottom: -12px;
        }
      }
    }
  }
  // &-con{
  //   margin: 16px 24px;

  // }
}
.scan-con {
  margin: 16px 0;
}
</style>
