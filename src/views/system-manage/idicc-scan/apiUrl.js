/*
 * @Author: jhy
 * @Date: 2023-05-22 09:49:39
 * @LastEditTime: 2023-06-05 17:59:01
 * @LastEditors: jhy
 * @Description: 
 * @FilePath: /QT-SASS-PC/src/views/system-manage/idicc-scan/apiUrl.js
 */
import axios from 'axios';
import store from '@/store'
import interfaceRequest from '@/utils/interfaceRequest'
import { getToken } from "@/utils/auth"; // get token from cookie

// import requestOther from '@/utils/requestOther'
// import axios from 'axios';
import { apiUrl,
  // cityJsonUrl 
} from '@/api/user'
// import store from '@/store'
// import { download } from '@/utils';
const apiDomain = apiUrl;

/**
 * 大屏
 */
export const chartApi = {
    mapRelation:apiDomain+'/admin/orgIndustryChainRelation/map', // 地图列表
    chainRelationGeo:apiDomain+'/admin/orgIndustryChainRelation/geo/data', // 地图 - 产业链节点数据
    defaultUserAuthInfo: '/dpar/industryOverview/defaultUserAuthInfo', // 产业速览-右上角-获取有权限的产业链和最小辖区范围接口
    loadByOrgId: '/admin/industryChainNode/tree',  // 根据机构产业链关系id查询产业链节点树
    orgChainList: '/admin/orgIndustryChainRelation/list', // 有权限的产业链列表
    getAllAddress: '/admin/administrativeDivision/getAll', // 获取所有的行政区划数据
    industryChainGetAll: '/admin/industryChain/getAll', // 
    

    region: '/admin/administrativeDivision/list', // 省市区
    linkPlaceList: '/admin/industryChain/linkPlaceList', // 产业环节下拉框

    industryEnterprise: '/dpar/industryOverview/industryEnterprise', // 产业企业
    industryInnovate: '/dpar/industryOverview/industryInnovate', // 产业创新
    overview: '/dpar/industryInvestment/overview', // 产业投资信息总览
    getForeignInvestmentEnterpriseTop: '/dpar/industryInvestment/getForeignInvestmentEnterpriseTop', // 对外投资企业细分环节top5
    getInvestedEnterpriseTop: '/dpar/industryInvestment/getInvestedEnterpriseTop', // 被投资企业细分环节top5
    getDimension: '/dpar/industryRanking/getDimension', // 排行榜维度
    rankingResult: '/dpar/industryRanking/rankingResult', // 获取产业排行

    investmentTop5: '/dpar/industryOverview/industryInnovate/investmentTop5', // 产业企业研发投入总额top5


    regionalComparison: '/dpar/industryAnalysis/regionalComparison', // 地区对比

    industryNodeAttribute: '/dpar/industryAnalysis/industryNodeAttribute', // 强链补链延链分析

    industryReport: '/dpar/industryReport/page', // 分页查询产业报告
    industryReportView: '/dpar/industryReport/view', // 报告预览
    industryReportUpload: '/dpar/industryReport/upload', // 上传报告

}


// 城市 - json 数据
export function getCityJson(code, areaType,cityData) {
  const cityUrl = apiUrl + chartApi.chainRelationGeo
  let params = ''
  if (code == 110100) {
    params = `110000_full`;
  } else if (code == 310100) {
    params = `310000_full`;
  } else if (code == 120100) {
    params = `120000_full`;
  } else if (code == 500100) {
    params = `500000_full`;
  } else {
    // 台湾
    if (code == 710000){
      params = `${code}`;
      // 东莞
    }else if(code == 441900){
      params = `${code}`;
    }
    else if (areaType === '1' || areaType === '2' || ['110100'].includes(code)) {
      params = `${code}_full`;
    } else if(areaType === '3' && cityData.length==3){
      params = `${code}_full`;
    }
      else {
      params = `${code}`;
    }
  }
  return interfaceRequest({
    url: cityUrl,
    params: { code: params },
    method: 'GET',
  })
}

export function getMapRelation(data) {
    return interfaceRequest({
        url:apiUrl+chartApi.mapRelation,
        method:'POST',
        data
    })
}
export function getRegion(data) {
    return interfaceRequest({
        url:apiUrl+chartApi.region,
        method:'get',
        params: { ...data }
    })
}
export function linkPlaceList(data) {
    return interfaceRequest({
        url:apiUrl+chartApi.linkPlaceList,
        method:'get',
        params: { ...data }
    })
}

export function industryEnterprise(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.industryEnterprise,
      method:'POST',
      data
  })
}
// 产业投资信息总览
export function overview(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.overview,
      method:'get',
      params: { ...data }
  })
}
// 产业速览-右上角-获取有权限的产业链和最小辖区范围接口
export function defaultUserAuthInfo(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.defaultUserAuthInfo,
      method:'get',
      params: { ...data }
  })
}
// 产业创新
export function industryInnovate(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.industryInnovate,
      method:'POST',
      data
  })
}
// 产业企业研发投入总额top5
export function investmentTop5(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.investmentTop5,
      method:'POST',
      data
  })
}
// // 对外投资企业细分环节top5
export function getForeignInvestmentEnterpriseTop(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.getForeignInvestmentEnterpriseTop,
      method:'get',
      params: { ...data }
  })
}
// // 对外投资企业细分环节top5
export function getInvestedEnterpriseTop(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.getInvestedEnterpriseTop,
      method:'get',
      params: { ...data }
  })
}
// 排行榜维度
export function getDimension(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.getDimension,
      method:'get',
      params: { ...data }
  })
}
// 获取产业排行
export function rankingResult(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.rankingResult,
      method:'POST',
      data
  })
}

// 地区对比
export function regionalComparison(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.regionalComparison,
      method:'POST',
      data
  })
}
// 根据机构产业链关系id查询产业链节点树
export function loadByOrgId(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.loadByOrgId,
      method:'get',
      params: { ...data }
  })
}

export function orgChainList(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.orgChainList,
      method:'POST',
      data
  })
}
// 获取所有的行政区划数据
export function getAllAddress(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.getAllAddress,
      method:'get',
      params: { ...data }
  })
}
// 强链补链延链分析
export function industryNodeAttribute(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.industryNodeAttribute,
      method:'get',
      params: { ...data }
  })
}
// 分页查询产业报告
export function industryReport(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.industryReport,
      method:'POST',
      data
  })
}
// 报告预览
export function industryReportView(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.industryReportView,
      method:'get',
      params: { ...data }
  })
}
// 上传报告
export function industryReportUpload(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.industryReportUpload,
      method:'POST',
      data
  })
}

// 文件下载
export function downloadExcelModel(dUrl) {
  return axios({
    method: 'get',
    url: dUrl,
    headers: {
      'Content-Type': 'application/json',
      'token': getToken()
      // store.getters.user.token
    },
    // data: data, // 参数
    responseType: 'blob' || '',
  })
}


export function industryChainGetAll(data) {
  return interfaceRequest({
      url:apiUrl+chartApi.industryChainGetAll,
      method:'get',
      params: { ...data }
  })
}
/**
 * 产业企业列表
 * @param {*} data 
 * @returns 
 */
export function listIndustryEnterpriseAPI(data) {
  return interfaceRequest({
      url: '/dpar/industryOverview/listIndustryEnterprise',
      method:'POST',
      data
  })
}
/**
 * 产业创新列表
 * @param {*} data 
 * @returns 
 */
export function listIndustryInnovateEnterpriseAPI(data) {
  return interfaceRequest({
      url: '/dpar/industryOverview/listIndustryInnovateEnterprise',
      method:'POST',
      data
  })
}
/**
 * 融资企业列表
 * @param {*} data 
 * @returns 
 */
export function financingPageAPI(data) {
  return interfaceRequest({
      url: '/dpar/industryInvestment/financingPage',
      method:'POST',
      data
  })
}
/**
 * 对外投资企业分页
 * @param {*} data 
 * @returns 
 */
export function InvestEnterprisePageAPI(data) {
  return interfaceRequest({
      url: '/dpar/industryInvestment/InvestEnterprisePage',
      method:'POST',
      data
  })
}
/**
 * 被投资企业分页
 * @param {*} data 
 * @returns 
 */
export function InvestedEnterprisePageAPI(data) {
  return interfaceRequest({
      url: '/dpar/industryInvestment/InvestedEnterprisePage',
      method:'POST',
      data
  })
}