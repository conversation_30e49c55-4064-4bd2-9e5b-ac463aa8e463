<template>
  <div
    id="flylineId"
    class="flyline"
  >
    <dv-flyline-chart-enhanced
      v-if="config"
      :config="config"
      :dev="true"
      :style="obj"
    />
  </div>
</template>

<script>
  export default {
    name: 'Flyline<PERSON>hart',
    props: {
      arr: {
        type: Array,
        default: ()=>{
          return []
        }
      },
    },
    data() {
      return {
        config: null,
        obj: {
          width: '',
          height: ''
        }
      }
    },
    watch: {
      arr(val) {
        //console.log('val', val);
        let config = {
          points: [
            {
              name: '北京市',
              coordinate: [0.70, 0.33],
            },
            {
                name: '新疆维吾尔自治区',
                coordinate: [0.24, 0.25]
            },
            {
                name: '西藏自治区',
                coordinate: [0.28, 0.53]
            },
            {
                name: '云南省',
                coordinate: [0.48, 0.63]
            },
            {
                name: '四川省',
                coordinate: [0.50, 0.52]
            },
            {
                name: '青海省',
                coordinate: [0.38, 0.41]
            },
            {
                name: '甘肃省',
                coordinate: [0.37, 0.32]
            },
            {
                name: '宁夏回族自治区',
                coordinate: [0.53, 0.39]
            },
            {
                name: '内蒙古自治区',
                coordinate: [0.62, 0.29]
            },
            {
                name: '重庆市',
                coordinate: [0.56, 0.54]
            },
            {
                name: '陕西省',
                coordinate: [0.57, 0.44]
            },
            {
                name: '山西',
                coordinate: [0.64, 0.37]
            },
            {
                name: '黑龙江省',
                coordinate: [0.89, 0.15]
            },
            {
                name: '吉林省',
                coordinate: [0.86, 0.25]
            },
            {
                name: '辽宁省',
                coordinate: [0.81, 0.30]
            },
            {
                name: '河北省',
                coordinate: [0.69, 0.36]
            },
            {
                name: '山东省',
                coordinate: [0.72, 0.40]
            },
            {
                name: '河南省',
                coordinate: [0.65, 0.45]
            },
            {
                name: '湖北省',
                coordinate: [0.63, 0.52]
            },
            {
                name: '江苏省',
                coordinate: [0.76, 0.48]
            },
            {
                name: '上海市',
                coordinate: [0.79, 0.52]
            },
            {
                name: '浙江省',
                coordinate: [0.76, 0.55]
            },
            {
                name: '安徽省',
                coordinate: [0.71, 0.50]
            },
            {
                name: '福建省',
                coordinate: [0.72, 0.61]
            },
            {
                name: '江西省',
                coordinate: [0.69, 0.57]
            },
            {
                name: '湖南省',
                coordinate: [0.62, 0.57]
            },
            {
                name: '贵州省',
                coordinate: [0.55, 0.59]
            },
            {
                name: '广西壮族自治区',
                coordinate: [0.58, 0.65]
            },
            {
                name: '广东省',
                coordinate: [0.65, 0.65]
            },
            {
                name: '海南省',
                coordinate: [0.59, 0.73]
            },
            {
                name: '台湾省',
                coordinate: [0.77, 0.65]
            },
            {
                name: '澳门特别行政区',
                coordinate: [0.64, 0.69]
            },
            {
                name: '香港特别行政区',
                coordinate: [0.67, 0.68]
            },
            {
                name: '天津市',
                coordinate: [0.71, 0.35]
            }
          ],
          lines: [
            {
              source: '广西省',
              target: '北京市',
              color: '#37a2da'
            },
          ],
          bgImgSrc: 'https://static.idicc.cn/cdn/pangu/china.png',
        };
        let lines = [];
        val.map(e=>{
          lines.push({
            target: e.from,
            source: e.to,
            color: '#37a2da'
          })
        })
        config.lines = lines;
        // this.$set(this.config, 'lines', lines);
        this.config = config;
        //console.log('this.config', this.config);
      }
    },
    mounted () {
      this.getW_H();
    },
    methods: {
      init(val){

        let config = {
          points: [
            {
              name: '北京市',
              coordinate: [0.70, 0.33],
            },
            {
                name: '新疆维吾尔自治区',
                coordinate: [0.24, 0.25]
            },
            {
                name: '西藏自治区',
                coordinate: [0.28, 0.53]
            },
            {
                name: '云南省',
                coordinate: [0.48, 0.63]
            },
            {
                name: '四川省',
                coordinate: [0.50, 0.52]
            },
            {
                name: '青海省',
                coordinate: [0.38, 0.41]
            },
            {
                name: '甘肃省',
                coordinate: [0.37, 0.32]
            },
            {
                name: '宁夏回族自治区',
                coordinate: [0.53, 0.39]
            },
            {
                name: '内蒙古自治区',
                coordinate: [0.62, 0.29]
            },
            {
                name: '重庆市',
                coordinate: [0.56, 0.54]
            },
            {
                name: '陕西省',
                coordinate: [0.57, 0.44]
            },
            {
                name: '山西',
                coordinate: [0.64, 0.37]
            },
            {
                name: '黑龙江省',
                coordinate: [0.89, 0.15]
            },
            {
                name: '吉林省',
                coordinate: [0.86, 0.25]
            },
            {
                name: '辽宁省',
                coordinate: [0.81, 0.30]
            },
            {
                name: '河北省',
                coordinate: [0.69, 0.36]
            },
            {
                name: '山东省',
                coordinate: [0.72, 0.40]
            },
            {
                name: '河南省',
                coordinate: [0.65, 0.45]
            },
            {
                name: '湖北省',
                coordinate: [0.63, 0.52]
            },
            {
                name: '江苏省',
                coordinate: [0.76, 0.48]
            },
            {
                name: '上海市',
                coordinate: [0.79, 0.52]
            },
            {
                name: '浙江省',
                coordinate: [0.76, 0.55]
            },
            {
                name: '安徽省',
                coordinate: [0.71, 0.50]
            },
            {
                name: '福建省',
                coordinate: [0.72, 0.61]
            },
            {
                name: '江西省',
                coordinate: [0.69, 0.57]
            },
            {
                name: '湖南省',
                coordinate: [0.62, 0.57]
            },
            {
                name: '贵州省',
                coordinate: [0.55, 0.59]
            },
            {
                name: '广西壮族自治区',
                coordinate: [0.58, 0.65]
            },
            {
                name: '广东省',
                coordinate: [0.65, 0.65]
            },
            {
                name: '海南省',
                coordinate: [0.59, 0.73]
            },
            {
                name: '台湾省',
                coordinate: [0.77, 0.65]
            },
            {
                name: '澳门特别行政区',
                coordinate: [0.64, 0.69]
            },
            {
                name: '香港特别行政区',
                coordinate: [0.67, 0.68]
            },
            {
                name: '天津市',
                coordinate: [0.71, 0.35]
            }
          ],
          lines: [
            {
              source: '广西省',
              target: '北京市',
              color: '#37a2da'
            },
          ],
          bgImgSrc: 'https://static.idicc.cn/cdn/pangu/china.png',
        };
        let lines = [];
        val.map(e=>{
          lines.push({
            target: e.to,
            source: e.from,
            color: '#FF7A00'
          })
        })
        config.lines = lines;
        // this.$set(this.config, 'lines', lines);
        this.config = config;
        //console.log('this.config', this.config);
      },
      getW_H() {
        let w = document.getElementById('flylineId').clientWidth;
        let h = document.getElementById('flylineId').clientHeight;
        let scale = 1212/1236; // h/w
        let new_scale = h/w;
        if(scale<new_scale){
          this.obj={
            width: w + 'px',
            height: w*scale + 'px'
          }
        }else{
          this.obj={
            width: h/scale + 'px',
            height: h + 'px'
          }
        }
        //console.log('this.obj', this.obj);
      }
    },
  }
</script>

<style lang="scss" scoped>
  .flyline{
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
  }
</style>