<!--
 * @Author: jhy
 * @Date: 2023-05-22 16:17:28
 * @LastEditTime: 2023-06-05 09:33:21
 * @LastEditors: jhy
 * @Description: 
 * @FilePath: /QT-SASS-PC/src/views/system-manage/idicc-scan/invest/index.vue
-->
<template>
  <div class="invest">
    <div class="invest-top">
      <div class="list list1">
        <div
          class="left"
          style="cursor: pointer;"
          @click="getexternal"
        >
          <div class="img">
            <img
              src="https://static.idicc.cn/cdn/pangu/icon07.png"
              alt=""
            >
          </div>
          <div class="sp-con">
            <div class="sp1">
              {{ +(dataInfo.foreignInvestmentNum || 0) }}
            </div>
            <div class="sp2">
              对外投资企业<img
                style="width: 12px;height: 12px;position: relative;top: 2px;left: 2px;"
                src="https://static.idicc.cn/cdn/pangu/listBs.svg"
              >
            </div>
          </div>
        </div>
        <div class="right">
          <div class="item">
            <div class="sp1">
              {{ +(dataInfo.investmentEnterpriseProportion || 0) }}%
            </div>
            <div class="sp2">
              投资企业占比
            </div>
          </div>
          <div class="item">
            <div class="sp1">
              {{ +(dataInfo.avgInvestor || 0) }}
            </div>
            <div class="sp2">
              平均投资家数
            </div>
          </div>
        </div>
      </div>
      <div class="list list2">
        <div
          class="left"
          style="cursor: pointer;"
          @click="getinvest"
        >
          <div class="img">
            <img
              src="https://static.idicc.cn/cdn/pangu/icon08.png"
              alt=""
            >
          </div>
          <div class="sp-con">
            <div class="sp1">
              {{ +(dataInfo.investedEnterpriseNum || 0) }}
            </div>
            <div class="sp2">
              投向企业<img
                style="width: 12px;height: 12px;position: relative;top: 2px;left: 2px;"
                src="https://static.idicc.cn/cdn/pangu/listBs.svg"
              >
            </div>
          </div>
        </div>
        <div class="right">
          <div class="item">
            <div class="sp1">
              {{ +(dataInfo.toLocalProportion || 0) }}%
            </div>
            <div class="sp2">
              本地占比
            </div>
          </div>
          <div class="item">
            <div class="sp1">
              {{ +(dataInfo.toThisIndustryProportion || 0) }}%
            </div>
            <div class="sp2">
              本产业占比
            </div>
          </div>
        </div>
      </div>
      <div class="list list3">
        <div
          class="left"
          style="cursor: pointer;"
          @click="getfinancing"
        >
          <div class="img">
            <img
              src="https://static.idicc.cn/cdn/pangu/icon06.png"
              alt=""
            >
          </div>
          <div class="sp-con">
            <div class="sp1">
              {{ +(dataInfo.financingEnterpriseNum || 0) }}
            </div>
            <div class="sp2">
              融资企业<img
                style="width: 12px;height: 12px;position: relative;top: 2px;left: 2px;"
                src="https://static.idicc.cn/cdn/pangu/listBs.svg"
              >
            </div>
          </div>
        </div>
        <div class="right">
          <div class="item">
            <div class="sp1">
              {{ +(dataInfo.addedFinancingEnterpriseNum || 0) }}
            </div>
            <div class="sp2">
              今年融资企业 
            </div>
          </div>
          <div class="item">
            <div class="sp1">
              {{ +(dataInfo.disclosedFinancingAmount || 0) }}亿
            </div>
            <div class="sp2">
              披露融资金额 
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="isShow"
      class="invest-con"
    >
      <!-- 对外投资分步 -->
      <div class="invest-list">
        <div class="title">
          对外投资分布
        </div>
        <div class="invest-list-con">
          <div class="invest-item">
            <div class="title1 t">
              ·对外投资企业产业环节分布情况
            </div>
            <div class="title2 t">
              ·产业环节投资企业数量排名TOP5
            </div>
            <div
              id="id1"
              class="item-list"
            />
            <div
              id="id2"
              class="item-list"
            />
          </div>
          <div class="invest-item">
            <div class="title1 t">
              ·投向企业本产业环节分布情况
            </div>
            <div class="title2 t">
              ·产业环节投向企业数量排名TOP5
            </div>
            <div
              id="id3"
              class="item-list"
            />
            <div
              id="id4"
              class="item-list"
            />
          </div>
        </div>
      </div>
      <!-- 企业融资情况 -->
      <div class="invest-list">
        <div class="title">
          企业融资情况
        </div>
        <div class="invest-list-con">
          <div class="invest-item">
            <div class="title1 t">
              ·产业企业融资笔数与融资金额情况
            </div>
            <div
              id="id5"
              class="item-lists"
            />
          </div>
          <div class="invest-item">
            <div class="title1 t">
              ·产业企业融资上市板块分布情况
            </div>
            <div
              id="id6"
              class="item-lists"
            />
          </div>
        </div>
      </div>
      <!-- 产业虹吸辐射 -->
      <div class="invest-list">
        <div class="title">
          产业虹吸辐射
        </div>
        <div class="invest-list-con">
          <div class="invest-item">
            <div class="title1 t">
              ·产业吸纳投资地区TOP5
            </div>
            <div
              id="id7"
              class="item-list"
            />
            <div
              id="id8"
              class="item-list"
            >
              <flylineChart
                ref="flyline1"
                :arr="dataInfo.topOutboundInvestment"
              />
            </div>
          </div>
          <div class="invest-item">
            <div class="title1 t">
              ·产业对外投资地区TOP5
            </div>
            <div
              id="id9"
              class="item-list"
            />
            <div
              id="id10"
              class="item-list"
            >
              <flylineChart
                ref="flyline2"
                :arr="dataInfo.topAbsorbInvestment"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import * as echarts from "echarts"
  import flylineChart from './component/flylineChart.vue'
  import { 
    getForeignInvestmentEnterpriseTop,
    getInvestedEnterpriseTop,
  } from './../../idicc-scan/apiUrl'
  export default {
    name: 'InVest',
    components: {
      flylineChart,
    },
    data() {
      return {
        param: {},
        dataInfo: {},
        config: {},
        isShow: false,
        graphic: [
             {
               type: 'rect',
               left: 'center',
               top: 'middle',
               shape: {
                 width: 160,
                 height: 70,
                 radius: 20 
               },
               style: {
                 fill: '#cdd1d7',
                 stroke: '#ccc',
               },
               z:10
             },
             {
               type: 'text',
               left: 'center',
               top: 'middle',
               style: {
                 text: '暂无数据',
                 textAlign: 'center',
                 textVerticalAlign: 'middle',
                 fill: '#fff',
                 fontSize: 18
               },
               z:10
             }
        ]
      }
    },
    mounted () {
      // this.init();
    },
    methods: {
      getexternal(){
        this.$emit('diggetexternal')
      },
      getfinancing(){
        this.$emit('digfinancing')
      },
      getinvest(){
        this.$emit('diginvest')
      },
      init(info, param) {
        this.param = param;
        this.dataInfo = info;
        this.isShow = false;
        let that = this;
        this.$nextTick(()=>{
          that.isShow = !false;
          setTimeout(()=>{
            that.roll1Render(info.topForeignInvestmentEnterpriseDistribution,info.foreignInvestmentEnterpriseCount);
            that.category1Render(info.lastForeignInvestmentEnterpriseDistribution);
            that.roll2Render(info.topInvestedEnterpriseDistribution,info.investedEnterpriseCount);
            that.category2Render(info.lastInvestedEnterpriseDistribution);
            that.financ1Render();
            that.financ2Render();

            that.flyline1Render();
            that.flyline2Render();
            that.$refs.flyline1.init(info.topAbsorbInvestment);
            that.$refs.flyline2.init(info.topOutboundInvestment);

          },200);

        })
      },
      // 对外投资分步
      roll1Render(arr,sum){
       /*  if(!arr || arr.length==0){
          return
        } */
        let list = [];
        arr.map(e=>{
          list.push({ value: e.number, name: e.industryNodeName, industryNodeId: e.industryNodeId });
        });    
        //const sum = list.map(item => Number(item.value)).reduce((acc, cur) => acc + cur, 0);
        let chartDom = document.getElementById('id1');
        let myChart = echarts.init(chartDom);
        let option;

        option = {
          grid: {
            // 离容器左侧的距离
            left: "-7%",
            top: "20%",
            right: "15%",
            bottom:"50%"
          },
          tooltip: {
            trigger: "item",
            backgroundColor: "#00112dbf", // 提示框浮层的背景颜色。
            borderColor: "#0066FF", // 提示框浮层的边框颜色。
            borderWidth: 1, // 提示框浮层的边框宽。
            textStyle: {
              // 提示框浮层的文本样式。
              color: "#fff",
              fontStyle: "normal",
              fontWeight: "normal",
              fontFamily: "sans-serif",
              fontSize: 12,
            },
            //实例
            formatter: function (params) {
              let res =
                params.data.name +
                "： " +
                '<span style="color: #00FFF0;font-size: 12px;">' +
                +params.data.value +
                "</span> 家" 
              return res;
            },
          },
          // 圆心文字
          title: [
            {
              text: '按环节分布',
              top: "46%",
              textAlign: "center",
              left: "34%",
              textStyle: {
                color: "rgba(0,0,0,0.45)",
                fontSize: 14,
                fontWeight: "400",
                fontFamily: "Source Han Sans CN-Regular, Source Han Sans CN",              
              },
            },
            {
              text: +sum || 0,
              top: "53%",
              textAlign: "center",
              left: "34%",
              textStyle: {
                color: "rgba(0,0,0,0.85)",
                fontSize: 30,
                fontWeight: "400",
                fontFamily: "Source Han Sans CN-Regular, Source Han Sans CN",
              },
            },
          ],
          legend: {
            textStyle: {
              color: "rgba(0,0,0,0.65)",
              fontSize: 12,
              opacity:0.8,
              lineHeight: 33, // 设置文字之间的上下间距
            },
            // right: "right",//调整图例位置
            left:"65%",    
            orient: 'vertical',
            bottom: 'middle',
            //itemGap: 16, // 设置图例项之间的间距
            itemWidth: 15,
            top: '12%', //调整图例位置
            itemHeight: 7, //修改icon图形大小
            icon: "circle", //图例前面的图标形状
            formatter:function(value){
              let newvalue=''
              if(value.length>6){
                newvalue=value.slice(0, 6) + '...'
              }else{
                newvalue=value
              }
              return newvalue
            }
          },
          series: [
            {
              name: 'Access From',
              type: 'pie',
              radius: ["40%", "50%"],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              left: "-30%",
              top: "10%",
              label: {
                show: false,
                position: 'center'
              },
              labelLine: {
                show: false
              },
              data: list
            }
          ],
        };
        let graphic= []
        if(list.length==0){
          graphic=  this.graphic
          option={
          graphic,
        }
        }  
        // let that = this;
        option && myChart.setOption(option);
        // myChart.on('click',function (params) {
        //   that.getForeignInvestmentEnterpriseTop(params.data.industryNodeId);
        // });
        // that.getForeignInvestmentEnterpriseTop(list[0].industryNodeId);
      },
      // 对外投资企业细分环节top5
      getForeignInvestmentEnterpriseTop(industryNodeId){
        let param = this.param;
        param.industryChainNodeId = industryNodeId;
        getForeignInvestmentEnterpriseTop(param).then(res=>{
          this.category1Render(res);
        });
      },
      // 被投资企业细分环节top5
      getInvestedEnterpriseTop(industryNodeId){
        let param = this.param;
        param.industryChainNodeId = industryNodeId;
        getInvestedEnterpriseTop(param).then(res=>{
          this.category2Render(res);
        });
      },
      category1Render(arr){
        /* if(!arr || arr.length==0){
          return
        } */
        let nameList=[];
        let list=[];
        arr.map(e=>{
          nameList.push(e.industryNodeName);
          list.push(e.number);
        });
        let graphic= []
        if(list.length==0){
          graphic=  this.graphic
        }  
        let chartDom = document.getElementById("id2");
        let myChart = echarts.init(chartDom);
        let option;

        option = {
          graphic,
          grid: {
            left: "5%",
            right: "15%",
            bottom: "10%",
            top: "15%",
            containLabel: true,
          },
          tooltip: {
            show: true,
            formatter: "{b}<br/>{c}家",
            textStyle: {
              fontWeight: "bold",
              fontSize: 12,
            },
          },
          yAxis: [
            {
              type: "category",
              inverse: true,
              //文字
              axisLabel: {
                show: true,
                fontSize: 12,
                color: "#86909C",
                formatter:function(value){
                 let newvalue=''
                  if(value.length>6){
                  newvalue=value.slice(0, 6) + '...'
                  }else{
                   newvalue=value
                 }
                  return newvalue
                }
              },
              // 边框
              splitLine: {
                show: false,
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#e0e3e6",
                },
              },
              // 刻度
              axisTick: {
                show: false,
              },
              data: nameList,
            },
          ],
          xAxis: [
            {
              type: "value",
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, // 隐藏 x 轴横线
              },
              // 刻度线 虚线
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed",
                },
              },
              name: "家",
              nameLocation: "end",
              nameTextStyle: {
                padding: [8, 0, 0, 5],
                verticalAlign: 'top',
              },
              axisLabel: {
                inside: false,
                textStyle: {
                  fontSize: 12,
                },
                interval: 0,
                formatter: "{value}",
              },
            },
          ],
          series: [
            {
              name: "assist",
              type: "bar",
              stack: "1",
              label: {
                show: true,
                position: "top",
              },
              itemStyle: {
                normal: {
                  barBorderColor: "rgba(0,0,0,0)",
                  color: "rgba(0,0,0,0)",
                },
                emphasis: {
                  barBorderColor: "rgba(0,0,0,0)",
                  color: "rgba(0,0,0,0)",
                },
              },
              tooltip: {
                trigger: "none",
              },
              data: [],
            },
            {
              type: "bar",
              stack: "1",
              barWidth: 12,
              barBorderRadius: 30,
              itemStyle: {
                normal: {
                  barBorderRadius: 20,
                  color: function () {
                    return {
                      type: "linear",
                      x: 1,
                      y: 0,
                      x2: 0,
                      y2: 0,
                      colorStops: [
                        {
                          offset: 0,
                          color: "#1e62ff", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#e5ecff", // 100% 处的颜色
                        },
                      ],
                    };
                  },
                },
              },
              data: list,
            },
          ],
        };
        option && myChart.setOption(option);
      },
      roll2Render(arr,sum){
        /* if(!arr || arr.length==0){
          return
        } */
        let list = [];
        arr.map(e=>{
          list.push({ value: e.number, name: e.industryNodeName, industryNodeId: e.industryNodeId });
        });
        let chartDom = document.getElementById('id3');
        let myChart = echarts.init(chartDom);
        let option;

        option = {
          grid: {
            // 离容器左侧的距离
            left: "-7%",
            top: "20%",
            right: "15%",
            bottom:"50%"
          },
          tooltip: {
            trigger: "item",
            backgroundColor: "#00112dbf", // 提示框浮层的背景颜色。
            borderColor: "#0066FF", // 提示框浮层的边框颜色。
            borderWidth: 1, // 提示框浮层的边框宽。
            textStyle: {
              // 提示框浮层的文本样式。
              color: "#fff",
              fontStyle: "normal",
              fontWeight: "normal",
              fontFamily: "sans-serif",
              fontSize: 12,
            },
            //实例
            formatter: function (params) {
              let res =
                params.data.name +
                "： " +
                '<span style="color: #00FFF0;font-size: 12px;">' +
                params.data.value +
                "</span> 家" 
              return res;
            },
          },
          // 圆心文字
          title: [
            {
              text: '按环节分布',
              top: "46%",
              textAlign: "center",
              left: "34%",
              textStyle: {
                color: "rgba(0,0,0,0.45)",
                fontSize: 14,
                fontWeight: "400",
                fontFamily: "Source Han Sans CN-Regular, Source Han Sans CN",              
              },
            },
            {
              text: +sum || 0,
              top: "53%",
              textAlign: "center",
              left: "34%",
              textStyle: {
                color: "rgba(0,0,0,0.85)",
                fontSize: 30,
                fontWeight: "400",
                fontFamily: "Source Han Sans CN-Regular, Source Han Sans CN",
              },
            },
          ],
          legend: {
            textStyle: {
              color: "rgba(0,0,0,0.65)",
              fontSize: 12,
              opacity:0.8,
              lineHeight: 33, // 设置文字之间的上下间距
            },
            // right: "right",//调整图例位置
            left:"65%",    
            orient: 'vertical',
            bottom: 'middle',
            //itemGap: 16, // 设置图例项之间的间距
            itemWidth: 15,
            top: '12%', //调整图例位置
            itemHeight: 7, //修改icon图形大小
            icon: "circle", //图例前面的图标形状
            formatter:function(value){
              let newvalue=''
              if(value.length>6){
                newvalue=value.slice(0, 6) + '...'
              }else{
                newvalue=value
              }
              return newvalue
            }
          },
          series: [
            {
              name: 'Access From',
              type: 'pie',
              radius: ["40%", "50%"],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              left: "-30%",
              top: "10%",
              label: {
                show: false,
                position: 'center'
              },
              labelLine: {
                show: false
              },
              data: list
            }
          ],
        };
        let graphic= []
        if(list.length==0){
          graphic=  this.graphic
          option={
          graphic,
        }
        }  
        // let that = this;
        option && myChart.setOption(option);
        // myChart.on('click',function (params) {
        //   that.getInvestedEnterpriseTop(params.data.industryNodeId);
        // });
        // that.getInvestedEnterpriseTop(list[0].industryNodeId);
      },

      category2Render(arr){
       /*  if(!arr || arr.length==0){
          return
        } */
        let nameList=[];
        let list=[];
        arr.map(e=>{
          nameList.push(e.industryNodeName);
          list.push(e.number);
        });
        let graphic= []  
        if(list.length==0){
          graphic=  this.graphic
        } 
        let chartDom = document.getElementById("id4");
        let myChart = echarts.init(chartDom);
        let option;

        option = {
          graphic,
          grid: {
            left: "5%",
            right: "15%",
            bottom: "10%",
            top: "15%",
            containLabel: true,
          },
          tooltip: {
            show: true,
            formatter: "{b}<br/>{c}家",
            textStyle: {
              fontWeight: "bold",
              fontSize: 12,
            },
          },
          yAxis: [
            {
              type: "category",
              inverse: true,
              //文字
              axisLabel: {
                show: true,
                fontSize: 12,
                color: "#86909C",
                formatter:function(value){
                 let newvalue=''
                  if(value.length>6){
                  newvalue=value.slice(0, 6) + '...'
                  }else{
                   newvalue=value
                 }
                  return newvalue
                }
              },
              // 边框
              splitLine: {
                show: false,
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#e0e3e6",
                },
              },
              // 刻度
              axisTick: {
                show: false,
              },
              data: nameList,
            },
          ],
          xAxis: [
            {
              type: "value",
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, // 隐藏 x 轴横线
              },
              // 刻度线 虚线
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed",
                },
              },
              name: "家",
              nameLocation: "end",
              nameTextStyle: {
                padding: [8, 0, 0, 5],
                verticalAlign: 'top',
              },
              axisLabel: {
                inside: false,
                textStyle: {
                  fontSize: 12,
                },
                interval: 0,
                formatter: "{value}",
              },
            },
          ],
          series: [
            {
              name: "assist",
              type: "bar",
              stack: "1",
              label: {
                show: true,
                position: "top",
              },
              itemStyle: {
                normal: {
                  barBorderColor: "rgba(0,0,0,0)",
                  color: "rgba(0,0,0,0)",
                },
                emphasis: {
                  barBorderColor: "rgba(0,0,0,0)",
                  color: "rgba(0,0,0,0)",
                },
              },
              tooltip: {
                trigger: "none",
              },
              data: [],
            },
            {
              type: "bar",
              stack: "1",
              barWidth: 12,
              barBorderRadius: 30,
              itemStyle: {
                normal: {
                  barBorderRadius: 20,
                  color: function () {
                    return {
                      type: "linear",
                      x: 1,
                      y: 0,
                      x2: 0,
                      y2: 0,
                      colorStops: [
                        {
                          offset: 0,
                          color: "#1e62ff", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#e5ecff", // 100% 处的颜色
                        },
                      ],
                    };
                  },
                },
              },
              data: list,
            },
          ],
        };
        option && myChart.setOption(option);
      },
      // 企业融资情况
      financ1Render(){
        let arr = this.dataInfo.financingEnterpriseTrendy;
        let year=[];
        let list1=[];
        let list2=[];
        arr.map(e=>{
          // ["1", "2", "3", "4", "5"]
          list1.push(e.amount || 0);
          list2.push(e.avgAmount || 0);
          year.push(e.year);
        });
        let graphic= []
        if(list1.length==0){
          graphic=  this.graphic
        }  
        var chartDom = document.getElementById("id5");
        var myChart = echarts.init(chartDom);
        var option;

        option = {
          graphic,
          grid: {
            left: "5%",
            right: "5%",
            bottom: "5%",
            top: "25%",
            containLabel: true,
          },
          tooltip: {
            trigger: "axis",
          },
          axisLabel: {
            show: true,
            fontSize: 12,
            color: "#86909C",
          },
          legend: {
            top: 38,
            data: [
              {
                name: "笔数",
              },
              {
                name: "平均融资金额",
                lineStyle: {
                  type: "solid",
                },
              },
            ],
            itemWidth: 15,
            itemHeight: 7, //修改icon图形大小
          },
          xAxis: [
            {
              type: "category",
              data: year,
              axisTick: {
                show: false,
              },
              axisPointer: {
                type: "shadow",
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "笔",
              interval: 50,
              nameTextStyle: {
                padding: [0, 28, 0, 0],
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed",
                },
              },
              axisLabel: {
                formatter: "{value}",
              },
            },
            {
              type: "value",
              name: "亿元",
              splitNumber : 5,
              nameTextStyle: {
                padding: [0, 0, 0, 28],
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed",
                },
              },
              axisLabel: {
                formatter: "{value} ",
              },
            },
          ],
          series: [
            {
              name: "笔数",
              type: "bar",
              barWidth: 20,
              barBorderRadius: 30,
              itemStyle: {
                normal: {
                  barBorderRadius: 5,
                  color: "#165DFF",
                },
              },
              tooltip: {
                valueFormatter: function (value) {
                  return value + "笔";
                },
              },
              data: list1,
            },
            {
              name: "平均融资金额",
              type: "line",
              yAxisIndex: 1,
              symbol: "none", //取消折点圆圈
              tooltip: {
                valueFormatter: function (value) {
                  return value?(value+'亿元'): "未披露";
                },
              },
              itemStyle: {
                normal: {
                  color: "#14C9C9",
                },
              },
              data: list2,
            },
          ],
        };

        option && myChart.setOption(option);
      },
      financ2Render(){
        let arr = this.dataInfo.listedSectorDistribution;
        let sector=[];
        let list=[];
        arr.map(e=>{
          list.push(e.amount);
          sector.push(e.sector);
        });
        let graphic= []
        if(list.length==0){
          graphic=  this.graphic
        }  
        var chartDom = document.getElementById("id6");
        var myChart = echarts.init(chartDom);
        var option;

        option = {
          graphic,
          grid: {
            left: "5%",
            right: "5%",
            bottom: "5%",
            top: "25%",
            containLabel: true,
          },
          tooltip: {
            trigger: "axis",
          },
          axisLabel: {
            show: true,
            fontSize: 12,
            color: "#86909C",
          },
          xAxis: [
            {
              type: "category",
              data: sector,
              axisTick: {
                show: false,
              },
              axisPointer: {
                type: "shadow",
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "家",
              interval: 50,
              nameTextStyle: {
                padding: [0, 28, 0, 0],
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed",
                },
              },
              axisLabel: {
                formatter: "{value}",
              },
            }
          ],
          series: [
            {
              name: "板块",
              type: "bar",
              barWidth: 20,
              barBorderRadius: 30,
              itemStyle: {
                normal: {
                  barBorderRadius: 5,
                  color: "#14C9C9",
                },
              },
              tooltip: {
                valueFormatter: function (value) {
                  return value + "家";
                },
              },
              data: list,
            }
          ],
        };

        option && myChart.setOption(option);
      },
      // 产业虹吸辐射
      flyline1Render(){
        let arr = this.dataInfo.topAbsorbInvestment;
        let nameList = [];
        let list= [];
        arr.map(e=>{
          nameList.push(e.fromName);
          list.push(e.amount);
        });
        let graphic= []
        if(list.length==0){
          graphic=  this.graphic
        }  
        let chartDom = document.getElementById("id7");
        let myChart = echarts.init(chartDom);
        let option;

        option = {
          graphic,
          grid: {
            left: "5%",
            right: "15%",
            bottom: "10%",
            top: "15%",
            containLabel: true,
          },
          tooltip: {
            show: true,
            formatter: "{b}<br/>{c}家",
            textStyle: {
              fontWeight: "bold",
              fontSize: 12,
            },
          },
          yAxis: [
            {
              type: "category",
              inverse: true,
              //文字
              axisLabel: {
                show: true,
                fontSize: 12,
                color: "#86909C",
              },
              // 边框
              splitLine: {
                show: false,
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#e0e3e6",
                },
              },
              // 刻度
              axisTick: {
                show: false,
              },
              data: nameList,
            },
          ],
          xAxis: [
            {
              type: "value",
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, // 隐藏 x 轴横线
              },
              // 刻度线 虚线
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed",
                },
              },
              name: "家",
              nameLocation: "end",
              nameTextStyle: {
                padding: [8, 0, 0, 5],
                verticalAlign: 'top',
              },
              axisLabel: {
                inside: false,
                textStyle: {
                  fontSize: 12,
                },
                interval: 0,
                formatter: "{value}",
              },
            },
          ],
          series: [
            {
              name: "assist",
              type: "bar",
              stack: "1",
              label: {
                show: true,
                position: "top",
              },
              itemStyle: {
                normal: {
                  barBorderColor: "rgba(0,0,0,0)",
                  color: "rgba(0,0,0,0)",
                },
                emphasis: {
                  barBorderColor: "rgba(0,0,0,0)",
                  color: "rgba(0,0,0,0)",
                },
              },
              tooltip: {
                trigger: "none",
              },
              data: [],
            },
            {
              type: "bar",
              stack: "1",
              barWidth: 12,
              barBorderRadius: 30,
              itemStyle: {
                normal: {
                  barBorderRadius: 20,
                  color: function () {
                    return {
                      type: "linear",
                      x: 1,
                      y: 0,
                      x2: 0,
                      y2: 0,
                      colorStops: [
                        {
                          offset: 0,
                          color: "#1e62ff", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#e5ecff", // 100% 处的颜色
                        },
                      ],
                    };
                  },
                },
              },
              data: list,
            },
          ],
        };
        option && myChart.setOption(option);
      },
      flyline2Render(){
        let arr = this.dataInfo.topOutboundInvestment;
        let nameList = [];
        let list= [];
        arr.map(e=>{
          nameList.push(e.toName);
          list.push(e.amount);
        });
        let graphic= []
        if(list.length==0){
          graphic=  this.graphic
        }  
        let chartDom = document.getElementById("id9");
        let myChart = echarts.init(chartDom);
        let option;

        option = {
          graphic,
          grid: {
            left: "5%",
            right: "15%",
            bottom: "10%",
            top: "15%",
            containLabel: true,
          },
          tooltip: {
            show: true,
            formatter: "{b}<br/>{c}家",
            textStyle: {
              fontWeight: "bold",
              fontSize: 12,
            },
          },
          yAxis: [
            {
              type: "category",
              inverse: true,
              //文字
              axisLabel: {
                show: true,
                fontSize: 12,
                color: "#86909C",
              },
              // 边框
              splitLine: {
                show: false,
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#e0e3e6",
                },
              },
              // 刻度
              axisTick: {
                show: false,
              },
              data: nameList,
            },
          ],
          xAxis: [
            {
              type: "value",
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false, // 隐藏 x 轴横线
              },
              // 刻度线 虚线
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed",
                },
              },
              name: "家",
              nameLocation: "end",
              nameTextStyle: {
                padding: [8, 0, 0, 5],
                verticalAlign: 'top',
              },
              axisLabel: {
                inside: false,
                textStyle: {
                  fontSize: 12,
                },
                interval: 0,
                formatter: "{value}",
              },
            },
          ],
          series: [
            {
              name: "assist",
              type: "bar",
              stack: "1",
              label: {
                show: true,
                position: "top",
              },
              itemStyle: {
                normal: {
                  barBorderColor: "rgba(0,0,0,0)",
                  color: "rgba(0,0,0,0)",
                },
                emphasis: {
                  barBorderColor: "rgba(0,0,0,0)",
                  color: "rgba(0,0,0,0)",
                },
              },
              tooltip: {
                trigger: "none",
              },
              data: [],
            },
            {
              type: "bar",
              stack: "1",
              barWidth: 12,
              barBorderRadius: 30,
              itemStyle: {
                normal: {
                  barBorderRadius: 20,
                  color: function () {
                    return {
                      type: "linear",
                      x: 1,
                      y: 0,
                      x2: 0,
                      y2: 0,
                      colorStops: [
                        {
                          offset: 0,
                          color: "#1e62ff", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#e5ecff", // 100% 处的颜色
                        },
                      ],
                    };
                  },
                },
              },
              data: list,
            },
          ],
        };
        option && myChart.setOption(option);
      }
    },
  }
</script>

<style lang="scss" scoped>
.invest{
  padding-bottom: 50px;
  .cu_p{
    cursor: pointer;
  }
  &-top{
    display: flex;
    margin: 16px 0;
    justify-content: space-between;
    .list{
      background: #fff;
      box-sizing: border-box;
      padding-top: 28px;
      padding-left: 10px;
      padding-bottom: 25px;
      padding-right: 15px;
      width: 32%;
      border-radius: 10px;
      display: flex;
      justify-content: space-between;
      .left{
        display: flex;
        .img{
          img{
            // width: 59px;
            height: 51px;
          }
        }
        .sp-con{
          display: block;
          margin-left: 21px;
        }
        .sp1{
          font-size: 30px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0,0,0,0.85);
          line-height: 30px;
        }
        .sp2{
          margin-top: 9px;
          font-size: 12px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0,0,0,0.45);
          line-height: 22px;
        }
      }
      .right{
        display: flex;
        .item{
          margin-left: 20px;
          .sp1{
            font-size: 14px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: rgba(0,0,0,0.85);
            line-height: 22px;
          }
          .sp2{
            padding-top: 15px;
            font-size: 12px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: rgba(0,0,0,0.45);
            line-height: 30px;
          }
        }
      }
      &.list2{
        padding-right: 45px;
      }
      &.list3{
        padding-right: 30px;
      }
    }
  }
  &-con{}
  &-list{
    height: 410px;
    background: #FFFFFF;
    box-shadow: 0px 4px 14px 0px #EEF1F8;
    border-radius: 10px;
    margin-bottom: 24px;
    .title{
      line-height: 54px;
      height: 55px;
      padding-left: 24px;
      box-sizing: border-box;
      border-bottom: 1px solid #E9E9E9;

      font-size: 16px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0,0,0,0.85);
    }
    &-con{
      height: 100%;
      display: flex;
    }
  }
  &-item{
    width: 50%;
    height: calc(100% - 55px);
    border-right: 1px solid #D9DCE0;
    display: flex;
    position: relative;
    .t{
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(29,33,41,0.85);
      line-height: 28px;
      position: absolute;
      top: 20px;
      &.title1{
        left: 20px;
      }
      &.title2{
        left: calc(50% + 20px);
      }
    }
    .item-list{
      width: 50%;
      height: 100%;
    }
    #id8{
      padding: 15px 15px 15px 0;
      margin: auto;
    }
    .item-lists{
      width: 100%;
      height: 100%;
    }
  }
}
.invest-top .list{

  box-shadow: 0px 4px 12px 0px #EEF1F8;
}
</style>