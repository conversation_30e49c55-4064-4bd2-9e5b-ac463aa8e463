<!--
 * @Author: jhy
 * @Date: 2023-05-19 14:14:25
 * @LastEditTime: 2023-06-05 11:39:57
 * @LastEditors: jhy
 * @Description: 
 * @FilePath: /QT-SASS-PC/src/views/system-manage/idicc-scan/enterprise/component/topTable.vue
-->
<template>
  <div
    v-if="tableData.length>0"
    class="topTable"
  >
    <div class="title">
      龙头企业TOP50
    </div>
    <div class="table-con">
      <div class="table-head">
        <div class="li li1">
          排序
        </div>
        <div class="li li2">
          企业名称
        </div>
        <!-- <div class="li li3">
          所在环节
        </div> -->
        <div class="li li4">
          所在地区
        </div>
        <div class="li li5">
          注册资本
        </div>
        <div class="li li6">
          成立日期
        </div>
      </div>
      <div class="table-body">
        <div
          v-for="(item,i) in tableData"
          :key="i"
          class="ul"
        >
          <div class="li li1">
            {{ i+1 }}
          </div>
          <div class="li li2">
            <div>
              <div
                class="txt1"
                style="cursor: pointer;"
                @click="goDetailsenterpriseDel(item,item.enterpriseId)"
              >
                {{ item.enterpriseName }}
              </div>
              <div class="tag">
                <div
                  v-for="(arr,k) in item.enterpriseLabelNames"
                  :key="k"
                  class="tag-list"
                >
                  {{ arr }}
                </div>
                <!-- <div class="tag-list">
                  专精特新小巨人
                </div>
                <div class="tag-list">
                  专精特新
                </div> -->
              </div>
            </div>
          </div>
          <!-- <div class="li li3">
            所在环节
          </div> -->
          <div class="li li4">
            {{ item.province }}
            {{ item.city }}
            {{ item.area }}
          </div>
          <div class="li li5">
            {{ item.registeredCapital }}
          </div>
          <div class="li li6">
            {{ item.registerDateStr }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'TopTable',
    props: {
      tableData: {
        type: Array,
        default: ()=>{
          return []
        }
      },
    },
    methods:{
      goDetailsenterpriseDel(item,id){
         this.$emit('goDetailsenterpriseDel',item,id,true)
    },
    }
  }
</script>

<style lang="scss" scoped>
  .topTable{
    background: #fff;
    padding-right: 32px;
    padding-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0px 4px 12px 0px #EEF1F8;
    .title{
      line-height: 50px;
      padding-left: 24px;
      font-size: 16px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0,0,0,0.85);
      border-bottom: 1px solid #E9E9E9;
    }
    .table{
      &-con{
        padding-left: 32px;
        padding-top: 24px;
        .li{
          box-sizing: border-box;
          display: flex;
          align-items: center;
        }
        .li1{
          width: 9.196428571428571%;
          padding-left: 16px;
        }
        .li2{
          // width: 29.107142857142858%;
          width: 35.107142857142858%;
        }
        .li3{
          width: 16.25%;
        }
        .li4{
          // width: 16.964285714285715%;
          width: 26.964285714285715%;
        }
        .li5{
          width: 16.16071428571429%;
        }
        .li6{width: 12.321428571428573%;}
      }
      &-head{
        height: 54px;
        background: #FAFAFA;
        display: flex;
        align-content: center;
        font-size: 14px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        color: rgba(0,0,0,0.85);
        line-height: 22px;
      }
      &-body{
        .ul{
          // padding-top: 16px;
          border-bottom: 1px solid #E9E9E9;
          display: flex;
          .li{
            min-height: 63px;
            font-size: 14px;
            font-family: Source Han Sans CN-Normal, Source Han Sans CN;
            font-weight: 400;
            color: rgba(0,0,0,0.65);
            line-height: 22px;
            &.li2{
              flex-wrap: wrap;
              .txt1{
                width: 100%;
              }
              .tag{
                display: flex;
                width: 100%;
                &-list{
                  padding: 0 6px;
                  background: #FFF2E6;
                  font-size: 11px;
                  font-family: PingFang SC-Medium, PingFang SC;
                  font-weight: 500;
                  color: #FF7D00;
                  line-height: 18px;
                  margin-right: 8px;
                  border-radius: 2px;
                }
              }
            }
          }
        }
      }
    }
  }
</style>