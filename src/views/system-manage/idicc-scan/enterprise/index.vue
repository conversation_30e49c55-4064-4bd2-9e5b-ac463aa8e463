<template>
  <div class="enterprise">
    <div class="enterprise-top">
      <div class="list list1">
        <div
          style="cursor: pointer;"
          class="left"
          @click="getenterprise(false)"
        >
          <div class="img">
            <img
              src="https://static.idicc.cn/cdn/pangu/icon001.png"
              alt=""
            >
          </div>
          <div class="sp-con">
            <div class="sp1">
              {{ +(dataInfo.enterpriseCount || 0) }}
            </div>
            <div class="sp2">
              企业总量<img
                style="width: 12px;height: 12px;position: relative;top: 2px;left: 2px;"
                src="https://static.idicc.cn/cdn/pangu/listBs.svg"
              >
            </div>
          </div>
        </div>
        <div class="right">
          <div
            v-if="dataInfo.enterpriseCountRankOfCountry"
            class="item"
          >
            <div class="sp1">
              NO.{{ +(dataInfo.enterpriseCountRankOfCountry || 0) }}
            </div>
            <div class="sp2">
              全国
            </div>
          </div>
          <div
            v-if="dataInfo.enterpriseCountRankOfProvince"
            class="item"
          >
            <div class="sp1">
              NO.{{ +(dataInfo.enterpriseCountRankOfProvince || 0) }}
            </div>
            <div class="sp2">
              全省
            </div>
          </div>
          <div
            v-if="dataInfo.enterpriseCountRankOfCity"
            class="item"
          >
            <div class="sp1">
              NO.{{ +(dataInfo.enterpriseCountRankOfCity || 0) }}
            </div>
            <div class="sp2">
              全市
            </div>
          </div>
        </div>
      </div>
      <div class="list list3">
        <div
          class="left"
          style="cursor: pointer;"
          @click="getenterprise(true)"
        >
          <div class="img">
            <img
              src="https://static.idicc.cn/cdn/pangu/icon003.png"
              alt=""
            >
          </div>
          <div class="sp-con">
            <div class="sp1">
              {{ +(dataInfo.thisYearRegisterEnterpriseNum || 0) }}
            </div>
            <div class="sp2">
              今年新增企业
              <el-tooltip
                effect="dark"
                content="今年累计值"
                placement="top"
              >
                <i class="el-icon-warning-outline cu_p" />
              </el-tooltip>
              <img
                style="width: 12px;height: 12px;position: relative;top: 2px;left: 2px;"
                src="https://static.idicc.cn/cdn/pangu/listBs.svg"
              >
            </div>
          </div>
        </div>
        <div class="right">
          <div class="item">
            <div class="sp1">
              {{ dataInfo.thisYearRegisterMostOfNode }}
            </div>
            <div class="sp2 fsz_14">
              新增最多环节
            </div>
          </div>
        </div>
      </div>
      <div class="list list2">
        <div class="left">
          <div class="img">
            <img
              src="https://static.idicc.cn/cdn/pangu/icon02.png"
              alt=""
            >
          </div>
          <div class="sp-con">
            <div class="sp1">
              {{ (dataInfo.chainNodeCoverRate || '0%') }}
            </div>
            <div class="sp2">
              产业链覆盖率
            </div>
          </div>
        </div>
        <div class="right">
          <div class="item">
            <div class="sp1">
              {{ dataInfo.mostOfNode }}
            </div>
            <div class="sp2">
              企业最多环节
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="enterprise-list">
      <div class="list list1">
        <div class="item">
          <div class="title">
            新增企业
          </div>
          <div class="item-con">
            <div class="item-title">
              ·近5年新增企业
            </div>
            <div
              id="newAddEnterprise"
              class="line"
            />
          </div>
        </div>
        <div class="item">
          <div class="title">
            优质企业
          </div>
          <div class="item-con">
            <div class="item-title">
              ·与全国同级地区优质企业数量对比情况
            </div>
            <div
              id="item2"
              class="line"
            />
          </div>
        </div>
      </div>
      <div class="list list2">
        <div class="item">
          <div class="title">
            企业环节分布
          </div>
          <div class="item-con">
            <div class="item-title">
              ·本地产业企业环节分布
            </div>
            <div
              id="item3"
              class="line"
            />
          </div>
        </div>
        <div class="item">
          <div class="title">
            企业地区分布
          </div>
          <div class="item-con">
            <div class="item-title">
              ·产业链企业地区分布情况
            </div>
            <MapMain ref="map" />
            <!-- <div
              id="item4"
              class="line"
            />
            <div
              id="item5"
              class="line"
            /> -->
          </div>
        </div>
      </div>
    </div>

    <topTable
      :table-data="leadingEnterpriseDTOS"
      @goDetailsenterpriseDel="goDetailsenterpriseDel"
    />
  </div>
</template>

<script>
import * as echarts from "echarts"
import MapMain from './component/map.vue'
import topTable from './component/topTable.vue'
export default {
  name: 'EnterPrise',
  components: {
    MapMain,
    topTable,
  },
  props: {
    dataInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      leadingEnterpriseDTOS: [],
      graphic: [
        {
          type: 'rect',
          left: 'center',
          top: 'middle',
          shape: {
            width: 160,
            height: 70,
            radius: 20
          },
          style: {
            fill: '#cdd1d7',
            stroke: '#ccc',
          },
          z: 10
        },
        {
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '暂无数据',
            textAlign: 'center',
            textVerticalAlign: 'middle',
            fill: '#fff',
            fontSize: 18
          },
          z: 10
        }
      ]
    }
  },
  mounted() {
    // this.init();
  },
  methods: {
    goDetailsenterpriseDel(item,id, top) {
      this.$emit('DetaiDel',item, id, top)
    },
    getenterprise(is) {
      this.$emit('digenterprise', is)
    },
    init(info, data) {
      // 近五年新增
      this.newAddEnterprise(info);
      // 优质企业
      this.bastEnterprise(info);
      // 企业环节分布
      this.rollEnterprise(info);
      // 企业地区分布
      this.$refs.map.init(info.industryRegionTop5, data);
      //this.leadingEnterpriseDTOS = info.leadingEnterpriseDTOS
    },
    // 近五年新增
    newAddEnterprise(info) {
      let xData = [];
      let yData = [];
      info.registerTrendyChart?.map(e => {
        xData.push(e.year);
        yData.push(e.enterpriseNum);
      });
      let graphic = []
      /*        
              if (yData.every(item => item == 0)) {
                graphic=this.graphic
              } */
      let chartDom = document.getElementById('newAddEnterprise');
      let myChart = echarts.init(chartDom);
      let option;

      option = {
        graphic,
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255,1)',
          borderColor: '#1e62ff',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xData
        },
        yAxis: {
          type: 'value',
          name: "家",
          nameTextStyle: {
            padding: [0, 28, 0, 0],
          },
          // splitLine: {
          //     show: true,
          //     lineStyle: {
          //       type: "dashed",
          //     },
          //   },
        },
        series: [
          {
            data: yData,
            type: 'line',
            showSymbol: false,
            lineStyle: {
              color: '#165DFF'
            },
            itemStyle: {
              color: '#165DFF',
              normal: {
                barBorderRadius: 4,
                type: 'linear',
                color: '#E5EDFF',
              }
            },
            areaStyle: {}
          }
        ]
      };

      option && myChart.setOption(option);
    },
    // 优质企业
    bastEnterprise(info) {
      let source = [];
      info.qualityCompare.map(e => {
        source.push([e.sector, e.avgGlobal, e.localAmount])
      })
      let chartDom = document.getElementById('item2');
      let myChart = echarts.init(chartDom);
      let option;

      option = {
        legend: {
          right: 0,
          itemWidth: 15,
          itemHeight: 7, //修改icon图形大小
        },
        tooltip: {},
        dataset: {
          source: [
            ['product', '全国平均', '本地'],
            ...source
          ]
        },
        xAxis: {
          type: 'category',
          axisLabel: {
            interval: 0,
            rotate: -20, //倾斜的程度
          },
        },
        yAxis: {

          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed",
            },
          },
        },
        series: [
          {
            type: 'bar',
            barWidth: 16,
            itemStyle: {
              normal: {
                barBorderRadius: 4,
                type: 'linear',
                color: '#165DFF',
              }
            },
            barMinHeight: 2,
          },
          {
            type: 'bar',
            barWidth: 16,
            itemStyle: {
              normal: {
                barBorderRadius: 4,
                type: 'linear',
                color: '#36CBCB',
              }
            },
            barMinHeight: 2,
          }
        ]
      };

      option && myChart.setOption(option);
    },
    // 企业环节分布
    rollEnterprise(info) {
      let source = []
      info.industryNodeCount.map((e,index) => {
        if(index!=0){
         source.push([e.industryNodeName, e.allNumber, e.number])
        }
      })
      let chartDom = document.getElementById('item3');
      let myChart = echarts.init(chartDom);
      let option;

      option = {
        grid: {
          left: '15%',
        },
        legend: {
          right: 0,
          itemWidth: 12,
          itemHeight: 7,
        },
        tooltip: {},
        dataset: {
          source: [
            ['product', '全国', '本地'],
            ...source
          ]
        },
        yAxis: {
          type: 'category',
          axisLabel: {
            interval: 0,
            rotate: 0,
            formatter: function (value) {
              return value.length > 5
                ? value.substr(0, 5) + '...'  // 超过5字截断加省略号
                : value;                     // 不足则完整显示
            }
          },
        },
        xAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed",
            },
          }
        },
        series: [
          {
            type: 'bar',
            barWidth: 10,
            itemStyle: {
              normal: {
                color: '#165DFF',
              }
            },
            barMinHeight: 2,
          },
          {
            type: 'bar',
            barWidth: 10,
            itemStyle: {
              normal: {
                //barBorderRadius: 4,
                color: '#36CBCB',
              }
            },
            barMinHeight: 2,
          }
        ]
      };

      option && myChart.setOption(option);
    }
  },
}
</script>

<style lang="scss" scoped>
.enterprise {
  .cu_p {
    cursor: pointer;
  }

  &-top {
    display: flex;
    margin: 16px 0;
    justify-content: space-between;

    .list {
      background: #fff;
      box-sizing: border-box;
      padding-top: 28px;
      padding-left: 24px;
      padding-bottom: 25px;
      padding-right: 25px;
      width: 32%;
      border-radius: 10px;
      display: flex;
      justify-content: space-between;
      box-shadow: 0px 4px 12px 0px #EEF1F8;

      .left {
        display: flex;

        .img {
          img {
            // width: 59px;
            height: 51px;
          }
        }

        .sp-con {
          display: block;
          margin-left: 21px;
        }

        .sp1 {
          font-size: 30px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 30px;
        }

        .sp2 {
          margin-top: 9px;
          font-size: 12px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.45);
          line-height: 22px;
        }
      }

      .right {
        display: flex;

        .item {
          margin-left: 20px;

          .sp1 {
            font-size: 14px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
            line-height: 27px;
          }

          .sp2 {
            padding-top: 11px;
            font-size: 12px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
            line-height: 22px;
          }
        }
      }

      &.list2 {
        padding-right: 45px;
      }

      &.list3 {
        padding-right: 30px;

        .right {
          .sp1 {
            font-size: 14px;
            line-height: 30px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
          }
        }
      }
    }
  }

  &-list {
    .list {
      display: flex;
      justify-content: space-between;
      margin-bottom: 24px;


      .item {
        width: 49%;
        height: 410px;
        background: #FFFFFF;
        box-shadow: 0px 4px 12px 0px #EEF1F8;
        border-radius: 10px;

        .title {
          line-height: 55px;
          padding-left: 24px;
          border-bottom: 1px solid #E9E9E9;
          font-size: 16px;
          font-family: Source Han Sans CN-Medium, Source Han Sans CN;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
        }

        &-con {
          padding: 12px 24px;
          position: relative;
          height: calc(100% - 56px);
          box-sizing: border-box;

          .item-title {
            font-size: 14px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: rgba(29, 33, 41, 0.85);
            line-height: 28px;
            position: absolute;
            left: 20px;
            top: 20px;
          }

          .line {
            width: 100%;
            height: 338px;
          }
        }
      }
    }
  }
}
</style>