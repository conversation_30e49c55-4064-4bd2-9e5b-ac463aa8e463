<template>
  <div class="enterprise">
    <div class="enterprise-top">
      <div class="list list1">
        <div class="left">
          <div class="img">
            <img
              src="https://static.idicc.cn/cdn/pangu/icon005.png"
              alt=""
            >
          </div>
          <div
            class="sp-con"
            style="white-space: nowrap"
          >
            <div class="sp1">
              {{ dataInfo.patentCount || 0 }}
            </div>
            <div class="sp2">
              产业专利总量
            </div>
          </div>
        </div>
        <div
          style="white-space: nowrap"
          class="right"
        >
          <div class="item">
            <div class="sp1">
              {{ dataInfo.patentYearIncr || 0 }}
            </div>
            <div class="sp2">
              今年累计新增
            </div>
          </div>
        </div>
      </div>
      <div class="list list2">
        <!--      @click="getinnovate(false)" -->
        <div class="left">
          <div class="img">
            <img
              src="https://static.idicc.cn/cdn/pangu/icon001.png"
              alt=""
            >
          </div>
          <div class="sp-con">
            <div class="sp1">
              {{ dataInfo.patentEnterpriseCount || 0 }}
            </div>
            <div class="sp2">
              拥有专利企业
            </div>
          </div>
        </div>
        <!--        @click="getinnovate(true)" -->
        <div
          class="right"
          style="white-space: nowrap"
        >
          <div class="item">
            <div class="sp1">
              {{ dataInfo.patentEnterpriseYearCount || 0 }}
            </div>
            <div class="sp2">
              今年累计新增
            </div>
          </div>
        </div>
      </div>
      <div class="list3">
        <div class="left">
          <div class="img">
            <img
              src="https://static.idicc.cn/cdn/pangu/icon04.png"
              alt=""
            >
          </div>
          <div class="sp-con">
            <div class="sp1">
              {{ dataInfo.avgPatentCountOfEnterprise || 0 }}
            </div>
            <div class="sp2">
              平均专利量
            </div>
          </div>
        </div>
        <div class="right2">
          <div
            v-if="param.divisionLevel > 0"
            class="item"
          >
            <div
              :class="
                dataInfo.avgPatentCountOfCountryEnterprise > 0
                  ? 'red'
                  : dataInfo.avgPatentCountOfCountryEnterprise < 0
                    ? 'green'
                    : 'blue'
              "
            >
              {{ dataInfo.avgPatentCountOfCountryEnterprise > 0 ? '+' : ''
              }}{{ dataInfo.avgPatentCountOfCountryEnterprise || 0 }}
            </div>
            <div class="span3">
              较全国
            </div>
          </div>
          <div
            v-if="param.divisionLevel > 1"
            class="item"
          >
            <div
              :class="
                dataInfo.avgPatentCountOfProvinceEnterprise > 0
                  ? 'red'
                  : dataInfo.avgPatentCountOfProvinceEnterprise < 0
                    ? 'green'
                    : 'blue'
              "
            >
              {{ dataInfo.avgPatentCountOfProvinceEnterprise > 0 ? '+' : ''
              }}{{ dataInfo.avgPatentCountOfProvinceEnterprise || 0 }}
            </div>
            <div class="span3">
              较本省
            </div>
          </div>
          <div
            v-if="param.divisionLevel > 2"
            class="item"
          >
            <div
              :class="
                dataInfo.avgPatentCountOfCityEnterprise > 0
                  ? 'red'
                  : dataInfo.avgPatentCountOfCityEnterprise < 0
                    ? 'green'
                    : 'blue'
              "
            >
              {{ dataInfo.avgPatentCountOfCityEnterprise > 0 ? '+' : ''
              }}{{ dataInfo.avgPatentCountOfCityEnterprise || 0 }}
            </div>
            <div class="span3">
              较本市
            </div>
          </div>
          <div class="item">
            <div class="none">
              {{ dataInfo.avgPatentRank || 0 }}
            </div>
            <div class="span3">
              同级排名
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="isShow"
      class="enterprise-list"
    >
      <div
        class="chart"
        style="display: none"
      >
        <div class="title">
          创新研发投入
          <el-tooltip
            effect="dark"
            content="以产业内上市公司研发投入情况来反映产业的创新投入"
            placement="top"
          >
            <i class="el-icon-info" />
          </el-tooltip>
        </div>
        <div class="item">
          <div class="item-con">
            <div class="item-title">
              ·近五年研发投入总额及研发投入占比
            </div>
            <div
              id="InputRatio"
              class="line"
            />
          </div>
          <div class="item-con">
            <div class="item-title">
              <span>·产业企业研发投入总额TOP5</span>
              <!-- <el-date-picker
                v-model="year"
                size="mini"
                type="year"
                format="yyyy"
                :picker-options="pickerOptions"
                placeholder="选择年"
                @change="yearChange"
              /> -->
              <el-select
                v-model="year"
                size="mini"
                placeholder="请选择"
                :popper-append-to-body="false"
                @change="yearChange"
              >
                <el-option
                  v-for="item in optYear"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div
              v-if="isShowSumtop"
              id="sumtop"
              class="line"
            />
          </div>
        </div>
      </div>
      <div class="chart">
        <div class="title">
          创新产出情况
        </div>
        <div class="item">
          <div class="item-part">
            <div class="con-left">
              <div class="item-title">
                ·产业专利类型分布情况
              </div>
              <div
                id="distribution1"
                class="line"
              />
            </div>
            <div class="con-right">
              <div class="tle">
                ·拥有专利企业占比情况
              </div>
              <div class="line progress">
                <el-progress
                  type="circle"
                  :stroke-width="15"
                  :width="165"
                  :percentage="+dataInfo.proportionOfPatentEnterprise"
                />
              </div>
            </div>
          </div>
          <div class="item-con">
            <div class="item-title">
              <span>·近五年发明专利增长趋势情况</span>
              <div class="invent">
                <div
                  v-for="(item, index) in patenttable"
                  :key="index"
                  :class="patent == index ? 'xzoption' : 'option'"
                  @click="cutpatent(index)"
                >
                  {{ item }}
                </div>
              </div>
            </div>
            <div
              id="growthTrend"
              class="line"
            />
          </div>
        </div>
      </div>

      <!-- 专利分布情况 -->
      <div class="charts-list">
        <div class="title">
          专利分布情况
        </div>
        <div class="charts-list-con">
          <div class="charts-item">
            <div class="title1 t">
              ·专利按产业环节分布情况
            </div>
            <div class="title2 t">
              ·产业环节专利数量排名TOP5
            </div>
            <div
              id="id1"
              class="item-list"
            />
            <div
              id="id2"
              class="item-list"
            />
          </div>
          <div class="charts-item">
            <div class="title1 t">
              ·拥有专利企业按产业环节分布情况
            </div>
            <div class="title2 t">
              ·产业环节拥有专利企业数量排名TOP5
            </div>
            <div
              id="id3"
              class="item-list"
            />
            <div
              id="id4"
              class="item-list"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { investmentTop5 } from './../../idicc-scan/apiUrl';
export default {
  name: 'InnovateA',
  components: {},
  data() {
    return {
      value: '', //研发投入总额切换年份
      patenttable: ['发明公布', '发明授权'],
      patent: 0, //0发明公布，1发明授权

      dataInfo: {},
      year: new Date().getFullYear() - 1,
      param: {}, //
      optYear: [],

      isShow: false,
      isShowSumtop: true,
      graphic: [
        {
          type: 'rect',
          left: 'center',
          top: 'middle',
          shape: {
            width: 160,
            height: 70,
            radius: 20,
          },
          style: {
            fill: '#cdd1d7',
            stroke: '#ccc',
          },
          z: 10,
        },
        {
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '暂无数据',
            textAlign: 'center',
            textVerticalAlign: 'middle',
            fill: '#fff',
            fontSize: 18,
          },
          z: 10,
        },
      ],
    };
  },
  mounted() {
    // this.init();
    for (let i = 1; i < 6; i++) {
      let year = new Date().getFullYear() - i;
      this.optYear.push({
        value: year,
        label: year,
      });
    }
  },
  methods: {
    getinnovate(is) {
      this.$emit('diginnovate', is);
    },
    init(info, param) {
      this.dataInfo = info;
      this.param = param;
      this.isShow = false;
      let that = this;
      this.$nextTick(() => {
        this.isShow = true;
        setTimeout(() => {
          // 近5年研发投入总额及研发投入占比
          that.InputRatio(info.totalResearchInvestmentDTOS || []);
          // ·近5年发明专利增长趋势情况
          that.growthTrend(info.inventionPatentTrendyNumberDTOS || []);
          // 产业专利类型分布情况
          that.distribution1(info.patentTypeDTOS || []);
          // 拥有专利占比情况
          // that.distribution2(info.proportionOfPatentEnterprise.replace('%',''));
          // 专利按产业环境分布情况
          that.roll1Render(info.patentCountByIndustryNode || []);
          that.category1Render(info.topPatentCountByIndustryNode || []);
          that.roll2Render(info.patentEnterpriseCountByIndustryNode || []);
          that.category2Render(
            info.topPatentEnterpriseCountByIndustryNode || []
          );

          that.investmentTop5();
        }, 200);
      });
    },
    yearChange() {
      this.isShowSumtop = false;
      let that = this;
      this.$nextTick(() => {
        setTimeout(() => {
          that.isShowSumtop = true;
          that.investmentTop5();
        }, 100);
      });
    },
    investmentTop5() {
      let data = {
        regionCode: this.param.regionCode,
        industryId: this.param.industryId,
        year: this.year,
      };
      investmentTop5(data).then((res) => {
        this.grossAmount(res);
      });
    },
    // 增长情况切换
    cutpatent(i) {
      this.patent = i;
      let arr = [];
      if (i == 0) {
        arr = this.dataInfo.inventionPatentTrendyNumberDTOS; // 近5年发明专利增长趋势
      } else if (i == 1) {
        arr = this.dataInfo.inventionPatentAuthorization; // 近5年发明授权增长趋势
      }
      this.growthTrend(arr);
    },
    // 产业企业研发投入总额top5
    grossAmount(arr) {
      /* if(!arr || arr.length==0){
        return
      } */
      let list = [];
      let nameList = [];
      arr.map((e) => {
        list.push(e.investment);
        nameList.push(e.enterpriseName);
      });
      let graphic = [];
      if (list.length == 0 && nameList.length == 0) {
        graphic = this.graphic;
      }
      var chartDom = document.getElementById('sumtop');
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        graphic,
        grid: {
          left: '5%',
          right: '15%',
          bottom: '5%',
          top: '5%',
          containLabel: true,
        },
        tooltip: {
          show: true,
          formatter: '{b}<br/>{c}亿元',
          textStyle: {
            fontWeight: 'bold',
            fontSize: 12,
          },
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            //文字
            axisLabel: {
              show: true,
              fontSize: 12,
              color: '#86909C',
            },
            // 边框
            splitLine: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#e0e3e6',
              },
            },
            // 刻度
            axisTick: {
              show: false,
            },
            data: nameList,
          },
        ],
        xAxis: [
          {
            type: 'value',
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false, // 隐藏 x 轴横线
            },
            // 刻度线 虚线
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
              },
            },
            name: '',
            nameLocation: 'end',
            nameTextStyle: {
              padding: [20, 0, 0, 25],
            },
            axisLabel: {
              inside: false,
              textStyle: {
                fontSize: 12,
              },
              interval: 0,
              formatter: '{value}',
            },
          },
        ],
        series: [
          {
            name: 'assist',
            type: 'bar',
            stack: '1',
            label: {
              show: true,
              position: 'top',
            },
            itemStyle: {
              normal: {
                barBorderColor: 'rgba(0,0,0,0)',
                color: 'rgba(0,0,0,0)',
              },
              emphasis: {
                barBorderColor: 'rgba(0,0,0,0)',
                color: 'rgba(0,0,0,0)',
              },
            },
            tooltip: {
              trigger: 'none',
            },
            data: [],
          },
          {
            type: 'bar',
            stack: '1',
            barWidth: 12,
            barBorderRadius: 30,
            itemStyle: {
              normal: {
                barBorderRadius: 20,
                color: function () {
                  return {
                    type: 'linear',
                    x: 1,
                    y: 0,
                    x2: 0,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: '#1e62ff', // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: '#e5ecff', // 100% 处的颜色
                      },
                    ],
                  };
                },
              },
            },
            data: list,
          },
        ],
      };
      option && myChart.setOption(option);
    },
    // 投入占比
    InputRatio(arr) {
      /*  if(!arr || arr.length==0){
         return
       } */
      let list1 = [];
      let list2 = [];
      let yData = [];
      arr.map((e) => {
        list1.push(e.amount);
        list2.push(e.proportion);
        yData.push(e.year);
      });

      let graphic = [];
      if (list1.length == 0 && list2.length == 0 && yData.length == 0) {
        graphic = this.graphic;
      }
      var chartDom = document.getElementById('InputRatio');
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        graphic,
        grid: {
          left: '5%',
          right: '5%',
          bottom: '5%',
          top: '18%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
        },
        axisLabel: {
          show: true,
          fontSize: 12,
          color: '#86909C',
        },

        legend: {
          itemWidth: 15,
          itemHeight: 7, //修改icon图形大小

          data: [
            {
              name: '总额',
            },
            {
              name: '创新占比',
            },
          ],
        },
        xAxis: [
          {
            type: 'category',
            data: yData,
            axisTick: {
              show: false,
            },
            axisPointer: {
              type: 'shadow',
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '百万',
            nameTextStyle: {
              padding: [0, 28, 0, 0],
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
              },
            },
            axisLabel: {
              formatter: '{value}',
            },
          },
          {
            type: 'value',
            name: '%',
            interval: 15,
            //alignTicks:true,
            nameTextStyle: {
              padding: [0, 0, 0, 28],
            },
            splitLine: {
              show: !true,
              lineStyle: {
                type: 'inherit',
              },
            },
            axisLabel: {
              formatter: '{value} ',
            },
          },
        ],
        series: [
          {
            name: '总额',
            type: 'bar',
            barWidth: 20,
            barBorderRadius: 30,
            itemStyle: {
              normal: {
                barBorderRadius: 5,
                color: '#165DFF',
              },
            },
            tooltip: {
              valueFormatter: function (value) {
                return value + '百万';
              },
            },
            data: list1,
          },
          {
            name: '创新占比',
            type: 'line',
            yAxisIndex: 1,
            symbol: 'none', //取消折点圆圈
            tooltip: {
              valueFormatter: function (value) {
                return value + '%';
              },
            },
            itemStyle: {
              normal: {
                color: '#14C9C9',
              },
            },
            data: list2,
          },
        ],
      };

      option && myChart.setOption(option);
    },
    // 近五年发明趋势增长
    growthTrend(arr) {
      if (!arr || arr.length == 0) {
        return;
      }
      let list1 = [];
      let list2 = [];
      let yData = [];
      arr.map((e) => {
        yData.push(e.year);
        list1.push(e.number || 0);
        list2.push(+(e.incrementRate || 0));
      });
      // console.error('list1', list1)
      // console.error('list2', list2)
      var chartDom = document.getElementById('growthTrend');
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        grid: {
          left: '5%',
          right: '5%',
          bottom: '5%',
          top: '18%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
        },
        axisLabel: {
          show: true,
          fontSize: 12,
          color: '#86909C',
        },
        legend: {
          data: [
            {
              name: '数量',
            },
            {
              name: '增长率',
              lineStyle: {
                type: 'solid',
              },
            },
          ],
          itemWidth: 15,
          itemHeight: 7, //修改icon图形大小
        },
        xAxis: [
          {
            type: 'category',
            data: yData,
            axisTick: {
              show: false,
            },
            axisPointer: {
              type: 'shadow',
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '',
            nameTextStyle: {
              padding: [0, 28, 0, 0],
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
              },
            },
            axisLabel: {
              formatter: '{value}',
            },
          },
          {
            type: 'value',
            name: '%',
            nameTextStyle: {
              padding: [0, 0, 0, 28],
            },
            splitLine: {
              show: !true,
              lineStyle: {
                type: 'dashed',
              },
            },
            axisLabel: {
              formatter: '{value} ',
            },
          },
        ],
        series: [
          {
            name: '数量',
            type: 'bar',
            barWidth: 20,
            barBorderRadius: 30,
            itemStyle: {
              normal: {
                barBorderRadius: 5,
                color: '#165DFF',
              },
            },
            tooltip: {
              valueFormatter: function (value) {
                return value + '个';
              },
            },
            data: list1,
          },
          {
            name: '增长率',
            type: 'line',
            yAxisIndex: 1,
            symbol: 'none', //取消折点圆圈
            tooltip: {
              valueFormatter: function (value) {
                return value + '%';
              },
            },
            itemStyle: {
              normal: {
                color: '#14C9C9',
              },
            },
            data: list2,
          },
        ],
      };

      option && myChart.setOption(option);
    },
    // 产业专利分布情况
    distribution1(arr) {
      /*  if(!arr || arr.length==0){
         return
       } */
      let list = [];
      arr.map((e) => {
        list.push({ value: e.count, name: e.type });
      });
      let chartDom = document.getElementById('distribution1');
      let myChart = echarts.init(chartDom);
      let option;

      option = {
        grid: {
          // 离容器左侧的距离
          left: '-7%',
          top: '20%',
          right: '15%',
          bottom: '50%',
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: '#00112dbf', // 提示框浮层的背景颜色。
          borderColor: '#0066FF', // 提示框浮层的边框颜色。
          borderWidth: 1, // 提示框浮层的边框宽。
          textStyle: {
            // 提示框浮层的文本样式。
            color: '#fff',
            fontStyle: 'normal',
            fontWeight: 'normal',
            fontFamily: 'sans-serif',
            fontSize: 12,
          },
          //实例
          formatter: function (params) {
            let res =
              params.data.name +
              '： ' +
              '<span style="color: #00FFF0;font-size: 12px;">' +
              params.data.value +
              '</span> 个';
            return res;
          },
        },
        legend: {
          textStyle: {
            color: 'rgba(0,0,0,0.65)',
            fontSize: 12,
            opacity: 0.8,
            lineHeight: 23, // 设置文字之间的上下间距
          },
          // right: "right",//调整图例位置
          left: '60%',
          orient: 'vertical',
          bottom: 'middle',
          //itemGap: 16, // 设置图例项之间的间距
          itemWidth: 15,
          top: '12%', //调整图例位置
          itemHeight: 7, //修改icon图形大小
          icon: 'circle', //图例前面的图标形状
        },
        series: [
          {
            type: 'pie',
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            left: '-42%',
            top: '10%',
            label: {
              show: false,
              position: 'center',
            },
            labelLine: {
              show: false,
            },
            data: list,
          },
        ],
      };
      if (list.length == 0) {
        let graphic = [];
        (graphic = this.graphic),
          (option = {
            graphic,
          });
      }

      option && myChart.setOption(option);
    },
    distribution2(value) {
      let list = [
        {
          value: value,
          name: '拥有专利企业占比',
        },
        { value: 100 - value, name: '专利企业占比' },
      ];

      let chartDom = document.getElementById('distribution2');
      let myChart = echarts.init(chartDom);
      let option;

      option = {
        grid: {
          // 离容器左侧的距离
          left: '7%',
          top: '0%',
          right: '1%',
          bottom: '10%',
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: '#00112dbf', // 提示框浮层的背景颜色。
          borderColor: '#0066FF', // 提示框浮层的边框颜色。
          borderWidth: 1, // 提示框浮层的边框宽。
          textStyle: {
            // 提示框浮层的文本样式。
            color: '#fff',
            fontStyle: 'normal',
            fontWeight: 'normal',
            fontFamily: 'sans-serif',
            fontSize: 12,
          },
          //实例
          formatter: function (params) {
            let res =
              params.data.name +
              '： ' +
              '<span style="color: #00FFF0;font-size: 12px;">' +
              params.data.value +
              '</span> %';
            return res;
          },
        },
        // 圆心文字
        title: [
          {
            text: '按环节分布',
            top: '33%',
            textAlign: 'center',
            left: '39%',
            textStyle: {
              color: 'rgba(0,0,0,0.45)',
              fontSize: 14,
              fontWeight: '400',
              fontFamily: 'Source Han Sans CN-Regular, Source Han Sans CN',
            },
          },
          {
            text: `${+value}%`,
            top: '40%',
            textAlign: 'center',
            left: '39%',
            textStyle: {
              color: 'rgba(0,0,0,0.85)',
              fontSize: 30,
              fontWeight: '400',
              fontFamily: 'Source Han Sans CN-Regular, Source Han Sans CN',
            },
          },
        ],
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['55%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            left: '-17%',
            top: '-10%',
            label: {
              show: false,
              position: 'center',
            },
            labelLine: {
              show: false,
            },
            data: list,
          },
        ],
      };

      option && myChart.setOption(option);
    },

    // 专利分布
    roll1Render(arr) {
      /*  if(!arr || arr.length==0){
         return
       } */
      let list = [];
      let topData = {};
      arr.map((e, i) => {
        if (i == 0) {
          topData = { value: e.number, name: e.industryNodeName };
        } else {
          list.push({ value: e.number, name: e.industryNodeName });
        }
      });
      let chartDom = document.getElementById('id1');
      let myChart = echarts.init(chartDom);
      let option;

      option = {
        grid: {
          // 离容器左侧的距离
          left: '-7%',
          top: '20%',
          right: '15%',
          bottom: '50%',
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: '#00112dbf', // 提示框浮层的背景颜色。
          borderColor: '#0066FF', // 提示框浮层的边框颜色。
          borderWidth: 1, // 提示框浮层的边框宽。
          textStyle: {
            // 提示框浮层的文本样式。
            color: '#fff',
            fontStyle: 'normal',
            fontWeight: 'normal',
            fontFamily: 'sans-serif',
            fontSize: 12,
          },
          //实例
          formatter: function (params) {
            let res =
              params.data.name +
              '： ' +
              '<span style="color: #00FFF0;font-size: 12px;">' +
              params.data.value +
              '</span> 个';
            return res;
          },
        },
        // 圆心文字
        title: [
          {
            text: '按环节分布',
            top: '43%',
            textAlign: 'center',
            left: '34%',
            textStyle: {
              color: 'rgba(0,0,0,0.45)',
              fontSize: 12,
              fontWeight: '400',
              fontFamily: 'Source Han Sans CN-Regular, Source Han Sans CN',
            },
          },
          {
            text: topData.value,
            top: '50%',
            textAlign: 'center',
            left: '34%',
            textStyle: {
              color: 'rgba(0,0,0,0.85)',
              fontSize: topData.value.length > 4 ? 20 : 25,
              fontWeight: '400',
              fontFamily: 'Source Han Sans CN-Regular, Source Han Sans CN',
            },
          },
        ],
        legend: {
          textStyle: {
            color: 'rgba(0,0,0,0.65)',
            fontSize: 12,
            opacity: 0.8,
            lineHeight: 33, // 设置文字之间的上下间距
          },
          // right: "right",//调整图例位置
          left: '65%',
          orient: 'vertical',
          bottom: 'middle',
          //itemGap: 16, // 设置图例项之间的间距
          itemWidth: 10,
          top: '12%', //调整图例位置
          itemHeight: 7, //修改icon图形大小
          // icon: 'circle', //图例前面的图标形状
          // 添加分页配置
          type: 'scroll', // 开启滚动翻页功能
          pageIconColor: '#165DFF', // 翻页按钮颜色 - 改为主题蓝色
          pageIconInactiveColor: '#D3D7E4', // 翻页按钮不激活时的颜色 - 改为浅灰色
          pageIconSize: 12, // 翻页按钮大小 - 从15减小到12
          pageTextStyle: {
            color: '#86909C', // 分页文本颜色
            fontSize: 11 // 分页文本字体大小
          },
          animation: true,
          animationDurationUpdate: 800,
          formatter: function (value) {
            let newvalue = '';
            if (value.length > 6) {
              newvalue = value.slice(0, 6) + '...';
            } else {
              newvalue = value;
            }
            return newvalue;
          },
        },
        series: [
          {
            //name: 'Access From',
            type: 'pie',
            radius: ['40%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            left: '-30%',
            top: '10%',
            label: {
              show: false,
              position: 'center',
            },
            labelLine: {
              show: false,
            },
            data: list,
          },
        ],
      };
      let graphic = [];
      if (list.length == 0) {
        graphic = this.graphic;
        option = {
          graphic,
        };
      }

      option && myChart.setOption(option);
    },
    category1Render(arr) {
      /*  if(!arr || arr.length==0){
         return
       } */
      let list = [];
      let nameList = [];
      arr.map((e) => {
        list.push(e.number);
        nameList.push(e.industryNodeName);
      });
      let graphic = [];
      if (list.length == 0) {
        graphic = this.graphic;
      }
      let chartDom = document.getElementById('id2');
      let myChart = echarts.init(chartDom);
      let option;

      option = {
        graphic,
        grid: {
          left: '5%',
          right: '15%',
          bottom: '10%',
          top: '15%',
          containLabel: true,
        },
        tooltip: {
          show: true,
          formatter: '{b}<br/>{c}个',
          textStyle: {
            fontWeight: 'bold',
            fontSize: 12,
          },
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            //文字
            axisLabel: {
              show: true,
              fontSize: 12,
              color: '#86909C',
              formatter: function (value) {
                let newvalue = '';
                if (value.length > 6) {
                  newvalue = value.slice(0, 6) + '...';
                } else {
                  newvalue = value;
                }
                return newvalue;
              },
            },
            // 边框
            splitLine: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#e0e3e6',
              },
            },
            // 刻度
            axisTick: {
              show: false,
            },
            data: nameList,
          },
        ],
        xAxis: [
          {
            type: 'value',
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false, // 隐藏 x 轴横线
            },
            // 刻度线 虚线
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
              },
            },
            name: '个',
            nameLocation: 'end',
            nameTextStyle: {
              padding: [8, 0, 0, 5],
              verticalAlign: 'top',
            },
            axisLabel: {
              inside: false,
              textStyle: {
                fontSize: 12,
              },
              interval: 0,
              hideOverlap: true,
              formatter: '{value}',
            },
          },
        ],
        series: [
          {
            name: 'assist',
            type: 'bar',
            stack: '1',
            label: {
              show: true,
              position: 'top',
            },
            itemStyle: {
              normal: {
                barBorderColor: 'rgba(0,0,0,0)',
                color: 'rgba(0,0,0,0)',
              },
              emphasis: {
                barBorderColor: 'rgba(0,0,0,0)',
                color: 'rgba(0,0,0,0)',
              },
            },
            tooltip: {
              trigger: 'none',
            },
            data: [],
          },
          {
            type: 'bar',
            stack: '1',
            barWidth: 12,
            barBorderRadius: 30,
            itemStyle: {
              normal: {
                barBorderRadius: 20,
                color: function () {
                  return {
                    type: 'linear',
                    x: 1,
                    y: 0,
                    x2: 0,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: '#1e62ff', // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: '#e5ecff', // 100% 处的颜色
                      },
                    ],
                  };
                },
              },
            },
            data: list,
          },
        ],
      };
      option && myChart.setOption(option);
    },

    roll2Render(arr) {
      /*  if(!arr || arr.length==0){
         return
       } */
      let list = [];
      let topData = {};
      arr.map((e, i) => {
        if (i == 0) {
          topData = { value: e.number, name: e.industryNodeName };
        } else {
          list.push({ value: e.number, name: e.industryNodeName });
        }
      });
      let chartDom = document.getElementById('id3');
      let myChart = echarts.init(chartDom);
      let option;

      option = {
        grid: {
          // 离容器左侧的距离
          left: '-7%',
          top: '20%',
          right: '15%',
          bottom: '50%',
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: '#00112dbf', // 提示框浮层的背景颜色。
          borderColor: '#0066FF', // 提示框浮层的边框颜色。
          borderWidth: 1, // 提示框浮层的边框宽。
          textStyle: {
            // 提示框浮层的文本样式。
            color: '#fff',
            fontStyle: 'normal',
            fontWeight: 'normal',
            fontFamily: 'sans-serif',
            fontSize: 12,
          },
          //实例
          formatter: function (params) {
            let res =
              params.data.name +
              '： ' +
              '<span style="color: #00FFF0;font-size: 12px;">' +
              params.data.value +
              '</span> 家';
            return res;
          },
        },
        // 圆心文字
        title: [
          {
            text: '按环节分布',
            top: '43%',
            textAlign: 'center',
            left: '34%',
            textStyle: {
              color: 'rgba(0,0,0,0.45)',
              fontSize: 12,
              fontWeight: '400',
              fontFamily: 'Source Han Sans CN-Regular, Source Han Sans CN',
            },
          },
          {
            text: topData.value,
            top: '50%',
            textAlign: 'center',
            left: '34%',
            textStyle: {
              color: 'rgba(0,0,0,0.85)',
              fontSize: topData.value.length > 4 ? 20 : 25,
              fontWeight: '400',
              fontFamily: 'Source Han Sans CN-Regular, Source Han Sans CN',
            },
          },
        ],
        legend: {
          textStyle: {
            color: 'rgba(0,0,0,0.65)',
            fontSize: 12,
            opacity: 0.8,
            lineHeight: 33, // 设置文字之间的上下间距
          },
          // right: "right",//调整图例位置
          left: '65%',
          orient: 'vertical',
          bottom: 'middle',
          //itemGap: 16, // 设置图例项之间的间距
          itemWidth: 10,
          top: '12%', //调整图例位置
          itemHeight: 7, //修改icon图形大小
          // icon: 'circle', //图例前面的图标形状
          // 添加分页配置
          type: 'scroll', // 开启滚动翻页功能
          pageIconColor: '#165DFF', // 翻页按钮颜色 - 改为主题蓝色
          pageIconInactiveColor: '#D3D7E4', // 翻页按钮不激活时的颜色 - 改为浅灰色
          pageIconSize: 12, // 翻页按钮大小 - 从15减小到12
          pageTextStyle: {
            color: '#86909C', // 分页文本颜色
            fontSize: 11 // 分页文本字体大小
          },
          animation: true,
          animationDurationUpdate: 800,
          formatter: function (value) {
            let newvalue = '';
            if (value.length > 6) {
              newvalue = value.slice(0, 6) + '...';
            } else {
              newvalue = value;
            }
            return newvalue;
          },
        },
        series: [
          {
            //name: 'Access From',
            type: 'pie',
            radius: ['40%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            left: '-30%',
            top: '10%',
            label: {
              show: false,
              position: 'center',
            },
            labelLine: {
              show: false,
            },
            data: list,
          },
        ],
      };
      let graphic = [];
      if (list.length == 0) {
        graphic = this.graphic;
        option = {
          graphic,
        };
      }

      option && myChart.setOption(option);
    },

    category2Render(arr) {
      /* if(!arr || arr.length==0){
        return
      } */
      let list = [];
      let nameList = [];
      arr.map((e) => {
        list.push(e.number);
        nameList.push(e.industryNodeName);
      });
      let graphic = [];
      if (list.length == 0) {
        graphic = this.graphic;
      }
      let chartDom = document.getElementById('id4');
      let myChart = echarts.init(chartDom);
      let option;

      option = {
        graphic,
        grid: {
          left: '5%',
          right: '15%',
          bottom: '10%',
          top: '15%',
          containLabel: true,
        },
        tooltip: {
          show: true,
          formatter: '{b}<br/>{c}家',
          textStyle: {
            fontWeight: 'bold',
            fontSize: 12,
          },
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            //文字
            axisLabel: {
              show: true,
              fontSize: 12,
              color: '#86909C',
              formatter: function (value) {
                let newvalue = '';
                if (value.length > 6) {
                  newvalue = value.slice(0, 6) + '...';
                } else {
                  newvalue = value;
                }
                return newvalue;
              },
            },
            // 边框
            splitLine: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#e0e3e6',
              },
            },
            // 刻度
            axisTick: {
              show: false,
            },
            data: nameList,
          },
        ],
        xAxis: [
          {
            type: 'value',
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false, // 隐藏 x 轴横线
            },
            // 刻度线 虚线
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
              },
            },
            name: '个',
            nameLocation: 'end',
            nameTextStyle: {
              padding: [8, 0, 0, 5],
              verticalAlign: 'top',
            },
            axisLabel: {
              inside: false,
              textStyle: {
                fontSize: 12,
              },
              interval: 0,
              hideOverlap: true,
              formatter: '{value}',
            },
          },
        ],
        series: [
          {
            name: 'assist',
            type: 'bar',
            stack: '1',
            label: {
              show: true,
              position: 'top',
            },
            itemStyle: {
              normal: {
                barBorderColor: 'rgba(0,0,0,0)',
                color: 'rgba(0,0,0,0)',
              },
              emphasis: {
                barBorderColor: 'rgba(0,0,0,0)',
                color: 'rgba(0,0,0,0)',
              },
            },
            tooltip: {
              trigger: 'none',
            },
            data: [],
          },
          {
            type: 'bar',
            stack: '1',
            barWidth: 12,
            barBorderRadius: 30,
            itemStyle: {
              normal: {
                barBorderRadius: 20,
                color: function () {
                  return {
                    type: 'linear',
                    x: 1,
                    y: 0,
                    x2: 0,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: '#1e62ff', // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: '#e5ecff', // 100% 处的颜色
                      },
                    ],
                  };
                },
              },
            },
            data: list,
          },
        ],
      };
      option && myChart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
//平均拥有专利
.list3 {
  background: #fff;
  box-sizing: border-box;
  padding-top: 28px;
  padding-left: 24px;
  padding-bottom: 25px;
  padding-right: 25px;
  border-radius: 10px;
  width: 32%;
  min-width: 435px;
  display: flex;
  justify-content: space-between;

  .left {
    display: flex;

    .img {
      img {
        // width: 59px;
        height: 51px;
      }
    }

    .sp-con {
      display: block;
      margin-left: 21px;
    }

    .sp1 {
      font-size: 30px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      line-height: 30px;
    }

    .sp2 {
      margin-top: 9px;
      font-size: 12px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.45);
      line-height: 22px;
      white-space: nowrap;
    }
  }

  .right2 {
    display: flex;
    margin-right: 30px;

    .item {
      margin-left: 20px;
      padding-top: 22.5px;

      .span3 {
        padding-top: 7.5px;
        font-size: 12px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
        white-space: nowrap;
      }

      .none {
        font-size: 12px;
      }

      .red {
        color: #f53f3f;
        font-size: 12px;
      }

      .green {
        color: #00b42a;
        font-size: 12px;
      }

      .blue {
        color: #165dff;
        font-size: 12px;
      }
    }
  }
}

//图表盒子
.chart {
  margin-bottom: 24px;
  height: 410px;
  border-radius: 10px 10px 10px 10px;
  background: #ffffff;
  box-shadow: 0px 4px 14px 0px #eef1f8;

  .title {
    line-height: 55px;
    padding-left: 24px;
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }

  .item {
    display: flex;
    width: 100%;

    .item-title {
      display: flex;
      justify-content: space-between;
      width: 90%;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(29, 33, 41, 0.85);
      line-height: 28px;
      position: absolute;
      left: 20px;
      top: 20px;
      z-index: 9;

      .invent {
        display: flex;

        .xzoption {
          width: 80px;
          height: 25px;
          color: #3370ff;
          background: #ffffff;
          border-radius: 2px 0px 0px 2px;
          opacity: 1;
          border: 1px solid #3370ff;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
        }

        .option {
          width: 80px;
          height: 25px;
          background: #ffffff;
          border-radius: 2px 0px 0px 2px;
          opacity: 1;
          border: 1px solid #d9d9d9;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
        }
      }
    }

    .item-con {
      width: 50%;
      height: 355px;
      padding: 12px 24px;
      position: relative;
      border-top: 1px solid #e9e9e9;
    }

    .item-part {
      width: 50%;
      display: flex;
      height: 355px;
      padding: 12px 24px;
      position: relative;
      border-top: 1px solid #e9e9e9;

      .con-right {
        width: 45%;

        .tle {
          font-size: 14px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: rgba(29, 33, 41, 0.85);
          line-height: 28px;
          position: relative;
          //left: 20px;
          top: 8px;
        }
      }

      .con-left {
        width: 55%;
      }
    }

    .line {
      width: 100%;
      height: 288px;
      padding-top: 40px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;

      &.progress {
        padding-top: 0px;
      }
    }

    .item-con:last-child {
      border-left: 1px solid #e9e9e9;
    }
  }
}

.charts {
  &-list {
    height: 410px;
    background: #ffffff;
    box-shadow: 0px 4px 14px 0px #eef1f8;
    border-radius: 10px;
    margin-bottom: 24px;

    .title {
      line-height: 54px;
      height: 55px;
      padding-left: 24px;
      box-sizing: border-box;
      border-bottom: 1px solid #e9e9e9;

      font-size: 16px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }

    &-con {
      height: 100%;
      display: flex;
    }
  }

  &-item {
    width: 50%;
    height: calc(100% - 55px);
    border-right: 1px solid #d9dce0;
    display: flex;
    position: relative;

    .t {
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(29, 33, 41, 0.85);
      line-height: 28px;
      position: absolute;
      top: 20px;

      &.title1 {
        left: 20px;
      }

      &.title2 {
        left: calc(50% + 20px);
      }
    }

    .item-list {
      width: 50%;
      height: 100%;
    }

    #id8 {
      padding: 15px 15px 15px 0;
      margin: auto;
    }

    .item-lists {
      width: 100%;
      height: 100%;
    }
  }
}

// 顶部
.enterprise {
  &-top {
    display: flex;
    margin: 16px 0;
    justify-content: space-between;

    .list {
      background: #fff;
      box-sizing: border-box;
      padding-top: 28px;
      padding-left: 24px;
      padding-bottom: 25px;
      padding-right: 25px;
      border-radius: 10px;
      width: 32%;
      display: flex;
      justify-content: space-between;

      .left {
        display: flex;

        .img {
          img {
            width: 59px;
            height: 51px;
          }
        }

        .sp-con {
          display: block;
          white-space: nowrap;
          margin-left: 21px;
        }

        .sp1 {
          font-size: 30px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 30px;
        }

        .sp2 {
          margin-top: 9px;
          font-size: 12px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.45);
          line-height: 22px;
        }
      }

      .right {
        display: flex;
        margin-right: 20px;

        .item {
          margin-left: 20px;

          .sp1 {
            font-size: 14px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
            line-height: 28px;
          }

          .sp2 {
            padding-top: 11px;
            font-size: 12px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
            line-height: 22px;
          }
        }
      }

      &.list2 {
        padding-right: 45px;
        border-radius: 10px;
      }

      &.list3 {
        padding-right: 30px;

        .right {
          .sp1 {
            font-size: 30px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
          }

          .span3 {
            //margin-top: 9px;
            font-size: 14px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
            line-height: 22px;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 1450px) {
  body {
    .enterprise-top .list {
      width: 31%;
    }
  }
}

.enterprise-top .list,
.enterprise-top .list3 {
  margin-right: 16px;
  box-shadow: 0px 4px 12px 0px #eef1f8;
}
</style>
