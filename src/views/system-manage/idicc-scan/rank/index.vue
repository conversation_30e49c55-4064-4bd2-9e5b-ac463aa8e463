<template>
  <div class="rank">
    <div class="rank-top">
      <div class="rank-top-list appendToBodyFalse">
        <div class="label">
          产业环节：
        </div>
        <el-cascader
          ref="cascader1"
          v-model="chainId"
          size="mini"
          :options="chainList"
          :props="{ checkStrictly: true }"
          @change="check"
        />
      </div>
      <div class="rank-top-list">
        <div class="label">
          榜单维度：
        </div>
        <el-select
          v-model="value1"
          size="mini"
          :popper-append-to-body="false"
          placeholder="请选择"
          @change="change1"
        >
          <el-option
            v-for="item in options1"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-select
          ref="select2"
          v-model="rankType"
          style="margin-left:10px;"
          :popper-append-to-body="false"
          size="mini"
          placeholder="请选择"
          @change="change2"
        >
          <el-option
            v-for="item in options2"
            :key="item.metrics"
            :label="item.name"
            :value="item.metrics"
          />
        </el-select>
      </div>
      <div class="rank-top-list">
        <div class="label">
          排行地区：
        </div>
        <div class="ul">
          <div 
            v-if="params.checkArea.length>0"
            class="li" 
            :class="divisionType===0?'on':''" 
            @click="clickLi(0)"
          >
            全国
          </div>
          <div
            v-if="params.checkArea.length>1"
            class="li" 
            :class="divisionType===1?'on':''" 
            @click="clickLi(1)"
          >
            本省
          </div>
          <div 
            v-if="params.checkArea.length>2"
            class="li" 
            :class="divisionType===2?'on':''" 
            @click="clickLi(2)"
          >
            本市
          </div>
          <div class="li zdy appendToBodyFalse">
            <!-- <i class="el-icon-plus" />
            自定义 -->
            <el-cascader
              v-model="areaArr"
              size="mini"
              clearable
              collapse-tags
              :options="areaList"
              :show-all-levels="false"
              :props="regionProp"
              @change="casChange"
              @expand-change="expandChange"
            />
          </div>
        </div>
      </div>
      <div class="btn">
        <div
          class="btn-list"
          @click="reset"
        >
          重置
        </div>
        <div
          class="btn-list on "
          :class="isSearch?'disabled':''"
          @click="search"
        >
          查询
        </div>
      </div>
    </div>

    <div
      v-if="rankList && rankList.top10"
      class="table-con"
    >
      <div class="title">
        地区排行榜
      </div>
      <div class="table">
        <div class="txt1">
          {{ txt1 }}{{ txt2 }}排行榜
        </div>
        <div class="table-body">
          <div class="tr on">
            <div class="td td1">
              排序
            </div>
            <div class="td td2">
              地区
            </div>
            <div class="td td3">
              排名指标
            </div>
            <div
              v-if="showrankType!=15"
              class="td td4"
            >
              占比
            </div>
            <div class="td td5">
              榜单日期
            </div>
          </div>
          <div v-if="rankList.top10.length>0">
            <div
              v-for="(item,i) in rankList.top10"
              :key="i"
              class="tr"
              :class="[(i == +rankList.rank-1 ? 'rank' : item.rank?'rank':''), (i%2==0?'':'on')]"
            >
              <div
                class="td td1 "
                :class="i<3?'on':''"
              >
                {{ item.rank ? item.rank : (i+1) }}
              </div>
              <div class="td td2">
                {{ item.location }}
              </div>
              <div
                class="td td3"
                :class="i>2?'on':''"
              >
                <div class="flex">
                  <div
                    v-if="item.proportionOfProportion"
                    class="zhi"
                    :style="'width:'+(item.proportionOfProportion)+'%;'"
                  />
                  <div
                    v-if="showrankType==15"
                    class="txt"
                  >
                    {{ item.num }}%
                  </div>
                  <div
                    v-else
                    class="txt"
                  >
                    {{ item.num }}
                  </div>
                </div>
              </div>
              <div
                v-if="showrankType!=15"
                class="td td4"
              >
                {{ item.proportion }}%
              </div>
              <div class="td td5">
                <div v-if="item.date">
                  {{ item.date.slice(0,10) }}
                </div>
              </div>
            </div>
          </div>
          <div
            v-else
            class="nodata"
          >
            暂无该地区数据
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { 
    getDimension, 
    rankingResult, 
    loadByOrgId, 
    // getRegion, 
    getAllAddress 
  } from './../apiUrl';
  export default {
    name: 'RanK',
    props: {
      defaultInfo: {
        type: Object,
        default: ()=>{
          return {}
        }
      },
    },
    data() {
      return {
        chainId: '',
        rankType: '',
        opt: [],
        chainList: [],
        divisionType: '',
        params: {
          checkArea: [],
        },
        rankList: null, // 地区排行列表
        areaArr: [],
        areaList: [], //

        regionProp:{ 
          multiple: true,
          // checkStrictly: true,
        },


        options1:[
          {label:'产业基础', value:'1'},
          // {label:'创新水平', value:'2'},
          {label:'投资活力', value:'3'},
        ],
        options2:[],
        value1: '',
        optInfo: [],

        isSearch: true,

        txt1:'',
        txt2:'',
        showrankType:''
      }
    },
    methods: {
      check(){
        let param = {
          industryChainNodeId: +this.chainId[this.chainId.length-1],
          regionCode: this.params.checkArea[this.params.checkArea.length-1],
          divisionLevel: +this.params.divisionLevel,
          rankType: +this.rankType,
          divisionType: this.divisionType,
          provinces: [],
          cities: [],
          areas: [],
        }
        this.areaArr.map(e=>{
          e[0] && param.provinces.push(e[0])
          e[1] && param.cities.push(e[1])
          e[2] && param.areas.push(e[2])
        });
        if(param.industryChainNodeId && param.rankType && (param.divisionType!=='' || param.provinces.length>0 )){
          this.isSearch = false;
        }else{
          this.isSearch = true;
        }
        if(this.divisionType!==''){
          this.areaArr=[];
        }
      },
      loadByOrgId(id){
        let data = {
          chainId: id,
          countEnterpriseNum: 0
        }
        loadByOrgId(data).then(res=>{
          //console.log('loadByOrgId', res);
          this.chainList = res.childNodes.map(e=>{
            return {
              value: e.id,
              label: e.nodeName,
              children: e.childNodes?.map(e2=>{
                return {
                  value: e2.id,
                  label: e2.nodeName,
                  children:e2.childNodes.length> 0 ?  e2.childNodes?.map(e3=>{
                    return{
                      value: e3.id,
                      label: e3.nodeName,
                    }
                  }) : null
                }
              })
            }
          })
        });
        this.chainId=[]
        this.check()
      },
      init(param){
        if(param.checkArea.length==1 && this.divisionType!==0){
          this.divisionType=''
          this.check()
        }else if(param.checkArea.length==2 && this.divisionType==2){
          this.divisionType=''
          this.check()
        }
        this.params=param;
        this.getDimension();
        this.loadByOrgId(param.chainId);
        this.getAllAddress(param);
      },
      getAllAddress(){
        getAllAddress().then(res=>{
          let addressList=[];
          let areaList = [];
          // 省
          res.map(e=>{
            if(e.parentId==0){
              e.label = e.province;
              e.value = e.code;
              addressList.push(e)
            }else{
              e.label = e.province;
              e.value = e.code;
              areaList.push(e)
            }
          });
          // 市
          addressList.map(el=>{
            res.map(e=>{
              if(el.id == e.parentId){
                e.label = e.city;
                e.value = e.code;
                if(el.children){
                  el.children.push(e);
                }else{
                  el.children=[];
                  el.children.push(e);
                }
              }
            })
            return el;
          })
          // 区
          addressList.map(el=>{
            if(el.children && el.children.length>0){
              for(let i=0; i<el.children.length; i++){
                let el1 = el.children[i];
                areaList.map(e=>{
                  if(e.parentId == el1.id){
                    e.label = e.area;
                    e.value = e.code;
                    if(el1.children){
                      el1.children.push(e);
                    }else{
                      el1.children=[];
                      el1.children.push(e);
                    }
                  }
                });
              }
            }
            return el
          })

          //console.log('addressList', addressList);

          this.areaList = addressList;

        });
      },
      search() {
        if(this.isSearch){
          return false;
        }
        //console.log('this.params', this.params, this.areaArr);
        if(!this.chainId[this.chainId.length-1]){
          this.$message.error('请选择产业环节！');
          return false;
        }
        if(!this.rankType[0]){
          this.$message.error('请选择榜单维度！');
          return false;
        } 
        let param = {
          industryChainNodeId: +this.chainId[this.chainId.length-1],
          regionCode: this.params.checkArea[this.params.checkArea.length-1],
          divisionLevel: +this.params.divisionLevel,
          rankType: +this.rankType,
          divisionType: this.divisionType,
          provinces: [],
          cities: [],
          areas: [],
        }
        this.showrankType= +this.rankType,
        this.areaArr.map(e=>{
          e[0] && param.provinces.push(e[0])
          e[1] && param.cities.push(e[1])
          e[2] && param.areas.push(e[2])
        })
        //console.log('param', param);

        let arrVal = this.$refs.cascader1.$children[0].getInput().value.split('/');
        this.txt1 = arrVal[arrVal.length-1]
        this.txt2 = this.$refs.select2.$children[0].getInput().value
        this.rankingResult(param);
      },
      reset(){
        this.value1 = '';
        this.rankType=null;
        this.showrankType=''
        this.chainId=null;
        this.areaArr=[];
        this.divisionType='';
        this.isSearch = true;
      },
      expandChange(e){
        //console.log('expandChange', e);
      },
      casChange(){
        this.divisionType='';
        this.check();
      },
      clickLi(val){
        this.divisionType = val;
        this.check();
      },
      // 榜单列表
      getDimension(){
        getDimension().then(res=>{
          this.optInfo = res;
        });
      },
      // 获取产业排行
      rankingResult(param){
        let data = param
        rankingResult(data).then(res=>{
          if(res.rank>10){
            res.mine.rank = res.rank;
            res.top10.push(res.mine);
            this.rankList = res;
          }else{
            this.rankList = res;
          }
         // //console.log(res);
        })
      },
      change1(val){
        //console.log(val);
        this.rankType='';
        if(val==1){
          this.options2 = this.optInfo.rankBase
        }
        else if(val==2){
          this.options2 = this.optInfo.rankInnovation
        }
        else if(val==3){
          this.options2 = this.optInfo.rankInvestment
        }
        this.check();
      },
      change2(val){
        //console.log(val);
        this.check();
      },
    },
  }
</script>

<style lang="scss" scoped>
.nodata{
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}
  .rank{
    &-top{
      background: #FFFFFF;
      box-shadow: 0px 4px 14px 0px  #EEF1F8;
      border-radius: 10px;
      padding: 22px 24px;
      &-list{
        display: flex;
        align-items: center;
        padding-bottom: 20px;
        .label{
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0,0,0,0.85);
        }
        .ul{
          display: flex;
          align-items: center;
          .li{
            font-size: 14px;
            font-family: Abel-Regular, Abel;
            font-weight: 400;
            color: rgba(0,0,0,0.65);
            line-height: 22px;
            margin-left: 32px;
            cursor: pointer;
            padding: 0 8px;
            &.on{
              background: #3370FF;
              border-radius: 2px;
              color: #FFFFFF;
            }
            &.zdy{
              color: #3370FF;
            }
          }
        }
      }
      .btn{
        display: flex;
        justify-content: center;
        &-list{
          width: 65px;
          height: 32px;
          background: #FFFFFF;
          border-radius: 5px;
          opacity: 1;
          border: 1px solid #D9D9D9;
          text-align: center;
          line-height: 32px;
          margin-right: 10px;
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0,0,0,0.65);
          cursor: pointer;
          &.on{
            background: #3370FF;
            border: 0px solid #D9D9D9;
            color: #FFFFFF;
          }
          &.disabled{
            background: #D9D9D9;
            border: 0px solid #D9D9D9;
            color: #FFFFFF;
            cursor: no-drop;
          }
        }
      }
    }
    .table{
      padding: 0 24px;
      padding-bottom: 30pz;
      &-con{
        margin-top: 16px;
        background: #FFFFFF;
        border-radius: 10px;
        padding-bottom: 15px;
        box-shadow: 0px 4px 14px 0px  #EEF1F8;
        .title{
          line-height: 54px;
          height: 55px;
          padding-left: 24px;
          box-sizing: border-box;
          border-bottom: 1px solid #E9E9E9;

          font-size: 16px;
          font-family: Source Han Sans CN-Medium, Source Han Sans CN;
          font-weight: 500;
          color: rgba(0,0,0,0.85);
        }
        .txt1{
          font-size: 20px;
          font-family: Source Han Sans CN-Medium, Source Han Sans CN;
          font-weight: 500;
          color: rgba(0,0,0,0.85);
          line-height: 24px;
          padding-top: 40px;
          padding-bottom: 26px;
          text-align: center;
        }
      }
      &-body{
        .tr{
          background: #fff;
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0,0,0,0.65);
          display: flex;
          &.on{
            background: #FAFAFA;
          }
          &.rank{
            color: #FB3737;
            .on{
              color: #FB3737;
              .flex{
                .zhi{
                  background: linear-gradient(90deg, #FB3737 0%, rgba(251,55,55,0) 100%);
                }
              }
            }
            .flex{
              .zhi{
                background: linear-gradient(270deg, #FB3737 0%, rgba(251,55,55,0) 100%);
              }
              .txt{
                color: #FB3737;
              }
            }
          }
        }
        .td{
          line-height: 54px;
        }
      }
      .td1{
        padding-left: 16px;
        // width: 86px;
        width: 9%;
        &.on{
          font-size: 20px;
          font-family: Source Han Sans CN-Bold, Source Han Sans CN;
          font-weight: bold;
          color: #3370FF;
        }
      }
      .td2{
        width: 9%;
        min-width: 200px;
        //width: 86px;
      }
      .td3{
        width: 49%;
        // width: 475px;
        .flex{
          display: flex;
          align-items: center;
          height: 100%;
          width: 92%;
          .zhi{
            height: 6px;
            background: linear-gradient(270deg, #165DFF 0%, rgba(22,93,255,0) 100%);
            border-radius: 3px;
          }
          .txt{
            margin-left: 12px;
            font-size: 14px;
            font-family: Source Han Sans CN-Normal, Source Han Sans CN;
            font-weight: 400;
            color: rgba(0,0,0,0.65);
            line-height: 22px;
          }
        }
        &.on{
          .flex{
            .zhi{
              background: linear-gradient(270deg, #36CBCB 0%, rgba(54,203,203,0) 100%);
            }
          }
        }
      }
      .td4{
        width: 19%;
        //width: 181px;
      }
      .td5{
        width: 14%;
        //width: 138px;
      }
    }
  }
</style>