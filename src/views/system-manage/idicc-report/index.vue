<template>
  <div v-if="examine">
    <div v-if="showcontent">
      <div class="report">
        <div class="search">
          <div class="search-box">
            <el-input
              v-model="keyword"
              placeholder="请输入内容"
              size="small"
            />
            <div
              class="searcher"
              @click="industryReport"
            >
              搜索
            </div>
          </div>
        </div>
        <div class="tab">
          <div
            class="tab-list"
            :class="tabCheck === '' ? 'on' : ''"
            @click="tabEvent('')"
          >
            全部
          </div>
          <div
            v-for="(item, i) in optChain"
            :key="i"
            class="tab-list"
            :class="tabCheck == item.value ? 'on' : ''"
            @click="tabEvent(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
        <div v-loading="listLoading">
          <div
            v-if="list.length > 0"
            class="report-con"
          >
            <div
              v-for="(item, i) in list"
              :key="i"
              class="report-list"
            >
              <div class="imgs">
                <!-- <img
                  v-if="item.imagePath"
                  :src="item.imagePath"
                > -->
                <img
                  :src="item.imagePath"
                  @click="preview(item)"
                >
              </div>

              <div class="txt">
                <div
                  class="txt1"
                  @click="preview(item)"
                >
                  <div class="p1">
                    {{ item.title }}
                  </div>
                  <div class="p2">
                    {{ item.industryName }}
                  </div>
                </div>
                <!-- <a
            class="download"
            href="http://test1.dissdao.cn/%E5%85%89%E4%BC%8F%E4%BA%A7%E4%B8%9A%E6%8A%A5%E5%91%8A.pdf"
            download
          > -->
                <div
                  class="download"
                  @click="goTo(item)"
                >
                  <img
                    width="18"
                    src="https://static.idicc.cn/cdn/pangu/icon13.png"
                    alt=""
                  >
                  下载
                </div>
                <!-- </a> -->
              </div>
            </div>
            <el-pagination
              v-if="total > 30"
              layout="prev, pager, next"
              :page-size.sync="pages.pageSize"
              :total="+total"
              :current-page.sync="pages.pageNum"
              @size-change="SettledList"
              @current-change="SettledList"
            />
          </div>
          <div
            v-else
            class="nodata2"
          >
            <div class="con">
              <img src="https://static.idicc.cn/cdn/pangu/vacancy.png">
              <span class="p1">暂无数据</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      class="nodata"
    >
      <div class="con">
        <img src="https://static.idicc.cn/cdn/SSO/zhaoshangnodata.png">
        <span class="p1">暂无产业链权限</span>
        <span class="p2">请联系机构管理员配置权限功能</span>
      </div>
    </div>
  </div>
</template>

<script>
import {
  orgChainList,
  industryReport,
  downloadExcelModel,
} from "./../idicc-scan/apiUrl";
import { verificationAPI } from "@/api/user";
export default {
  name: "RePort",
  data() {
    return {
      tabCheck: "",
      pages: {
        pageNum: 1,
        pageSize: 30,
      },
      industryIds: "",
      keyword: "",
      list: [],
      total: 0,
      optChain: [],
      examine: false,
      showcontent: false,
      listLoading: false,
    };
  },
  created() {
    //this.industryReport();
    this.orgChainList();
  },
  methods: {
    async orgChainList() {
      try {
        const res = await orgChainList();
        if (!res || res.length == 0) {
          this.showcontent = false;
          // this.$message.error("暂无内容，请联系机构管理员配置功能权限");
          return;
        } else {
          this.showcontent = true;
          this.industryReport();
          res.map((e) => {
            let data = {};
            data.value = e.industryChainId;
            data.label = e.chainName.replace("产业金脑·", "");
            this.optChain.push(data);
          });
          this.optChain.sort((a, b) => {
            if (a.label === "光伏") {
              return -1;
            } else if (b.label === "光伏") {
              return 1;
            } else {
              return 0;
            }
          });
        }
      } finally {
        this.examine = true;
      }
    },
    /*  orgChainList() {
      orgChainList().then((res) => {
        if (!res || res.length == 0) {
          this.$message.error("暂无内容，请联系机构管理员配置功能权限");
          return;
        }
        res.map((e) => {
          let data = {};
          data.value = e.industryChainId;
          data.label = e.chainName.replace("产业金脑·", "");
          this.optChain.push(data);
        });
        this.optChain.sort((a, b) => {
          if (a.label === "光伏") {
            return -1; 
          } else if (b.label === "光伏") {
            return 1; 
          } else {
            return 0; 
          }
        });
      });
    }, */
    SettledList(val) {
      this.pages.pageNum = val;
      this.industryReport();
    },
    tabEvent(val) {
      this.tabCheck = val;
      this.industryReport();
    },
    goTo(item) {
      window.open(item.downLoadUrl);
    },
    // 预览
    preview(item) {
      downloadExcelModel(
        "/dpar/industryReport/view?url=" + item.reportUrl
      ).then((res) => {
        let url = window.URL.createObjectURL(res.data);
        window.open(url);
      });
    },
    async industryReport() {
      try {
        this.listLoading = true;
        let data = {
          ...this.pages,
          keyword: this.keyword,
          industryIds: this.tabCheck ? [this.tabCheck] : "",
        };
        /* const is = await verificationAPI({
        industryChainId:this.tabCheck
      }) 
      if(is){ */
        if (data.industryIds == "") {
          delete data.industryIds;
        }
        const res = await industryReport(data);
        if (!res.records) {
          res.records = [];
        }
        this.list = res.records;
        this.total = res.totalNum;
        /*  }else{
        this.$message.error('暂无该产业链权限')
        setTimeout(()=>{
          location.reload()
        },500)
      } */
      } finally {
        this.listLoading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 0px;
  /* 对应横向滚动条的宽度 */
  height: 5px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #e9e9e9;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  display: none;
}
.report-list:hover {
  box-shadow: 0 0 10px #c9ccd5; /* 添加阴影效果 */
}
.nodata2 {
  min-width: 88.5vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  .con {
    margin-top: 10%;
    width: 220px;
    height: 300px;
    display: flex;
    align-items: center;
    flex-direction: column;
    img {
      width: 216px;
      height: 177px;
    }
    .p1 {
      margin-top: 10px;
      font-size: 14px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}
.nodata {
  min-width: 88.5vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  .con {
    margin-top: 10%;
    width: 170px;
    height: 300px;
    display: flex;
    align-items: center;
    flex-direction: column;
    img {
      width: 160px;
      height: 165.03px;
    }
    .p1 {
      margin-top: 38px;
      font-size: 14px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
    .p2 {
      margin-top: 16px;
      font-size: 12px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
.report {
  // background: #f7f8fa;
  min-height: 100vh;
  .search {
    display: flex;
    justify-content: left;
    // background: #fff;
    &-box {
      margin: 16px 0;
      width: 552px;
      height: 40px;
      display: flex;
      border-radius: 4px;
      .searcher {
        border-radius: 10px;
        width: 70px;
        min-width: 70px;
        height: 32px;
        background-color: #3370ff;
        border: 1px solid #3370ff;
        border-radius: 0px 4px 4px 0px;
        cursor: pointer;
        font-size: 14px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      ::v-deep {
        .el-input-group__append {
          background: #3370ff;
          color: #ffffff;
          cursor: pointer;
        }
      }
    }
  }
  .tab {
    display: flex;
    font-size: 14px;
    font-family: Abel-Regular, Abel;
    font-weight: 400;
    line-height: 22px;
    padding-bottom: 12px;
    // padding-left: 34px;
    // background: #fff;
    overflow-x: auto;
    border-radius: 10px;
    // border-bottom: 1px solid #e9e9e9;
    &-list {
      color: rgba(0, 0, 0, 0.65);
      margin-right: 54px;
      width: auto;
      min-width: 80px;
      text-align: center;
      white-space: nowrap;
      cursor: pointer;

      &.on {
        font-weight: bold;
        color: #3370ff;
        position: relative;
        &::after {
          content: "";
          width: 28px;
          height: 2px;
          background: #3370ff;
          position: absolute;
          left: 50%;
          bottom: -8px;
          transform: translateX(-50%);
        }
      }
    }
  }
  &-con {
    margin: 16px 0;
    display: flex;
    flex-wrap: wrap;
  }
  &-list {
    width: 250px;
    height: 300px;
    margin-right: 16px;
    margin-bottom: 25px;
    background: #fff;
    cursor: pointer;
    border-radius: 6px;
    box-shadow: 0px 4px 12px 0px #eef1f8;
    .imgs {
      width: 100%;
      height: 200px;
      border-radius: 6px 6px 0px 0px;
      //background-color: rgba(237, 242, 253,0.8);
      background-color: #edf2fd;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 118px;
        height: 150px;
        //box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
      }
    }
    .txt {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 23px;
      padding-left: 20px;
      padding-right: 20px;
      .txt1 {
        .p1 {
          font-size: 15px;
          font-family: Abel-Regular, Abel;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          line-height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .p2 {
          font-size: 15px;
          font-family: Abel-Regular, Abel;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.45);
          line-height: 23px;
          padding-top: 5;
        }
      }
      .download {
        width: 70px;
        height: 32px;
        min-width: 70px;
        background: #ffffff;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #3370ff;
        box-sizing: 7px;
        font-size: 14px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        color: #3370ff;
        line-height: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>