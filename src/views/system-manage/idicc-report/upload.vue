<!--
 * @Author: jhy
 * @Date: 2023-06-05 09:36:46
 * @LastEditTime: 2023-06-05 18:04:47
 * @LastEditors: jhy
 * @Description: 
 * @FilePath: /QT-SASS-PC/src/views/system-manage/idicc-report/upload.vue
-->
<template>
  <div class="report-upload">
    <div class="report-upload-con">
      <div class="list">
        <div class="label">
          报告封面
        </div>
        <div class="list-con">
          <el-upload
            class="avatar-uploader"
            :action="chartApi.zstjUpload" 
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
            :headers="{
                  token
                }"
          >
            <img
              v-if="imageUrl"
              :src="imageUrl"
              class="avatar"
            >
            <i
              v-else
              class="el-icon-plus avatar-uploader-icon"
            />
          </el-upload>
        </div>
      </div>


      <div class="list">
        <div class="label">
          报告标题
        </div>
        <div class="list-con">
          <el-input
            v-model="param.title"
            size="mini"
            placeholder="请输入标题"
          />
        </div>
      </div>
      <div class="list">
        <div class="label">
          报告类型
        </div>
        <div class="list-con">
          <el-select
            ref="select"
            v-model="param.industryId"
            size="mini"
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <div class="list">
        <div class="label">
          产业链
        </div>
        <div class="list-con">
          <el-select
            ref="select"
            v-model="param.industryId"
            size="mini"
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <div class="list">
        <div class="label">
          文件
        </div>
        <div class="list-con">
          <input
            id="file1"
            type="file"
            name=""
          >
        </div>
      </div>
      <div class="btn">
        <div
          class="btn-list"
          @click="submit"
        >
          提交
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { industryReportUpload, industryChainGetAll } from './../idicc-scan/apiUrl'
import { chartApi,} from '@/admin/apiUrl'
import { getToken } from "@/utils/auth"; // get token from cookie
export default {
  name: 'ReportUpload',
  data () {
    return {
      token:'',
      options: [],
      param: {
        title: '',
        industryId: '',
        file: ''
      },
      imageUrl: '',
       actionUrl:"",
       chartApi,
    }
  },
  created () {
    this.token =  getToken()
    this.actionUrl=localStorage.getItem('originPath')
    this.orgChainList();
  },
  methods: {
    orgChainList () {
      industryChainGetAll().then(res => {
        //console.log('res', res);
        res.map(e => {
          this.options.push({
            label: e.chainName.replace('产业金脑·', ''),
            value: e.id
          });
        });
      });
    },
    submit () {
      let file_obj = document.getElementById("file1");
      if (file_obj.value == "") {
        this.$message.error("请选择需要上传的文件");
        return;
      }
      var formData = new FormData();
      formData.append('file', file_obj.files[0]);
      formData.append('title', this.param.title);
      formData.append('industryId', this.param.industryId);

      this.param.file = formData;

      industryReportUpload(formData).then(res => {
       // //console.log(res);
        this.$message.success('上传成功！');
        location.reload();
      })
    },
    handleAvatarSuccess (res, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
      //console.log(this.imageUrl);
    },
    beforeAvatarUpload (file) {
      const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    }
  },
}
</script>

<style lang="scss" scoped>
.report-upload {
  // padding: 30px;
  background: #f7f7f7;

  &-con {
    background: #fff;
    min-height: 80vh;
    padding-top: 30px;
    box-sizing: border-box;

    .list {
      display: flex;
      align-items: center;
      padding: 15px 0;

      .label {
        width: 120px;
        text-align: right;
        box-sizing: border-box;
        padding-right: 20px;
      }
    }

    .btn {
      display: flex;
      justify-content: center;

      &-list {
        width: 65px;
        height: 32px;
        background: #FFFFFF;
        border-radius: 5px;
        opacity: 1;
        border: 1px solid #D9D9D9;
        text-align: center;
        line-height: 32px;
        margin-right: 10px;
        font-size: 14px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        cursor: pointer;
        background: #3370FF;
        border: 0px solid #D9D9D9;
        color: #FFFFFF;
      }
    }
  }
}
</style>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #3370FF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px !important;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>