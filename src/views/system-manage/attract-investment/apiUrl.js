import request from '@/utils/request'
// 递归取最后一项
export function getLastItem(arr) {
    let res = [];
    arr.forEach(item => {
        if (Array.isArray(item)) {
            res.push(getLastItem(item));
        } else {
            res.push(item);
        }
    });
    return res[res.length - 1];
}
/**
* 我的产业链
* @returns 
*/
export function homeListAPI(data) {
    return request({
        url: '/admin/orgIndustryChainRelation/list',
        method: 'POST',
        data
    })
}
/**
* 获取指定关键字类型的关键字词典集合 关键字类型 0未知 1招商资讯新闻主题 2招商情报推荐理由
* @returns 
*/
export function listByTypeAPI(params) {
    return request({
        url: '/admin/keyword/dictionary/listByType',
        method: 'GET',
        params
    })
}
/**
* 企业名称联想
* @returns 
*/
export function nameAssociateAPI(params) {
    return request({
        url: '/merchants/enterprise/nameAssociate',
        method: 'GET',
        params
    })
}
/**
* 意向企业列表
* @returns 
*/
export function intentionPageAPI(data) {
    return request({
        url: '/miniapps/investment/attraction/clue/intentionPage',
        method: 'POST',
        data
    })
}
/**
* 任务企业列表
* @returns 
*/
export function myTaskPageAPI(data) {
    return request({
        url: '/miniapps/mydata/myTaskPage',
        method: 'POST',
        data
    })
}
/**
* 接洽企业列表
* @returns 
*/
export function visitPageAPI(data) {
    return request({
        url: '/miniapps/investment/attraction/clue/visitPage',
        method: 'POST',
        data
    })
}
/**
* 委托企业列表
* @returns 
*/
export function entrustPageAPI(data) {
    return request({
        url: '/miniapps/investment/attraction/clue/entrustPage',
        method: 'POST',
        data
    })
}
/**
* 指派企业列表
* @returns 
*/
export function assignPageAPI(data) {
    return request({
        url: '/miniapps/investment/attraction/clue/assignPage',
        method: 'POST',
        data
    })
}
/**
* 指派企业列表
* @returns 
*/
export function pageMyFocusEnterpriseDataAPI(data) {
    return request({
        url: '/miniapps/mydata/pageMyFocusEnterpriseData',
        method: 'POST',
        data
    })
}
/**
* 委托招商
* @returns 
*/
export function commissionConnectionAPI(data) {
    return request({
        url: '/admin/investment/attraction/clue/commissionConnection',
        method: 'POST',
        data
    })
}
/**
* 委托招商
* @returns 
*/
export function assignmentClueAPI(data) {
    return request({
        url: '/admin/investment/attraction/clue/assignmentClue',
        method: 'POST',
        data
    })
}
/**
* 新增跟进记录
* @returns 
*/
export function addfollowUpRecordAPI(data) {
    return request({
        url: '/admin/investmentAttractionClue/followUpRecord/add',
        method: 'POST',
        data
    })
}
/**
* 线索处理
* @returns 
*/
export function dealAPI(data) {
    return request({
        url: '/miniapps/investment/attraction/clue/deal',
        method: 'POST',
        data
    })
}
/**
* 线索详情-线索信息
* @returns 
*/
export function clueDetailAPI(params) {
    return request({
        url: '/miniapps/investment/attraction/clue/clueDetail',
        method: 'GET',
        params
    })
}
/**
* 线索详情-指派路径
* @returns 
*/
export function clueAssignRecordAPI(data) {
    return request({
        url: '/miniapps/clueAssignRecord/page',
        method: 'POST',
        data
    })
}
/**
* 线索详情-线索处理记录
* @returns 
*/
export function clueDealRecordAPI(data) {
    return request({
        url: '/miniapps/clueDealRecord/page',
        method: 'POST',
        data
    })
}
/**
* 委托详情-根据线索id分页查询关联的委托跟进记录
* @returns 
*/
export function pageByClueIdAPI(data) {
    return request({
        url: '/admin/investment/entrust/task/pageByClueId',
        method: 'POST',
        data
    })
}
/**
* 指派企业-重新指派
* @returns 
*/
export function reAssignmentClueAPI(data) {
    return request({
        url: '/admin/investment/attraction/clue/reAssignmentClue',
        method: 'POST',
        data
    })
}
/**
* 取消关注
* @returns 
*/
export function cancelCollectAPI(params) {
    return request({
        url: '/miniapps/enterprise/cancelCollect',
        method: 'GET',
        params
    })
}
/**
* 关注企业
* @returns 
*/
export function collectAPI(params) {
    return request({
        url: '/ai/investment/enterprise/collection',
        method: 'post',
        data: params
    })
}
/**
* 产业360-企业列表搜索
* @returns 
*/
export function searchAPI(data) {
    return request({
        url: '/merchants/enterprise/search',
        method: 'POST',
        data
    })
}
/**
* 根据标签类型名称查询标签列表
* @returns 
*/
export function listByTypeNameAPI(params) {
    return request({
        url: '/admin/enterprise/label/listByTypeName',
        method: 'GET',
        params
    })
}
/**
* 获取科技型企业标签查询条件
* @returns 
*/
export function listTechnologicalLabelAPI(params) {
    return request({
        url: '/miniapps/enterprise/listTechnologicalLabel',
        method: 'GET',
        params
    })
}
/**
* 产业链节点树
* @returns 
*/
export function industryChainNodeTreeAPI(params) {
    return request({
        url: '/admin/industryChainNode/tree',
        method: 'GET',
        params
    })
}
/**
* 企业详情-基本信息
* @returns 
*/
export function enterprisedetailAPI(data) {
    return request({
        url: '/admin/enterprise/detail',
        method: 'POST',
        data
    })
}
/**
* 企业详情-获取指定企业招商相关状态
* @returns 
*/
export function getMerchantsStatusAPI(params) { //废弃
    return request({
        url: '/merchants/enterprise/getMerchantsStatus',
        method: 'GET',
        params
    })
}
/**
* 纳入招商意向
* @returns 
*/
export function inclusionIntentionAPI(data) {
    return request({
        url: '/ai/investment/enterprise/addEnterprise',
        method: 'POST',
        data
    })
}
/**
* 获取指定企业和当前操作人所在机构的招商策略信息
* @returns 
*/
export function getByEnterpriseIdAPI(params) {
    return request({
        url: '/merchants/investment_strategy/getByEnterpriseId',
        method: 'GET',
        params
    })
}
/**
* 获取指定企业最新的招商资讯列表
* @returns 
*/
export function bestNewListAPI(params) {
    return request({
        url: '/merchants/information/bestNewList',
        method: 'GET',
        params
    })
}
/**
* 获取指定招商线索的跟进记录列表
* @returns 
*/
export function followUpRecordAPI(data) {
    return request({
        url: '/admin/investmentAttractionClue/followUpRecord/pageList',
        method: 'POST',
        data
    })
}
/**
* 获取财务分析-主要指标列表
* @returns 
*/
export function mainIndexListAPI(params) {
    return request({
        url: '/merchants/finance_analysis/mainIndexList',
        method: 'GET',
        params
    })
}
/**
* 获取财务分析-资产负债类指标列表
* @returns 
*/
export function assetsDebtListAPI(params) {
    return request({
        url: '/merchants/finance_analysis/assetsDebtList',
        method: 'GET',
        params
    })
}
/**
* 获取财务分析-营收利润类指标列表
* @returns 
*/
export function incomeProfitListAPI(params) {
    return request({
        url: '/merchants/finance_analysis/incomeProfitList',
        method: 'GET',
        params
    })
}
/**
* 获取财务分析-现金流量类指标列表
* @returns 
*/
export function cashFlowListAPI(params) {
    return request({
        url: '/merchants/finance_analysis/cashFlowList',
        method: 'GET',
        params
    })
}
/**
* 获取指定企业的专利类型数量统计
* @returns 
*/
export function typeCountAPI(params) {
    return request({
        url: '/merchants/patent/typeCount',
        method: 'GET',
        params
    })
}
/**
* 获取指定企业的专利增长趋势
* @returns 
*/
export function growthTrendAPI(params) {
    return request({
        url: '/merchants/patent/growthTrend',
        method: 'GET',
        params
    })
}
/**
* 获取企业相关专利排行
* @returns
*/
export function rankingAPI(params) {
    return request({
        url: '/merchants/patent/ranking',
        method: 'GET',
        params
    })
}
/**
* 获取指定企业的专利信息分页列表
* @returns 
*/
export function pageByEnterpriseIdAPI(data) {
    return request({
        url: '/merchants/patent/pageByEnterpriseId',
        method: 'POST',
        data
    })
}
/**
* 获取指定企业关联产业链的关联度列表
* @returns
*/
export function chainCorrelationListAPI(params) {
    return request({
        url: '/merchants/strategic_layout/chainCorrelationList',
        method: 'GET',
        params
    })
}
/**
* 获取指定产业链和指定企业的产业布局列表
* @returns
*/
export function nodeCorrelationListAPI(params) {
    return request({
        url: '/merchants/strategic_layout/nodeCorrelationList',
        method: 'GET',
        params
    })
}

/**
* 获取指定产业链和指定企业的产业布局列表
* @returns
*/
export function typeCountsAPI(params) {
    return request({
        url: '/merchants/strategic_layout/typeCount',
        method: 'GET',
        params
    })
}
/**
* 获取战略布局列表
* @returns 
*/
export function strategic_layoutAPI(data) {
    return request({
        url: '/merchants/strategic_layout/page',
        method: 'POST',
        data
    })
}
// 城市 - json 数据
export function getCityJson(code, areaType, cityData) {
    const cityUrl = '/admin/orgIndustryChainRelation/geo/data'
    let params = ''
    if (code == 110100) {
        params = `110000_full`;
    } else if (code == 310100) {
        params = `310000_full`;
    } else if (code == 120100) {
        params = `120000_full`;
    } else if (code == 500100) {
        params = `500000_full`;
    } else {
        // 台湾
        if (code == 710000) {
            params = `${code}`;
            // 东莞
        } else if (code == 441900) {
            params = `${code}`;
        }
        else if (areaType === '1' || areaType === '2' || ['110100'].includes(code)) {
            params = `${code}_full`;
        } else if (areaType === '3' && cityData.length == 3) {
            params = `${code}_full`;
        }
        else {
            params = `${code}`;
        }
    }
    return request({
        url: cityUrl,
        params: { code: params },
        method: 'GET',
    })
}
/**
* 强延补推荐情况和新增企业推荐情况统计
* @returns
*/
export function recommendedStatisticsAPI(params) {
    return request({
        url: '/merchants/investment_enterprise/recommendedStatistics',
        method: 'GET',
        params
    })
}
/**
* 强延补推荐情况和新增企业推荐情况统计
* @returns
*/
export function intentionRateAPI(params) {
    return request({
        url: '/merchants/investment_enterprise/intentionRate',
        method: 'GET',
        params
    })
}
/**
* 获取当前登陆用户关联的推荐企业特征
* @returns
*/
export function characteristicAPI(params) {
    return request({
        url: '/merchants/investment_enterprise/characteristic',
        method: 'GET',
        params
    })
}
/**
* 企业舆情-概览
* @returns
*/
export function overviewAPI(params) {
    return request({
        url: '/merchants/information/overview',
        method: 'GET',
        params
    })
}
/**
* 企业舆情-舆情资讯
* @returns 
*/
export function informationlistAPI(data) {
    return request({
        url: '/merchants/information/list',
        method: 'POST',
        data
    })
}
/**
* 获取强延补链环节节点
* @returns
*/
export function getTypeNodeLinkPlaceAPI(params) {
    return request({
        url: '/merchants/chain_node_attribute/getTypeNodeLinkPlace',
        method: 'GET',
        params
    })
}
/**
* 模糊查询关键字列表
* @returns
*/
export function likeKeyWordListAPI(params) {
    return request({
        url: '/merchants/keyword_dictionary/likeKeyWordList',
        method: 'GET',
        params
    })
}
/**
* 我的招商漏斗
* @returns
*/
export function myFunnelAPI(params) {
    return request({
        url: '/merchants/intelligentManager/myFunnel',
        method: 'GET',
        params
    })
}
/**
* 我的任务进度
* @returns
*/
export function myTaskProgressStatusAPI(params) {
    return request({
        url: '/merchants/intelligentManager/myTaskProgressStatus',
        method: 'GET',
        params
    })
}
/**
* 招商经理排行榜
* @returns
*/
export function investmentManagerRankListAPI(params) {
    return request({
        url: '/merchants/intelligentManager/investmentManagerRankList',
        method: 'GET',
        params
    })
}
/**
* 关注企业动态
* @returns
*/
export function enterpriseDynamicAPI(params) {
    return request({
        url: '/merchants/intelligentManager/enterpriseDynamic',
        method: 'GET',
        params
    })
}
/**
* 关注企业动态事件
* @returns
*/
export function collectEnterpriseEventListAPI(data) {
    return request({
        url: '/pg/enterprise/event/collectEnterpriseEventList',
        method: 'POST',
        data
    })
}
/**
* 动态事件
* @returns 
*/
export function enterpriseDynamicEventAPI(data) {
    return request({
        url: '/merchants/intelligentManager/enterpriseDynamicEvent',
        method: 'POST',
        data
    })
}
/**
* 舆情资讯
* @returns 
*/
export function informationAPI(data) {
    return request({
        url: '/merchants/intelligentManager/information',
        method: 'POST',
        data
    })
}
/**
* 招商企业推荐列表
* @returns 
*/
export function merchantsAPI(data) {
    return request({
        url: '/merchants/investment_enterprise/search',
        method: 'POST',
        data
    })
}
/**
* 暂不处理
* @returns
*/
export function leaveAsideAPI(params) {
    return request({
        url: '/admin/business/enterprise/leaveAside',
        method: 'GET',
        params
    })
}
/**
* 删除推荐
* @returns
*/
export function deleteAPI(params) {
    return request({
        url: '/app/business/relation/delete',
        method: 'GET',
        params
    })
}
/**
* 获取当前登陆用户所在机构和当前用户拥有权限的产业链关联的推荐企业类型数量统计
* @returns
*/
export function merchantstypeCountAPI(params) {
    return request({
        url: '/merchants/investment_enterprise/typeCount',
        method: 'GET',
        params
    })
}
/**
* 获取当前登陆用户所在机构和当前用户拥有权限的产业链关联的推荐企业类型数量统计
* @returns
*/
export function typeCountSumAPI(params) {
    return request({
        url: '/merchants/investment_enterprise/typeCount',
        method: 'GET',
        params
    })
}

/**
* 根据指定招商模型获取对应的推荐企业列表
* @returns 
*/
export function listByModelTypeAPI(data) {
    return request({
        url: '/merchants/investment_enterprise/listByModelType',
        method: 'POST',
        data
    })
}
/**
* 推荐企业地区分布图
* @returns 
*/
export function areaDistributionAPI(data) {
    return request({
        url: '/merchants/investment_enterprise/areaDistribution',
        method: 'POST',
        data
    })
}
/**
* 推荐企业地区分布列表
* @returns 
*/
export function areaDistributionListAPI(data) {
    return request({
        url: '/merchants/investment_enterprise/areaDistributionList',
        method: 'POST',
        data
    })
}
/**
* 推荐企业产业链分布列表
* @returns 
*/
export function industryChainDistributionListAPI(data) {
    return request({
        url: '/merchants/investment_enterprise/industryChainDistributionList',
        method: 'POST',
        data
    })
}
/**
* 推荐企业产业链分布图
* @returns 
*/
export function industryChainDistributionAPI(data) {
    return request({
        url: '/merchants/investment_enterprise/industryChainDistribution',
        method: 'POST',
        data
    })
}
/**
* 获取行政区划列表
* @returns
*/
export function getRegion(params) {
    return request({
        url: '/admin/administrativeDivision/list',
        method: 'GET',
        params
    })
}
/**
* 获取指定产业链的所有2级节点集合
* @returns
*/
export function getAllSecondLevelChainNodeByIndustryChainIdAPI(params) {
    return request({
        url: '/admin/industryChainNode/getAllSecondLevelChainNodeByIndustryChainId',
        method: 'GET',
        params
    })
}
/**
* 个人简介
* @returns
*/
export function personalInformationAPI(params) {
    return request({
        url: '/merchants/relation/personalInformation',
        method: 'GET',
        params
    })
}
/**
* 获取用户关联产业
* @returns
*/
export function industryListAPI(params) {
    return request({
        url: '/merchants/relation/industryList',
        method: 'GET',
        params
    })
}
/**
* 关联企业产业分布
* @returns
*/
export function industryDistributionAPI(params) {
    return request({
        url: '/merchants/relation/industryDistribution',
        method: 'GET',
        params
    })
}
/**
* 关联企业列表
* @returns
*/
export function pageEnterpriseAPI(data) {
    return request({
        url: '/merchants/relation/pageEnterprise',
        method: 'POST',
        data
    })
}
/**
* 取当前用户招商相关的数据数量
* @returns
*/
export function getMyCountAPI(params) {
    return request({
        url: '/merchants/intelligentManager/getMyCount',
        method: 'GET',
        params
    })
}
/**
* 亲商关联图谱
* @returns
*/
export function proBusinessImageAPI(params) {
    return request({
        url: '/merchants/investment_enterprise/proBusinessImage',
        method: 'GET',
        params
    })
}
/**
* 获取登陆用户机构下所有用户
* @returns
*/
export function listOrgUserListAPI(params) {
    return request({
        url: '/admin/user/listOrgUserList',
        method: 'GET',
        params
    })
}
/**
* 获取省市
* @param {*} data
* @returns
*/
export function getAllAddress(params) {
    return request({
        url: "/admin/administrativeDivision/getAll",
        method: "GET",
        params,
    });
}
/**
* 根据产品查企业
* @returns
*/
export function enterpriseByChainNodeAPI(data) {
    return request({
        url: '/ai/industryChain/enterpriseByChainNode',
        method: 'POST',
        data
    })
}
/**
* 根据
* @returns
*/
export function AIindustryChainNodeTreeAPI(data) {
    return request({
        url: '/merchants/enterprise/searchStats',
        method: 'POST',
        data
    })
}