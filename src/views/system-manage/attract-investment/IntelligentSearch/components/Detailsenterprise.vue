<template>
  <!--  v-if="information.enterprise?.enterpriseName" -->
  <!-- 企业详情 -->
  <div
    v-if="information.enterprise?.enterpriseName"
    class="box"
  >
    <div class="title">
      <div class="iconarrow">
        <i
          style="cursor: pointer"
          class="el-icon-arrow-left"
          @click="gobacka"
        />
      </div>
    </div>
    <div class="head">
      <div class="operation">
        <div
          v-if="!information.enterprise?.isCollect"
          v-loading="AttentionLoading"
          class="attention"
          @click="attention"
        >
          <img src="https://static.idicc.cn/cdn/pangu/colloction.png">
          <span>收藏</span>
        </div>
        <div
          v-else
          v-loading="AttentionLoading"
          class="noattention"
          @click="cancel"
        >
          <img src="https://static.idicc.cn/cdn/pangu/attention.png">
          <span>已收藏</span>
        </div>
        <div
          v-if="!information.enterprise?.isInvestClue"
          class="bringinto"
          @click="InclusionIntention"
        >
          <img src="https://static.idicc.cn/cdn/pangu/bringinto.png">
          <span>纳入招商意向</span>
        </div>
        <el-dropdown v-else>
          <span class="bringintois">
            <span>已纳入招商意向</span>
            <!-- <i
              style="color: #3370FF"
              class="el-icon-arrow-down el-icon--right"
            /> -->
          </span>
          <!-- <el-dropdown-menu
            slot="dropdown"
            :append-to-body="false"
          >
            <el-dropdown-item
              v-if="!isAssign && $store.getters.Assignauthority"
              @click.native="designate"
            >
              <span class="dropdow">线索指派</span>
            </el-dropdown-item>
            <el-dropdown-item v-if="isAssign && $store.getters.Assignauthority">
              <span class="dropdow">已指派</span>
            </el-dropdown-item>
            <el-dropdown-item
              v-if="!entrustOrNot"
              @click.native="entrustment"
            >
              <span class="dropdow">委托招商</span>
            </el-dropdown-item>
            <el-dropdown-item v-else>
              <span class="dropdow">已委托</span>
            </el-dropdown-item>
            <el-dropdown-item @click.native="addsheet">
              <span class="dropdow">添加走访记录</span>
            </el-dropdown-item>
          </el-dropdown-menu> -->
        </el-dropdown>
      </div>
      <img
        :src="getIconByType(information?.enterprise?.showLabelType)"
        class="iconImg"
      >
      <div class="content">
        <div>
          <div class="name">
            <span>{{ information.enterprise?.enterpriseName }}</span>
            <div
              v-if="information?.enterprise?.chainNameList"
              class="tag"
            >
              <div
                v-for="(it, ind) in information.enterprise.chainNameList"
                :key="ind"
                class="industryTag"
              >
                {{ it.replace('产业金脑·', '') }}
              </div>
            </div>
          </div>
          <div class="EnterpriseLabel">
            <div
              v-if="showTag.length > 0"
              class="tags"
            >
              <div
                v-for="(it, ind) in showTag"
                :key="ind"
                class="firmTag"
              >
                {{ it.labelName }}
              </div>
              <div v-if="information.enterprise.enterpriseLabelDTOS">
                <div
                  v-if="information.enterprise.enterpriseLabelDTOS.length > 6"
                  class="firmTag"
                  style="cursor: pointer"
                  @click="more"
                >
                  {{ isshow ? '收起' : '更多' }}
                  <i
                    :class="
                      isshow ? 'el-icon-caret-top' : 'el-icon-caret-bottom'
                    "
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="content-two">
          <div class="two-c">
            <span class="key">快速成长指数：</span>
            <span
              style="color: #34c759"
              class="value"
            >
              {{
                !information?.enterprise?.growthIndex ||
                  information?.enterprise?.growthIndex == '0.0'
                  ? '-'
                  : information.enterprise.growthIndex
              }}</span>
          </div>
          <div class="two-c">
            <span class="key">扩张意愿指数：</span>
            <span
              style="color: #ff9500"
              class="value"
            >
              {{
                !information.enterprise.expansionIndex ||
                  information.enterprise.expansionIndex == '0.0'
                  ? '-'
                  : information.enterprise.expansionIndex
              }}</span>
          </div>
          <div class="two-a">
            <span class="key">联系电话：</span>
            <span
              v-if="information.enterprise.mobile"
              class="value"
            >
              <span class="value">{{ information.enterprise.mobile }}
                <span v-if="information.enterprise.moreMobile">
                  <span v-if="information.enterprise.moreMobile.length >= 1">更多<el-tooltip
                    effect="dark"
                    placement="top"
                  >
                    <template slot="content">
                      <p
                        v-for="(it, ind) in information.enterprise.moreMobile"
                        :key="ind"
                      >
                        <span>{{ it }}</span>
                      </p>
                    </template>
                    <span>(<span style="color: #56adff">{{
                      information.enterprise.moreMobile.length
                    }}</span>)</span>
                  </el-tooltip>
                  </span>
                </span>
              </span>
            </span>
          </div>
          <div class="two-b">
            <span class="key">注册地址：</span>
            <span class="value">{{
              information.enterprise.enterpriseAddress
            }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="scan">
      <div class="scan-top">
        <div class="scan-tab-con">
          <div class="scan-tab">
            <div
              v-for="(it, index) in tabList"
              :key="index"
              class="scan-tab-list"
              :class="CurrentSelectio == it.id ? 'on' : ''"
              @click="cut(it.id)"
            >
              {{ it.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="manag">
        <basic
          v-if="CurrentSelectio == 1"
          :information="information"
        />
        <business-movement
          v-if="CurrentSelectio == 2"
          :event-list="information.enterprise.eventList || []"
        />
        <insight-clearly
          v-if="CurrentSelectio == 3"
          :enterprise="information.enterprise || {}"
          :chain-list="information.chainDTOS || []"
        />
        <strategy
          v-if="CurrentSelectio == 4"
          :recommend-region-code="recommendRegionCode"
          :enterprise-i-d="enterpriseids"
          :enterprise-data="Enterprise"
        />
        <!--       <strategy
          v-if="CurrentSelectio == 2"
          :enterprise-i-d="enterpriseids"
          :enterprise-data="Enterprise"
        />
        <finance
          v-if="CurrentSelectio == 3"
          :enterprise-i-d="enterpriseids"
        /> 
        <innovate
          v-if="CurrentSelectio == 4"
          :enterprise-i-d="enterpriseids"
        />-->
        <layout
          v-if="CurrentSelectio == 5"
          :enterprise-i-d="enterpriseids"
        />
        <popular
          v-if="CurrentSelectio == 6"
          :enterprise-i-d="enterpriseids"
          @updatafirm="updatafirm"
        />
        <!-- 自行跟进记录 -->
        <FollowUpRecord  
          v-if="CurrentSelectio == 7"
          :clue-id="companyDetial.id"
          :selected-row="companyDetial"
        />
        <!-- 委托跟踪信息 -->
        <TrackeInfo
          v-if="CurrentSelectio == 8"
          :uni-code="companyDetial?.unifiedSocialCreditCode || companyDetial?.uniCode"
        />
      </div>
      <entrust
        v-if="entrustball"
        :entrustball="entrustball"
        :clue-id="clueId"
        :enterprise-name="information.enterprise.enterpriseName"
        @closeentrust="closeentrust"
        @getList="getList"
      />
      <designate
        v-if="assignball"
        :assignball="assignball"
        :clue-id="clueId"
        :enterprise-name="information.enterprise.enterpriseName"
        @closeassignball="closeassignball"
        @getList="getList"
      />
      <addrecord
        v-if="recordball"
        :recordball="recordball"
        :clue-id="clueId"
        :enterprise-name="information.enterprise.enterpriseName"
        @closerecord="closerecord"
        @getList="getList"
      />
    </div>
    <AlertDialog
      :show-dialog="showDialog"
      @onClose="onClose"
    />
  </div>
</template>

<script>
import { getEnterpriseIconByType } from '@/utils/utils';
import basic from './particulars/essentialInformation.vue'; //基本信息
import strategy from './particulars/strategy.vue'; //招商策略
import finance from './particulars/finance.vue'; //财务分析
// import innovate from "./particulars/innovate.vue"; //创新能力
import layout from './particulars/layout.vue'; //战略布局
import popular from './particulars/popular.vue'; //企业舆情
// import interview from './particulars/interview.vue'; //走访记录
import businessMovement from './particulars/businessMovement.vue'; //企业动态
import InsightClearly from './particulars/clearly360.vue'; //360洞察
import entrust from '../../manage/components/operation/entrust.vue';
import designate from '../../manage/components/operation/designate.vue';
import addrecord from '../../manage/components/operation/addinterview.vue';
import {
  collectAPI,
  inclusionIntentionAPI,
} from '../../apiUrl';
import AlertDialog from '@/views/system-manage/attract-investment/manage/components/alertDialog.vue';
import {
  enterprisedetailAPI_V2,
  getModelTypesAPI,
} from '../../apiUrl_v2';
import FollowUpRecord from './FollowUpRecord.vue';
import TrackeInfo from './TrackeInfo.vue';
import { getInvestEnterpriseDetailAPI_investment} from '@/api/attractInvestment';
export default {
  name: 'DetailsEnterprise',
  components: {
    basic,
    'business-movement': businessMovement,
    'insight-clearly': InsightClearly,
    // innovate,
    layout,
    popular,
    // interview,
    entrust,
    designate,
    addrecord,
    AlertDialog,
    strategy,
    FollowUpRecord,
    TrackeInfo
  },
  props: {
    enterpriseID: {
      type: String,
      default: null,
    },
    listedSectorIds: {
      type: Array,
      default: () => [],
    },
    technologyCertificationIds: {
      type: Array,
      default: () => [],
    },
    modelType: {
      type: String,
      default: null,
    },
    chainId: {
      type: String,
      default: null,
    },
    recommendRegionCode: {
      type: String,
      default: null,
    },
    companyDetial: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      enterpriseids: '',
      showDialog: false,
      CurrentSelectio: 1,
      tabList: [
        {
          name: '基本信息',
          id: 1,
        },
        // {
        //   name: '企业动态',
        //   id: 2,
        // },
        {
          name: '360洞察',
          id: 3,
        },
        /*  {
          name: "招商策略",
          id: 2,
        },
        {
          name: "财务分析",
          id: 3,
        },
        {
          name: "创新能力",
          id: 4,
        },
        {
          name: "战略布局",
          id: 5,
        },
        {
          name: "企业舆情",
          id: 6,
        }, */
        /*         {
          name: "走访记录",
          id: 7,
        }, */
      ],
      showTag: [],
      isshow: false,
      stateLoading: false,
      information: {}, //企业基本信息
      isAttention: false, //是否关注
      AttentionLoading: false, //关注loading
      isIntention: false, //是否纳入意向
      entrustOrNot: false, //是否委托
      isAssign: false, //是否指派
      clueId: '', //线索id
      entrustball: false, //委托招商弹层
      assignball: false, //线索指派弹层
      recordball: false, //线索指派弹层

      Enterprise: {}, // 招商策略
    };
  },
  created() {
    document.body.style.overflow = 'auto';
    this.$nextTick(() => {
      document.body.scrollTop = 0;
      document.body.style.overflow = 'visible';
    });
    this.enterpriseids = this.enterpriseID;
    this.getenterprisedetail(); //基本信息
 
  },
  methods: {
    getIconByType(type) {
      return getEnterpriseIconByType(type, 'webp');
    },
    async tabShow() {
        let _tabList=[
        {
          name: '基本信息',
          id: 1,
        },
        {
          name: '360洞察',
          id: 3,
          },
        ]
      if (this.information.enterprise.eventList&& this.information.enterprise.eventList.length){
      _tabList.push({ name: '企业动态', id: 2 });
      }
      let enterpriseId = this.enterpriseids || this.enterpriseID || '';
      if (enterpriseId) {
        const code = await getModelTypesAPI({
        id: enterpriseId,
        regionCode: this.recommendRegionCode,
      });
      let modelType = code.result;
      this.Enterprise = modelType || [];
      if (this.Enterprise[0]?.chains[0]?.id != this.chainId) {
        const chains = this.Enterprise[0]?.chains || [];
        const index = chains.findIndex((item) => item?.id === this.chainId);
        if (index > 0) {
          this.Enterprise[0].chains = [
            chains[index],
            ...chains.filter((_, i) => i !== index),
          ];
        }
      }
         
          if (this.Enterprise.length){
          _tabList.push({
            name: '招商策略',
            id: 4,
          });
          }
  
      }
      (this?.companyDetial?.isInvestClue || this.information?.enterprise?.isInvestClue) && _tabList.push({ name: '自行跟进记录', id: 7 });
      if (this?.companyDetial?.entrustOrNot) {
        _tabList.push({ name: '委托跟踪信息', id:8 });
      }
      _tabList.sort(function (a, b) {
        return a.id - b.id;
      });
      this.tabList=_tabList
    },
    // 关闭委托招商弹层
    closeentrust() {
      this.entrustball = false;
    },
    // 关闭线索指派弹层
    closeassignball() {
      this.assignball = false;
    },
    // 关闭线索指派弹层
    closerecord() {
      this.recordball = false;
    },
    // 委托招商
    entrustment() {
      this.showDialog = true;
    },
    onClose() {
      this.showDialog = false;
    },
    // 线索指派
    designate() {
      this.assignball = true;
    },
    // 添加走访记录
    addsheet() {
      this.recordball = true;
    },
    getList() {
      this.getenterprisedetail();
      // if (this.CurrentSelectio == 7) {
      //   this.$refs.interview.updata();
      // }
    },
    // 展开与关闭
    more() {
      if (this.isshow) {
        this.showTag = this.information.enterprise.enterpriseLabelDTOS.slice(
          0,
          6
        );
        this.isshow = !this.isshow;
      } else {
        this.showTag = this.information.enterprise.enterpriseLabelDTOS;
        this.isshow = !this.isshow;
      }
    },
    // 获取状态
    async getMerchantsStatus(data) {},
    updatafirm(ID) {
      this.enterpriseids = ID;
      this.tabList = [
        {
          name: '基本信息',
          id: 1,
        },
        {
          name: '企业动态',
          id: 2,
        },
        {
          name: '360洞察',
          id: 3,
        },
      ];
      this.CurrentSelectio = 1;
      document.documentElement.scrollTop = 0;
      this.getenterprisedetail();
    },
    getDetailNew(){
      this.clueId = this.companyDetial.id;
      getInvestEnterpriseDetailAPI_investment({
        clueId: this.companyDetial.id
      })
      .then((res2) => {
        let chainList = [];
        res2.result.chainDTOS?.forEach((e) => {
          if (e.nodeLevel == 1) {
            chainList.push({
              ...e,
              children: [],
            });
          }
        });
        chainList.forEach((e) => {
          res2.result.industryChainList.forEach((e1) => {
            if (e1.nodeLevel >= 2 && e1.chainName == e.chainName) {
              let obj = {
                ...e1,
              };
              e.children.push(obj);
            }
          });
        });
        this.information = {
          enterprise: res2.result,
          activities: chainList,
        };
        this.tabShow();
        if (this.information.enterprise&&this.information.enterprise.enterpriseLabelDTOS) {
          this.showTag = this.information.enterprise.enterpriseLabelDTOS.slice(
            0,
            6
          );
        }
      });
    },
    // 企业详情
    async getenterprisedetail() {
      if (this.companyDetial?.clueSource == 2 && !this.companyDetial.enterpriseId){
        this.getDetailNew();
        return;
      }
      try {
        const res2 = await enterprisedetailAPI_V2({
          enterpriseId: this.enterpriseids,
          listedSectorIds: this.listedSectorIds || [],
          technologyCertificationIds: this.technologyCertificationIds || [],
        });
        let chainList = [];
        res2.result.industryChainList.forEach((e) => {
          if (e.nodeLevel == 1) {
            chainList.push({
              ...e,
              children: [],
            });
          }
        });
        chainList.forEach((e) => {
          res2.result.industryChainList.forEach((e1) => {
            if (e1.nodeLevel >= 2 && e1.chainName == e.chainName) {
              let obj = {
                ...e1,
              };
              e.children.push(obj);
            }
          });
        });
         this.clueId = res2.result.enterprise?.clueId;
        this.information = {
          ...res2.result,
          businessScope: res2.businessScope,
          activities: chainList,
        };
        this.tabShow();
        this.information.enterprise.moreMobile =
          this.information.enterprise.moreMobile.split(';');
        if (this.information.enterprise.enterpriseLabelDTOS) {
          this.showTag = this.information.enterprise.enterpriseLabelDTOS.slice(
            0,
            6
          );
        }
      } catch (error) {
        // this.$emit('goback');
      }
    },
    // 返回上一级
    gobacka() {
      this.$emit('goback');
    },
    //tab切换
    cut(id) {
      this.CurrentSelectio = id;

    },
    // 关注企业
    async attention() {
      if (this.AttentionLoading == true) {
        return;
      }
      try {
        this.AttentionLoading = true;
        const res = await collectAPI({
          status: true,
          enterpriseUniCode:
            this.information.enterprise?.unifiedSocialCreditCode,
        });
        if (res.code === 'SUCCESS') {
          this.getenterprisedetail(); //刷新
          this.$message.success('收藏成功！');
        }
      } finally {
        this.AttentionLoading = false;
      }
    },
    // 取消关注
    async cancel() {
      if (this.AttentionLoading == true) {
        return;
      }
      try {
        this.AttentionLoading = true;
        const res = await collectAPI({
          status: false,
          enterpriseUniCode:
            this.information.enterprise?.unifiedSocialCreditCode,
        });
        if (res.code === 'SUCCESS') {
          this.getenterprisedetail(); //招商相关状态
          this.$message.success('取消收藏成功！');
        }
      } finally {
        this.AttentionLoading = false;
      }
    },
    // 纳入招商意向
    async InclusionIntention() {
      let data = {
        clueSource: 1,
        uniCode: this.information.enterprise?.unifiedSocialCreditCode,
      };
      await inclusionIntentionAPI(data);
      this.getenterprisedetail(); //招商相关状态
      this.$message.success('纳入招商意向成功！');
    },
  },
};
</script>

<style lang="scss" scoped>
.dropdow {
  white-space: nowrap;
}

.box {
  padding-bottom: 20px;
  overflow-y: scroll;

  .manag {
    padding: 24px;
  }
}

.title {
  .iconarrow {
    color: #666;
    font-size: 20px;
    background: white;
    padding: 2px;
    width: 25px;
    height: 25px;
    border-radius: 16px;
  }
}

.head {
  width: 100%;
  margin-top: 16px;
  display: flex;
  background: #ffffff;
  border-radius: 10px;
  opacity: 1;

  box-shadow: 0px 4px 12px 0px #eef1f8;

  .operation {
    position: absolute;
    display: flex;
    //margin-left: 80%;
    right: 5vw;
    margin-top: 22px;

    .attention {
      width:90px;
      height: 32px;
      background-color: #fff;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid #3370ff;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      img {
        width: 18px;
        height: 18px;
        margin-right: 8px;
        margin-bottom: 2px;
      }

      span {
        color: #3370ff;
        font-weight: 400;
        font-size: 14px;
      }
    }

    .attention:hover {
      background-color: #f4faff;
    }

    .noattention {
      width: 90px;
      height: 32px;
      background: #f7f8fa;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
          img {
        width: 18px;
        height: 18px;
        margin-right: 8px;
        margin-bottom: 2px;
      }
    }

    .noattention:hover {
      background-color: #f4faff;
    }

    .bringintois {
      width: 126px;
      height: 32px;
      background: #ffffff;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid #3370ff;
      margin-left: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      img {
        width: 18px;
        height: 18px;
        margin-right: 8px;
        margin-bottom: 2px;
      }

      span {
        font-weight: 400;
        color: #3370ff;
        font-size: 14px;
      }
    }

    .bringinto {
      width: 126px;
      height: 32px;
      background: #3370ff;
      border-radius: 2px 2px 2px 2px;
      margin-left: 8px;
      opacity: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      img {
        width: 18px;
        height: 18px;
        margin-right: 8px;
        margin-bottom: 2px;
      }

      span {
        font-weight: 400;
        color: #ffffff;
        font-size: 14px;
      }
    }

    .bringintois:hover {
      background-color: #f4faff;
    }
  }

  .iconImg {
    width: 32px;
    height: 32px;
    margin-top: 28px;
    margin-left: 26px;
    margin-right: 22px;
  }

  .content {
    margin-top: 25px;
    width: 100%;

    .name {
      display: flex;
      width: 70%;
      font-size: 18px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);

      .tag {
        display: flex;
        padding-left: 10px;
        margin-top: 3px;

        .industryTag {
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #3370ff;
          color: #3370ff;
          font-size: 11px;
          padding: 0 6px;
          margin-left: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
        }
      }
    }

    .EnterpriseLabel {
      padding: 13px 0;
      width: 70%;

      .tags {
        display: flex;
        flex-wrap: wrap;

        .firmTag {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11px;
          margin-bottom: 5px;
          padding: 0 6px;
          color: #ff7d00;
          background: #fff2e6;
          margin-right: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
        }
      }
    }

    .content-two {
      display: flex;
      margin-top: 4px;
      padding-bottom: 22px;

      .two-a {
        width: 20%;
      }

      .two-c {
        width: 15%;
      }

      .two-b {
        width: 50%;
      }

      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }

      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }
  }
}

.scan {
  width: 100%;
  margin-top: 16px;
  background-color: #fff;
  border-radius: 10px;

  box-shadow: 0px 4px 12px 0px #eef1f8;

  &-top {
    padding: 16px 0px;
    // background: #fff;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 10px 10px 0px 0px;
  }

  &-tab {
    display: flex;
    padding-left: 16px;

    &-con {
      display: flex;
      justify-content: space-between;
    }

    &-select {
      display: flex;
    }

    &-list {
      margin-right: 64px;
      font-size: 16px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      cursor: pointer;

      &.on {
        font-weight: bold;
        color: #3370ff;
        position: relative;

        &::after {
          content: '';
          width: 28px;
          height: 2px;
          background: #3370ff;
          position: absolute;
          left: 18px;
          bottom: -8px;
        }
      }
    }
  }

  &-con {
    margin: 16px 24px;
  }
}
</style>
