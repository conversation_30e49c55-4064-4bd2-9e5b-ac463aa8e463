<template>
  <!-- 高级智搜 -->
  <div style="padding-bottom: 50px">
    <div v-show="isParticulars == 3">
      <div class="title">
        <div class="text">
          <el-dropdown
            trigger="click"
            @command="handleCommand"
          >
            <span class="el-dropdown-link">
              {{ chainName }}<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu
              slot="dropdown"
              style="max-height: 400px; overflow-y: scroll; margin-left: 20px"
            >
              <el-dropdown-item
                v-for="(item, index) in chainList"
                :key="index"
                :command="item"
              >
                {{ item.chainName }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <div style="display: flex">
            <div
              class="title-right"
              style="margin-right: 20px"
              @click="gogetProductSearch"
            >
              {{ listType == 1 ? "产品搜索" : "返回" }}
            </div>
            <div
              class="title-right"
              @click="gowholh"
            >
              产业全景
            </div>
          </div>
        </div>
      </div>
      <div
        v-show="listType == 1"
        class="search"
      >
        <div class="fromItem">
          <div class="formText">
            企业名称：
          </div>
          <div class="formControl">
            <el-input
              v-model="form.keyword"
              placeholder="请输入"
              size="mini"
            />
          </div>
        </div>
        <div class="fromItem">
          <div class="formText">
            所在地区：
          </div>
          <div class="formControl">
            <el-cascader
              v-model="cityCode"
              clearable
              :options="areaList"
              size="mini"
              :props="cascader_props"
              placeholder="选择关注地区"
              class="cascaderList"
            />
          </div>
        </div>
        <div class="fromItem">
          <div class="formText">
            企业规模：
          </div>
          <div class="formControl">
            <el-select
              v-model="form.enterpriseScale"
              :popper-append-to-body="false"
              size="mini"
              multiple
              collapse-tags
              placeholder="请选择"
            >
              <el-option
                v-for="item in enterpriseScale"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </div>
        </div>
        <div class="fromItem">
          <div class="formText">
            成立时间：
          </div>
          <div class="formControl">
            <el-select
              v-model="form.establishment"
              size="mini"
              multiple
              collapse-tags
              :popper-append-to-body="false"
              placeholder="请选择"
            >
              <el-option
                v-for="item in establishment"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </div>
        </div>
        <div class="fromItem">
          <div class="formText">
            注册资本：
          </div>
          <div class="formControl">
            <el-select
              v-model="form.finalExecution"
              size="mini"
              multiple
              collapse-tags
              :popper-append-to-body="false"
              placeholder="请选择"
            >
              <el-option
                v-for="item in finalExecution"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </div>
        </div>
        <div class="fromItem">
          <div class="formText">
            融资轮次：
          </div>
          <div class="formControl">
            <el-select
              v-model="form.financingRounds"
              :popper-append-to-body="false"
              multiple
              collapse-tags
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in financingRounds"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </div>
        </div>
        <div class="fromItem">
          <div class="formText">
            上市板块：
          </div>
          <div class="formControl">
            <el-select
              v-model="form.listedSectorIds"
              size="mini"
              multiple
              collapse-tags
              :popper-append-to-body="false"
              placeholder="请选择"
            >
              <el-option
                v-for="item in appearList"
                :key="item.id"
                :label="item.labelName"
                :value="item.id"
              />
            </el-select>
          </div>
        </div>
        <div class="fromItem">
          <div class="formText">
            科技创新：
          </div>
          <div class="formControl">
            <el-select
              v-model="form.technologyCertificationIds"
              :popper-append-to-body="false"
              size="mini"
              multiple
              collapse-tags
              placeholder="请选择"
            >
              <el-option
                v-for="item in scienceList"
                :key="item.id"
                :label="item.labelName"
                :value="item.id"
              />
            </el-select>
          </div>
        </div>
        <div class="fromItem">
          <div class="formText">
            指数排序：
          </div>
          <div class="formControl">
            <el-select
              v-model="form.orderType"
              :popper-append-to-body="false"
              placeholder="请选择"
              size="mini"
            >
              <el-option
                v-for="item in sortList"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>
        <div class="fromItem">
          <div
            class="cz"
            @click="reset"
          >
            重置
          </div>
          <div
            v-loading="ListLoading"
            class="seek"
            style="margin-left: 20px"
            @click="inquire"
          >
            查询
          </div>
        </div>
      </div>
      <div
        v-show="listType == 2"
        class="search"
      >
        <div class="fromItem">
          <div class="formText">
            产品名称：
          </div>
          <div class="formControl">
            <el-input
              v-model="product.productName"
              placeholder="请输入"
              size="mini"
            />
          </div>
        </div>
        <div class="fromItem">
          <div
            class="cz"
            @click="reset2"
          >
            重置
          </div>
          <div
            v-loading="ListLoading"
            class="seek"
            style="margin-left: 20px"
            @click="inquire2"
          >
            查询
          </div>
        </div>
      </div>
      <div
        v-if="showlist && listType == 1"
        class="treeEnBox"
      >
        <div class="treeBox">
          <el-tree
            v-loading="treeLoading"
            class="tree"
            :data="treeData"
            :expand-on-click-node="false"
            :props="defaultProps"
            node-key="nodeId"
            :default-expanded-keys="DefaultExpansion"
            @node-click="handleNodeClick"
          >
            <span
              slot-scope="{ node, data }"
              class="custom-tree-node"
            >
              <span style="display: flex">
                <span class="nodeNames">{{ node.label }} (
                  <span style="color: #3370ff">{{
                    data.searchEnterpriseCount || 0
                  }}</span>
                  / {{ data.sumEnterpriseCount || 0 }} )
                  <el-tooltip
                    v-if="node.label == '全部'"
                    class="item"
                    effect="dark"
                    content="该数值已去重：因同一企业可能关联多个产业链节点，故统计时剔除重复值"
                    placement="top"
                  >
                    <img
                      src="https://static.idicc.cn/cdn/pangu/i.png"
                      class="iIcon"
                      alt=""
                    > </el-tooltip></span>
              </span>
            </span>
          </el-tree>
        </div>
        <div class="EnterpriseList">
          <div class="headline">
            <div class="total">
              <span>共找到{{ total }}家企业</span>
            </div>
          </div>
          <div style="height: 550px; overflow: scroll">
            <EnterpriseList
              v-loading="ListLoading"
              :enterprise-list="EnterpriseList"
              @particulars="particulars"
            />
          </div>

          <div class="ye">
            <el-pagination
              :current-page.sync="form.pageNum"
              :page-sizes="[5, 10, 20, 50]"
              :page-size.sync="form.pageSize"
              :total="+total"
              layout="sizes, prev, pager, next, jumper"
              @size-change="getList"
              @current-change="getList"
            />
          </div>
        </div>
      </div>

      <div
        v-if="showlist && listType == 2"
        class="EnterpriseList"
      >
        <div class="headline">
          <div class="total">
            <span>共找到{{ productTotal }}家企业</span>
          </div>
        </div>
        <EnterpriseList
          v-loading="ListLoading"
          :enterprise-list="productList"
          @particulars="particulars"
        />
        <div class="ye">
          <el-pagination
            :current-page.sync="product.pageNum"
            :page-sizes="[5, 10, 20, 50]"
            :page-size.sync="product.pageSize"
            :total="+productTotal"
            layout="sizes, prev, pager, next, jumper"
            @size-change="getProductList"
            @current-change="getProductList"
          />
        </div>
      </div>
    </div>

    <Detailsenterprise
      v-if="isParticulars === 2"
      :enterprise-i-d="enterpriseID"
      :listed-sector-ids="form.market"
      :chain-id="chainId"
      :technology-certification-ids="form.science"
      :company-detial="companyDetial"
      @goback="goback"
    />
  </div>
</template>

<script>
import EnterpriseList from ".././EnterpriseList.vue";
import Detailsenterprise from ".././Detailsenterprise.vue";
import { filterData } from "@/utils/utils";
import { getSearchParamAPI } from "@/api/360";
import {
  listByTypeNameAPI,
  listTechnologicalLabelAPI,
  searchAPI,
  homeListAPI,
  getAllAddress,
  enterpriseByChainNodeAPI,
  AIindustryChainNodeTreeAPI,
} from "../../../apiUrl";
import { cityAPI } from "@/api/city";
export default {
  name: "SmartA",
  components: {
    EnterpriseList,
    Detailsenterprise,
  },
  props: {
    befrom: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "nodeName",
      },
      productList: [],
      total: 0,
      productTotal: 0,
      listType: 1,
      areaList: [],
      isParticulars: 3, //搜索1 详情2 智搜3
      chainList: [],
      chainName: "",
      chainId: "",
      cascader_props: {
        checkStrictly: true,
      },
      cityCode: [],
      product: {
        productName: "",
        pageNum: 1,
        pageSize: 10,
      },
      form: {
        keyword: "",
        enterpriseScale: [],
        establishment: [],
        finalExecution: [],
        financingRounds: [],
        orderType: "",
        listedSectorIds: [],
        technologyCertificationIds: [],
        pageNum: 1,
        pageSize: 10,
      },
      ListLoading: false,
      EnterpriseList: [],
      enterpriseScale: [], //企业规模
      establishment: [], //成立时间
      finalExecution: [], //注册资本
      financingRounds: [], //融资轮次
      appearList: [], //上市板块
      scienceList: [], //科技认定
      allNodeIds: [],
      sortList: [
        { name: "默认排序", value: "" },
        { name: "快速成长指数", value: 5 },
        { name: "扩张意愿指数", value: 7 },
      ],
      showList: false,
      treeLoading: false,
      treeData: [],
      industryId: "",
      DefaultExpansion: [],
      companyDetial: {},
      showlist: false,
    };
  },
  created() {
    this.getHomeList();
    this.getScreeningItems();
    this.certification();
    this.getbytype();
  },
  mounted() {
    this.getAllAddressList();
  },
  methods: {
    getAllNodeIds(node) {
      let result = [];

      // 提取当前节点的 nodeId
      if (node.nodeId) {
        result.push(node.nodeId);
      }
      // 递归处理 children
      if (node.children && node.children.length > 0) {
        node.children.forEach((child) => {
          result = result.concat(this.getAllNodeIds(child));
        });
      }

      return result;
    },
    async handleNodeClick(data) {
      this.allNodeIds = this.getAllNodeIds(data);
      this.getList();
    },
    gogetProductSearch() {
      if (this.listType == 1) {
        this.listType = 2;
        this.getProductList();
      } else {
        this.listType = 1;
        this.getList();
        this.getTree();
      }
    },
    async getProductList() {
      try {
        this.ListLoading = true;
        let data = {
          ...this.product,
          chainId: this.chainId,
        };
        filterData(data);
        const res = await enterpriseByChainNodeAPI(data);
        this.showlist = true;
        this.productList = res.result.records;
        this.productTotal = res.result.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.ListLoading = false;
      }
    },
    async getTree() {
      try {
        this.treeLoading = true;
        let data = {
          ...this.form,
          chainId: this.chainId,
          province: this.cityCode[0],
          city: this.cityCode[1],
          area: this.cityCode[2] ? [this.cityCode[2]] : [],
        };
        filterData(data);
        const res = await AIindustryChainNodeTreeAPI(data);
        this.treeData = res.result;
        if (
          this.treeData &&
          this.treeData?.length != 0 &&
          this.treeData[0]?.children.length != 0
        ) {
          this.DefaultExpansion = [this.treeData[0]?.children[0]?.nodeId];
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.treeLoading = false;
      }
    },
    async getList() {
      try {
        this.ListLoading = true;
        let data = {
          ...this.form,
          chainId: this.chainId,
          province: this.cityCode[0],
          city: this.cityCode[1],
          area: this.cityCode[2] ? [this.cityCode[2]] : [],
          chainNodeIds: this.allNodeIds,
        };
        filterData(data);
        const res = await searchAPI(data);
        this.showlist = true;
        this.EnterpriseList = res.result.records;
        this.total = res.result.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.ListLoading = false;
      }
    },
    handleCommand(e) {
      this.chainName = e.chainName;
      this.industryId = e.id;
      this.chainId = e.industryChainId;
      if (this.listType == 1) {
        this.form.pageNum = 1;
        this.allNodeIds = [];
        this.getList();
        this.getTree();
      } else {
        this.product.pageNum = 1;
        this.getProductList();
      }
    },
    goback() {
      this.isParticulars = this.startpoint;
    },
    async getHomeList() {
      const res = await homeListAPI();
      this.chainList = res.result;
      if (this.chainList.length != 0) {
        this.chainName = this.chainList[0].chainName;
        this.industryId = this.chainList[0].id;
        this.chainId = this.chainList[0].industryChainId;
        this.allNodeIds = [];
        this.getList();
        this.getTree();
      }
    },
    // 科技认定
    async certification() {
      const res = await listTechnologicalLabelAPI();
      this.scienceList = res.result;
    },

    // 上市板块
    async getbytype() {
      const res = await listByTypeNameAPI({
        labelTypeName: "上市板块",
      });
      this.appearList = res.result;
      const order = [
        "主板",
        "创业板",
        "科创板",
        "北交所",
        "港股",
        "中概股",
        "新三板",
      ];
      this.appearList = this.appearList.sort((a, b) => {
        const indexA = order.indexOf(a.labelName);
        const indexB = order.indexOf(b.labelName);
        return indexA - indexB;
      });
    },
    async getScreeningItems() {
      const res = await getSearchParamAPI();
      // console.log(res, "res");
      res.result.map((it) => {
        if (it.paramKey == "enterpriseScale") {
          this.enterpriseScale = it.paramValueList;
        }
        if (it.paramKey == "establishment") {
          this.establishment = it.paramValueList;
        }
        if (it.paramKey == "finalExecution") {
          this.finalExecution = it.paramValueList;
        }
        if (it.paramKey == "financingRounds") {
          this.financingRounds = it.paramValueList;
        }
      });
    },
    // 进入企业详情
    particulars(item) {
      this.companyDetial = item;
      this.startpoint = this.isParticulars;
      this.enterpriseID = item.enterpriseId || item.id;
      this.isParticulars = 2;
    },
    inquire() {
      this.form.pageNum = 1;
      this.getList();
      this.getTree();
    },
    inquire2() {
      this.product.pageNum = 1;
      this.getProductList();
    },
    reset() {
      this.form = {
        keyword: "",
        enterpriseScale: [],
        establishment: [],
        finalExecution: [],
        financingRounds: [],
        orderType: "",
        listedSectorIds: [],
        technologyCertificationIds: [],
        pageNum: 1,
        pageSize: 10,
      };
      this.cityCode = [];
      this.getList();
      this.getTree();
    },
    reset2() {
      this.product = {
        productName: "",
        pageNum: 1,
        pageSize: 10,
      };
      this.getProductList();
    },
    getAllAddressList2(res) {
      let addressList = [];
      let areaList = [];
      res.result.map((e) => {
        if (e.parentId == 0) {
          e.label = e.province;
          //e.value = e.code;
          e.value = e.province;
          addressList.push(e);
        } else {
          e.label = e.province;
          e.value = e.province;
          areaList.push(e);
        }
      });
      // 市
      addressList.map((el) => {
        res.result.map((e) => {
          if (el.id == e.parentId) {
            e.label = e.city;
            e.value = e.city;
            if (el.children) {
              el.children.push(e);
            } else {
              el.children = [];
              el.children.push(e);
            }
          }
        });
        return el;
      });
      this.areaList2 = addressList;
    },
    getAllAddressList() {
      this.listLoading = true;
      getAllAddress().then((res) => {
        let ress = JSON.parse(JSON.stringify(res));
        this.getAllAddressList2(ress);
        let addressList = [];
        let areaList = [];
        res?.result?.map((e) => {
          if (e.parentId == 0) {
            e.label = e.province;
            e.value = e.province;
            addressList.push(e);
          } else {
            e.label = e.province;
            e.value = e.province;
            areaList.push(e);
          }
        });
        // 市
        addressList.map((el) => {
          res.result.map((e) => {
            if (el.id == e.parentId) {
              e.label = e.city;
              e.value = e.city;
              if (el.children) {
                el.children.push(e);
              } else {
                el.children = [];
                el.children.push(e);
              }
            }
          });
          return el;
        });
        // 区
        addressList.map((el) => {
          if (el.children && el.children.length > 0) {
            for (let i = 0; i < el.children.length; i++) {
              let el1 = el.children[i];
              areaList.map((e) => {
                if (e.parentId == el1.id) {
                  e.label = e.area;
                  e.value = e.area;
                  if (el1.children) {
                    el1.children.push(e);
                  } else {
                    el1.children = [];
                    el1.children.push(e);
                  }
                }
              });
            }
          }
          return el;
        });
        this.areaList = addressList;
        this.listLoading = false;
      });
    },
    gowholh() {
      if (this.industryId) {
        localStorage.setItem("routerQuery", this.industryId);
        const baseURL = process.env.VUE_APP_PORT_URL
        let newPath = `${baseURL}#/IndustryGraph?id=${this.industryId}`;
        window.open(newPath, '_blank');
      } else {
        this.$message.error("未获取到产业链");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.iIcon {
  width: 16px;
  height: 16px;
  margin-left: 12px;
}
.nodeNames {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  color: #1d2129;
  display: flex;
  align-items: center;
}

.el-dropdown-link {
  font-size: 20px;
  font-weight: bold;
  color: #1d2129;
  cursor: pointer;
}

::-webkit-scrollbar {
  display: none;
}

::v-deep {
  .el-input__inner:focus {
    border-color: #dcdfe6 !important;
  }

  .el-dropdown-menu {
    height: 400px !important;
    overflow: scroll;
  }

  .el-dropdown-menu .el-popper {
    height: 400px !important;
    overflow: scroll !important;
  }
}

.ye {
  padding-top: 10px;
  padding-bottom: 20px;
}

.no {
  background: #d9d9d9 !important;
  border: 0px solid #d9d9d9 !important;
  color: #ffffff !important;
  cursor: no-drop !important;
}

.seek {
  width: 65px;
  border-radius: 4px;
  height: 32px;
  background: #3370ff;
  opacity: 1;
  display: flex;
  align-items: center;
  cursor: pointer;
  justify-content: center;
  font-size: 14px;
  font-family: Source Han Sans CN-Normal, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}

.cz {
  width: 65px;
  height: 32px;
  background: #ffffff;
  border-radius: 5px;
  opacity: 1;
  border: 1px solid #d9d9d9;
  text-align: center;
  line-height: 32px;
  margin-right: 10px;
  font-size: 14px;
  font-family: Source Han Sans CN-Normal, Source Han Sans CN;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
  cursor: pointer;
}

.iconarrow {
  color: #666;
  font-size: 20px;
  background: white;
  padding: 2px;
  width: 25px;
  height: 25px;
  border-radius: 16px;
}

::v-deep {
  .el-pagination {
    position: relative;
    padding-right: 60px;
    right: 0px;
    text-align: right;
    height: 50px;
    padding-top: 10px;
    border: 10px;
  }

  .el-icon-arrow-left {
    margin-top: 0 !important;
  }
}

.treeEnBox {
  width: 100%;
  display: flex;
  height: 700px;
  overflow: scroll;
  border-radius: 10px;
  background: #ffffff;
  box-shadow: 0px 4px 12px 0px #eef1f8;

  .treeBox {
    width: 30%;
    height: 700px;
    overflow: scroll;
    padding: 20px;
    box-sizing: border-box;
  }

  .EnterpriseList {
    width: 70%;
    background-color: #fff;
    height: auto;
    margin: 0 0;

    .headline {
      display: flex;
      height: 64px;
      justify-content: space-between;
      border-bottom: 1px solid #e9e9e9;

      .total {
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
        font-size: 16px;
        padding: 22px 0px 13px 24px;

        .export {
          cursor: pointer;
          margin-left: 16px;
          width: 82px;
          height: 24px;
          border-radius: 4px 4px 4px 4px;
          opacity: 1;
          border: 1px solid #ced4db;
          font-size: 12px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          color: #3f4a59;
          line-height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;

          .up {
            width: 14px;
            height: 14px;
            margin-right: 5px;
          }
        }
      }

      .orderingRule {
        padding: 12px 24px 9px 0px;
      }
    }
  }
}

.EnterpriseList {
  background-color: #fff;
  height: auto;
  border-radius: 10px;
  margin: 16px 0;
  box-shadow: 0px 4px 12px 0px #eef1f8;

  .headline {
    display: flex;
    height: 64px;
    justify-content: space-between;
    border-bottom: 1px solid #e9e9e9;

    .total {
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
      font-size: 16px;
      padding: 22px 0px 13px 24px;

      .export {
        cursor: pointer;
        margin-left: 16px;
        width: 82px;
        height: 24px;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px solid #ced4db;
        font-size: 12px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #3f4a59;
        line-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;

        .up {
          width: 14px;
          height: 14px;
          margin-right: 5px;
        }
      }
    }

    .orderingRule {
      padding: 12px 24px 9px 0px;
    }
  }
}

.title {
  height: 32px;
  // background-color: #fff;
  padding-left: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;

  .text {
    width: 99%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title-left {
      font-size: 16px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
    }

    .title-right {
      width: 109px;
      cursor: pointer;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #fbfcfe;
      box-shadow: 0px 2px 1px 0px rgba(64, 72, 82, 0.05);
      opacity: 1;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #3370ff;
      border: 1px solid #dde4f0;
    }
  }
}

.search {
  height: auto;
  margin: 16px 0;
  padding: 24px;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0px 4px 12px 0px #eef1f8;
  border-radius: 10px 10px 10px 10px;
  display: flex;
  flex-wrap: wrap;

  .fromItem {
    width: 20%;
    display: flex;
    align-items: center;
    margin-right: 70px;
    margin-bottom: 20px;

    .formText {
      font-size: 14px;
      font-weight: normal;
      color: #4e5969;
    }

    .formControl {
      flex: 1;
    }
  }

  .using {
    display: flex;
    margin-bottom: 20px;

    .left {
      width: 70px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
    }

    .antistop {
      width: 80%;
      margin-left: 14px;
      display: flex;
      flex-wrap: wrap;

      .pitchon {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 26px;
        cursor: pointer;
        background: #3370ff;
        border-radius: 2px 2px 2px 2px;
        margin-top: 8px;
        margin-right: 20px;
        padding: 2px 8px;
        opacity: 1;
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        font-weight: 400;
        color: #ffffff;
      }

      .pitch {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 26px;
        cursor: pointer;
        border-radius: 2px 2px 2px 2px;
        margin-top: 8px;
        padding: 2px 8px;
        margin-right: 20px;
        opacity: 1;
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
      }

      .multi {
        margin-right: 30px;
        margin-bottom: 10px;
      }
    }
  }

  .selected {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;
    background: #e6f7ff;
    opacity: 1;
    border: 1px solid #bae7ff;

    .left {
      display: flex;
      width: 95%;

      .yet {
        padding: 9px 0;
        font-weight: 400;
        margin-left: 14px;
        display: flex;
        min-width: 75px;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
      }

      .selectedTag {
        padding-top: 9px;
        display: flex;
        width: 100%;
        flex-wrap: wrap;
      }

      .tagBox {
        max-width: 200px;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .tag {
        font-size: 12px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        color: #3370ff;
        padding: 2px 8px;
        margin-bottom: 8px;
        margin-left: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 22px;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #3370ff;

        .name {
          color: #6c6c6c;
        }

        .contentSetting {
          max-width: 200px;
          white-space: nowrap;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .reset {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 22px;
      font-size: 14px;
      font-family: Abel-Regular, Abel;
      font-weight: 400;
      color: #3370ff;
    }
  }

  .find {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
  }
}
</style>
