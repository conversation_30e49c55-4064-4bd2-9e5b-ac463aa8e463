<template>
  <!-- 创新能力 -->
  <div>
    <div class="patentanalysis">
      <span>专利分析</span>
      <div style="display: flex; width: 100%">
        <div style="display: flex; width: 100%">
          <div class="analyst">
            <div class="text">
              企业当前有效专利{{
                typeCount.efficientPatentCount || 0
              }}个,其中发明公开类型专利数量{{
                typeCount.inventionDisclosureCount || 0
              }}个,其中发明授权类型专利数量{{
                typeCount.inventionAuthorizationCount || 0
              }}个,实用新型专利{{
                typeCount.utilityModelCount || 0
              }}个,外观设计专利{{ typeCount.exteriorDesignCount || 0 }}个。
            </div>
            <div style="display: flex;">
              <div class="Patents">
                <div
                  id="Patent"
                  class="Patent"
                />
              </div>
              <div
                class="tieBox"
              >
                <span class="tieheds">
                  <div>
                    ·产业相关专利排名<el-tooltip
                      effect="dark"
                      content="该企业产业相关专利数量在产业链企业内排名"
                      placement="top"
                    >
                      <i class="el-icon-info" /> </el-tooltip>
                  </div>
                  <el-select
                    v-model="inGId"
                    placeholder="请选择"
                    style="width: 100px;"
                    size="mini"
                    @change="getseniority"
                  >
                    <el-option
                      v-for="item in industryList"
                      :key="item.id"
                      :label="item.chainName"
                      :value="item.id"
                    />
                  </el-select>
                </span>
                <div class="ranking">
                  <div class="name">
                    所在地排名
                  </div>
                  <el-progress
                    class="progress"
                    :percentage="
                      nativeranking || 0
                    "
                    :stroke-width="8"
                    :show-text="false"
                  />
                  <span class="percentage">超过{{
                    nativeranking ||
                      0
                  }}%</span>
                </div>
                <div class="ranking">
                  <div class="name">
                    全国排名
                  </div>
                  <el-progress
                    class="progress"
                    :percentage="
                      nationwideranking ||
                        0
                    "
                    :stroke-width="8"
                    :show-text="false"
                  />
                  <span class="percentage">超过{{
                    nationwideranking ||
                      0
                  }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="Patentgrowth">
      <span>专利增长趋势</span>
      <div
        id="growthtrend"
        class="growthtrend"
      />
    </div>
    <div
      v-if="patent"
      class="patentinformation"
    >
      <span>专利信息明细</span>
      <div class="table">
        <el-table
          :data="tableData"
          style="width: 100%"
        >
          <!--           <el-table-column
            type="index"
            align="center"
            label="序号"
            width="50"
          /> -->
          <el-table-column
            prop="patentName"
            label="专利名称"
            width="230"
            align="center"
          />
          <el-table-column
            prop="documentType"
            label="专利类型"
            width="100"
            align="center"
          >
            <template slot-scope="{ row }">
              <div v-if="row.documentType == 1">
                发明公开
              </div>
              <div v-if="row.documentType == 2">
                发明授权
              </div>
              <div v-if="row.documentType == 3">
                实用新型
              </div>
              <div v-if="row.documentType == 4">
                外观设计
              </div>
            </template>
          </el-table-column>
          <!--           <el-table-column
            width="100"
            label="注册地址"
            align="center"
          >
            <template slot-scope="{ row }">
              {{ row.applicantsCountryProvince }}
            </template>
          </el-table-column> -->
          <el-table-column
            prop="authorizationStatus"
            width="100"
            label="授权状态"
            align="center"
          >
            <template slot-scope="{ row }">
              <div v-if="row.authorizationStatus == 0">
                未授权
              </div>
              <div v-if="row.authorizationStatus == 1">
                已授权
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="applicationNumber"
            label="专利申请号"
            align="center"
            width="140"
          />
          <el-table-column
            prop="applicationDate"
            label="专利申请日"
            align="center"
            width="120"
          />
          <el-table-column
            prop="digest"
            align="center"
            label="摘要"
          />
        </el-table>
      </div>
      <div class="ye">
        <el-pagination
          :current-page.sync="pageNum"
          :page-size.sync="pageSize"
          :total="+total"
          layout="total,prev, pager, next"
          @size-change="getpageByEnterpriseId"
          @current-change="getpageByEnterpriseId"
        />
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import {
  typeCountAPI,
  growthTrendAPI,
  rankingAPI,
  pageByEnterpriseIdAPI,
} from "../../../apiUrl";
export default {
  name: "InnovateA",
  props: {
    enterpriseID: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      pageNum: 1,
      pageSize: 5,
      total: 0,
      inGId:"",
      industryList:[],
      nationwideranking:0,//全国排名
      nativeranking:0,//本地排名
      tableData: [],
      typeCount: {},
      patent: true, //是否展示专利
      graphic: [
             {
               type: 'rect',
               left: 'center',
               top: 'middle',
               shape: {
                 width: 160,
                 height: 70,
                 radius: 20 
               },
               style: {
                 fill: '#cdd1d7',
                 stroke: '#ccc',
               },
               z:10
             },
             {
               type: 'text',
               left: 'center',
               top: 'middle',
               style: {
                 text: '暂无数据',
                 textAlign: 'center',
                 textVerticalAlign: 'middle',
                 fill: '#fff',
                 fontSize: 18
               },
               z:10
             }
        ]
    };
  },
  created() {
    this.getseniority(); //相关专利排行
    this.getpageByEnterpriseId(); //指定企业专利分页
  },
  mounted() {
    this.gettypeCount(); //专利类型数量统计
    this.growthtrendFn();
  },
  methods: {
    //专利信息分页列表
    async getpageByEnterpriseId() {
      const res = await pageByEnterpriseIdAPI({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        enterpriseId: this.enterpriseID,
      });
      if (res.result.total == 0) {
        this.patent = false;
      }
      this.tableData = res.result.records;
      this.total = res.result.total;
    },
    // 专利排名
    async getseniority() {
      let chainId = this.inGId ? this.inGId : ''
      const res = await rankingAPI({
        enterpriseId: this.enterpriseID,
        chainId,
      });
      this.industryList=res.result.chainList
      this.inGId=res.result.defaultChainId
      if (res.result != null) {
        if(res.result.enterpriseChainPatentRanking != null){
          this.nationwideranking = Number(res.result.enterpriseChainPatentRanking.rankingPercentage);
        }else{
          this.nationwideranking=0
        }
        if( res.result.enterpriseAreaPatentRanking != null){
          this.nativeranking = Number(res.result.enterpriseAreaPatentRanking.rankingPercentage
        )
        }else{
          this.nativeranking=0
        }
      }else{
        this.nationwideranking=0
        this.nativeranking=0
      }
    },
    // 专利分析
    async gettypeCount() {
      const res = await typeCountAPI({
        enterpriseId: this.enterpriseID,
      });
      this.typeCount = res.result;
      this.PatentFn();
    },
    PatentFn() {
      var chartDom = document.getElementById("Patent");
      var myChart = echarts.init(chartDom);
      var option;
      // 总数
      let sum = this.typeCount.efficientPatentCount || 0;
      // 各种类型
      let data = [
        {
          name: "发明公开类型专利",
          value: this.typeCount.inventionDisclosureCount || 0,
        },
        {
          name: "发明授权类型专利",
          value: this.typeCount.inventionAuthorizationCount || 0,
        },
        {
          name: "实用新型类型专利",
          value: this.typeCount.utilityModelCount || 0,
        },
        {
          name: "外观设计类型专利",
          value: this.typeCount.exteriorDesignCount || 0,
        },
      ];

      option = {
        tooltip: {
          trigger: "item",
        },
        title: [
          {
            text: "按类型分布",
            top: "42%",
            textAlign: "center",
            left: "49.5%",
            textStyle: {
              color: "#000000",
              fontSize: 14,
              fontWeight: "400",
              //fontFamily: "PangMenZhengDao",
              opacity: 0.6,
            },
          },
          {
            text: sum,
            top: "50%",
            textAlign: "center",
            left: "49.5%",
            textStyle: {
              color: "#000000",
              fontSize: 24,
              fontWeight: "400",
            },
          },
        ],
        series: [
          {
            type: "pie",
            radius: ["40%", "50%"],
            label: {
              show: true,
              formatter: (params) => {
                let color = "blue";
                return `${params.name} {${color}|${params.value}}`;
              },
              rich: {
                blue: {
                  color: "#40a2ff",
                  fontWeight: 500,
                  fontSize: 12,
                },
              },
            },
            labelLine: {
              show: true,
            },
            data,
          },
        ],
      };

      option && myChart.setOption(option);
    },
    // 增长趋势
    async growthtrendFn() {
      let chartDom = document.getElementById("growthtrend");
      let myChart = echarts.init(chartDom);
      let option;
      const res = await growthTrendAPI({
        enterpriseId: this.enterpriseID,
      });
      let list = res.result;
      let key = Object.keys(list);
      let value = Object.values(list);
      let  graphic =[]
      if(key.length ==0){
        graphic=this.graphic
      }
      option = {
        graphic,
        grid: {
          left: "5%",
          right: "10%",
          bottom: "5%",
          top: "10%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow", // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
            axis: "auto", // 指示器的坐标轴。
            snap: true, // 坐标轴指示器是否自动吸附到点上
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: key,
          // 刻度
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          name: "个",
          nameTextStyle: {
            padding: [0, 28, 0, 0],
          },
        },
        series: [
          {
            data: value,
            type: "line",
            showSymbol: false,
            lineStyle: {
              color: "#165DFF",
            },
            itemStyle: {
              color: "#165DFF",
              normal: {
                barBorderRadius: 4,

                color: "#E5EDFF",
              },
            },
            areaStyle: {},
          },
        ],
      };

      option && myChart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-progress-bar__inner {
    background-color: #1191ff;
    width: 300px;
  }
  .el-progress__text {
    font-style: 12px !important;
  }
  .el-pagination {
    display: flex;
    justify-content: center;
    -webkit-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.1) !important;
  }
}
.patentanalysis {
  width: 100%;
  border-bottom: 1px solid #e8e8e8;
  span {
    font-size: 16px;
    margin-left: 16px;
    margin-right: 40px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #4c586c;
  }
  .analyst {
    width: 100%;
    .text {
      font-size: 14px;
      margin-top: 34px;
      width: 80%;
      margin-left: 16px;
      margin-bottom: 44px;
      font-family: Abel-Regular, Abel;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
    }
    .tieBox{
      display: flex; 
      flex-direction: column;
      width: 50%;    
      margin-top: 50px;
      margin-left: 10%;
      padding-right: 10%;
    }
    .tieheds {
      width: 100%;
      margin: 0px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(29, 33, 41, 0.85);
    }
    .ranking {
      display: flex;
      width: 100%;
      margin-top: 43px;
      .name {
        width: 100px;
        min-width: 100px;
        font-size: 14px;
        margin-right: 5px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
        span {
          font-size: 14px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          margin-left: 0px;
          margin-right: 0px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
      .progress{
        flex-grow: 1; /* 占满剩余空间 */
        margin-top: 6px
      }
      .percentage {
        font-size: 14px;
        display: flex;
        width: 80px;
        min-width: 80px;
        white-space: nowrap;
        margin-left: 40px;
        margin-right: 0px;
        margin-top: 2px;
        font-family: Abel-Regular, Abel;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
  .Patents {
    width: 40%;
    .distribute {
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(29, 33, 41, 0.85);
    }
    .Patent {
      height: 300px;
    }
  }
}
.Patentgrowth {
  width: 100%;
  margin-top: 32px;
  span {
    font-size: 16px;
    margin-left: 16px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #4c586c;
  }
  .growthtrend {
    width: 100%;
    height: 400px;
    border-bottom: 1px solid #e8e8e8;
  }
}
.patentinformation {
  width: 100%;
  margin-top: 32px;
  span {
    font-size: 16px;
    margin-left: 16px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #4c586c;
  }
  .table {
    margin-top: 20px;
  }
}
</style>