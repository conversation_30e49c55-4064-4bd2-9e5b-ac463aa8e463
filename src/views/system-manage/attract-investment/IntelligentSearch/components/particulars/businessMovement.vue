<template>
  <!-- 企业动态 -->
  <div>
    <div class="interview">
      <span class="title">企业动态</span>
      <div class="total">
        当前共<span class="tot"> {{ eventList.length }} </span>条企业动态
      </div>
    </div>
    <div class="record">
      <div
        v-for="(item, index) in eventList"
        :key="index"
        class="single"
      >
        <div class="head">
          <span class="time">{{ item?.publishDate&&formatTimestamp(item?.publishDate) }}</span>
        </div>
        <div class="box">
          <div class="one">
            <span class="jqTitle"> {{ item.eventTitle }}</span>
          </div>
          <div class="ones">
            <span class="jg"> {{ item.eventContent
            }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs  from "dayjs";

export default {
    name: "InterviewA",
    props: {
        eventList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
        };
    },
    created() {
    },
    methods: {
        formatTimestamp(timestamp) {
          if (!timestamp) return '';
          
          // 字符串类型的时间戳处理
          if (typeof timestamp === 'string') {
            // 检查是否是纯数字字符串
            const isNumericString = /^\d+$/.test(timestamp);
            
            if (isNumericString && timestamp.length >= 10) {
              // 是数字字符串格式的时间戳
              const numTimestamp = Number(timestamp);
              // 处理秒级和毫秒级时间戳
              const dateValue = timestamp.length > 10 ? numTimestamp : numTimestamp * 1000;
              return dayjs(dateValue).format("YYYY-MM-DD");
            } else {
              // 可能是日期字符串格式
              return dayjs(timestamp).format("YYYY-MM-DD");
            }
          } 
          
          // 数字类型的时间戳处理
          else if (typeof timestamp === 'number') {
            const dateValue = String(timestamp).length > 10 ? timestamp : timestamp * 1000;
            return dayjs(dateValue).format("YYYY-MM-DD");
          }
          
          // 其他情况
          return dayjs(timestamp).format("YYYY-MM-DD");
        }
    },
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-pagination {
        display: flex;
        justify-content: center;
        -webkit-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.1) !important;
    }
}

.green {
    display: flex;
    background: #10aa38;
    margin-left: 25px;
    content: "";
    position: relative;
    width: 6px;
    right: 5px;
    top: 5px;
    height: 6px;
    border-radius: 50%;
}

.red {
    display: flex;
    background: #ff2034;
    content: "";
    margin-left: 25px;
    position: relative;
    width: 6px;
    right: 5px;
    top: 5px;
    height: 6px;
    border-radius: 50%;
}

.yuan {
    display: flex;
    background: #c9cdd4;
    content: "";
    position: relative;
    width: 6px;
    right: 3px;
    top: 6px;
    height: 6px;
    border-radius: 50%;
}

.interview {
    .title {
        font-size: 16px;
        margin-left: 16px;
        margin-right: 40px;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #4c586c;
    }

    .total {
        font-size: 14px;
        margin-left: 16px;
        margin-top: 16px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);

        .tot {
            color: #3370FF;
        }
    }
}

.record {
    margin-left: 16px;
    margin-top: 16px;

    .head {
        display: flex;
        height: 19px;

        .time {
            margin-left: 25px;
            font-size: 13px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #4e5969;
        }

        .letter {
            font-size: 14px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #1d2129;
        }
    }

    .box {
        margin-left: 25px;
        margin-top: 6px;
        margin-bottom: 25px;
        height: auto;
        background: #f7f8fa;
        padding: 20px 18px;

        .one {
            display: flex;
            margin-bottom: 16px;
        }

        .ones {
            display: flex;
        }

        .describe {
            font-size: 14px;
            width: 56px;
            margin-right: 10px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #1d2129;
        }

        .jg {
            font-size: 14px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            max-width: 92%;
            color: #4e5969;
            line-height: 20px;
        }

        .jqTitle {
            font-size: 14px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            max-width: 92%;
            color: #1D2129;
        }
    }
}

.head::after {
    background-color: #3e80ff;
    content: "";
    position: absolute;
    //left: 65px;
    border: 4px solid #bedaff;
    width: 16px;
    height: 16px;
    z-index: 6;
    border-radius: 50%;
}
.single{
    position: relative;
}

.single:not(:last-child)::before {
    background-color: #f0f0f0;
    content: "";
    position: absolute;
    left: 7px;
    z-index: 5;
    width: 2px;
    height: calc(100% + 25px);
}
</style>