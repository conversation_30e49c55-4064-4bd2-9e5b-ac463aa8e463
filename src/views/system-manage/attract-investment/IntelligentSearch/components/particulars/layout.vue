<template>
  <!-- 战略布局 -->
  <div>
    <div class="industrial">
      <div
        v-if="patenttable.length>0"
        class="planning"
      >
        <span>产业布局</span>
        <div class="invent">
          <el-select
            ref="chain"
            v-model="patentId"
            placeholder="请选择"
            @change="moneyflowFn"
          >
            <el-option
              v-for="item in patenttable"
              :key="item.chainId"
              :label="item.chainName"
              :value="item.chainId"
            >
              {{ item.chainName.replace("产业金脑·", "") }}
              <el-tooltip
                effect="dark"
                content="关联度最高"
                placement="top"
              >
                <img
                  v-if="item.isBest"
                  src="https://static.idicc.cn/cdn/pangu/relevancy.png"
                  class="rele"
                >
              </el-tooltip>
            </el-option>
          </el-select>
        </div>
      </div>
      <div
        v-if="patenttable.length>0"
        class="table1"
      >
        <el-table
          :border="true"
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column
            prop="name"
            label="产业链名称"
            width="330"
            align="center"
          >
            <template slot-scope="{}">
              {{ patentname }}
            </template>
          </el-table-column>
          <el-table-column
            label="产业链节点"
            width="330"
            align="center"
          >
            <template slot-scope="{ row }">
              {{ row.nodeName }}
              <el-tooltip
                effect="dark"
                content="关联度最高"
                placement="top"
              >
                <img
                  v-if="row.isBest"
                  src="https://static.idicc.cn/cdn/pangu/relevancy.png"
                  class="rele"
                >
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="布局相关企业"
            align="center"
          >
            <template slot-scope="{ row }">
              <div class="strategy">
                <div />
                {{ row.enterpriseName }}
                <div
                  v-if=" row.relation !=null"
                  class="enterTag"
                >
                  {{ row.relation }}
                </div>
                <div v-else />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div
      v-if="Industrylayout!==null"
      class="guild"
    >
      <span>行业布局</span>
      <div class="content">
        <span>{{ Industrylayout.desc }}</span>
        <div class="tabs">
          <div
            id="Patent"
            class="Patent"
          />
          <div class="table2">
            <el-table
              :border="true"
              :data="industryList"
              style="width: 100%"
            >
              <el-table-column
                prop="enterpriseName"
                label="企业名称"
                width="230"
                align="center"
              />
              <el-table-column
                label="行业"
                prop="nationalStandardIndustry"
                width="160"
                align="center"
              />
              <el-table-column
                prop="registerDate"
                align="center"
                label="成立日期"
              />
              <el-table-column
                prop="registeredCapital"
                align="center"
                label="注册资本"
              />
              <el-table-column
                prop="shareholdingRatio"
                align="center"
                label="持股比例"
              />
            </el-table>
            <el-pagination
              :current-page.sync="staform.pageNum"
              :page-sizes="[3, 10, 20, 50]"
              :page-size.sync="staform.pageSize"
              :total="+staform.total"
              layout="total,sizes, prev, pager, next, jumper"
              @size-change="getList"
              @current-change="getList"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="arealdistribution!==null"
      class="guild"
    >
      <span>区域布局</span>
      <div class="content">
        <span>{{ arealdistribution.desc }}</span>
        <div class="tabs">
          <mapMain
            ref="map"
            class="Patent"
          />
          <div class="table2">
            <el-table
              :border="true"
              :data="areaList"
              style="width: 100%"
            >
              <el-table-column
                prop="enterpriseName"
                label="企业名称"
                width="230"
                align="center"
              />
              <el-table-column
                label="地区"
                prop="indicator"
                width="180"
                align="center"
              >
                <template slot-scope="{ row }">
                  {{ row.province
                  }}<span
                    v-if="row.province !== row.city"
                    style="margin-left: 0px"
                  >{{ row.city }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="registerDate"
                align="center"
                label="成立日期"
              />
              <el-table-column
                prop="registeredCapital"
                align="center"
                label="注册资本"
              />
              <el-table-column
                prop="shareholdingRatio"
                align="center"
                label="持股比例"
              />
            </el-table>
            <el-pagination
              :current-page.sync="form.pageNum"
              :page-sizes="[3, 10, 20, 50]"
              :page-size.sync="form.pageSize"
              :total="+form.total"
              layout="total,sizes, prev, pager, next, jumper"
              @size-change="getList2"
              @current-change="getList2"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="arealdistribution==null &&Industrylayout==null && patenttable.length<=0"
      class="nodata"
    >
      <div class="con">
        <img src="https://static.idicc.cn/cdn/pangu/vacancy.png">
        <span class="p1">暂无数据</span>
      </div>
    </div> 
  </div>
</template>

<script>
import {
  nodeCorrelationListAPI,
  chainCorrelationListAPI,
  typeCountsAPI,
  strategic_layoutAPI,
} from "@/views/system-manage/attract-investment/apiUrl";
import mapMain from "./map.vue";
import * as echarts from "echarts";
export default {
  name: "LayoutA",
  components: {
    mapMain,
  },
  props: {
    enterpriseID: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      tableData: [],
      patenttable: [], //产业链
      patentId: 0,
      patentname: "",
      staform: {
        pageNum: 1,
        pageSize: 3,
        total: 0,
      },
      form: {
        pageNum: 1,
        pageSize: 3,
        total: 0,
      },
      Industrylayout: {}, //行业布局展示
      arealdistribution: {},
      industryList: [],
      areaList: [],
    };
  },
  created() {
    this.getindustrial(); //行业布局
    this.getList();
    this.getList2();
  },
  mounted() {
    this.PatentFn(); //行业分布
    this.distributionFn();//区域分布
  },
  methods: {
    async getList() {
      let data = {
        pageNum: this.staform.pageNum,
        pageSize: this.staform.pageSize,
        enterpriseId: this.enterpriseID,
      };
      const res = await strategic_layoutAPI(data);
      this.industryList = res.result.records;
      this.staform.total = res.result.total;
    },
    async getList2() {
      let data = {
        pageNum: this.form.pageNum,
        pageSize: this.form.pageSize,
        enterpriseId: this.enterpriseID,
      };
      const res = await strategic_layoutAPI(data);
      this.areaList = res.result.records;
      this.form.total = res.result.total;
    },
    //产业布局
    async getindustrial() {
      const res = await chainCorrelationListAPI({
        enterpriseId: this.enterpriseID,
      });
      this.patenttable = res.result;
      if( this.patenttable.length>0){
        this.patentId = this.patenttable[0].chainId;
      this.patentname = this.patenttable[0].chainName;
      const resList = await nodeCorrelationListAPI({
        enterpriseId: this.enterpriseID,
        chainId: this.patentId,
      });
      this.tableData = resList.result;
      }
    },
    // 切换产业链
    async moneyflowFn(i) {
      this.tableData=[]
      this.patenttable.forEach(it=>{
        if(it.chainId==i){
          this.patentname = it.chainName;
        }
      })
      const resList = await nodeCorrelationListAPI({
        enterpriseId: this.enterpriseID,
        chainId: this.patentId,
      });
      this.tableData = resList.result;
    },
    // 区域分布
    async distributionFn() {
      const res = await typeCountsAPI({
        type: 2, //行业布局统计
        enterpriseId: this.enterpriseID,
      });
      this.arealdistribution = res.result;
      if(this.arealdistribution!=null){
        const info = Object.keys(res.result.map).map((key) => ({
        regionName: key,
        number: res.result.map[key],
      }));
        this.$refs.map.init(info);
      }
    },
    // 行业布局
    async PatentFn() {
      const res = await typeCountsAPI({
        type: 1, //行业布局统计
        enterpriseId: this.enterpriseID,
      });
      this.Industrylayout = res.result;
      if(this.Industrylayout==null){
        return 
      }
      const newArray = Object.keys(this.Industrylayout.map).map((key) => ({
        name: key,
        value: this.Industrylayout.map[key],
      }));
      let sum = newArray.reduce(
        (sum, opinion) => sum + Number(opinion.value),
        0
      );
      var chartDom = document.getElementById("Patent");
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        tooltip: {
          trigger: "item",
        },
        title: [
          {
            text: "按类型分布",
            top: "42%",
            textAlign: "center",
            left: "48%",
            textStyle: {
              color: "#000000",
              fontSize: 14,
              fontWeight: "400",
              //fontFamily: "PangMenZhengDao",
              opacity: 0.6,
            },
          },
          {
            text: sum || 0,
            top: "50%",
            textAlign: "center",
            left: "48%",
            textStyle: {
              color: "#000000",
              fontSize: 24,
              fontWeight: "400",
            },
          },
        ],
        series: [
          {
            type: "pie",
            radius: ["40%", "50%"],
            label: {
              show: true,
              formatter: (params) => {
                let color = "blue";
                return `${params.name} {${color}|${params.value}}`;
              },
              rich: {
                blue: {
                  color: "#40a2ff",
                  fontWeight: 500,
                  fontSize: 12,
                },
              },
            },
            labelLine: {
              show: true,
            },
            data: newArray,
          },
        ],
      };

      option && myChart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
.nodata {
  min-width: 88.5vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  .con {
    margin-top: 10%;
    width: 170px;
    height: 300px;
    display: flex;
    align-items: center;
    flex-direction: column;
    img {
      width: 216px;
      height: 177px;
    }
    .p1 {
      margin-top: 38px;
      font-size: 14px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
    .p2 {
      margin-top: 16px;
      font-size: 12px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
.strategy {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .enterTag {
    float: right;
    width: auto;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f2f2f2;
    color: #000000;
    padding: 4px;
    font-size: 11px;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.45);
  }
}

::v-deep {
  .el-pagination {
    position: relative;
    padding-right: 60px;
    right: 0px;
    text-align: right;
    height: 50px;
    vertical-align: top;
    border: 10px;
  }
  .el-icon-arrow-left {
    margin-top: 0 !important;
  }
}
// 关联度最高图标
.rele {
  width: 14px;
  height: 14px;
  margin-left: 2px;
}
.industrial {
  span {
    font-size: 16px;
    margin-left: 16px;
    margin-right: 40px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #4c586c;
  }
  .table1 {
    margin-top: 8px;
  }
  .planning {
    display: flex;
    justify-content: space-between;
    .invent {
      display: flex;
      .xzoption {
        width: 88px;
        height: 32px;
        color: #3370FF;
        background: #ffffff;
        border-radius: 2px 0px 0px 2px;
        opacity: 1;
        border: 1px solid #3370FF;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
      .option {
        width: 88px;
        height: 32px;
        background: #ffffff;
        border-radius: 2px 0px 0px 2px;
        opacity: 1;
        border: 1px solid #d9d9d9;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
    }
  }
}
.guild {
  width: 100%;
  margin-top: 32px;
  span {
    font-size: 16px;
    margin-left: 16px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #4c586c;
  }
  .content {
    margin-top: 24px;
    span {
      font-size: 14px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
    }
    .tabs {
      display: flex;
      .Patent {
        width: 30%;
        height: 400px;
      }
      .table2 {
        width: 68%;
        margin-top: 65px;
        margin-left: 16px;
      }
    }
  }
}
</style>