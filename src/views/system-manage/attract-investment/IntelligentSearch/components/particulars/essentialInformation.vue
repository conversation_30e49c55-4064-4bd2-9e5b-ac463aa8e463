<template>
  <!-- 基本信息 -->
  <div>
    <el-descriptions
      class="margin-top"
      style="margin-bottom: 50px;"
      title="工商注册"
      :column="3"
      :label-style="{ 'word-break': 'keep-all' }"
      :content-style="{ 'max-width': '300px', 'word-break': 'keep-all' }"
      border
    >
      <el-descriptions-item label="统一社会信用代码">
        {{ information?.enterprise?.unifiedSocialCreditCode || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="成立日期">
        {{ information?.enterprise?.registerDate || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="企业规模">
        {{ information?.enterprise?.scale || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="融资轮次">
        {{
          information?.enterprise?.enterpriseFinancingRoundsNames
            ? Array.isArray(information.enterprise.enterpriseFinancingRoundsNames)
              ? information.enterprise.enterpriseFinancingRoundsNames.join('、')
              : information.enterprise.enterpriseFinancingRoundsNames
            : '--'
        }}
      </el-descriptions-item>
      <el-descriptions-item label="法定代表人">
        {{ information?.enterprise?.legalPerson || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="注册资本">
        {{ information?.enterprise?.registeredCapital || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="企业类型">
        {{ information?.enterprise?.enterpriseType || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="登记状态">
        {{ information?.enterprise?.registerStatus || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="所在地区">
        {{ information?.enterprise?.province || '--' }}
        <span
          v-if="information?.enterprise?.province !== information?.enterprise?.city"
        >{{ information?.enterprise?.city || '--' }}</span>
        {{ information?.enterprise?.area || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="所属行业">
        {{ information?.enterprise?.nationalStandardIndustry || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="参保人数">
        {{ information?.enterprise?.insuredPersonsNumber || '--' }}
      </el-descriptions-item>
      <el-descriptions-item
        label="注册地址"
      >
        {{ information?.enterprise?.enterpriseAddress || '--' }}
      </el-descriptions-item>
      <el-descriptions-item
        :span="3"
        label="经营范围"
      >
        {{ information?.enterprise?.businessScope || '--' }}
      </el-descriptions-item>
    </el-descriptions>
    <!--     <p
      v-if="information?.shareholders?.length>0"
      style="font-size: 16px; font-weight: bold; margin: 20px 0;"
    >
      股东信息
    </p>
    <el-table
      v-if="information?.shareholders?.length>0"
      :data="information?.shareholders"
      style="width: 100%; margin-bottom: 50px;"
    >
      <el-table-column
        width="120"
        align="center"
        type="index"
        label="序号"
      />
      <el-table-column
        prop="shareholder"
        label="股东名称"
        align="center"
        width="200"
      />
      <el-table-column
        prop="shareholdingRatio"
        label="持股比例"
        align="center"
      />
      <el-table-column
        prop="subscribedCapitalContribution"
        align="center"
        label="认缴出资额"
      >
        <template slot-scope="{row}">
          <span v-if="row.subscribedCapitalContribution!=null">{{ (row.subscribedCapitalContribution / 10000).toFixed(2) }}万元</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="subscribedDate"
        align="center"
        label="认缴出资日期"
      />
      <el-table-column
        prop="paidInCapitalContribution"
        align="center"
        label="实缴出资额"
      >
        <template slot-scope="{row}">
          <span v-if="row.paidInCapitalContribution!=null">{{ (row.paidInCapitalContribution / 10000).toFixed(2) }}万元</span> 
        </template>
      </el-table-column>
      <el-table-column
        prop="paidInDate"
        align="center"
        label="实缴出资日期"
      />
      <el-table-column
        prop="ultimateBeneficiaryShares"
        align="center"
        label="最终收益股份"
      />
    </el-table>
    <p
      v-if="information?.mainStaffs?.length > 0"
      style="font-size: 16px; font-weight: bold; margin: 20px 0;"
    >
      主要人员信息
    </p>
    <el-table
      v-if="information?.mainStaffs?.length > 0"
      :data="information?.mainStaffs"
      border
      style="width: 100%; margin-bottom: 50px;"
    >
      <el-table-column
        width="120"
        align="center"
        type="index"
        label="序号"
      />
      <el-table-column
        prop="name"
        label="人员名称"
        align="center"
      />
      <el-table-column
        prop="position"
        label="人员职务"
        align="center"
      />
    </el-table>
    <p
      v-if="information?.activities?.length > 0"
      style="font-size: 16px; font-weight: bold; margin: 20px 0;"
    >
      所属产业链及环节节点
    </p>
    <el-timeline
      v-if="information?.activities?.length > 0"
      style="margin-bottom: 50px;"
      :reverse="reverse"
    >
      <el-timeline-item
        v-for="(activity, index) in information.activities"
        :key="index"
      >
        <template slot="dot">
          <div class="dot">
            <div class="dot-raido">
              <div class="dit" />
            </div>
          </div>
        </template>
        <div class="dot-box">
          <div class="span1">
            {{ activity?.chainName || '' }}
          </div>
          <div class="span2">
            {{ activity.children.length>1 ? (activity.children.map(e=>e.nodeName)).join(','): '' }}
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
    <p
      style="font-size: 16px; font-weight: bold; margin: 20px 0;"
    >
      公司介绍
    </p>
    <div style="margin-top: 20px;">
      <span class="span2">{{ information?.enterprise?.businessScope }}</span>
    </div> -->
  </div>
</template>

<script>
export default {
  name: "EssentialInformation",
  props: {
    information: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      activities:[
        {
          content: '活动按期开始',
          timestamp: '2018-04-15'
        }, {
          content: '通过审核',
          timestamp: '2018-04-13'
        }, {
          content: '创建成功',
          timestamp: '2018-04-11'
        }
      ]
    };
  },
  mounted(){
    // console.log(this.information)
  },
};
</script>

<style lang="scss" scoped>
::v-deep.el-timeline {
  .el-timeline-item__tail {
    left: 6px;
    border-left: 1px solid #dfe4ed
  }
}
.dot {
  display: flex;
  .dot-raido {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 12px;
    height: 12px;
    background: #BEDAFF;
    border-radius: 50%;
    .dit {
      width: 6px;
      height: 6px;
      background: #417FFF;
      border-radius: 50%;
    }
  }
}
.span1 {
  font-weight: 600;
  font-size: 14px;
  color: #1D2129;
}
.span2 {
  font-weight: 300;
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
}
.mgt-40 {
  margin-top: 40px !important;
}
</style>