<template>
  <!-- 企业动态 -->
  <div>
    <div class="interdiv">
      <div
        v-if="chainList && chainList?.length != 0"
        class="title"
      >
        产业链节点及产品情况
      </div>
      <div
        v-if="chainList && chainList?.length != 0"
        class="tag-p"
      >
        <div class="tag-container">
          <div
            v-for="(item, index) in chainList"
            :key="index"
            class="tag-title"
            :style="{
              'background-color': selectedTagIndex == index ? '#fff' : '#f5f5f5', color: selectedTagIndex == index ? '#417FFF' : '#72787d', 'font-weight': selectedTagIndex == index ? '600' : '500',
            }"
            @click="selectTag(index)"
          >
            <div v-if="item.chainName !== null">
              {{ item.chainName.replace("产业金脑·", "") }}
            </div>
          </div>
        </div>
        <div class="tag-it">
          <div
            v-for="(item, inde) in chainList"
            :key="inde"
          >
            <div
              v-if="selectedTagIndex == inde"
              class="nodeNamesTag"
            >
              <div
                v-for="(it, ind) in item.chainNodeList"
                :key="ind"
                :class="selectedTagIndex2 == ind ? 'tag-xz' : 'tag-wxz'"
                @click="pitchsecond(ind)"
              >
                {{ it.nodeName }}
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="chainList[selectedTagIndex]?.chainNodeList[selectedTagIndex2]?.productNames"
          class="nameItemBox"
        >
          <div
            v-for="(it, ind) in chainList[selectedTagIndex]?.chainNodeList[selectedTagIndex2].productNames"
            :key="ind"
            class="nameItem"
          >
            {{ it }}
          </div>
        </div>
      </div>
    </div>

    <div class="title">
      企业简介
    </div>
    <div class="introduction">
      {{ enterprise.introduction }}
    </div>
    <div
      v-if="enterprise.associationList?.length>0"
      class="title"
    >
      商会/协会/学会/校友会
    </div>
    <div class="chamberBox">
      <div
        v-for="(item, index) in enterprise.associationList"
        :key="index"
        class="associationListItem"
      >
        <div class="titleBox">
          <img
            src="https://static.idicc.cn/cdn/pangu/yonghu.png"
            class="yonghuIocn"
            alt=""
          >
          {{ item.relateName }}
          <div
            v-if="item?.positions?.length != 0 && item?.positions != null"
            class="longString"
          />
          <div
            v-for="(it, ind) in item?.positions"
            :key="ind"
            class="chamberTag"
          >
            {{ it }}
          </div>
        </div>
        <div
          v-if="item?.ancestorHome"
          class="Singleline"
        >
          <div class="chamberKey">
            籍贯：
          </div>
          <div class="chamberValue">
            {{ item?.ancestorHome ? item?.ancestorHome : '-' }}
          </div>
        </div>
        <div
          v-if="item?.schools?.length != 0 && item?.schools != null "
          class="Singleline"
        >
          <div class="chamberKey">
            毕业院校：
          </div>
          <div class="chamberValue">
            {{ item?.schools?.length != 0 && item?.schools != null ? item?.schools?.join('、') : '-' }}
          </div>
        </div>
        <div
          v-if="item?.commerceNames?.length != 0 && item?.commerceNames != null "
          class="Singleline"
        >
          <div class="chamberKey">
            所属商会：
          </div>
          <div class="chamberValue">
            {{ item?.commerceNames?.length != 0 && item?.commerceNames != null ? item?.commerceNames?.join('、') : '-' }}
          </div>
        </div>
        <div
          v-if="item?.alumniNames?.length != 0 && item?.alumniNames != null "
          class="Singleline"
        >
          <div class="chamberKey">
            所属校友会：
          </div>
          <div class="chamberValue">
            {{ item?.alumniNames?.length != 0 && item?.alumniNames != null ? item?.alumniNames?.join('、') : '-' }}
          </div>
        </div>
      </div>
    </div> 
  </div>
</template>

<script>
export default {
  name: "Clearly",
  props: {
    enterprise: {
      type: Object,
      default: () => { },
    },
    chainList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      selectedTagIndex: 0,
      selectedTagIndex2: 0,
    };
  },
  created() {
    // console.log(this.chainList, 'chainList');

  },
  methods: {
    // 切换产业画像
    selectTag(index) {
      this.selectedTagIndex = index;
      this.selectedTagIndex2 = 0;
    },
    pitchsecond(index) {
      this.selectedTagIndex2 = index;
    },
  },
};
</script>

<style lang="scss" scoped>
.tag-container{
  display: flex;
}
.nodeNamesTag {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;

  .tag-wxz {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3px 17.5px;
    background-color: #EFF4FF;
    font-size: 14px;
    font-weight: normal;
    line-height: 18px;
    border-radius: 2px;
    color: #3370FF;
    cursor: pointer;
    margin-right: 12px;
    margin-bottom: 12px;
  }

  .tag-xz {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3px 17.5px;
    background-color: #3370FF;
    font-size: 14px;
    font-weight: normal;
    border-radius: 2px;
    line-height: 18px;
    color: #FFFFFF;
    cursor: pointer;
    margin-right: 12px;
    margin-bottom: 12px;
  }
}

.tag-title {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 9px 12px;
  background: #FFFFFF;
  font-size: 14px;
  font-weight: 500;
  color: #3370FF;
  width: 328px;
  border: 1px solid #F4F4F5;
  margin-bottom: 20px;
  cursor: pointer;
}

.nameItemBox {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;

  .nameItem {
    width: auto;
    border-radius: 11px;
    background: #F5F7FA;
    padding: 3px 12px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: normal;
    color: #86909C;
    margin-bottom: 10px;
    margin-right: 10px;
  }
}



.chamberBox {
  display: flex;
  flex-wrap: wrap;

  .associationListItem {
    width: calc(25% - 20px);
    max-height: 140px;
    border-radius: 10px;
    background: #F8FAFF;
    margin-right: 20px;
    margin-top: 16px;
    padding: 12px;
    box-sizing: border-box;

    .Singleline {
      display: flex;
      font-weight: 400;
      font-size: 14px;
      color: #7E8791;
      line-height: 22px;

      .chamberKey {
        width: auto;
        white-space: nowrap;
      }

      .chamberValue {
        width: auto;
      }
    }

    .titleBox {
      font-size: 14px;
      font-weight: normal;
      color: #1D2129;
      display: flex;
      align-items: center;
      .yonghuIocn{
        width: 16px;
        height: 16px;
        margin-right: 2px;
      }

      .longString {
        width: 2px;
        height: 16px;
        background-color: #CED4DB;
        margin: 5px;
      }

      .chamberTag {
        width: auto;
        height: 18px;
        border-radius: 2px;
        padding: 0px 6px;
        background: #FFF2E6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        font-weight: 500;
        color: #FF7D00;
      }
    }
  }
}

.title {
  font-size: 16px;
  margin-right: 40px;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #4c586c;
  margin-bottom: 16px;
}

.introduction {
  font-size: 14px;
  line-height: 20px;
  font-weight: normal;
  color: #4E5969;
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #E8E8E8;
}

.interdiv {}
</style>