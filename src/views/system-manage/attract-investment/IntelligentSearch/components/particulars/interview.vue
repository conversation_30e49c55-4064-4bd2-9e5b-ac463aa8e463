<template>
  <!-- 走访记录 -->
  <div>
    <div class="interview">
      <span class="title">走访记录</span>
      <div class="total">
        当前共<span class="tot"> {{ total }} </span>条走访记录
      </div>
    </div>
    <div class="record">
      <div
        v-for="(item, index) in recordList"
        :key="index"
        class="single"
      >
        <div class="head">
          <span class="time">{{ item.followUpDate.substring(0, 10) }}</span>
          <i :class="item.havaInvestmentIntention ? 'green' : 'red'" />
          <span class="letter">{{
            item.havaInvestmentIntention ? "有投资意向" : "无投资意向"
          }}</span>
        </div>
        <div class="box">
          <div class="one">
            <i class="yuan" />
            <div class="describe">
              走访人
            </div>
            <span class="jg"> {{ item.fillInPerson }}</span>
          </div>
          <div class="ones">
            <i class="yuan" />
            <div class="describe">
              走访概述
            </div>
            <span class="jg"> {{ item.overview }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="ye">
      <el-pagination
        :current-page.sync="pageNum"
        :page-size.sync="pageSize"
        :total="+total"
        layout="prev, pager, next"
        @size-change="getfollowUpRecord"
        @current-change="getfollowUpRecord"
      />
    </div>
  </div>
</template>

<script>
import { followUpRecordAPI } from "../../../apiUrl";
export default {
  name: "InterviewA",
  props: {
    clueId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      total: 0,
      pageNum: 1,
      pageSize: 5,
      recordList: [],
    };
  },
  created() {
    this.getfollowUpRecord();
  },
  methods: {
    async getfollowUpRecord() {
      const res = await followUpRecordAPI({
        clueId: this.clueId,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      });
      this.recordList = res.result.records;
      this.total = res.result.total;
    },
    updata() {
      this.pageNum = 1;
      this.getfollowUpRecord();
    },
  },
};
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-pagination {
      display: flex;
      justify-content: center;
      -webkit-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.1) !important;
    }
  }
.green {
  display: flex;
  background: #10aa38;
  margin-left: 25px;
  content: "";
  position: relative;
  width: 6px;
  right: 5px;
  top: 5px;
  height: 6px;
  border-radius: 50%;
}

.red {
  display: flex;
  background: #ff2034;
  content: "";
  margin-left: 25px;
  position: relative;
  width: 6px;
  right: 5px;
  top: 5px;
  height: 6px;
  border-radius: 50%;
}

.yuan {
  display: flex;
  background: #c9cdd4;
  content: "";
  position: relative;
  width: 6px;
  right: 3px;
  top: 6px;
  height: 6px;
  border-radius: 50%;
}

.interview {
  .title {
    font-size: 16px;
    margin-left: 16px;
    margin-right: 40px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #4c586c;
  }

  .total {
    font-size: 14px;
    margin-left: 16px;
    margin-top: 16px;
    font-family: Source Han Sans CN-Normal, Source Han Sans CN;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);

    .tot {
      color: #3370FF;
    }
  }
}
.record {
  margin-left: 16px;
  margin-top: 16px;

  .head {
    display: flex;
    height: 19px;
    .time {
      margin-left: 25px;
      font-size: 13px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #4e5969;
    }

    .letter {
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #1d2129;
    }
  }

  .box {
    margin-left: 25px;
    margin-top: 6px;
    margin-bottom: 25px;
    height: 110px;
    background: #f7f8fa;
    padding: 20px 18px;

    .one {
      display: flex;
      margin-bottom: 16px;
    }
    .ones {
      display: flex;
    }
    .describe {
      font-size: 14px;
      width: 56px;
      margin-right: 10px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #1d2129;
    }

    .jg {
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      max-width: 92%;
      color: #4e5969;
    }
  }
}
.head::after {
  background-color: #3e80ff;
  content: "";
  position: absolute;
  //left: 65px;
  border: 4px solid #bedaff;
  width: 16px;
  height: 16px;
  z-index: 6;
  border-radius: 50%;
}

.single:not(:last-child)::before {
  background-color: #f0f0f0;
  content: "";
  position: absolute;
  left: 46px;
  z-index: 5;
  width: 2px;
  height: 160px;
}
</style>