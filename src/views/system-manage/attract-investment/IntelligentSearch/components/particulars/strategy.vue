<template>
  <!-- 招商策略 -->
  <div>
    <div>
      <el-form :inline="true">
        <el-form-item label="招商策略">
          <el-select
            v-model="form.modelType"
            placeholder="请选择"
            @change="modeTypeChange"
          >
            <el-option
              v-for="(item, index) in modeltypeList"
              :key="index"
              :label="item.modelName"
              :value="item.modelType"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产业链">
          <el-select
            v-model="form.cyl"
            placeholder="请选择"
            @change="getByEnterprise"
          >
            <el-option
              v-for="(item, index) in cylList"
              :key="index"
              :label="item.formerName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="EnterpriseDetail.modelType == '1'">
      <div
        v-if="EnterpriseDetail?.recommend?.relationText"
        class="comprehensive"
      >
        <div class="analysess">
          <div class="p">
            亲缘信息
          </div>
          <div class="gement">
            <span
              class="value"
              v-html="EnterpriseDetail?.recommend?.relationText"
            />
          </div>
        </div>
      </div>
      <div
        v-if="EnterpriseDetail?.recommend?.recommendReason"
        class="comprehensive"
      >
        <div class="analysess">
          <div class="p">
            AIR分析观点
          </div>
          <div class="gement">
            <span
              class="value"
              v-html="EnterpriseDetail?.recommend?.recommendReason"
            />
          </div>
        </div>
      </div>
      <div class="comprehensive">
        <!-- <div class="analysess">
          <div class="p">
            招商策略方式
          </div>
          <div class="gement">
            <span class="value">{{ EnterpriseDetail?.recommend?.recommendReason || '' }}</span>
          </div>
        </div> -->
      </div>
    </div>
    <div v-if="EnterpriseDetail.modelType == '3'">
      <div
        v-if="EnterpriseDetail?.recommend?.recommendReason"
        class="comprehensive"
      >
        <div class="analysess">
          <div class="p">
            推荐理由
          </div>
          <div class="gement">
            <span
              class="value"
              v-html="EnterpriseDetail?.recommend?.recommendReason"
            />
          </div>
        </div>
      </div>
      <div
        v-if="EnterpriseDetail?.recommend?.modelReason"
        class="comprehensive"
      >
        <div class="analysess">
          <div class="p">
            AIR分析观点
          </div>
          <div class="gement">
            <span
              class="value"
              v-html="EnterpriseDetail?.recommend?.modelReason"
            />
          </div>
        </div>
      </div>
      <!-- <div
        v-if="EnterpriseDetail?.recommend?.enterpriseContact"
        class="comprehensive"
      >
        <div class="analysess">
          <div class="p">
            招商策略方式
          </div>
          <div class="gement">
            <span
              class="value"
              v-html="EnterpriseDetail?.recommend?.enterpriseContact"
            />
          </div>
        </div>
      </div>
      </div>   -->
    </div>
    <div v-if="EnterpriseDetail.modelType == '5'">
      <div
        v-if="EnterpriseDetail?.recommend?.modelReason"
        class="comprehensive"
      >
        <div class="analysess">
          <div class="p">
            哒达招商扫描
          </div>
          <div class="gement">
            <span
              class="value"
              v-html="EnterpriseDetail?.recommend?.modelReason"
            />
          </div>
        </div>
      </div>
      <div
        v-if="EnterpriseDetail?.recommend?.recommendReason"
        class="comprehensive"
      >
        <div class="analysess">
          <div class="p">
            AIR分析观点
          </div>
          <div class="gement">
            <span
              class="value"
              v-html="EnterpriseDetail?.recommend?.recommendReason"
            />
          </div>
        </div>
      </div>
      <!-- <div
        v-if="EnterpriseDetail?.recommend?.enterpriseContact"
        class="comprehensive"
      >
        <div class="analysess">
          <div class="p">
            招商触达方式
          </div>
          <div class="gement">
            <span
              class="value"
              v-html="EnterpriseDetail?.recommend?.enterpriseContact"
            />
          </div>
        </div>
      </div> -->
    </div>
    <div v-if="EnterpriseDetail.modelType == '7'">
      <div
        v-if="
          EnterpriseDetail?.recommend?.talentName ||
            EnterpriseDetail?.recommend?.enterpriseAddress ||
            EnterpriseDetail?.recommend?.researchField
        "
        class="comprehensive"
      >
        <div class="analysess">
          <div class="p">
            人才关联
          </div>
          <div class="gement">
            <p
              v-if="EnterpriseDetail?.recommend?.talentName"
              class="value"
            >
              人才姓名：{{ EnterpriseDetail?.recommend?.talentName }}
            </p>
            <p
              v-if="EnterpriseDetail?.recommend?.enterpriseAddress"
              class="value"
            >
              机构所在地：{{ EnterpriseDetail?.recommend?.enterpriseAddress }}
            </p>
            <p
              v-if="EnterpriseDetail?.recommend?.researchField"
              class="value"
            >
              研究方向：{{ EnterpriseDetail?.recommend?.researchField }}
            </p>
          </div>
        </div>
      </div>
      <div
        v-if="EnterpriseDetail?.recommend?.recommendReason"
        class="comprehensive"
      >
        <div class="analysess">
          <div class="p">
            AIR分析观点
          </div>
          <div class="gement">
            <span
              class="value"
              v-html="EnterpriseDetail?.recommend?.recommendReason"
            />
          </div>
        </div>
      </div>
      <div
        v-if="EnterpriseDetail?.recommend?.enterpriseContact"
        class="comprehensive"
      >
        <div class="analysess">
          <div class="p">
            触达方式
          </div>
          <div class="gement">
            <span
              class="value"
              v-html="EnterpriseDetail?.recommend?.enterpriseContact"
            />
          </div>
        </div>
      </div>
    </div>
    <div v-if="EnterpriseDetail.modelType == '8'">
      <div
        v-if="
          EnterpriseDetail?.recommend?.projectName ||
            EnterpriseDetail?.recommend?.projectDetail
        "
        class="comprehensive"
      >
        <div class="analysess">
          <div class="p">
            经济高价值专利培育方式
          </div>
          <div class="gement">
            <p
              v-if="EnterpriseDetail?.recommend?.projectName"
              class="value"
            >
              项目名称：{{ EnterpriseDetail?.recommend?.projectName }}
            </p>
            <p
              v-if="EnterpriseDetail?.recommend?.projectDetail"
              class="value"
            >
              项目介绍：{{ EnterpriseDetail?.recommend?.projectDetail }}
            </p>
          </div>
        </div>
      </div>
      <div
        v-if="EnterpriseDetail?.recommend?.recommendReason"
        class="comprehensive"
      >
        <div class="analysess">
          <div class="p">
            AIR分析观点
          </div>
          <div class="gement">
            <span
              class="value"
              v-html="EnterpriseDetail?.recommend?.recommendReason"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
<script>
import { getByEnterpriseIdAPI, bestNewListAPI } from "../../../apiUrl";
import { getByEnterpriseIdAPI_V2 } from "../../../apiUrl_v2";
import * as echarts from "echarts";
export default {
  name: "StrategyA",
  props: {
    enterpriseID: {
      type: String,
      default: null,
    },
    enterpriseData: {
      type: Array,
      default: () => [],
    },
    recommendRegionCode: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      strategy: {},
      form: {
        cyl: "",
        modelType: "",
      },
      opportunityList: [],
      data360: "",
      showdata360: "", //展示的data360
      showunfold: false, //是否展示展开收起
      isshow: false,
      modeltypeList: [],
      cylList: [],
      EnterpriseDetail: {},
    };
  },
  created() {
    // 确保 enterpriseData 有数据再进行操作
    this.modeltypeList = this.enterpriseData || [];

    if (this.modeltypeList.length > 0) {
      this.form.modelType = this.modeltypeList[0].modelType;

      this.cylList = this.modeltypeList[0].chains || [];
      if (this.cylList.length > 0) {
        this.form.cyl = this.cylList[0].id;
      }
    }

    // 只有当有必要的数据时才调用API
    if (this.form.modelType && this.form.cyl) {
      this.getByEnterprise();
    }
    // console.log(this.form, 'from')
    //this.getstrategy();
    // this.getinfList();
  },
  mounted() {
    this.getstrategy();
  },
  methods: {
    getByEnterprise() {
      let params = {
        enterpriseId: this.enterpriseID,
        modelType: this.form.modelType,
        chainId: this.form.cyl,
        regionCode: this.recommendRegionCode,
      };
      this.EnterpriseDetail = {};
      getByEnterpriseIdAPI_V2(params).then((res) => {
        this.EnterpriseDetail = res.result;
        for (const key in this.EnterpriseDetail.recommend) {
          if (typeof this.EnterpriseDetail.recommend[key] === "string") {
            this.EnterpriseDetail.recommend[key] =
              this.EnterpriseDetail.recommend[key].replace(/\n/g, "<br>");
          }
        }
      });
    },
    modeTypeChange(val) {
      this.form.cyl = "";
      this.cylList = [];
      let filter = this.modeltypeList.filter((e) => {
        return e.modelType == val;
      });
      if (filter.length > 0) {
        this.cylList = filter[0].chains || [];
      }
    },
    async getinfList() {
      const res = await bestNewListAPI({
        enterpriseId: this.enterpriseID,
      });
      this.opportunityList = res.result;
    },
    // 收起展开
    more() {
      if (this.isshow) {
        var index = this.data360.indexOf("<br>");
        this.showdata360 = this.data360.substring(0, index);
        this.isshow = !this.isshow;
      } else {
        this.showdata360 = this.data360;
        this.isshow = !this.isshow;
      }
    },
    async getstrategy() {
      const res = await getByEnterpriseIdAPI({
        enterpriseId: this.enterpriseID,
      });
      if (res.result == null) {
        return;
      }
      this.strategy = res.result;
      this.data360 = this.strategy.data360;
      if (this.data360 != null) {
        // 获取待截取的字符串
        var index = this.data360.indexOf("<br>");
        if (index === -1) {
          this.showdata360 = this.data360;
          this.showunfold = false;
        } else {
          this.showdata360 = this.data360.substring(0, index);
          this.showunfold = true;
        }
      }
      this.strategy.recommendedLevel;
      this.strategy.recommendedLevel = Number(this.strategy.recommendedLevel);

      // 在下一个渲染周期后再初始化图表，确保DOM元素已经渲染
      this.$nextTick(() => {
        this.analysisFn();
        //this.feasibilityFn();
      });
    },
    analysisFn() {
      var chartDom = document.getElementById("analysis");
      if (!chartDom) {
        // console.warn("Analysis chart DOM element not found");
        return;
      }
      var myChart = echarts.init(chartDom);
      var option;
      let value = [];
      value.push(this.strategy.taxAbilityScore);
      value.push(this.strategy.innovationAbilityScore);
      value.push(this.strategy.aptitudeScore);
      value.push(this.strategy.employmentContributionScore);
      value.push(this.strategy.businessGrowthScore);
      let maxNum = Math.max(...value);
      let indicator = [
        { name: "纳税能力", max: maxNum },
        { name: "创新能力", max: maxNum },
        { name: "企业资质", max: maxNum },
        { name: "就业贡献", max: maxNum },
        { name: "业务增长", max: maxNum },
      ];
      option = {
        radar: {
          indicator,
          //axisLabel: { show: true },
        },
        tooltip: {
          trigger: "item",
        },
        series: [
          {
            type: "radar",
            itemStyle: {
              //此属性的颜色和下面areaStyle属性的颜色都设置成相同色即可实现
              color: "#185eff",
              borderColor: "#185eff",
            },
            areaStyle: {
              color: "#bdcff7",
            },
            data: [
              {
                value,
                name: "企业综合价值分析",
              },
            ],
          },
        ],
      };

      option && myChart.setOption(option);
    },
    feasibilityFn() {
      var chartDom = document.getElementById("feasibility");
      if (!chartDom) {
        // console.warn("Feasibility chart DOM element not found");
        return;
      }
      var myChart = echarts.init(chartDom);
      var option;
      let indicator = [
        { name: "资金实力" },
        { name: "业务增长" },
        { name: "投资偏好" },
        { name: "投资活跃度" },
        { name: "投资动机" },
      ];
      let value = [];
      value.push(this.strategy.financialStrengthScore);
      value.push(this.strategy.businessGrowthScore);
      value.push(this.strategy.investmentPreferenceScore);
      value.push(this.strategy.investmentActivenessScore);
      value.push(this.strategy.investmentMotiveScore);
      option = {
        radar: {
          indicator,
          axisLabel: { show: true },
        },
        tooltip: {
          trigger: "item",
        },

        series: [
          {
            type: "radar",
            itemStyle: {
              //此属性的颜色和下面areaStyle属性的颜色都设置成相同色即可实现
              color: "#185eff",
              borderColor: "#185eff",
            },
            areaStyle: {
              color: "#bdcff7",
            },
            data: [
              {
                value,
                name: "招商可行性分析",
              },
            ],
          },
        ],
      };

      option && myChart.setOption(option);
    },
  },
};
</script>
<style lang="scss" scoped>
.recommend {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
  width: 100%;
  height: 95px;

  span {
    font-size: 16px;
    margin-left: 16px;
    margin-right: 40px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #4c586c;
  }
}

.comprehensive {
  display: flex;
  width: 100%;
  margin-left: 16px;
  border-bottom: 1px solid #e8e8e8;

  .analyse {
    width: 60%;

    .p {
      margin-top: 32px;
      margin-bottom: 24px;
      font-size: 16px;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #4c586c;
    }
    .gement {
      margin-bottom: 12px;
      .key {
        font-size: 14px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.45);
      }
      .value {
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        line-height: 20px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
  .analysess {
    width: 100%;

    .p {
      margin-top: 32px;
      margin-bottom: 24px;
      font-size: 16px;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #4c586c;
    }
    .gement {
      margin-bottom: 12px;
      .key {
        font-size: 14px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.45);
      }
      .value {
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        line-height: 20px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
  .analysis {
    margin-top: 44px;
    margin-bottom: 44px;
    width: 400px;
    height: 220px;
  }
}
.opportunity {
  width: 100%;
  margin-left: 16px;
  border-bottom: 1px solid #e8e8e8;
  .p {
    margin-top: 32px;
    margin-bottom: 24px;
    font-size: 16px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #4c586c;
  }
  .opportunityList {
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-bottom: 16px;
    width: 60%;
    //justify-content: space-between;
    .toptitle {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .text {
        display: flex;
        width: 85%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
      }

      .time {
        font-size: 14px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
        line-height: 22px;
      }
      .text:hover {
        color: #1c91ff;
      }
    }
    .tagList {
      display: flex;
      width: 100%;
      margin-top: 5px;
      .tag {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        margin-top: 2px;
        padding: 0 6px;
        color: #3370ff;
        background: #e4edf8;
        margin-right: 8px;
        height: 18px;
        border-radius: 2px 2px 2px 2px;
      }
    }
  }
}
</style>