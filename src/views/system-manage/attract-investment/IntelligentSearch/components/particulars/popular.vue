<template>
  <!-- 企业舆情 -->
  <div>
    <div class="feelings">
      <span>舆情概述          
        <el-tooltip
          effect="dark"
          content="统计近一年舆情数据"
          placement="top"
        >
          <i class="el-icon-info" />
        </el-tooltip></span>
      <div class="box">
        <div class="overview">
          <div class="information">
            {{ summarize.opinionCount || 0 }}
          </div>
          <div class="explain">
            舆情总量
          </div>
        </div>
        <div class="overview">
          <div class="information">
            {{ summarize.monthIncrement || 0 }}
          </div>
          <div class="explain">
            近一月新增
          </div>
        </div>
        <div class="overview">
          <div class="information">
            {{ summarize.yearIncrementRate|| 0 }}%
          </div>
          <div class="explain">
            同比增长
          </div>
        </div>
        <div class="overview">
          <div class="information">
            {{ summarize.monthIncrementRate || 0 }}%
          </div>
          <div class="explain">
            环比增长
          </div>
        </div>
      </div>
      <div class="diagram">
        <div class="tendencys">
          <span>·舆情发生趋势        
            <el-tooltip
              effect="dark"
              content="统计近两周舆情数据"
              placement="top"
            >

              <i class="el-icon-info" />
            </el-tooltip></span>
          <div
            id="tendency"
            class="tendency"
          />
        </div>
        <div
          v-if="showpublic"
          class="themes"
        >
          <span>·舆情主题分布            
            <el-tooltip
              effect="dark"
              content="统计近一月舆情数据"
              placement="top"
            >
              <i class="el-icon-info" />
            </el-tooltip></span>
          <div
            id="theme"
            class="theme"
          />
        </div>
      </div>
    </div>
    <div
      v-if="showmessage"
      class="message"
    >
      <span>舆情资讯</span>
      <el-form
        :inline="true"
        class="demo-form-inline"
      >
        <el-form-item
          label="主题分类:"
          class="appendToBodyFalse"
        >
          <!-- <div   v-for="item in themeList"
              :key="item.id">
{{ item.name }}
          </div> -->
          <el-select
            v-model="subjec"
            :popper-append-to-body="false"
            multiple
            collapse-tags
            clearable
            placeholder="请选择"
            @change="changeList"
          >
            <el-option
              v-for="item in themeList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <!--  <el-form-item label="新闻来源:">
          <el-select
            v-model="source"
            multiple
            filterable
            collapse-tags
            remote
            reserve-keyword
            placeholder="请输入关键词"
            :remote-method="remoteMethod"
            :loading="loading"
            @change="changeList"
          >
            <el-option
              v-for="item in sourceList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item
          label="发布日期:"
          class="appendToBodyFalse"
        >
          <el-date-picker
            v-model="daterange"
            :append-to-body="false"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="——"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            @change="changeList"
          />
        </el-form-item>
      </el-form>
      <div
        v-if="reportList.length>0"
        v-loading="listLoading"
      >
        <div
          v-for="(item, index) in reportList"
          :key="index"
          class="report"
        >
          <div class="head">
            <a
              :href="item.url"
              target="_blank"
              class="name"
            >{{ item.title }}</a>
            <div class="right">
              <span>{{ item.source }}</span>
              <span class="i" />
              <span>{{ item.publishDate }}</span>
            </div>
          </div>
          <div
            v-if="item.themes"
            class="tags"
          >
            <div
              v-for="(it, ind) in item.themes"
              :key="ind"
              class="tag"
            >
              {{ it }}
            </div>
          </div>
          <div class="firms">
            <div class="correlation">
              相关企业：
            </div>
            <div class="enterpriseInfos">
              <div
                v-for="(it, ind) in item.enterpriseInfos"
                v-show="ind<5"
                :key="ind"
                class="firm"
                @click="goqyxq(it)"
              >
                {{ it.enterpriseName }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else
        class="empty"
      >
        暂无数据
      </div>
      <el-pagination
        :current-page.sync="pageNum"
        :page-sizes="[5, 10, 20, 50]"
        :page-size.sync="pageSize"
        :total="+total"
        layout="total,sizes, prev, pager, next, jumper"
        @size-change="getinformationList"
        @current-change="getinformationList"
      />
    </div>
  </div>
</template>

<script>
import { listByTypeAPI,overviewAPI ,informationlistAPI,likeKeyWordListAPI} from "@/views/system-manage/attract-investment/apiUrl";
import * as echarts from "echarts";
export default {
  name: "PopularA",
  props: {
    enterpriseID: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      pageNum:1,
      pageSize:5,
      listLoading:false,
      total:0,
      loading: false,
      summarize:{},
      subjec: [], //主题分类
      themeList: [],
      source:[],//主题来源
      sourceList:[],
      showpublic:true,
      daterange: "", //日期范围
      pickerOptions: {
        disabledDate(time) {
            return time.getTime() > Date.now();
          },
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      reportList: [
      ],
      showmessage:true,
      thefirsttime:true,
      graphic: [
             {
               type: 'rect',
               left: 'center',
               top: 'middle',
               shape: {
                 width: 160,
                 height: 70,
                 radius: 20 
               },
               style: {
                 fill: '#cdd1d7',
                 stroke: '#ccc',
               },
               z:10
             },
             {
               type: 'text',
               left: 'center',
               top: 'middle',
               style: {
                 text: '暂无数据',
                 textAlign: 'center',
                 textVerticalAlign: 'middle',
                 fill: '#fff',
                 fontSize: 18
               },
               z:10
             }
        ]
    };
  },
  created() {
    this.getthemeList();
    this.getinformationList()
  },
  mounted() {
    this.getoverview()//舆情概述
    //this.themeFn();
    //this.tendencyFn();
  },
  methods: {
    goqyxq(it){
      //console.log(it);
       this.$emit('updatafirm',it.enterpriseId)
    },
   async remoteMethod(keyword){
    try {
      this.loading=true
     const res =  await  likeKeyWordListAPI({
        type:3,
        keyword
      })
      this.sourceList=res.result
    } catch (error) {
      console.log(error);
    } finally{
      this.loading=false
    }
    },
    changeList(){
        this.pageNum=1
        this.getinformationList()
    },
   async getinformationList(){
    try {
      let startDate=''
      let endDate=''
      if(this.daterange){
        startDate=this.daterange[0]
        endDate=this.daterange[1]
      }
      this.listLoading=true
      const res=   await informationlistAPI({
      pageNum:this.pageNum,
      pageSize:this.pageSize,
      themes:this.subjec,
      sources:this.source,
      enterpriseId:this.enterpriseID,
      startDate,
      endDate,
     })
     this.reportList=res.result.records
     this.total=res.result.totalNum
     if(this.total==0 && this.thefirsttime){
      this.showmessage=false
     }
     this.thefirsttime=false
    } catch (error) {
      console.log(error);
    } finally{
      this.listLoading=false
    }   
    },
   async getoverview(){
       const res =await overviewAPI({
        enterpriseId:this.enterpriseID
       })
       this.summarize=res.result
       this.themeFn()
       this.tendencyFn()
    },
    // 获取招商资讯新闻主题
    async getthemeList() {
      const res = await listByTypeAPI({
        keywordType: 1,
      });

      this.themeList = res.result;
    },
    tendencyFn() {
      let  key = this.summarize.dayTrendies.map((item)=>{
             return item.date
      })
      let  value = this.summarize.dayTrendies.map((item)=>{
             return item.count
      })
      let chartDom = document.getElementById("tendency");
      let myChart = echarts.init(chartDom);
      let option;

      option = {
        grid: {
          left: "5%",
          right: "10%",
          bottom: "5%",
          top: "15%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow", // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
            axis: "auto", // 指示器的坐标轴。
            snap: true, // 坐标轴指示器是否自动吸附到点上
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: key,
          // 刻度
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          /*  name: "家",
          nameTextStyle: {
            padding: [0, 28, 0, 0],
          }, */
        },
        series: [
          {
            data: value,
            type: "line",
            smooth: true,
            showSymbol: false,
            lineStyle: {
              color: "#165DFF",
            },
            itemStyle: {
              color: "#165DFF",
              normal: {
                barBorderRadius: 4,

                color: "#E5EDFF",
              },
            },
            areaStyle: {},
          },
        ],
      };

      option && myChart.setOption(option);
    },
    themeFn() {
      let  key = this.summarize.themes.map((item)=>{
             return item.theme
      })
      let  value = this.summarize.themes.map((item)=>{
             return item.opinionCount
      })
      //console.log(key, value,'keyvalue');
      let graphic =[]
      if(key.length==0 && value.length==0){
        graphic=this.graphic
        //console.log('无数据');
        //this.showpublic=false
      }
      var chartDom = document.getElementById("theme");
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        graphic,
        grid: {
          left: "5%",
          right: "10%",
          bottom: "5%",
          top: "15%",
          containLabel: true,
        },
        tooltip: {
          show: true,
          formatter: "{b}<br/>{c}条",
          textStyle: {
            fontWeight: "bold",
            fontSize: 12,
          },
        },
        yAxis: [
          {
            type: "category",
            inverse: true,
            //文字
            axisLabel: {
              show: true,
              fontSize: 12,
              color: "#86909C",
            },
            // 边框
            splitLine: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#e0e3e6",
              },
            },
            // 刻度
            axisTick: {
              show: false,
            },
            data: key
          },
        ],
        xAxis: [
          {
            type: "value",
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false, // 隐藏 x 轴横线
            },
            // 刻度线 虚线
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed",
              },
            },
            // name: "亿元",
            nameLocation: "end",
            nameTextStyle: {
              padding: [20, 0, 0, 25],
            },
            axisLabel: {
              inside: false,
              textStyle: {
                fontSize: 12,
              },
              interval: 0,
              formatter: "{value}",
            },
          },
        ],
        series: [
          {
            name: "assist",
            type: "bar",
            stack: "1",
            label: {
              show: true,
              position: "top",
            },
            itemStyle: {
              normal: {
                barBorderColor: "rgba(0,0,0,0)",
                color: "rgba(0,0,0,0)",
              },
              emphasis: {
                barBorderColor: "rgba(0,0,0,0)",
                color: "rgba(0,0,0,0)",
              },
            },
            tooltip: {
              trigger: "none",
            },
            data: [],
          },
          {
            type: "bar",
            stack: "1",
            barWidth: 12,
            barBorderRadius: 30,
            itemStyle: {
              normal: {
                barBorderRadius: 20,
                color: function () {
                  return {
                    type: "linear",
                    x: 1,
                    y: 0,
                    x2: 0,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: "#1e62ff", // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: "#e5ecff", // 100% 处的颜色
                      },
                    ],
                  };
                },
              },
            },
            data:value,
          },
        ],
      };
      option && myChart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-form--inline .el-form-item {
    margin-right: 50px;
  }
}
::v-deep {
  .el-pagination {
    position: relative;
    padding-right: 60px;
    right: 0px;
    text-align: right;
    height: 50px;
    vertical-align: top;
    border: 10px;
  }
  .el-icon-arrow-left {
    margin-top: 0 !important;
  }
}
.feelings {
  border-bottom: 1px solid #e8e8e8;
  span {
    font-size: 16px;
    margin-left: 16px;
    margin-right: 40px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #4c586c;
  }
  .box {
    margin-left: 16px;
    margin-top: 30px;
    display: flex;
    .overview {
      width: 25%;
      height: 90px;
      margin-right: 24px;
      background: #fbfcfe;
      box-shadow: 0px 2px 1px 0px rgba(64, 72, 82, 0.05);
      opacity: 1;
      border: 1px solid #dde4f0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .information {
        font-size: 24px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #3370FF;
        margin-bottom: 7px;
      }
      .explain {
        font-size: 14px;
        margin-top: 7px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }
  .diagram {
    width: 100%;
    margin-top: 35px;
    display: flex;
    .tendencys {
      width: 50%;
      margin-bottom: 27px;
      span {
        font-size: 14px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: rgba(29, 33, 41, 0.85);
      }
      .tendency {
        height: 300px;
      }
    }
    .themes {
      width: 50%;
      margin-bottom: 27px;
      span {
        font-size: 14px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: rgba(29, 33, 41, 0.85);
      }
      .theme {
        height: 300px;
      }
    }
  }
}
.message {
  min-height: 450px;
  margin-top: 32px;
  span {
    font-size: 16px;
    margin-left: 16px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #4c586c;
  }
  .demo-form-inline {
    margin-left: 16px;
    margin-top: 30px;
    padding:0 !important;
   box-shadow: 0 0 0 !important;  
  background: transparent !important;
   border-radius: 0px !important;
  }
  .report {
    border-bottom: 1px solid #e8e8e8;
  }
  .head {
    display: flex;
    justify-content: space-between;
    width: 95%;
    margin-left: 16px;
    .name {
      font-size: 18px;
      margin-top: 24px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
    .right {
      display: flex;
      margin-top: 24px;
      span {
        font-size: 14px;
        margin-right: 0px;
        margin-left: 0px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
      }
      .i {
        display: flex;
        height: 15px;
        margin-left: 6px;
        margin-right: 6px;
        width: 1px;
        background-color: #d1d1d1;
      }
    }
  }
  .tags {
    display: flex;
    margin-left: 16px;
    margin-top: 13px;
    .tag {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      padding: 0 6px;
      color: #417fff;
      background: #e7eef8;
      margin-right: 8px;
      height: 18px;
      border-radius: 2px 2px 2px 2px;
    }
  }
  .firms {
    display: flex;
    margin-top: 13px;
    margin-bottom: 24px;
    margin-left: 16px;
    .correlation {
      font-size: 14px;
      min-width: 80px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      line-height: 30px;
      color: rgba(0, 0, 0, 0.45);
    }
    .enterpriseInfos{
      display: flex;
      flex-wrap: wrap;
    }
    .firm {
      font-size: 14px;
      font-family: Abel-Regular, Abel;
      font-weight: 400;
      color: #3370FF;
      line-height: 30px;
      margin-right: 5px;
      cursor: pointer;
    }
  }
}
.empty{
  width: 100%;
  height: 300px;
  display: flex ;
  justify-content: center;
  align-items: center;
    color: #818181;
}
</style>