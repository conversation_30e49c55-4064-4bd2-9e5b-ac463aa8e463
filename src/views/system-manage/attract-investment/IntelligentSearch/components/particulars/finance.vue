<template>
  <!-- 财务分析 -->
  <div>
    <div
      class="indicator"
    >
      <span>主要指标</span>
      <div
        v-if="time.length>0"
        style="display: flex"
      >
        <div
          v-loading="timeLoading"
          class="List"
        >
          <div class="head">
            <div class="single">
              日期
            </div>
            <div
              v-for="(item, index) in principal"
              :key="index"
              class="single"
              :style="{ 'padding': index == 3 ? '0 20px' : '0px' }"
            >
              {{ item }}
            </div>
          </div>
          <div class="table">
            <div class="head-time">
              <div
                v-for="(item, index) in time"
                :key="index"
                class="time"
              >
                {{ item }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in main"
                :key="ind"
                class="text-single"
              >
                {{ it.businessIncome }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in main"
                :key="ind"
                class="text-single"
              >
                {{ it.profitToParent }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in main"
                :key="ind"
                class="text-single"
              >
                {{ it.totalAssets }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in main"
                :key="ind"
                class="text-single"
              >
                {{ it.businessActivitiesNetFlow }}
              </div>
            </div>
            <!--             <div class="text">
              <div
                v-for="(it, ind) in main"
                :key="ind"
                class="text-single"
              >
                {{ it.gearingRatio }}
              </div>
            </div> -->
          </div>
        </div>
        <div
          id="recentyears"
          class="recentyears"
        />
      </div>
      <div
        v-else
        class="onList"
      >
        暂无数据
      </div>
    </div>
    <div
      class="liabilities"
    >
      <div class="title">
        <span>资产负债类指标</span>
        <div class="invent">
          <div
            v-for="(item, index) in patenttable"
            :key="index"
            :class="patent == item.id ? 'xzoption' : 'option'"
            @click="cutpatent(item.id)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div
        v-if="liabilitiesTime.length>0"
        style="display: flex"
      >
        <div class="List">
          <div class="head">
            <div class="single">
              日期
            </div>
            <div
              v-for="(item, index) in liabilitiesPrincipal"
              :key="index"
              class="single"
            >
              {{ item }}
            </div>
          </div>
          <div class="table">
            <div class="head-time">
              <div
                v-for="(item, index) in liabilitiesTime"
                :key="index"
                class="time"
              >
                {{ item }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in liabilitiesMain"
                :key="ind"
                class="text-single"
              >
                {{ it.totalAssets }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in liabilitiesMain"
                :key="ind"
                class="text-single"
              >
                {{ it.totalCurrentAssets }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in liabilitiesMain"
                :key="ind"
                class="text-single"
              >
                {{ it.totalDebt }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in liabilitiesMain"
                :key="ind"
                class="text-single"
              >
                {{ it.totalCurrentDebt }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in liabilitiesMain"
                :key="ind"
                class="text-single"
              >
                {{ it.shortLoan }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in liabilitiesMain"
                :key="ind"
                class="text-single"
              >
                {{ it.longLoan }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in liabilitiesMain"
                :key="ind"
                class="text-single"
              >
                {{ it.totalEquityInterest }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in liabilitiesMain"
                :key="ind"
                class="text-single"
              >
                {{ it.paidInCapital }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in liabilitiesMain"
                :key="ind"
                class="text-single"
              >
                {{ it.undistributedProfit }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else
        class="onList"
      >
        暂无数据
      </div>
    </div>
    <div
      class="liabilities"
    >
      <div class="title">
        <span>营收利润类指标</span>
        <div class="invent">
          <div
            v-for="(item, index) in patenttable"
            :key="index"
            :class="profit == item.id ? 'xzoption' : 'option'"
            @click="profitFn(item.id)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div
        v-if="profitTime.length>0"
        style="display: flex"
      >
        <div class="List">
          <div class="head">
            <div class="single">
              日期
            </div>
            <div
              v-for="(item, index) in profitPrincipal"
              :key="index"
              class="single"
            >
              {{ item }}
            </div>
          </div>
          <div class="table">
            <div class="head-time">
              <div
                v-for="(item, index) in profitTime"
                :key="index"
                class="time"
              >
                {{ item }}
              </div>
            </div>
            <!--             <div class="text">
              <div
                v-for="(it, ind) in profitMain"
                :key="ind"
                class="text-single"
              >
                {{ it.totalOperatingIncome }}
              </div>
            </div> -->
            <div class="text">
              <div
                v-for="(it, ind) in profitMain"
                :key="ind"
                class="text-single"
              >
                {{ it.operatingIncome }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in profitMain"
                :key="ind"
                class="text-single"
              >
                {{ it.operatingProfit }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in profitMain"
                :key="ind"
                class="text-single"
              >
                {{ it.totalProfit }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in profitMain"
                :key="ind"
                class="text-single"
              >
                {{ it.netProfit }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else
        class="onList"
      >
        暂无数据
      </div>
    </div>
    <div
      class="liabilities"
    >
      <div class="title">
        <span>现金流量类指标</span>
        <div class="invent">
          <div
            v-for="(item, index) in patenttable"
            :key="index"
            :class="moneyflow == item.id ? 'xzoption' : 'option'"
            @click="moneyflowFn(item.id)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div
        v-if="cashTime.length>0"
        style="display: flex"
      >
        <div class="List">
          <div
            class="head"
            style="width: 200px"
          >
            <div
              class="single"
              style="width: 200px"
            >
              日期
            </div>
            <div
              v-for="(item, index) in cashPrincipal"
              :key="index"
              class="single"
              style="width: 200px"
            >
              {{ item }}
            </div>
          </div>
          <div class="table">
            <div class="head-time">
              <div
                v-for="(item, index) in cashTime"
                :key="index"
                class="time"
              >
                {{ item }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in cashMain"
                :key="ind"
                class="text-single"
              >
                {{ it.businessActivitiesNetFlow }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in cashMain"
                :key="ind"
                class="text-single"
              >
                {{ it.investActivitiesNetFlow }}
              </div>
            </div>
            <div class="text">
              <div
                v-for="(it, ind) in cashMain"
                :key="ind"
                class="text-single"
              >
                {{ it.fundraisingActivitiesNetFlow }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else
        class="onList"
      >
        暂无数据
      </div>
    </div>
  </div>
</template>

<script>
import {
  mainIndexListAPI,
  assetsDebtListAPI,
  incomeProfitListAPI,
  cashFlowListAPI,
} from "../../../apiUrl";
import * as echarts from "echarts";
export default {
  name: "FinanceA",
  props: {
    enterpriseID: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      time: [''], //主要信息时间维度
      main: [], //主要指标列表
      principal: [
        "营业收入",
        "归母净利润",
        "总资产",
        "经营活动产生的现金流量净额",
      ], //主要指标的展示项
      liabilitiesTime: [], //资产负债
      liabilitiesMain: [], //资产负债
      liabilitiesPrincipal: [
        "资产总计",
        "流动资产合计",
        "负债合计",
        "流动负债合计",
        "短期借款",
        "长期借款",
        "股票权益合计",
        "实收资本(或股本)",
        "未分配利润",
      ], //资产负债
      profitTime: [],
      profitMain: [],
      profitPrincipal: [
        //"营业总收入",
        "营业收入",
        "营业利润",
        "利润总额",
        "净利润",
      ],
      cashTime: [],
      cashMain: [],
      cashPrincipal: [
        "经营活动产生的现金流量净额",
        "投资活动产生的现金流量净额",
        "筹资活动产生的现金流量净额",
      ],
      patenttable: [
        {
          name: "按报告期",
          id: 0,
        },
        {
          name: "按年度",
          id: 3,
        },
      ],
      patent: 0, //资产负债类指标
      profit: 0, //营收利润类指标
      moneyflow: 0, //现金流量类指标
      timeLoading:false,
    };
  },
  created() {
    this.getliabilities(); //资产负债
    this.getPrincipal(); //利润指标
    this.getCash(); //现金指标
  },
  mounted() {
    this.recentyearsFn();
  },
  methods: {
    // 现金
    async getCash() {
      let data = {
        enterpriseId: this.enterpriseID,
        reportType: this.moneyflow,
      };
      const res = await cashFlowListAPI(data);
      this.cashMain = res.result;
      this.cashTime = Object.keys(this.cashMain);
    },
    // 利润
    async getPrincipal() {
      let data = {
        enterpriseId: this.enterpriseID,
        reportType: this.profit,
      };
      const res = await incomeProfitListAPI(data);
      this.profitMain = res.result;
      this.profitTime = Object.keys(this.profitMain);
    },
    // 负债类
    async getliabilities() {
      let data = {
        enterpriseId: this.enterpriseID,
        reportType: this.patent,
      };
      const res = await assetsDebtListAPI(data);
      this.liabilitiesMain = res.result;
      this.liabilitiesTime = Object.keys(this.liabilitiesMain);
    },
    cutpatent(i) {
      this.patent = i;
      this.getliabilities(); //资产负债
    },
    profitFn(i) {
      this.profit = i;
      this.getPrincipal(); //利润指标
    },
    moneyflowFn(i) {
      this.moneyflow = i;
      this.getCash(); //现金指标
    },
    //主要
    async recentyearsFn() {
      let chartDom = document.getElementById("recentyears");
      let myChart = echarts.init(chartDom);
      let option;
      try {
        this.timeLoading =true
        const res = await mainIndexListAPI({
        enterpriseId: this.enterpriseID,
      });
      this.main = res.result;
      this.time = Object.keys(this.main);
      let data = Array.from(this.time);
      let textDate = [];
      textDate = Object.values(this.main).map((item) => item.businessIncome);
/*        textDate = Object.values(textDate).map(
        (item) =>  item.replace("亿元", "") && item.replace("万元", "")
      );  */
      textDate = Object.values(textDate).map((item) =>{
        if(item.includes("万元")){
         return item.replace("万元", "")
        }else{
         item = item.replace("亿元", "")
         return Math.floor(Number(item) * 10000);
        }
      }
      );
      textDate = textDate.reverse();
      data = data.reverse();
      option = {
        title: {
          text: `营业收入`,
          textStyle: {
            color: "#1D2129",
            opacity: 0.6,
            fontSize: 14,
          },
          padding: [5, 0, 0, 18],
        },
        grid: {
          left: "5%",
          right: "10%",
          bottom: "0%",
          top: "22%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow", // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
            axis: "auto", // 指示器的坐标轴。
            snap: true, // 坐标轴指示器是否自动吸附到点上
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data,
          /*           axisLabel: {
            interval: 0,
            rotate: -20, //倾斜的程度
          }, */
          // 刻度
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          name: "万元",
          nameTextStyle: {
            padding: [0, 28, 0, 0],
          },
        },
        series: [
          {
            data: textDate,
            type: "line",
            showSymbol: false,
            lineStyle: {
              color: "#165DFF",
            },
            itemStyle: {
              color: "#165DFF",
              normal: {
                barBorderRadius: 4,

                color: "#E5EDFF",
              },
            },
            areaStyle: {},
          },
        ],
      };

      option && myChart.setOption(option);
      } finally{
       this.timeLoading =false
      }
      
    },
  },
};
</script>

<style lang="scss" scoped>
.onList{
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  font-size: 14px;
  height: 270px;
}
.nodata {
  min-width: 88.5vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  .con {
    margin-top: 10%;
    width: 170px;
    height: 300px;
    display: flex;
    align-items: center;
    flex-direction: column;
    img {
      width: 216px;
      height: 177px;
    }
    .p1 {
      margin-top: 38px;
      font-size: 14px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
    .p2 {
      margin-top: 16px;
      font-size: 12px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
/* ::-webkit-scrollbar {
  width: 50px;
  height: 20px;
}
::-webkit-scrollbar-thumb {
  background-color: #324156;
  border-radius: 32px;
}
::-webkit-scrollbar-track {
  display: none;
} */
.indicator {
  span {
    font-size: 16px;
    margin-left: 16px;
    margin-right: 40px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #4c586c;
  }
  .List {
    margin-top: 20px;
    display: flex;
    width: 60%;
    .head {
      width: 140px;
      display: flex;
      flex-direction: column;
      background: #f7f8fa;
      border-bottom: 1px solid #d9dce0;
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #4c586c;
      .single {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 140px;
        height: 70px;
        padding: 0px 4px;
        background: #f7f8fa;
        border-radius: 0px 0px 0px 0px;
        opacity: 1;
        border-bottom: 1px solid #d9dce0;
      }
    }
    .table {
      width: 100%;
      display: inline-flex;
      flex-direction: column;
      overflow-x: scroll;
      border-bottom: 1px solid #d9dce0;
      font-size: 14px;
      line-height: 18px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #1f2e47;
      .head-time {
        display: flex;
        .time {
          width: 100%;
          min-width: 150px;
          height: 70px;
          background-color: #f7f8fa;
          display: flex;
          align-items: center;
          justify-content: center;
          border-bottom: 1px solid #d9dce0;
        }
      }
      .text {
        display: flex;
        .text-single {
          width: 100%;
          min-width: 150px;
          height: 70px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-bottom: 1px solid #d9dce0;
        }
      }
    }
  }
  .recentyears {
    margin-left: 16px;
    margin-top: 20px;
    width: 38%;
    height: 350px;
    border-left: 1px solid #e8e8e8;
  }
}
.liabilities {
  margin-top: 50px;
  .title {
    display: flex;
    justify-content: space-between;
    span {
      font-size: 16px;
      margin-left: 16px;
      margin-right: 40px;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #4c586c;
    }
    .invent {
      display: flex;
      font-size: 15px;
      .xzoption {
        width: 88px;
        height: 32px;
        color: #3370FF;
        background: #ffffff;
        border-radius: 2px 0px 0px 2px;
        opacity: 1;
        border: 1px solid #3370FF;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
      .option {
        width: 88px;
        height: 32px;
        background: #ffffff;
        border-radius: 2px 0px 0px 2px;
        opacity: 1;
        border: 1px solid #d9d9d9;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
    }
  }
  .List {
    margin-top: 20px;
    display: flex;
    width: 100%;
    .head {
      width: 140px;
      display: flex;
      flex-direction: column;
      background: #f7f8fa;
      border-bottom: 1px solid #d9dce0;
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #4c586c;
      .single {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 140px;
        height: 50px;
        background: #f7f8fa;
        border-radius: 0px 0px 0px 0px;
        padding: 0px 4px;
        opacity: 1;
        border-bottom: 1px solid #d9dce0;
      }
    }
    .table {
      width: 100%;
      display: inline-flex;
      flex-direction: column;
      overflow-x: scroll;
      border-bottom: 1px solid #d9dce0;
      font-size: 14px;
      line-height: 18px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #1f2e47;
      .head-time {
        display: flex;
        .time {
          width: 100%;
          min-width: 150px;
          height: 50px;
          background-color: #f7f8fa;
          display: flex;
          align-items: center;
          justify-content: center;
          border-bottom: 1px solid #d9dce0;
        }
      }
      .text {
        display: flex;
        .text-single {
          width: 100%;
          min-width: 150px;
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-bottom: 1px solid #d9dce0;
        }
      }
    }
  }
}
</style>