<!-- 委托跟踪信息 -->
<template>
  <div class="tracke-info">
    <!-- 记录表单区域 -->

    <div class="record-form">
      <div
        v-if="clueDetial"
        class="form-content"
      >
        <div class="info-item">
          <span class="label">订单号：</span>
          <span class="value">{{ clueDetial.orderSn || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">发起委托时间：</span>
          <span class="value">{{ clueDetial.startDatetime || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">期望对接时间：</span>
          <span class="value">{{ clueDetial.exceptedDatetime || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">委托单位：</span>
          <span class="value">{{ clueDetial.payOrgName || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">招商对接人：</span>
          <span class="value">{{ clueDetial.contact || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">联系方式：</span>
          <span class="value">{{ clueDetial.contactPhone || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">招商要求：</span>
          <span class="value">{{ clueDetial.note || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">金额：</span>
          <span class="value">{{ clueDetial.amount || '-' }}</span>
        </div>
      </div>
    </div>

    <!-- logs
      reminds -->
    <div class="tabs">
      <div
        class="tab1"
        :class="state === '1' ? 'active' : 'default'"
        @click="chantTab('1')"
      >
        进度信息
      </div>
      <div
        class="tab2"
        :class="state === '2' ? 'active' : 'default'"
        @click="chantTab('2')"
      >
        催办信息
      </div>
    </div>

    <div
      v-if="state === '1' && clueDetial?.logs?.length > 0"
      class="timeline"
    >
      <div
        v-for="(record, index) in clueDetial?.logs"
        :key="index"
        class="timeline-item"
      >
        <div class="time-point active" />
        <div class="time-line" />
        <div class="timeline-content">
          <div class="timeline-header">
            <span class="date-time">{{ record.gmtCreate }}</span>
            <div :class="['status']">
              <span class="status-text">{{ record.statusName }}</span>
            </div>
          </div>
          <div
            v-if="record.childObjectText == 'audit' && record.tip"
            style="
              font-weight: 400;
              font-size: 30rpx;
              color: #ff0000;
              margin-left: 36rpx;
              margin-bottom: 16rpx;
            "
          >
            <span>（超过5天，系统自动审批通过）</span>
          </div>
          <!-- 结单申请 -->
          <div
            v-if="record.childObjectText == 'apply' && record.apply"
            class="timeline-body"
          >
            <div class="info-item">
              <span class="label">申请人：</span>
              <span class="value">{{ record.apply.applyName }}</span>
            </div>
            <div class="info-item">
              <span class="label">结单说明：</span>

              <span class="value">{{ record.apply.note }}</span>
            </div>
            <div class="info-item">
              <span class="label">附件：</span>

              <div class="annex">
                <div
                  v-for="(it, ind) in record.apply.attachUrls"
                  :key="ind"
                >
                  <el-image
                    style="width: 100px; height: 100px"
                    :src="it"
                    fit="contain"
                  />
                </div>
              </div>
            </div>
          </div>
          <!-- 申请审批 -->
          <div
            v-if="record.childObjectText == 'audit' && record.audit"
            class="timeline-body"
          >
            <div class="info-item">
              <span class="label">审批人：</span>

              <span class="value">{{ record.audit.auditName }}</span>
            </div>
            <div class="info-item">
              <span class="label">是否通过：</span>

              <span class="value">{{ record.audit.auditStatusName }}</span>
            </div>
            <div class="info-item">
              <span class="label">审批说明：</span>

              <span class="value">{{ record.audit.note || '无' }}</span>
            </div>
          </div>
          <!-- 评论信息 -->
          <div
            v-if="record.childObjectText == 'comment' && record.comment"
            class="timeline-body"
          >
            <div class="info-item">
              <span class="label">委托人：</span>

              <span class="value">{{ record.comment.name }}</span>
            </div>
            <div class="info-item">
              <span class="label">评论信息：</span>

              <span class="value">{{ record.comment.note }}</span>
            </div>
          </div>
          <!-- 认领信息 -->
          <div
            v-if="record.childObjectText == 'claim' && record.claim"
            class="timeline-body"
          >
            <div class="info-item">
              <span class="label">认领人：</span>
              <span class="value">{{ record?.claim?.contact }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系方式：</span>
              <span class="value">{{ record?.claim?.contactPhone }}</span>
            </div>
          </div>
          <!-- 跟进信息 -->
          <div
            v-if="record.childObjectText == 'follow' && record.follow"
            class="timeline-body"
          >
            <div class="info-item">
              <span class="label">提交人：</span>

              <span class="value">{{ record.follow.createBy }}</span>
            </div>
            <div class="info-item">
              <span class="label">企业对接人：</span>

              <span class="value">{{ record.follow.enterpriseContact }}</span>
            </div>
            <div class="info-item">
              <span class="label">职务：</span>

              <span class="value">{{ record.follow.contactPosition }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系方式：</span>

              <span class="value">{{ record.follow.contactPhone }}</span>
            </div>
            <div class="info-item">
              <span class="label">跟进概述：</span>

              <span class="value">{{ record.follow.note }}</span>
            </div>
            <div class="info-item">
              <span class="label">附件：</span>

              <div
                v-if="record.follow.attachUrls"
                class="annex"
              >
                <div
                  v-for="(it, ind) in record.follow.attachUrls"
                  :key="ind"
                  @click="previewImg(record.follow.attachUrls, ind)"
                >
                  <image
                    :src="it"
                    class="annexImg"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="state === '1' && !clueDetial?.logs?.length">
      <NoData />
    </div>

    <div
      v-if="state === '2' && clueDetial?.reminds?.length > 0"
      class="timeline"
    >
      <div
        v-for="(remind, index) in clueDetial?.reminds"
        :key="index"
        class="timeline-item"
      >
        <div class="time-point active" />
        <div class="time-line" />
        <div class="timeline-content">
          <div class="timeline-header">
            <span class="date-time">{{ remind.remindTimeStr }}</span>
            <div :class="['status']">
              <span class="status-text">{{ remind.type }}</span>
            </div>
          </div>
          <div class="timeline-body">
            <div class="info-item">
              <span class="label">委托人：</span>
              <span class="value">{{ remind?.createBy }}</span>
            </div>
            <div class="info-item">
              <span class="label">催办信息：</span>
              <span class="value">{{ remind?.note }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="state === '2' && !clueDetial?.reminds?.length">
      <NoData />
    </div>
  </div>
</template>

<script>
import NoData from '@/views/overview/components/component/noData3.vue';
import { getClueDetial } from '@/api/CattractInvestment';
export default {
  name: 'TrackeInfo2',
  components: {
    NoData,
  },
  props: {
    uniCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      clueDetial: '',
      state: '1',
    };
  },
  watch: {
    uniCode: {
      handler(newVal) {
        if (newVal) {
          this.getFollowUpRecordList();
        }
      },
      immediate: true,
    },
  },
  methods: {
    chantTab(state) {
      this.state = state;
    },
    getFollowUpRecordList() {
      getClueDetial({
        uniCode: this.uniCode,
      }).then((res) => {
        if (res && res.code === 'SUCCESS') {
          this.clueDetial = res.result || '';
        } else {
          this.$message.error(res.msg || '获取记录失败');
          this.clueDetial = '';
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tracke-info {
  overflow: scroll;
  width: 100%;
  font-family: puhuiti;
  .record-form {
    border-radius: 6px;
    margin-bottom: 20px;
    position: relative;
    .form-content {
      padding: 24px 16px 8px;

      .info-item {
        margin-bottom: 15px;
        font-family: puhuiti;
        font-size: 14px;
        .label {
          color: #333;
          margin-right: 10px;
          min-width: 120px;
          display: inline-block;
          width: 100px;
          text-align: left;
        }

        .value {
          color: #333;
        }

        .progress-tag {
          background: rgba(255, 255, 255, 0.2);
          padding: 2px 8px;
          border-radius: 2px;
          font-size: 12px;

          &.no-follow {
            background: rgba(255, 153, 0, 0.2);
            color: #ff9900;
          }
        }
      }
    }
  }
}
.timeline {
  position: relative;
  margin-top: 16px;
  .timeline-item {
    display: flex;
    padding-bottom: 30px;
    position: relative;

    &:last-child {
      padding-bottom: 0;

      .time-line {
        display: none;
      }
    }

    .time-point {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #3370ff;
      margin-top: 10px;
      z-index: 2;

      &.active {
        box-shadow: 0 0 0 4px rgba(51, 112, 255, 0.2);
      }
    }

    .time-line {
      position: absolute;
      left: 4px;
      top: 12px;
      bottom: 0;
      width: 1px;
      background: #666;
    }

    .timeline-content {
      flex: 1;
      margin-left: 20px;
      padding-top: 7px;
      .timeline-header {
        display: flex;
        justify-content: flex-start;
        margin-bottom: 15px;

        .date-time {
          color: #a8abb2;
          font-size: 14px;
        }

        .status {
          margin-left: 16px;
          display: flex;
          align-items: center;

          &.completed {
            .status-text {
              background: rgba(224, 72, 72, 0.2);
              color: #e04848;
            }

            .status-desc {
              color: #8392a5;
              margin-left: 5px;
              font-size: 12px;
            }
          }
          &.ing {
            background: rgba(51, 112, 255, 0.2);
            color: #3370ff;
          }
          &.success {
            .status-text {
              background: rgba(76, 193, 105, 0.2);
              color: #4cc169;
            }
          }

          .status-text {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
          }
        }
      }

      .timeline-body {
        padding: 16px;
        background: rgba(216, 216, 216, 0.08);
        border-radius: 6px;
        .info-item {
          margin-bottom: 15px;
          // line-height: 1.5;
          font-family: puhuiti;
          font-size: 14px;
          .label {
            color: #333;
            margin-right: 10px;
            min-width: 120px;
            display: inline-block;
            width: 100px;
            text-align: left;
          }

          .value {
            color: #333;
          }
        }
      }
    }
  }
}
.tabs {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 25px;
  .tab1.active,
  .tab2.active {
    background: #3370ff;
    color: #ffffff;
  }

  .tab1 {
    border-radius: 15px 0 0 15px;
  }
  .tab2 {
    border-radius: 0 15px 15px 0;
  }
  .tab1,
  .tab2 {
    /* 自动布局子元素 */
    color: #1677ff;
    width: 116px;
    height: 30px;

    z-index: 0;
    background: transparent;
    border: 1px solid #1677ff;
    display: flex;
    font-size: 14px;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
  }
}
.noData {
  font-size: 14px;
  color: rgba(181, 193, 209, 0.7);
  line-height: 15vh;
  width: 100%;
  text-align: center;
}
</style>
