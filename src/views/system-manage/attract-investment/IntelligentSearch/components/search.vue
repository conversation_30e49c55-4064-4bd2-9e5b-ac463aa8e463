<template>
  <!-- 普通搜索 -->
  <div style="padding-bottom: 50px;">
    <div v-show="isParticulars == 1">
      <div class="iconarrow">
        <i
          style="cursor: pointer"
          class="el-icon-arrow-left"
          @click="gobacka"
        />
      </div>
      <div class="search">
        <div class="buttom">
          <div style="display: flex; flex-direction: column">
            <el-input
              v-model="criteria"
              class="wisdom-input"
              placeholder="请输入企业关键词"
              @input="associate"
              @blur="lose"
              @focus="showlx=true"
              @keyup.enter.native="search"
            />
            <div
              v-if="associationalword.length >= 1 && showlx"
              class="asso"
            >
              <div
                v-for="(item, index) in associationalword"
                :key="index"
                class="alone"
                @click="godetails(item)"
              >
                {{ item.enterpriseName }}
              </div>
            </div>
          </div>
          <div
            class="searcher"
            @click="search"
          >
            搜索
          </div>
          <el-button
            class="advanced"
            type="text"
            @click="advanced"
          >
            高级智搜
          </el-button>
        </div>
      </div>
      <div class="EnterpriseList">
        <div class="headline">
          <div class="total">
            <span>共找到{{ total }}家企业</span>
            <el-tooltip
              effect="dark"
              content="默认导出前5000条数据"
              placement="top"
            >
              <div
                v-loading="xzloading"
                class="export"
                @click="exportex"
              >
                <img
                  src="https://static.idicc.cn/cdn/pangu/interactive.png"
                  class="up"
                >导出数据
              </div>
            </el-tooltip>
          </div>
          <el-select
            v-model="sort"
            placeholder="请选择"
            class="orderingRule"
            @change="sortord"
          >
            <el-option
              v-for="item in orderingRule"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </div>
        <EnterpriseList
          v-loading="ListLoading"
          :enterprise-list="EnterpriseList"
          @particulars="particulars"
        />
        <div class="ye">
          <el-pagination
            :current-page.sync="staform.pageNum"
            :page-sizes="[5, 10, 20, 50]"
            :page-size.sync="staform.pageSize"
            :total="+totals"
            layout="sizes, prev, pager, next, jumper"
            @size-change="search"
            @current-change="search"
          />
        </div>
      </div>
      <div
        v-if="total>=10000"
        style="text-align: center;margin-top: 20px;padding-bottom: 20px;font-size: 14px;"
      >
        最多查看10000条数据
      </div>
    </div>
    <Detailsenterprise
      v-if="isParticulars == 2"
      :enterprise-i-d="enterpriseID"
      :company-detial="companyDetial"
      @goback="goback"
    />
  </div>
</template>

<script>
import EnterpriseList from "./EnterpriseList.vue";
import Detailsenterprise from "./Detailsenterprise.vue";
import { searchAPI ,nameAssociateAPI} from "../../apiUrl";
import { merchantsdownloadAPI } from '@/api/export';
export default {
  name: "CommonSearch",
  components: {
    EnterpriseList,
    Detailsenterprise,
  },
  props: {
    input: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      criteria: "", //搜索框
      EnterpriseList: [], //企业列表
      total: 0, //总数
      totals:0,//分页的页码
      sort: "", //默认排序
      staform: {
        pageNum: 1,
        pageSize: 10,
      },
      isParticulars: 1, //搜索1 详情2 智搜3
      enterpriseID: "", //企业id
      startpoint: "", //出发点
      ListLoading: false, //列表loading
      time:null,
      showlx:true,
      associationalword:[],
      orderingRule: [
        {
          name: "默认排序",
          value: "",
        },
        {
          name: "注册资本从高到低",
          value: 1,
        },
        {
          name: "注册资本从低到高",
          value: 2,
        },
        {
          name: "成立日期从晚到早",
          value: 3,
        },
        {
          name: "成立日期从早到晚",
          value: 4,
        },
      ],
      xzloading: false,
      companyDetial:{}
    };
  },
  created() {
    this.criteria = this.input;
    this.search();
    this.associate() 
    this.showlx=false
  },
  methods: {
    async exportex(){
    if(this.xzloading){
      return this.$message.warning("正在导出中，请耐心等待")
    }
    if(this.total==0){
      return this.$message.warning("这里还什么都没有~")
    }
    let data = {
          keyword: this.criteria,
          pageNum: 1,
          pageSize: 5000,
          orderType: this.sort, //排序规则 1注册资本倒序 2注册资本正序 3注册日期倒序 4注册日期正序
          //currentPageMaxId,
        };
        try {
        this.xzloading=true
        const res=   await merchantsdownloadAPI(data)
        if(res.msg){
          return this.$message.error(res.msg)
        }
        let blob = new Blob([res], {
          type: "text/csv,charset=UTF-8",
        });
        let objectUrl = URL.createObjectURL(blob);
        const fileName = `企业列表.xlsx`;
        const downloadLink = document.createElement("a");
        downloadLink.href = objectUrl;
        downloadLink.download = fileName;
        downloadLink.click(); 
        } finally  {
          this.xzloading=false
        }

    },
    // 进入高级智搜
    advanced() {
      this.$emit("upgrade");
    },
    async associate() {
      if (this.time != null) {
        clearTimeout(this.time);
      }
      this.time = setTimeout(async () => {
        const res = await nameAssociateAPI({
          keyword: this.criteria,
        });
        this.associationalword = res.result;
      }, 100);
    },
    lose(){
      setTimeout(()=>{
        this.showlx=false
      },200)
    },
    godetails(item) {
      this.companyDetial=item
      this.startpoint = this.isParticulars;
      this.enterpriseID = item.enterpriseId;
      this.isParticulars = 2;
    },
    sortord() {
      this.search();
    },
    // 获取列表
    async search() {
       if (this.criteria == "") {
        return this.$message.error("搜索条件不能为空");
      }
      try {
        this.ListLoading = true;
       /*  let currentPageMaxId=''
        if(this.EnterpriseList.length>0){
          currentPageMaxId=this.EnterpriseList[this.EnterpriseList.length-1].enterpriseId;
        } */
        let data = {
          keyword: this.criteria,
          pageNum: this.staform.pageNum,
          pageSize: this.staform.pageSize,
          orderType: this.sort, //排序规则 1注册资本倒序 2注册资本正序 3注册日期倒序 4注册日期正序
          //currentPageMaxId,
        };
        const res = await searchAPI(data);
        this.EnterpriseList = res.result.records;
        this.total = res.result.total;
        this.totals = res.result.total;
        if(res.result.total>10000){
          this.totals = 10000
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.ListLoading = false;
      }
    },
    // 跳转到详情
    particulars(item) {
      this.startpoint = this.isParticulars;
      this.enterpriseID = item.enterpriseId;
      this.isParticulars = 2;
    },
    // 回到原来的页面
    goback() {
      this.isParticulars = this.startpoint;
    },
    gobacka() {
      this.$emit("regression");
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-input__inner:focus {
    border-color: #1592fc;
  }
}
.asso {
  margin-top: 1px;
  width: 482px;
  position: absolute;
  top: 128px;
  z-index: 99;
  border-radius: 4px 0px 0px 4px;
  border: 1px solid #dddddd;
  background-color: #fff;
  .alone {
    cursor: pointer;
    height: 30px;
    display: flex;
    font-size: 14px;
    align-items: center;
    padding-left: 10px;
  }
  .alone:hover {
    background-color: #dee0e3;
    font-size: 14px;
  }
}
.iconarrow {
  color: #666;
    font-size: 20px;
    background: white ;
    padding: 2px;
    width: 25px;
    height: 25px;
    border-radius: 16px;
}
.scan {
  background: #fafcff;
  min-height: 800px;
  height: 100vh;
  box-sizing: border-box;
  min-width: 1110px;
  &-top {
    padding: 16px 0px;
    background: #fff;
    padding-bottom: 12px;
  }
}
::v-deep {
  .el-pagination {
    position: relative;
    padding-right: 60px;
    right: 0px;
    text-align: right;
    height: 50px;
    padding-top: 10px;
    border: 10px;
  }
}
.ye {
  position: relative;
  padding-top: 10px;
  padding-bottom: 20px;
}
.search {
  background-color: #fff;
  margin: 16px 0;
    box-shadow: 0px 4px 12px 0px #EEF1F8;
  min-height: 20vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  .buttom {
    display: flex;
    margin-top: 50px;
    .wisdom-input {
      width: 482px;
      height: 40px;
      border-radius: 4px 0px 0px 4px;
    }
    .searcher {
      width: 70px;
      height: 40px;
      background-color: #3370FF;
      border: 1px solid #3370FF;
      border-radius: 0px 4px 4px 0px;
      cursor: pointer;
      font-size: 16px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .advanced {
      color: #3370FF;
      font-weight: 400;
      font-size: 16px;
      margin-left: 17px;
    }
  }
}
.EnterpriseList {
  background-color: #fff;
  height: auto;
  border-radius: 10px;
  margin: 16px 0;
    box-shadow: 0px 4px 12px 0px #EEF1F8;
  .headline {
    display: flex;
    height: 64px;
    justify-content: space-between;
    border-bottom: 1px solid #e9e9e9;
    .total {
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
      font-size: 16px;
      padding: 22px 0px 13px 24px;
      .export{
        cursor: pointer;
        margin-left: 16px;
        width: 82px;
        height: 24px;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px solid #CED4DB;
        font-size: 12px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #3F4A59;
        line-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        .up{
        width: 14px;
        height: 14px;
        margin-right: 5px;
      }
      }
    }

    .orderingRule {
      padding: 12px 24px 9px 0px;
    }
  }
}
</style>