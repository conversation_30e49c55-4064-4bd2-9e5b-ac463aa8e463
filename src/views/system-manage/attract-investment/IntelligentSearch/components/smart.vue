<template>
  <!-- 高级智搜 -->
  <div style="padding-bottom: 50px">
    <div v-show="isParticulars == 3">
      <div class="title">
        <div class="iconarrow">
          <i
            style="cursor: pointer"
            class="el-icon-arrow-left"
            @click="gobacka"
          />
        </div>
        <div class="text">
          <div class="title-left">
            高级智搜
          </div>
          <div
            class="title-right"
            @click="gowholh"
          >
            产业全景
          </div>
        </div>
      </div>
      <div class="search">
        <div class="using">
          <div class="left">
            关键词：
          </div>
          <div class="antistop">
            <el-input
              v-model="form.antistop"
              placeholder="请输入关键词"
              @input="achangeindata"
            />
          </div>
        </div>
        <div
          v-loading="!showindustry"
          class="using"
        >
          <div class="left">
            所属产业：
          </div>
          <div
            v-if="showindustry"
            class="antistop"
          >
            <div
              v-for="(item, index) in industry"
              :key="index"
              class="multi appendToBodyFalse"
            >
              <el-cascader
                v-if="opList[index].length > 0"
                ref="cascader"
                v-model="arr[index]"
                style="width: 320px"
                :placeholder="opList[index][0].label"
                :options="opList[index]"
                collapse-tags
                :props="{
                  multiple: true,
                }"
                clearable
                @change="Subordinate(index)"
              />
            </div>
          </div>
        </div>
        <div class="using">
          <div class="left">
            所在地区：
          </div>
          <div class="antistop">
            <div class="multi">
              <el-select
                v-model="form.province"
                :popper-append-to-body="false"
                placeholder="选择省份"
                @change="cityList2"
              >
                <el-option
                  v-for="item in city"
                  :key="item.value"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </div>
            <div class="multi">
              <el-select
                v-model="form.city"
                :popper-append-to-body="false"
                placeholder="选择城市"
                :no-data-text="'请先选择上一级'"
                @change="cityList3"
              >
                <el-option
                  v-for="item in city1"
                  :key="item.value"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </div>
            <div class="multi">
              <el-select
                v-model="form.area"
                multiple
                :popper-append-to-body="false"
                collapse-tags
                placeholder="选择区县"
                :no-data-text="'请先选择上一级'"
              >
                <el-option
                  v-for="item in city2"
                  :key="item.value"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </div>
          </div>
        </div>
        <div class="using">
          <div class="left">
            上市板块：
          </div>
          <div class="antistop">
            <div
              v-for="(item, index) in appearList"
              :key="index"
              :class="form.market.includes(item.id) ? 'pitchon' : 'pitch'"
              @click="screenappear(item)"
            >
              {{ item.labelName }}
            </div>
          </div>
        </div>
        <div class="using">
          <div class="left">
            科技认定：
          </div>
          <div class="antistop">
            <div
              v-for="(item, index) in scienceList"
              :key="index"
              :class="form.science.includes(item.id) ? 'pitchon' : 'pitch'"
              @click="scienceappear(item)"
            >
              {{ item.labelName }}
            </div>
          </div>
        </div>
        <div class="selected">
          <div class="left">
            <span class="yet"> 已选条件： </span>
            <div class="selectedTag">
              <!--  <div
                v-for="(item, index) in showindustrys"
                :key="index"
                style="display: flex; flex-wrap: wrap"
              >
                <span
                  v-for="(it, ind) in item"
                  :key="ind"
                  class="tag"
                >
                  {{ it.label }}
                  <i
                    style="cursor: pointer; margin-left: 9px"
                    class="el-icon-close"
                    @click="deleteTag(6, it.value, index, it.level, it.data)"
                  />
                </span>
              </div> -->
              <div
                v-if="form.antistop"
                class="tag"
              >
                <span class="name">关键词：</span>
                <el-tooltip
                  effect="dark"
                  :visible-arrow="false"
                  :content="form.antistop"
                  placement="top"
                >
                  <div class="contentSetting">
                    {{ form.antistop }}
                  </div>
                </el-tooltip>

                <i
                  style="cursor: pointer; margin-left: 9px"
                  class="el-icon-close"
                  @click="deleteTag(0)"
                />
              </div>
              <div
                v-if="showyx"
                class="tag"
              >
                <span class="name">所属产业：</span>
                <el-tooltip
                  effect="dark"
                  :visible-arrow="false"
                  placement="top"
                >
                  <template slot="content">
                    <span
                      v-for="(item, index) in showindustrys"
                      v-show="item !== undefined"
                      :key="index"
                    >
                      <span
                        v-for="(it, ind) in item"
                        :key="ind"
                      >
                        <i v-if="ind != 0">，</i>{{ it.label }}
                      </span>
                      <i
                        v-if="
                          index != showindustrys.length - 1 &&
                            item !== undefined &&
                            item !== null &&
                            item.length !== 0
                        "
                      >，</i>
                    </span>
                  </template>
                  <div class="contentSetting">
                    <span
                      v-for="(item, index) in showindustrys"
                      v-show="item !== undefined"
                      :key="index"
                    >
                      <span
                        v-for="(it, ind) in item"
                        :key="ind"
                      >
                        <i v-if="ind != 0">，</i>{{ it.label }}
                      </span>
                      <i
                        v-if="
                          index != showindustrys.length - 1 &&
                            item !== undefined &&
                            item !== null &&
                            item.length !== 0
                        "
                      >，</i>
                    </span>
                  </div>
                </el-tooltip>

                <i
                  style="cursor: pointer; margin-left: 9px"
                  class="el-icon-close"
                  @click="deleteTag(6)"
                />
              </div>
              <div
                v-if="form.province"
                class="tag"
              >
                <span class="name">所在地区：</span>
                <el-tooltip
                  effect="dark"
                  :visible-arrow="false"
                  placement="top"
                >
                  <template slot="content">
                    <span v-if="form.province">{{ form.province }}</span>
                    <span v-if="form.city && form.province !== form.city">，{{ form.city }}</span>
                    <span v-if="form.area.length > 0"><span
                      v-for="item in form.area"
                      :key="item"
                    >，{{ item }}</span></span>
                  </template>
                  <div class="contentSetting">
                    <span v-if="form.province">{{ form.province }}</span>
                    <span v-if="form.city && form.province !== form.city">，{{ form.city }}</span>
                    <span v-if="form.area.length > 0"><span
                      v-for="item in form.area"
                      :key="item"
                    >，{{ item }}</span></span>
                  </div>
                </el-tooltip>
                <i
                  style="cursor: pointer; margin-left: 9px"
                  class="el-icon-close"
                  @click="deleteTag(3)"
                />
              </div>
              <!--  <div
                v-for="item in showmarket"
                :key="item.id"
                class="tag"
              >
                {{ item.labelName
                }}<i
                  style="cursor: pointer; margin-left: 9px"
                  class="el-icon-close"
                  @click="deleteTag(1, item.id)"
                />
              </div> -->
              <div
                v-if="showmarket.length > 0"
                class="tag"
              >
                <span class="name">上市板块：</span>
                <el-tooltip
                  effect="dark"
                  :visible-arrow="false"
                  placement="top"
                >
                  <template slot="content">
                    <span
                      v-for="(item, index) in showmarket"
                      :key="item.id"
                    >
                      {{ item.labelName
                      }}<i v-if="index != showmarket.length - 1">，</i>
                    </span>
                  </template>
                  <div class="contentSetting">
                    <span
                      v-for="(item, index) in showmarket"
                      :key="item.id"
                    >
                      {{ item.labelName
                      }}<i v-if="index != showmarket.length - 1">，</i>
                    </span>
                  </div>
                </el-tooltip>
                <i
                  style="cursor: pointer; margin-left: 9px"
                  class="el-icon-close"
                  @click="deleteTag(1)"
                />
              </div>
              <!--  <div
                v-for="item in showscience"
                :key="item.id"
                class="tag"
              >
                {{ item.labelName
                }}<i
                  style="cursor: pointer; margin-left: 9px"
                  class="el-icon-close"
                  @click="deleteTag(2, item.id)"
                />
              </div> -->
              <div
                v-if="showscience.length > 0"
                class="tag"
              >
                <span class="name">科技认定：</span>
                <el-tooltip
                  effect="dark"
                  :visible-arrow="false"
                  placement="top"
                >
                  <template slot="content">
                    <span
                      v-for="(item, index) in showscience"
                      :key="item.id"
                    >
                      {{ item.labelName
                      }}<i v-if="index != showscience.length - 1">，</i>
                    </span>
                  </template>
                  <div class="contentSetting">
                    <span
                      v-for="(item, index) in showscience"
                      :key="item.id"
                    >
                      {{ item.labelName
                      }}<i v-if="index != showscience.length - 1">，</i>
                    </span>
                  </div>
                </el-tooltip>
                <i
                  style="cursor: pointer; margin-left: 9px"
                  class="el-icon-close"
                  @click="deleteTag(2)"
                />
              </div>
              <!--               <div
                v-if="form.province"
                class="tag"
              >
                {{ form.province
                }}<i
                  style="cursor: pointer; margin-left: 9px"
                  class="el-icon-close"
                  @click="deleteTag(3)"
                />
              </div>
              <div
                v-if="form.city && form.city !== form.province"
                class="tag"
              >
                {{ form.city
                }}<i
                  style="cursor: pointer; margin-left: 9px"
                  class="el-icon-close"
                  @click="deleteTag(4)"
                />
              </div>
              <div
                v-for="item in form.area"
                :key="item"
                class="tag"
              >
                {{ item
                }}<i
                  style="cursor: pointer; margin-left: 9px"
                  class="el-icon-close"
                  @click="deleteTag(5, item)"
                />
              </div> -->
            </div>
          </div>
        </div>
        <div class="find">
          <div
            class="cz"
            @click="reset"
          >
            重置
          </div>
          <div
            v-loading="ListLoading"
            class="seek"
            style="margin-left: 20px"
            :class="Prohibit ? '' : 'no'"
            @click="inquire"
          >
            查询
          </div>
        </div>
      </div>
      <div
        v-if="showlist"
        class="EnterpriseList"
      >
        <div class="headline">
          <div class="total">
            <span>共找到{{ total }}家企业</span>
            <el-tooltip
              effect="dark"
              content="默认导出前5000条数据"
              placement="top"
            >
              <div
                v-loading="xzloading"
                class="export"
                @click="exportex"
              >
                <img
                  src="https://static.idicc.cn/cdn/pangu/interactive.png"
                  class="up"
                >导出数据
              </div>
            </el-tooltip>
          </div>
          <el-select
            v-model="sort"
            placeholder="请选择"
            class="orderingRule"
            :popper-append-to-body="false"
            @change="search"
          >
            <el-option
              v-for="item in orderingRule"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </div>
        <EnterpriseList
          v-loading="ListLoading"
          :enterprise-list="EnterpriseList"
          @particulars="particulars"
        />
        <div class="ye">
          <el-pagination
            :current-page.sync="form.pageNum"
            :page-sizes="[5, 10, 20, 50]"
            :page-size.sync="form.pageSize"
            :total="+totals"
            layout="sizes, prev, pager, next, jumper"
            @size-change="search"
            @current-change="search"
          />
        </div>
      </div>
      <div
        v-if="total >= 10000"
        style="
          text-align: center;
          margin-top: 20px;
          padding-bottom: 20px;
          font-size: 14px;
        "
      >
        最多查看10000条数据
      </div>
    </div>

    <Detailsenterprise
      v-if="isParticulars == 2"
      :enterprise-i-d="enterpriseID"
      :listed-sector-ids="form.market"
      :technology-certification-ids="form.science"
      :company-detial="companyDetial"
      @goback="goback"
    />
  </div>
</template>

<script>
import EnterpriseList from "./EnterpriseList.vue";
import Detailsenterprise from "./Detailsenterprise.vue";
import { filterData } from "@/utils/utils";
import { merchantsdownloadAPI } from "@/api/export";
import {
  homeListAPI,
  listByTypeNameAPI,
  listTechnologicalLabelAPI,
  searchAPI,
  industryChainNodeTreeAPI,
  getLastItem,
} from "../../apiUrl";
import { cityAPI } from "@/api/city";
export default {
  name: "SmartA",
  components: {
    EnterpriseList,
    Detailsenterprise,
  },
  props: {
    befrom: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      showlist: false,
      city: [],
      city1: [],
      city2: [],
      form: {
        province: "",
        city: "",
        area: "",
        antistop: "", //关键词
        pageNum: 1,
        pageSize: 10,
        market: [], //上市标签
        science: [],
      },
      // 展示的上市标签
      showmarket: [],
      // 展示的科技认定
      showscience: [],
      showindustrys: [],
      chainNodeIds: [],
      arr: [], //绑定的值
      opList: [], //树状图列表
      EnterpriseList: [], //企业列表
      total: 0, //总数
      totals: 0, //分页总数
      sort: "", //默认排序
      industryId: "", //产业链id
      input: "", //输入框
      isParticulars: 3, //搜索1 详情2 智搜3
      enterpriseID: "", //企业id
      startpoint: "", //出发点
      industry: [], //产业链列表
      orderingRule: [
        {
          name: "默认排序",
          value: "",
        },
        {
          name: "注册资本从高到低",
          value: 1,
        },
        {
          name: "注册资本从低到高",
          value: 2,
        },
        {
          name: "成立日期从晚到早",
          value: 3,
        },
        {
          name: "成立日期从早到晚",
          value: 4,
        },
      ],
      appearList: [], //上市板块
      scienceList: [], //企业资质类型
      ListLoading: false,
      showindustry: false,
      Prohibit: false,
      showyx: false,
      xzloading: false,
      companyDetial: {},
    };
  },
  watch: {
    arr() {
      this.showyx = false;
      this.arr.map((item) => {
        if (item !== undefined && item.length > 0) {
          this.showyx = true;
        }
      });
    },
  },
  created() {
    this.getList(); //产业链
    this.cityList(); //省
    this.getbytype(); //上市板块
    this.certification(); //资质类型
    //this.search();
  },
  methods: {
    async exportex() {
      if (this.xzloading) {
        return this.$message.warning("正在导出中，请耐心等待");
      }
      if (this.total == 0) {
        return this.$message.warning("这里还什么都没有~");
      }
      let data = {
        keyword: this.form.antistop,
        pageNum: 1,
        pageSize: 5000,
        orderType: this.sort, //排序规则 1注册资本倒序 2注册资本正序 3注册日期倒序 4注册日期正序
        province: this.form.province,
        city: this.form.city,
        area: this.form.area,
        technologyCertificationIds: this.form.science,
        listedSectorIds: this.form.market,
        chainNodeIds: this.chainNodeIds,
        //currentPageMaxId,
      };
      filterData(data);
      try {
        this.xzloading = true;
        const res = await merchantsdownloadAPI(data);
        if (res.msg) {
          return this.$message.error(res.msg);
        }
        let blob = new Blob([res], {
          type: "text/csv,charset=UTF-8",
        });
        let objectUrl = URL.createObjectURL(blob);
        const fileName = `企业列表.xlsx`;
        const downloadLink = document.createElement("a");
        downloadLink.href = objectUrl;
        downloadLink.download = fileName;
        downloadLink.click();
      } finally {
        this.xzloading = false;
      }
    },
    // 删除标签
    deleteTag(i, id) {
      //i, id, index, level, data
      if (i == 0) {
        this.form.antistop = "";
      } else if (i == 1) {
        //this.form.market = this.form.market.filter((it) => it != id);
        //this.showmarket = this.form.market.filter((it) => it != id);
        this.form.market = [];
        this.showmarket = [];
      } else if (i == 2) {
        /*  this.form.science = this.form.science.filter((it) => it != id);
        this.showscience = this.form.science.filter((it) => it != id); */
        this.form.science = [];
        this.showscience = [];
      } else if (i == 3) {
        this.form.province = "";
        this.form.city = "";
        this.form.area = "";
      } else if (i == 4) {
        this.form.city = "";
        this.form.area = "";
      } else if (i == 5) {
        this.form.area = this.form.area.filter((it) => it != id);
      } else if (i == 6) {
        this.arr = [];
        this.showindustrys = [];
        /*         if (level == 1) {
          this.$set(this.arr, index, []);
          //this.showindustrys[index] = this.showindustrys[index].filter(item => item.value !== id);
          this.$set(
            this.showindustrys,
            index,
            this.showindustrys[index].filter((item) => item.value !== id)
          );
          //this.Subordinate(index)
          this.changeIdList();
          return;
        } else if (level == 2) {
          let IdList = data.map((item) => item.value);
          //console.log(IdList);
          IdList.forEach((item) => {
            const result = this.arr[index].map(
              (innerItem) => innerItem[innerItem.length - 1]
            );
            const indexToRemove = result.indexOf(item);
            if (indexToRemove !== -1) {
              this.$set(
                this.arr,
                index,
                this.arr[index]
                  .slice(0, indexToRemove)
                  .concat(this.arr[index].slice(indexToRemove + 1))
              );
              //this.showindustrys[index] =  this.showindustrys[index].filter(item=>item.value!==id)
              this.$set(
                this.showindustrys,
                index,
                this.showindustrys[index].filter((item) => item.value !== id)
              );
            }
          });
          this.changeIdList();
          //this.Subordinate(index)
          return;
        } else {
          const result = this.arr[index].map(
            (innerItem) => innerItem[innerItem.length - 1]
          );
          const indexToRemove = result.indexOf(id);
          if (indexToRemove !== -1) {
            this.$set(
              this.arr,
              index,
              this.arr[index]
                .slice(0, indexToRemove)
                .concat(this.arr[index].slice(indexToRemove + 1))
            );
            //this.showindustrys[index] =  this.showindustrys[index].filter(item=>item.value!==id)
            this.$set(
              this.showindustrys,
              index,
              this.showindustrys[index].filter((item) => item.value !== id)
            );
          }
        }
       // console.log(this.showindustrys, "this.showindustrys");
        this.changeIdList(index); */
      }
      this.achangeindata();
    },
    changeIdList() {
      const result = [];
      if (this.arr.length > 0) {
        this.arr.forEach((item) => {
          if (item) {
            item.forEach((subItem) => {
              result.push(getLastItem(subItem));
            });
          }
        });
        this.chainNodeIds = [...new Set(result)];
        //console.log(this.chainNodeIds, "所得的结果集id");
        //console.log(this.opList, "opList树状图");
        this.achangeindata();
      }
    },
    achangeindata() {
      //console.log("数据变化");
      //console.log("省", this.form.province);
      //console.log("关键词", this.form.antistop);
      //console.log("科技认定", this.form.science);
      //console.log("上市板块", this.form.market);
      //console.log(this.arr, "this.arr", this.arr.length);
      const filteredArr = this.arr.filter(
        (subArr) => subArr !== undefined && subArr.length > 0
      );
      this.showmarket = this.form.market.map((item) => {
        let foundItem = this.appearList.find(
          (dataItem) => dataItem.id === item
        );
        if (foundItem) {
          return foundItem;
        }
      });
      this.showscience = this.form.science.map((item) => {
        let foundItem = this.scienceList.find(
          (dataItem) => dataItem.id === item
        );
        if (foundItem) {
          return foundItem;
        }
      });
      //console.log(filteredArr, "filteredArr", filteredArr.length);
      if (
        this.form.province !== "" ||
        this.form.antistop !== "" ||
        this.form.science.length > 0 ||
        this.form.market.length > 0 ||
        filteredArr.length > 0
      ) {
        this.Prohibit = true;
        //console.log(true);
      } else {
        this.Prohibit = false;
        //console.log(false);
      }
    },
    Subordinate(index) {
      let nodesObj = this.$refs["cascader"][index].getCheckedNodes();
      this.showindustrys[index] = [];
      //console.log(nodesObj, "已选节点");
      //console.log(this.arr, "arrrr=-r=");
      let results = this.arr.every((item) => item == null);
      //console.log(results, "===>>>>>this.arrr"); // true
      if (results) {
        nodesObj = [];
      }
      let Existingnode = [];
      if (nodesObj) {
        nodesObj?.some((item) => {
          if (item.level == 1) {
            this.showindustrys[index] = [];
            this.showindustrys[index][0] = {
              label: item.label,
              value: item.value,
              level: 1,
            };
            return true; // 提前结束循环
          } else if (item.level == 2) {
            //console.log("二级节点");
            item.data.children.map((item) => {
              Existingnode.push(item.label);
            });
            this.showindustrys[index].push({
              label: item.label,
              value: item.value,
              level: 2,
              data: item.data.children,
            });
            return false;
          } else if (item.level == 3) {
            if (!Existingnode.includes(item.label)) {
              this.showindustrys[index].push({
                label: item.label,
                value: item.value,
                level: 3,
              });
            }
            return false;
          }
        });
      }
      const result = [];
      if (this.arr.length > 0) {
        this.arr.forEach((item) => {
          if (item) {
            item.forEach((subItem) => {
              result.push(getLastItem(subItem));
            });
          }
        });
        this.chainNodeIds = [...new Set(result)];
        this.achangeindata();
      }
      //console.log(this.chainNodeIds, "所得的结果集id");
      //console.log(this.opList, "opList树状图");
    },
    inquire() {
      if (!this.Prohibit) {
        return false;
      }
      this.form.pageNum = 1;
      this.search();
    },
    // 重置
    reset() {
      this.form = {
        province: "",
        city: "",
        area: "",
        antistop: "", //关键词
        pageNum: 1,
        pageSize: 5,
        market: [], //上市标签
        science: [],
      };
      this.arr = [];
      (this.city1 = []),
        (this.city2 = []),
        //this.search();
        //console.log(this.showindustrys, "showindustrys");
        this.$set(this, "showindustrys", []);
      this.$nextTick(() => {
        //console.log(this.showindustrys, "showindustrys");
        //console.log(this.showindustrys[0], "showindustrys[0]");
      });
      this.achangeindata();
    },
    // 上市板块
    async getbytype() {
      const res = await listByTypeNameAPI({
        labelTypeName: "上市板块",
      });
      this.appearList = res.result;
      const order = [
        "主板",
        "创业板",
        "科创板",
        "北交所",
        "港股",
        "中概股",
        "新三板",
      ];
      this.appearList = this.appearList.sort((a, b) => {
        const indexA = order.indexOf(a.labelName);
        const indexB = order.indexOf(b.labelName);
        return indexA - indexB;
      });
    },
    // 科技认定
    async certification() {
      const res = await listTechnologicalLabelAPI();
      this.scienceList = res.result;
    },
    // 选择上市板块
    screenappear(item) {
      if (this.form.market.includes(item.id)) {
        this.form.market = this.form.market.filter((it) => it != item.id);
      } else {
        this.form.market.push(item.id);
      }
      this.achangeindata();
    },
    // 选择科技认定
    scienceappear(item) {
      if (this.form.science.includes(item.id)) {
        this.form.science = this.form.science.filter((it) => it != item.id);
      } else {
        this.form.science.push(item.id);
      }
      this.achangeindata();
    },
    // 省
    async cityList() {
      const res = await cityAPI({
        type: 1,
      });
      this.city = res.result;
    },
    // 市
    async cityList2(value) {
      this.achangeindata();
      const data = this.city.filter((item) => {
        if (item.name == value) {
          return item;
        }
      });
      this.cityid = data[0].id;
      const res = await cityAPI({
        type: 2,
        parentId: this.cityid,
      });
      this.form.city = "";
      this.form.area = "";
      this.city2 = [];
      this.city1 = res.result;
    },
    // 区
    async cityList3(value) {
      const data = this.city1.filter((item) => {
        if (item.name == value) {
          return item;
        }
      });
      this.areaid = data[0].id;
      const res = await cityAPI({
        type: 3,
        parentId: this.areaid,
      });
      this.form.area = "";
      this.city2 = res.result;
    },
    // 跳转到产业全景
    gowholh() {
      if (this.industryId) {
        // let res = this.$router.resolve({
        //   path: "/IndustryGraph",
        //   query: { id: this.industryId },
        // });
        // localStorage.setItem("routerQuery", this.industryId);
        // const baseURL = process.env.VUE_APP_PORT_URL
        // let newPath = `${baseURL}#/pangu/IndustryGraph?id=${this.industryId}`;
        // window.open(newPath,'_blank');
        // window.$wujie?.bus.$emit('newRouter', { path: '/pangu/IndustryGraph' });
        // window.open(res.href, "_blank");
      } else {
        this.$message.error("未获取到产业链");
      }
    },
    //获取产业链列表
    async getList() {
      const res = await homeListAPI();
      this.industry = res.result;
      /*       this.industry.forEach(async (item, index) => {
        let NodeTree = await industryChainNodeTreeAPI({
          chainId: item.industryChainId,
          countEnterpriseNum: 0,
        });
        let arr = [];
        arr.push(NodeTree.result);
        this.opList[index] = arr.map((e) => {
          return {
            value: e.id,
            label: e.nodeName,
            children: e.childNodes?.map((e2) => {
              return {
                value: e2.id,
                label: e2.nodeName,
                children: e2.childNodes?.map((e3) => {
                  return {
                    value: e3.id,
                    label: e3.nodeName,
                  };
                }),
              };
            }),
          };
        });
      }); */
      Promise.all(
        this.industry.map(async (item, index) => {
          let NodeTree = await industryChainNodeTreeAPI({
            chainId: item.industryChainId,
            countEnterpriseNum: 0,
          });
          if (NodeTree.result !== null) {
            let arr = [];
            arr.push(NodeTree.result);
            this.opList[index] = arr.map((e) => {
              return {
                value: e.id,
                label: e.nodeName,
                children: e.childNodes?.map((e2) => {
                  return {
                    value: e2.id,
                    label: e2.nodeName,
                    children: e2.childNodes?.map((e3) => {
                      return {
                        value: e3.id,
                        label: e3.nodeName,
                        children:
                          e3.childNodes.length > 0
                            ? e3.childNodes.map((e4) => {
                                return {
                                  value: e4.id,
                                  label: e4.nodeName,
                                };
                              })
                            : null,
                      };
                    }),
                  };
                }),
              };
            });
          } else {
            this.opList[index] = [];
          }
        })
      ).then(() => {
        this.showindustry = true;
      });
      if (res.result.length >= 1) {
        this.industryId = res.result[0].id;
      }
    },
    // 返回原来页面
    gobacka() {
      this.$emit("comeback", this.befrom);
    },
    // 返回列表
    goback() {
      this.isParticulars = this.startpoint;
    },
    // 获取列表数据
    async search() {
      try {
        this.ListLoading = true;
        /* let currentPageMaxId=''
        if(this.EnterpriseList.length>0){
          currentPageMaxId=this.EnterpriseList[this.EnterpriseList.length-1].enterpriseId;
        } */
        let data = {
          keyword: this.form.antistop,
          pageNum: this.form.pageNum,
          pageSize: this.form.pageSize,
          orderType: this.sort, //排序规则 1注册资本倒序 2注册资本正序 3注册日期倒序 4注册日期正序
          province: this.form.province,
          city: this.form.city,
          area: this.form.area,
          technologyCertificationIds: this.form.science,
          listedSectorIds: this.form.market,
          chainNodeIds: this.chainNodeIds,
          //currentPageMaxId,
        };
        filterData(data);
        const res = await searchAPI(data);
        this.showlist = true;
        this.EnterpriseList = res.result.records;
        this.total = res.result.total;
        this.totals = res.result.total;
        if (res.result.total > 10000) {
          this.totals = 10000;
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.ListLoading = false;
      }
    },
    // 进入企业详情
    particulars(item) {
      this.companyDetial = item;
      this.startpoint = this.isParticulars;
      this.enterpriseID = item.enterpriseId;
      this.isParticulars = 2;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-input__inner:focus {
    border-color: #dcdfe6 !important;
  }
}
.ye {
  padding-top: 10px;
  padding-bottom: 20px;
}
.no {
  background: #d9d9d9 !important;
  border: 0px solid #d9d9d9 !important;
  color: #ffffff !important;
  cursor: no-drop !important;
}
.cz {
  width: 65px;
  height: 32px;
  background: #ffffff;
  border-radius: 5px;
  opacity: 1;
  border: 1px solid #d9d9d9;
  text-align: center;
  line-height: 32px;
  margin-right: 10px;
  font-size: 14px;
  font-family: Source Han Sans CN-Normal, Source Han Sans CN;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
  cursor: pointer;
}
.iconarrow {
  color: #666;
  font-size: 20px;
  background: white;
  padding: 2px;
  width: 25px;
  height: 25px;
  border-radius: 16px;
}

::v-deep {
  .el-pagination {
    position: relative;
    padding-right: 60px;
    right: 0px;
    text-align: right;
    height: 50px;
    padding-top: 10px;
    border: 10px;
  }

  .el-icon-arrow-left {
    margin-top: 0 !important;
  }
}

.EnterpriseList {
  background-color: #fff;
  height: auto;
  border-radius: 10px;
  margin: 16px 0;
  box-shadow: 0px 4px 12px 0px #eef1f8;

  .headline {
    display: flex;
    height: 64px;
    justify-content: space-between;
    border-bottom: 1px solid #e9e9e9;

    .total {
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
      font-size: 16px;
      padding: 22px 0px 13px 24px;
      .export {
        cursor: pointer;
        margin-left: 16px;
        width: 82px;
        height: 24px;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px solid #ced4db;
        font-size: 12px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #3f4a59;
        line-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        .up {
          width: 14px;
          height: 14px;
          margin-right: 5px;
        }
      }
    }

    .orderingRule {
      padding: 12px 24px 9px 0px;
    }
  }
}

.title {
  height: 32px;
  // background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;

  .text {
    width: 99%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title-left {
      font-size: 16px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
    }

    .title-right {
      width: 109px;
      cursor: pointer;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #fbfcfe;
      box-shadow: 0px 2px 1px 0px rgba(64, 72, 82, 0.05);
      opacity: 1;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #3370ff;
      border: 1px solid #dde4f0;
    }
  }
}

.search {
  height: auto;
  margin: 16px 0;
  padding: 24px;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0px 4px 12px 0px #eef1f8;
  border-radius: 10px 10px 10px 10px;

  .using {
    display: flex;
    margin-bottom: 20px;

    .left {
      width: 70px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
    }

    .antistop {
      width: 80%;
      margin-left: 14px;
      display: flex;
      flex-wrap: wrap;

      .pitchon {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 26px;
        cursor: pointer;
        background: #3370ff;
        border-radius: 2px 2px 2px 2px;
        margin-top: 8px;
        margin-right: 20px;
        padding: 2px 8px;
        opacity: 1;
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        font-weight: 400;
        color: #ffffff;
      }

      .pitch {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 26px;
        cursor: pointer;
        border-radius: 2px 2px 2px 2px;
        margin-top: 8px;
        padding: 2px 8px;
        margin-right: 20px;
        opacity: 1;
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
      }

      .multi {
        margin-right: 30px;
        margin-bottom: 10px;
      }
    }
  }

  .selected {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;
    background: #e6f7ff;
    opacity: 1;
    border: 1px solid #bae7ff;

    .left {
      display: flex;
      width: 95%;

      .yet {
        padding: 9px 0;
        font-weight: 400;
        margin-left: 14px;
        display: flex;
        min-width: 75px;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
      }
      .selectedTag {
        padding-top: 9px;
        display: flex;
        width: 100%;
        flex-wrap: wrap;
      }
      .tagBox {
        max-width: 200px;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .tag {
        font-size: 12px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        color: #3370ff;
        padding: 2px 8px;
        margin-bottom: 8px;
        margin-left: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 22px;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #3370ff;
        .name {
          color: #6c6c6c;
        }
        .contentSetting {
          max-width: 200px;
          white-space: nowrap;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .reset {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 22px;
      font-size: 14px;
      font-family: Abel-Regular, Abel;
      font-weight: 400;
      color: #3370ff;
    }
  }
  .find {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
    .seek {
      width: 65px;
      border-radius: 4px;
      height: 32px;
      background: #3370ff;
      opacity: 1;
      display: flex;
      align-items: center;
      cursor: pointer;
      justify-content: center;
      font-size: 14px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }
  }
}
</style>