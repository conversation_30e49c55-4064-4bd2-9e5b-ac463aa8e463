<template>
  <!-- 搜索结果列表 -->
  <div>
    <div
      v-for="(item, index) in enterpriseList"
      :key="index"
      class="single"
    >
      <img
        :src="getIconByType(item.showLabelType)"
        class="iconImg"
      >
      <div class="content">
        <div class="name">
          <span
            style="cursor: pointer;min-width: 120px;"
            @click="Entrydetails(item)"
          >{{
            item.enterpriseName
          }}</span>
          <div
            v-if="item.chainNameList || item.chainNames"
            class="tag"
          >
            <div
              v-for="(it, ind) in item.chainNameList || item.chainNames"
              :key="ind"
              class="industryTag"
            >
              {{ it.replace("产业金脑·", "") }}
            </div>
          </div>
        </div>
        <div class="EnterpriseLabel">
          <div
            v-if="item.enterpriseLabelNameList || item.enterpriseLabelNames"
            class="tags"
          >
            <div
              v-for="(it, ind) in item.enterpriseLabelNameList || item.enterpriseLabelNames"
              :key="ind"
              class="firmTag"
            >
              {{ it }}
            </div>
          </div>
        </div>
        <div class="content-one">
          <div class="one-a">
            <span class="key">快速成长指数：</span>
            <span
              style="color: #34C759;"
              class="value"
            >{{ (!item.growthIndex || item.growthIndex=='0.0') ? '-' : item.growthIndex }}</span>
          </div>
          <div class="one-a">
            <span class="key">扩张意愿指数：</span>
            <span
              style="color: #FF9500;"
              class="value"
            >{{ (!item.expansionIndex || item.expansionIndex=='0.0') ? '-' : item.expansionIndex }}</span>
          </div>
          <div class="one-a">
            <span class="key">成立日期：</span>
            <span class="value">{{ item.registerDate }}</span>
          </div>
          <div class="one-b">
            <span class="key">注册资本：</span>
            <span class="value">{{ item.registeredCapital }}</span>
          </div>
        </div>
        <div class="content-two">
          <div class="two-a">
            <span class="key">法定代表人：</span>
            <span class="value">{{ item.legalPerson }}</span>
          </div>
          <div
            v-if="item.mobileList"
            class="two-a"
          >
            <span class="key">联系电话：</span>
            <span
              v-if="item.mobileList!==null && item.mobileList.length >= 1"
              class="value"
            >{{ item.mobileList[0] }}
              <span v-if="item.mobileList.length > 1">更多<el-tooltip
                effect="dark"
                placement="top"
              > 
                <template slot="content">
                  <p
                    v-for="(it,ind) in item.mobileList"
                    :key="ind"
                  >
                    <span v-if="ind!==0">{{ it }}</span>
                  </p>
                </template>
                <span>(<span style="color: #56adff;">{{ item.mobileList.length-1 }}</span>)</span>
              </el-tooltip>
              </span>
            </span>
            <span
              v-else
              class="value"
            >
              暂无
            </span>
          </div>
          <div class="two-b">
            <span class="key">注册地址：</span>
            <span class="value">{{ item.enterpriseAddress }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getEnterpriseIconByType } from '@/utils/utils'
export default {
  name: "EnterpriseList",
  props: {
    enterpriseList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
    };
  },
  methods: {
    getIconByType (type) {
      return getEnterpriseIconByType(type, 'webp');
    },
    Entrydetails(item) {
      this.$emit("particulars", item, 2);
      document.documentElement.scrollTop = 0;
    },
  },
};
</script>

<style lang="scss" scoped>
.single {
  width: 100%;
  height: 160px;
  display: flex;
  border-bottom: 1px solid #e9e9e9;
  padding-left: 24px;

  .iconImg {
    width: 32px;
    height: 32px;
    margin-top: 28px;
    margin-right: 22px;
  }
  .content {
    margin-top: 25px;
    width: 100%;
    .name {
      display: flex;
      font-size: 18px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      .tag {
        display: flex;
        padding-left: 10px;
        margin-top: 3px;
        .industryTag {
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #3370FF;
          color: #3370FF;
          font-size: 11px;
          padding: 0 6px;
          margin-left: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
          white-space: nowrap;
        }
      }
    }
    .EnterpriseLabel {
      padding: 13px 0;
      .tags {
        display: flex;
        .firmTag {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11px;
          padding: 0 6px;
          color: #ff7d00;
          background: #fff2e6;
          margin-right: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
        }
      }
    }
    .content-one {
      display: flex;
      margin-top: 6px;
      .one-a {
        width: 25%;
      }
      .one-b {
        width: 25%;
      }
      .one-c {
        width: 25%;
      }
      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }
      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }
    .content-two {
      display: flex;
      margin-top: 14px;
      .two-a {
        width: 25%;
      }
      .two-b {
        width: 50%;
      }
      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }
      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }
  }
}
</style>