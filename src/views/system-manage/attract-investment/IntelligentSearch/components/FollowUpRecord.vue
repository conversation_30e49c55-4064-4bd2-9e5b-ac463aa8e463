<!-- 跟进记录 -->
<template>
  <div class="tracke-info">
    <!-- 添加跟进记录 -->
    <div
      v-if="
        selectedRow?.clueDealState === '0' &&
          selectedRow?.beAssignPersonId === $store.getters.user.userId
      "
      class="road"
    >
      <el-button
        size="small"
        type="primary"
        @click="addFollowRecord"
      >
        添加跟进记录
      </el-button>
    </div>
    <div
      v-if="latestAssignClue"
      class="record-form"
    >
      <div class="form-content">
        <div
          v-if="latestAssignClue.assignDate"
          class="info-item"
        >
          <span class="label">指派日期：</span>
          <span class="value">{{ latestAssignClue.assignDate || "-" }}</span>
        </div>
        <div
          v-if="latestAssignClue.operateUserName"
          class="info-item"
        >
          <span class="label">指派人：</span>
          <span class="value">{{
            latestAssignClue.operateUserName || "-"
          }}</span>
        </div>
        <div
          v-if="latestAssignClue.assignUserName"
          class="info-item"
        >
          <span class="label">跟进人：</span>
          <span class="value">{{
            latestAssignClue.assignUserName || "-"
          }}</span>
        </div>
        <div
          v-if="latestAssignClue.clueDealState || selectedRow?.isNotFollowMonth"
          class="info-item"
        >
          <span class="label">当前进度：</span>
          <span>{{
            ["跟进中", "签约成功", "签约失败"][selectedRow?.clueDealState]
          }}</span>
          <span
            v-if="selectedRow?.isNotFollowMonth"
            class="value progress-tag"
            :class="{
              'no-follow': selectedRow.currentProgress === '近一个月未跟进',
            }"
          >
            <i class="el-icon-warning-outline" />
            {{ selectedRow.currentProgress }}
          </span>
        </div>
        <div class="info-item">
          <span class="label">流转备注：</span>
          <span class="value">{{ latestAssignClue.remark }}</span>
        </div>
      </div>
      <el-button
        size="small"
        type="primary"
        @click="addRecord"
      >
        指派路径
      </el-button>
    </div>

    <TimeLine :records="followUpRecords" />

    <!-- 指派路径抽屉 -->
    <el-drawer
      title="指派路径"
      :visible.sync="showRode"
      direction="rtl"
      size="25%"
      custom-class="assign-path-drawer"
      append-to-body
    >
      <div class="assign-path-content">
        <div
          v-if="assignPath?.length > 0"
          class="timeline-container"
        >
          <div
            v-for="(item, index) in assignPath"
            :key="index"
            class="timeline-item"
          >
            <div class="time-point">
              <div class="dot-circle" />
            </div>
            <div class="timeline-date">
              {{ item.assignDate }}
            </div>
            <div class="timeline-info">
              <div class="assign-info">
                <span class="label">指派人：</span>
                <span class="value">{{ item.operateUserName }}</span>
              </div>
              <div class="follow-info">
                <span class="label">跟进人：</span>
                <span class="value">{{ item.assignUserName }}</span>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <NoData />
        </div>
      </div>
    </el-drawer>

    <add-follow-record
      ref="addFollowRecord"
      @updateList="updateList"
    />
  </div>
</template>

<script>
import NoData from "@/views/overview/components/component/noData3.vue";

import {
  getFollowUpRecord,
  getLatestAssignClue,
  getAssignPath,
} from "@/api/CattractInvestment";
import TimeLine from "./TimeLine.vue";
import AddFollowRecord from "../../newSmartManagement/pageList/pop/addFollowRecord.vue";
export default {
  name: "FollowUpRecord",
  components: {
    TimeLine,
    NoData,
    AddFollowRecord,
  },
  props: {
    clueId: {
      type: String,
      default: "",
    },
    selectedRow: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      followUpRecords: [], // 存储从API获取的跟进记录
      assignPath: {
        assignClueList: [], // 初始化为空数组，避免渲染时出错
      },
      latestAssignClue: "",
      showRode: false,
    };
  },
  watch: {
    clueId: {
      handler(newVal) {
        if (newVal) {
          this.getFollowUpRecordList();
        }
      },
      immediate: true,
    },
  },
  methods: {
    updateList() {
      this.getFollowUpRecordList();
    },
    getFollowUpRecordList() {
      // 跟进记录
      getFollowUpRecord({ clueId: this.clueId })
        .then((res) => {
          if (res && res.code === "SUCCESS") {
            this.followUpRecords = res.result || [];
          } else {
            this.$message.error(res.msg || "获取跟进记录失败");
            this.followUpRecords = [];
          }
        })
        .catch((error) => {
          // console.error("获取跟进记录出错:", error);
          this.$message.error("获取跟进记录出错");
          this.followUpRecords = [];
        });
      // 获取最新一次指派记录
      getLatestAssignClue({ clueId: this.clueId }).then((res) => {
        if (res && res.code === "SUCCESS") {
          this.latestAssignClue = res.result || "";
        } else {
          this.$message.error(res.msg || "获取失败");
          this.latestAssignClue = "";
        }
      });
      // 指派路径
      getAssignPath({ clueId: this.clueId }).then((res) => {
        if (res && res.code === "SUCCESS") {
          this.assignPath = res.result || { assignClueList: [] };
        } else {
          this.$message.error(res.msg || "获取指派路径失败");
          this.assignPath = { assignClueList: [] };
        }
      });
    },
    addRecord() {
      this.showRode = true;
    },
    addFollowRecord() {
      this.$refs.addFollowRecord.openDialog(this.selectedRow);
    },
  },
};
</script>

<style lang="scss" scoped>
.tracke-info {
  overflow: scroll;
  width: 100%;
  font-family: puhuiti;
  .road {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-content: center;
    margin-bottom: 16px;
  }
  .record-form {
    margin-top: 15px;
    margin-bottom: 20px;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .form-content {
      display: flex;

      .info-item {
        font-size: 14px;
        margin-right: 40px;
        .label {
          color: #333;
          display: inline-block;
          text-align: left;
          margin-right: 6px;
        }
        .value {
          color: #333;
        }
        .progress-tag {
          padding: 2px 8px;
          border-radius: 2px;
          font-size: 12px;

          &.no-follow {
            color: #ff9900;
          }
        }
      }
    }
  }

  .process-records {
    // background-color: #273047;
    border-radius: 4px;
    padding: 20px;
  }
  .records-header {
    margin-bottom: 20px;
    font-size: 14px;

    .record-count {
      font-family: puhuiti;
      color: #16d0ff;
      font-size: 14px;
      font-weight: bold;
    }
  }

  .timeline {
    position: relative;

    .timeline-item {
      display: flex;
      padding-bottom: 30px;
      position: relative;

      &:last-child {
        padding-bottom: 0;

        .time-line {
          display: none;
        }
      }

      .time-point {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #3370ff;
        margin-top: 10px;
        z-index: 2;

        &.active {
          box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.2);
        }
      }

      .time-line {
        position: absolute;
        left: 5px;
        top: 20px;
        bottom: 0;
        width: 2px;
      }

      .timeline-content {
        flex: 1;
        margin-left: 20px;
        padding-top: 7px;
        .timeline-header {
          display: flex;
          justify-content: flex-start;
          margin-bottom: 15px;

          .date-time {
            color: #a8abb2;
            font-size: 14px;
          }

          .status {
            margin-left: 16px;
            display: flex;
            align-items: center;

            &.completed {
              .status-text {
                background: rgba(224, 72, 72, 0.2);
                color: #e04848;
              }

              .status-desc {
                color: #8392a5;
                margin-left: 5px;
                font-size: 12px;
              }
            }

            &.success {
              .status-text {
                background: rgba(76, 193, 105, 0.2);
                color: #4cc169;
              }
            }

            .status-text {
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
            }
          }
        }

        .timeline-body {
          padding: 16px;
          background: rgba(216, 216, 216, 0.08);
          border-radius: 6px;
          .info-item {
            margin-bottom: 15px;
            // line-height: 1.5;
            font-family: puhuiti;
            font-size: 14px;
            .label {
              color: #333;
              margin-right: 10px;
              min-width: 120px;
              display: inline-block;
              width: 100px;
              text-align: left;
            }

            .value {
              color: #a8abb2;
            }
          }
        }
      }
    }
  }
}
// 添加指派路径抽屉相关样式
.assign-path-drawer {
  .assign-path-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0 20px;
  }

  .timeline-container {
    flex: 1;
    overflow-y: auto;
    margin-top: 20px;
  }

  .timeline-item {
    position: relative;
    padding-left: 40px;
    margin-bottom: 30px;

    &:last-child {
      margin-bottom: 0;
    }

    &::before {
      content: "";
      position: absolute;
      left: 20px;
      top: 30px;
      bottom: -30px;
      width: 2px;
      background: #3370ff;
    }

    &:last-child::before {
      display: none;
    }

    .time-point {
      position: absolute;
      left: 13px;
      top: 10px;
      width: 16px;
      height: 16px;
      background: #09a1f3;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .dot-circle {
        width: 6px;
        height: 6px;
        background: #09a1f3;
        border-radius: 50%;
      }
    }

    .timeline-date {
      font-size: 16px;
      color: #09a1f3;
      padding-top: 7px;
      margin-bottom: 10px;
    }

    .timeline-info {
      padding: 15px;
      border-radius: 4px;

      .assign-info,
      .follow-info {
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #333;
          margin-right: 10px;
        }

        .value {
          color: #a8abb2;
        }
      }
    }
  }

  .drawer-footer {
    padding: 20px 0;
    display: flex;
    justify-content: center;
  }
}

::v-deep .assign-path-drawer {
  .el-drawer__header {
    padding: 15px 20px;
    margin-bottom: 0;
    font-size: 16px;
    color: #333;
    font-weight: 500;
  }

  .el-drawer__body {
    padding: 0;
    overflow: hidden;
  }
}
</style>
