<template>
  <!-- 搜索 -->
  <div v-if="examine">
    <div v-if="showcontent">
      <industry />
    </div>
    <div
      v-else
      class="nodata"
    >
      <div class="con">
        <img src="https://static.idicc.cn/cdn/SSO/zhaoshangnodata.png">
        <span class="p1">暂无产业链权限</span>
        <span class="p2">请联系机构管理员配置权限功能</span>
      </div>
    </div>
  </div>
</template>

<script>
import industry from "./components/360/index.vue";
// import search from "./components/search.vue";
// import smart from "./components/smart.vue";
import { nameAssociateAPI, homeListAPI } from "../apiUrl";
export default {
  name: "IntelligentSearch",
  components: {
    // search,
    // smart,
    industry,
  },
  data() {
    return {
      input: "", //输入框内容
      initial: "2",
      befrom: "0",
      associationalword: [],
      time: null,
      enterpriseID: "",
      isParticulars: 1,
      examine: false,
      showcontent: false,
    };
  },
  created() {
    this.viewdetails();
  },
  methods: {
    async viewdetails() {
      try {
        const res = await homeListAPI();
        if (res.result == null || res.result.length < 1) {
          this.showcontent = false;
        } else {
          this.showcontent = true;
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.examine = true;
      }
    },
    search() {
      if (this.input == "") {
        return this.$message.error("搜索条件不能为空");
      }
      this.initial = 1;
    },
    goback() {
      this.isParticulars = 1;
    },
    godetails(item) {
      this.enterpriseID = item.enterpriseId;
      this.isParticulars = 2;
    },
    async associate() {
      if (this.time != null) {
        clearTimeout(this.time);
      }
      this.time = setTimeout(async () => {
        const res = await nameAssociateAPI({
          keyword: this.input,
        });
        this.associationalword = res.result;
      }, 100);
    },
    // 回到之前页面
    comeback(i) {
      this.initial = i;
    },
    //回到我自己
    regression() {
      this.initial = 0;
    },
    // 从主页进入高级智搜
    advanced() {
      this.befrom = 0;
      this.initial = 2;
    },
    //从普通搜索进入高级智搜
    upgrade() {
      this.befrom = 1;
      this.initial = 2;
    },
  },
};
</script>

<style lang="scss" scoped>
.nodata {
  min-width: 1110px;
  height: 100vh;
  display: flex;
  justify-content: center;
  .con {
    margin-top: 10%;
    width: 170px;
    height: 300px;
    display: flex;
    align-items: center;
    flex-direction: column;
    img {
      width: 160px;
      height: 165.03px;
    }
    .p1 {
      margin-top: 38px;
      font-size: 14px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
    .p2 {
      margin-top: 16px;
      font-size: 12px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
::v-deep {
  .el-input__inner:focus {
    border-color: #1592fc;
  }
}
.asso {
  margin-top: 1px;
  width: 482px;
  border-radius: 4px 0px 0px 4px;
  border: 1px solid #dddddd;
  background-color: #fff;
  .alone {
    cursor: pointer;
    height: 30px;
    display: flex;
    font-size: 14px;
    align-items: center;
    padding-left: 10px;
  }
  .alone:hover {
    background-color: #dee0e3;
    font-size: 14px;
  }
}

.scan {
  // background: #f5f6f7;
  min-height: 100vh;
  box-sizing: border-box;
  min-width: 1110px;

  &-top {
    padding: 16px 0px;
    // background: #fff;
    padding-bottom: 12px;
  }

  .search {
    // background-color: #fff;
    height: 120vh;
    margin-top: 4%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .top {
      display: flex;
      margin-top: 10%;
      margin-right: 4%;

      .idicc {
        width: 140px;
        height: 46px;
        margin-right: 27px;
      }

      .title {
        width: 300px;
        //background-color:red;
        border-left: 1px solid #d9dce0;
        font-size: 30px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        display: flex;
        text-align: center;
        align-items: center;

        .span {
          margin-left: 28px;
        }
      }
    }

    .buttom {
      display: flex;
      margin-top: 50px;

      .wisdom-input {
        width: 482px;
        height: 40px;
        border-radius: 4px 0px 0px 4px;
      }

      .searcher {
        width: 70px;
        height: 40px;
        background-color: #3370ff;
        border: 1px solid #3370ff;
        border-radius: 0px 4px 4px 0px;
        cursor: pointer;
        font-size: 16px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .advanced {
        color: #3370ff;
        display: flex;
        font-weight: 400;
        font-size: 16px;
        margin-left: 17px;
      }
    }
  }
}
</style>