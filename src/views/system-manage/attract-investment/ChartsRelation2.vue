<template>
  <div>
    <div class="box">
      <div
        v-if="!nodata"
        style="width: 100%;height: 100%;"
      >
        <div
          id="it"
          v-loading="echartsLoading"
          class="atlas"
        />
      </div>
      <div
        v-else
        class="nodata"
      >
        <div class="con">
          <img src="https://static.idicc.cn/cdn/pangu/vacancy.png">
          <span class="p1">暂无数据</span>
        </div>
      </div>
    </div>
  </div>
</template>
    
  <script>
  import * as echarts from "echarts";
import { proBusinessImageAPI } from "./apiUrl";
  export default {
    name: "AtlasC",
    data() {
      return {
        nodata: false,
        echartsLoading: false,
        Fullscreenor:false,
        icon: require("@/views/system-manage/attract-investment/img/amplification.png"),
        icon2: require("@/views/system-manage/attract-investment/img/reduce.png"),
        isecharts:true
      };
    },
    created() {},
    mounted() {
      this.open();
    },
    methods: {
      open() {
          this.atlasFn();
      },
      async atlasFn() {
        try {
          this.echartsLoading = true;
          const res = await proBusinessImageAPI({
            count: 30,
          });
          let data = res.result.dataSet.map((item) => {
            let itemStyle = {
              normal: {
                color: "#ffca00",
              },
            };
            if (item.type == 1) {
              itemStyle.normal.color = "#FDA41F";
            } else if (item.type == 2) {
              itemStyle.normal.color = "#3370FF";
            } else {
              itemStyle.normal.color = "#4EC30F";
            }
            return {
              name: item.name,
              id: item.uniqueId,
              itemStyle,
            };
          });
          let links = res.result.relationSet.map((item) => {
            let lineStyle = {
              normal: {
                color: "#ffca00",
              },
            };
            if (item.targetType == 1) {
              lineStyle.normal.color = "#FDA41F";
            } else if (item.targetType == 2) {
              lineStyle.normal.color = "#3370FF";
            } else {
              lineStyle.normal.color = "#4EC30F";
            }
            return {
              source: item.source,
              target: item.target,
              name: item.relation == null ? "" : item.relation,
              lineStyle,
            };
          });
          if (links.length == 0 && data.length == 0) {
            this.nodata = true;
          }
          //console.log(data, links);
          var chartDom = document.getElementById("it");
          var myChart = echarts.init(chartDom);
          var option;
          option = {
            tooltip: {
              formatter: function (x) {
                return x.data;
              },
            },
            series: [
              {
                type: "graph",
                layout: "force",
                symbolSize: 120,
                roam: true,
                zoom: 0.3,
                scaleLimit: {
                  min: 0.15,
                  max: 1,
                },
                emphasis: {
                  focus: "adjacency",
                  lineStyle: {
                    width: 5,
                  },
                },
                edgeSymbol: ["circle", "arrow"],
                edgeSymbolSize: [6, 6],
                edgeLabel: {
                  color: "#fff",
                  normal: {
                    show: true,
                    formatter: function (x) {
                      return x.data.name;
                    },
                    textStyle: {
                      fontSize: 12,
                    },
                  },
                },
                force: {
                  repulsion: 20000,
                  edgeLength: [200, 200],
                  avoidOverlap: true,
                  layoutAnimation: false,
                },
                //draggable: true,
                label: {
                  normal: {
                    show: true,
                    color: "#fff",
                    fontSize: 11,
                    textStyle: {},
                    formatter: function (params) {
                      var splitText = [];
                      var str = params.name;
                      for (var i = 0, len = str.length; i < len; i += 4) {
                        splitText.push(str.substr(i, 4));
                      }
                      return splitText.join("\n");
                    },
                  },
                },
                data,
                links,
              },
            ],
          };
          myChart.on('zoom', function(params) {
            //console.log(params,'params');
            myChart.setOption(option);
          });
          option && myChart.setOption(option,true);
        } catch (error) {
          console.log(error);
        } finally {
          this.echartsLoading = false;
        }
      },
    },
  };
  </script>
    
    <style scoped lang="scss">
  .box {
    width: 100vw;
    height: 100vh;
  
    .atlas {
      width: 100%;
      height: 100%;
    }
    .img{
      width: 30px;
      height: 30px;
      float: right;
      cursor: pointer;
    }
    .nodata {
      display: flex;
      width: 200px;
      height: 600px;
      align-items: center;
      justify-content: center;
      .con {
        margin-top: 10%;
        width: 170px;
        height: 300px;
        display: flex;
        align-items: center;
        flex-direction: column;
        img {
          width: 216px;
          height: 177px;
        }
        .p1 {
          margin-top: 38px;
          font-size: 14px;
          font-family: Source Han Sans CN-Medium, Source Han Sans CN;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }
  }
  </style>