<template>
  <div>
    <div
      style="height: calc(100vh)"
      @click="unhighlight"
    > 
      <h1
        v-if="nodata"
        class="nodata"
      >
        暂无数据
      </h1>
      <RelationGraph
        v-if="!nodata"
        ref="seeksRelationGraph"
        :options="graphOptions"
        :on-node-click="onNodeClick"
        :on-line-click="onLineClick"
      />
    </div>
  </div>
</template>

<script>
// relation-graph也支持在main.js文件中使用Vue.use(RelationGraph);这样，你就不需要下面这一行代码来引入了。
import RelationGraph from "relation-graph";
import { proBusinessImageAPI } from "./apiUrl";
export default {
  name: "DemoS",
  components: { RelationGraph },
  data() {
    return {
      graphOptions: {
        allowSwitchLineShape: true,
        allowSwitchJunctionPoint: true,
        defaultJunctionPoint: "border",
        defaultFocusRootNode: false,
        hideNodeContentByZoom: true,
        downloadImageFileName:"亲商管理图谱",
        // 这里可以参考"Graph 图谱"中的参数进行设置
        layouts:  [
              {
                layoutName:  'center',
                distance_coefficient:2
              }
          ]
      },
      nodata:false
    };
  },
  mounted() {
    this.showSeeksGraph();
  },
  methods: {
    unhighlight(){
      const _all_nodes = this.$refs.seeksRelationGraph.getInstance().getNodes();
      const allLinks = this.$refs.seeksRelationGraph.getLinks();
      _all_nodes.forEach((thisNode) => {
        thisNode.opacity =  1;
      });
      allLinks.forEach(link => {
        link.relations.forEach(line => {
          if (line.data.orignColor) {
            line.color = line.data.orignColor;
          }
          if (line.data.orignFontColor) {
            line.fontColor = line.data.orignColor;
          }
          if (line.data.orignLineWidth) {
            line.lineWidth = line.data.orignLineWidth;
          }
        });
      });
      this.$refs.seeksRelationGraph.getInstance().dataUpdated();
    },
   async  showSeeksGraph() {
      const res = await proBusinessImageAPI({
          count: 30,
        });
        let data = res.result.dataSet.map((item) => {
          let color = "#ffca00"
          if (item.type == 1) {
            color = "#FDA41F";
          } else if (item.type == 2) {
            color = "#3370FF";
          } else {
            color = "#4EC30F";
          }
          return {
            text: item.name,
            id: item.uniqueId,
            color,
            borderColor:color
          };
        });
        //console.log(data,'data');
        let links = res.result.relationSet.map((item) => {
          let color = "#ffca00"
          if (item.targetType == 1) {
            color = "#FDA41F";
          } else if (item.targetType == 2) {
            color = "#3370FF";
          } else {
            color = "#4EC30F";
          }
          return {
            from: item.source,
            to: item.target,
            text: item.relation == null ? "" : item.relation,
            color,
            //useTextPath:true
          };
        });
        //console.log(links,'links'); 
        if(links.length==0 && data.length==0){
          this.nodata=true
        }
      const __graph_json_data = {
        rootId: "a",
        nodes:data,
        lines:links,
      };
      // 以上数据中的node和link可以参考"Node节点"和"Link关系"中的参数进行配置
      this.$refs.seeksRelationGraph.setJsonData(
        __graph_json_data,
        (seeksRGGraph) => {
          ////console.log(seeksRGGraph);
          // Called when the relation-graph is completed
        }
      );
    },
    onNodeClick(nodeObject, $event) {
      //console.log($event);
      //console.log("onNodeClick:", nodeObject);
      let names = nodeObject.targetFrom.map((item) => {
        return item.text;
      });
      let namess = nodeObject.targetNodes.map((item) => {
        return item.text;
      });
      names = [...names, ...namess, nodeObject.text];
      //console.log(names);
      const _all_nodes = this.$refs.seeksRelationGraph.getInstance().getNodes();
      const allLinks = this.$refs.seeksRelationGraph.getLinks();
      //const _all_links = this.$refs.seeksRelationGraph.getInstance().getLinks();
      _all_nodes.forEach((thisNode) => {
        //console.log(thisNode, "thisNode");
        let _isHideThisLine = true;
        if (names.includes(thisNode.text)) {
          _isHideThisLine = false;
        } else {
          _isHideThisLine = true;
        }
        thisNode.opacity = _isHideThisLine ? 0.1 : 1;
      });
      allLinks.forEach(link => { // 还原所有样式
        link.relations.forEach(line => {
          if (line.data.orignColor) {
            line.color = line.data.orignColor;
          }
          if (line.data.orignFontColor) {
            line.fontColor = line.data.orignColor;
          }
          if (line.data.orignLineWidth) {
            line.lineWidth = line.data.orignLineWidth;
          }
        });
      });
      allLinks.filter(link => (link.fromNode === nodeObject || link.toNode === nodeObject)).forEach(link => {
        link.relations.forEach(line => {
          //console.log('line:', line);
          line.data.orignColor = line.color;
          line.data.orignFontColor = line.fontColor || line.color;
          line.data.orignLineWidth = line.lineWidth || 1;
          line.color = '#1c90ff';
          line.fontColor = '#1c90ff';
          line.lineWidth = 3;
        });
      });
      this.$refs.seeksRelationGraph.getInstance().dataUpdated();
    },
    onLineClick(lineObject, $event) {
      //console.log($event);
      //console.log("onLineClick:", lineObject);
    },
  },
};
</script>
<style lang="scss" scoped>
.nodata{
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>