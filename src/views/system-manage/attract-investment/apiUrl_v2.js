// 产业金脑瘦身接口在这
import request from '@/utils/request'
/**
* 企业详情-基本信息
* @returns 
*/
export function enterprisedetailAPI_V2(data) {
  return request({
      url:'/admin/enterprise/detail/v2',
      method: 'POST',
      data
  })
}
/**
* 企业详情-查询企业招商策略
* @returns 
*/
export function getByEnterpriseIdAPI_V2(params) {
  return request({
      url:'/admin/enterprise/strategy',
      method: 'get',
      params
  })
}


/**
* 强延补推荐情况和新增企业推荐情况统计
* @returns
*/
export function recommendedStatisticsAPI_V2(params) {
  return request({
      url:'/merchants/investment_enterprise/newRecommendedStatistics',
      method: 'GET',
      params
  })
}

  /**
* 获取当前登陆用户关联的推荐企业特征
* @returns
*/
export function characteristicAPI_V2(params) {
  return request({
      url:'/merchants/investment_enterprise/newCharacteristic',
      method: 'GET',
      params
  })
}

/**
* 获取当前登陆用户所在机构和当前用户拥有权限的产业链关联的推荐企业类型数量统计
* @returns
*/
export function merchantstypeCountAPI_V2(params) {
  return request({
      url:'/merchants/investment_enterprise/fiveTypeCount',
      method: 'GET',
      params
  })
}

 /**
* 强延补推荐情况和新增企业推荐情况统计
* @returns
*/
export function intentionRateAPI_V2(params) {
  return request({
      url:'/merchants/investment_enterprise/newIntentionRate',
      method: 'GET',
      params
  })
}

  /**
* 招商企业推荐列表
* @returns 
*/
export function merchantsAPI_V2(data) {
  return request({
      url:'/merchants/investment_enterprise/search/v2',
      method: 'POST',
      data
  })
}

/**
* 根据指定招商模型获取对应的推荐企业列表
* @returns 
*/
export function listByModelTypeAPI_V2(data) {
  return request({
      url:'/merchants/investment_enterprise/listByModelType/v2',
      method: 'POST',
      data
  })
}

 /**
* 推荐企业产业链分布图
* @returns 
*/
export function industryChainDistributionAPI_V2(data) {
  return request({
      url:'/merchants/investment_enterprise/industryChainDistribution/v2',
      method: 'POST',
      data
  })
}

/**
* 推荐企业产业链分布列表
* @returns 
*/
export function industryChainDistributionListAPI_V2(data) {
  return request({
      url:'/merchants/investment_enterprise/industryChainDistributionList/v2',
      method: 'POST',
      data
  })
}

/**
* 推荐企业地区分布图
* @returns 
*/
export function areaDistributionAPI_V2(data) {
  return request({
      url:'/merchants/investment_enterprise/areaDistribution/v2',
      method: 'POST',
      data
  })
}
/**
* 推荐企业地区分布列表
* @returns 
*/
export function areaDistributionListAPI_V2(data) {
  return request({
      url:'/merchants/investment_enterprise/areaDistributionList/v2',
      method: 'POST',
      data
  })
}
 /**
* 强延补推荐情况和新增企业推荐情况统计
* @returns
*/
export function getModelTypesAPI(params) {
  return request({
      url:'/pg/admin/enterprise/getModelTypes',
      method: 'GET',
      params
  })
}
 /**
 * 招商情报统计
 * @param {*} data
 * @returns
 */
export function InformationStatisticsV2API(params) {
  return request({
    url:  "/pg/admin/business/enterprise/attractInvestmentInformationStatistics/v2",
    method: "GET",
    params,
  });
}