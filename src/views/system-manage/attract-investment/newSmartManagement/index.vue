<template>
  <!-- 我的企业管理 -->
  <div v-if="examine">
    <div v-if="showcontent">
      <div class="title">
        <div class="scan">
          <div class="scan-top">
            <div class="scan-tab-con">
              <div class="scan-tab">
                <div
                  class="scan-tab-list"
                  :class="whichs == 0 ? 'on' : ''"
                  @click="cut(0)"
                >
                  意向企业
                </div>
                <div
                  v-if="jurisdiction.isWodeshenpi"
                  class="scan-tab-list"
                  :class="whichs == 1 ? 'on' : ''"
                  @click="cut(1)"
                >
                  我的审批
                </div>
                <div
                  class="scan-tab-list"
                  :class="whichs == 2 ? 'on' : ''"
                  @click="cut(2)"
                >
                  我的收藏
                </div>
                <div
                  v-if="jurisdiction.isKanban"
                  class="scan-tab-list"
                  :class="whichs == 3 ? 'on' : ''"
                  @click="cut(3)"
                >
                  数据看板
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="whichs !== 3"
        class="list"
      >
        <!-- 根据tab切换显示不同组件 -->
        <intention-company
          v-if="whichs === 0"
          :is-zhipai="jurisdiction.isZhipai"
        />
        <my-approval v-if="whichs === 1" />
        <my-collection v-if="whichs === 2" />
      </div>

      <div
        v-if="whichs === 3"
        class="tabDashboard"
      >
        <div class="tabs">
          <div
            v-if="jurisdiction.isFenxi"
            class="tab1"
            :class="state === '1' ? 'active' : 'default'"
            @click="chantTab('1')"
          >
            统计分析
          </div>
          <div
            v-if="jurisdiction.isZhoubao"
            class="tab2"
            :class="state === '2' ? 'active' : 'default'"
            @click="chantTab('2')"
          >
            周报
          </div>
        </div>
        <data-board v-if="state === '1' && jurisdiction.isFenxi" />
        <WeeklyReport
          v-if="state === '2' && jurisdiction.isZhoubao"
          :is-xiezhoubao="jurisdiction.isXiezhoubao"
        />
      </div>
    </div>
    <div
      v-else
      class="nodata"
    >
      <div class="con">
        <img src="https://static.idicc.cn/cdn/SSO/zhaoshangnodata.png">
        <span class="p1">暂无产业链权限</span>
        <span class="p2">请联系机构管理员配置权限功能</span>
      </div>
    </div>
  </div>
</template>

<script>
// 导入对应的组件
import IntentionCompany from "./pageList/IntentionCompany.vue";
import MyApproval from "./pageList/MyApproval.vue";
import MyCollection from "./pageList/MyCollection.vue";
import DataBoard from "./pageList/DataBoard.vue";
import WeeklyReport from "./pageList/WeeklyReport.vue";
import { queryResourceByType } from "@/api/xiaoAI";
import { homeListAPI } from "../apiUrl";
export default {
  name: "IntentionQ",
  components: {
    IntentionCompany,
    MyApproval,
    MyCollection,
    DataBoard,
    WeeklyReport,
  },
  props: {},
  data() {
    return {
      whichs: 0,
      examine: false,
      showcontent: false,
      tabList: [
        {
          name: "意向企业",
          id: 0,
        },
        {
          name: "我的审批",
          id: 1,
        },
        {
          name: "我的收藏",
          id: 2,
        },
        {
          name: "数据看板",
          id: 3,
        },
      ],
      jurisdiction: {
        isZhipai: false,
        isWodeshenpi: false,
        isShenpi: false,
        isKanban: false,
        isZhoubao: false,
        isXiezhoubao: false,
        isFenxi: false,
      },
      isParticulars: 1,
      enterpriseID: "", //企业id
      rowId: "", //线索id
      state: "1",
    };
  },
  created() {
    this.getJurisdictionFn();
    this.getList();
  },
  methods: {
    async getList() {
      try {
        const res = await homeListAPI();
        if (res.result == null || res.result.length < 1) {
          this.showcontent = false;
        } else {
          this.showcontent = true;
        }
      } finally {
        this.examine = true;
      }
    },
    async getJurisdictionFn() {
      const res = await queryResourceByType({
        type: 3,
      });
      res.result.map((item) => {
        if (item.resourceCode == "ClueAssignmentButton") {
          //线索指派
          this.jurisdiction.isZhipai = true;
        }
        if (item.resourceCode == "Myapproval") {
          //我的审批
          this.jurisdiction.isWodeshenpi = true;
        }
        if (item.resourceCode == "DataBoard") {
          //数据看板
          this.jurisdiction.isKanban = true;
        }
        if (item.resourceCode == "weeklyNewspaper") {
          //周报
          this.jurisdiction.isZhoubao = true;
        }
        if (item.resourceCode == "WriteWeeklyReportButton") {
          //写周报
          this.jurisdiction.isXiezhoubao = true;
        }
        if (item.resourceCode == "statisticAnalysis") {
          //统计分析
          this.jurisdiction.isFenxi = true;
        }
      });
    },
    // 进入详情
    godel(id, enterpriseId, go) {
      this.rowId = id;
      this.enterpriseID = enterpriseId;
      this.isParticulars = go;
      document.documentElement.scrollTop = 0;
    },
    godelenterprise(id, go) {
      this.enterpriseID = id;
      this.isParticulars = go;
      document.documentElement.scrollTop = 0;
    },
    cut(id) {
      this.whichs = id;
    },
    chantTab(state) {
      this.state = state;
    },
  },
};
</script>

<style lang="scss" scoped>
.nodata {
  min-width: 88.5vw;
  height: 100vh;
  display: flex;
  justify-content: center;

  .con {
    margin-top: 10%;
    width: 170px;
    height: 300px;
    display: flex;
    align-items: center;
    flex-direction: column;

    img {
      width: 160px;
      height: 165.03px;
    }

    .p1 {
      margin-top: 38px;
      font-size: 14px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }

    .p2 {
      margin-top: 16px;
      font-size: 12px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}

.list {
  // margin: 16px 0;
  padding: 20px;
}
.tabDashboard {
  padding: 20px;
}
.tabs {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  .tab1.active,
  .tab2.active {
    background: #3370ff;
    color: #ffffff;
    border: 0;
  }

  .tab1,
  .tab2 {
    /* 自动布局子元素 */
    color: #3f4a59;
    width: 116px;
    height: 30px;
    border-radius: 15px;
    z-index: 0;
    background: #ffffff;
    border: 1px solid #ced4db;
    display: flex;
    font-size: 14px;
    justify-content: center;
    align-items: center;
    margin-right: 16px;
  }
}
.iconarrow {
  color: #666;
  font-size: 20px;
  background: white;
  padding: 2px;
  width: 25px;
  height: 25px;
  border-radius: 16px;
}

.scan {
  width: 100%;
  // margin-top: 24px;
  // background-color: #fff;
  &-top {
    padding: 16px 0px;
    // background: #fff;
    padding-bottom: 12px;
    //border-bottom: 1px solid #E8E8E8
  }
  &-tab {
    display: flex;
    padding-left: 16px;
    &-con {
      display: flex;
      justify-content: space-between;
    }
    &-select {
      display: flex;
    }
    &-list {
      margin-right: 64px;
      font-size: 16px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      cursor: pointer;
      &.on {
        font-weight: bold;
        color: #3370ff;
        position: relative;
        &::after {
          content: "";
          width: 28px;
          height: 2px;
          background: #3370ff;
          position: absolute;
          left: 18px;
          bottom: -8px;
        }
      }
    }
  }
  &-con {
    margin: 16px 24px;
  }
}
</style>
