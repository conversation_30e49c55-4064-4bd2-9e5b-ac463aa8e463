<template>
  <div class="weekly-report">
    <div class="header">
      <div class="title">
        当前共 {{ total }} 条周报
      </div>
      <div class="search-box">
        <el-select
          v-model="value"
          placeholder="请选择"
          @change="handleSearch"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>

        <el-button
          v-if="isXiezhoubao"
          type="primary"
          size="small"
          @click="openWriteReport"
        >
          写周报
        </el-button>
      </div>
    </div>

    <div
      v-if="Number(total) > 0"
      class="report-list"
    >
      <div
        v-for="(group, index) in reportGroups"
        :key="index"
        class="report-list-item"
      >
        <!-- 日期分组标题 -->
        <div class="date-group">
          <div class="date-label">
            {{ group.date }} 周报
          </div>
        </div>
        <!-- 周报条目 -->
        <div
          v-for="(item, idx) in group.items"
          :key="idx"
          class="report-item"
        >
          <div class="avatar">
            <img
              :src="item.avatar"
              alt="头像"
            >
          </div>
          <div class="report-content">
            <div class="report-title">
              <span class="reporter-name">{{ item.name }}的周报</span>
              <span class="report-time">{{ item.time }}</span>
            </div>
            <div class="report-body">
              {{ item.content }}
            </div>
          </div>
          <!-- 添加操作按钮 -->
          <div class="report-actions">
            <el-button
              type="text"
              size="small"
              @click="showDetails(item)"
            >
              详情
            </el-button>
            <el-button
              v-if="userId === item?.userId"
              type="text"
              size="small"
              class="delete-btn"
              @click="confirmDelete(item)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <el-empty :image-size="200" />
    </div>

    <!-- 分页器 -->
    <!-- <div class="pagination">
      <el-pagination
        background
        layout="prev, pager, next, jumper, sizes"
        :total="total"
        :current-page.sync="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div> -->

    <!-- 添加加载状态 -->
    <div
      v-if="loading"
      v-loading="loading"
      class="loading-wrapper"
    />

    <!-- 详情抽屉 -->
    <el-drawer
      :visible.sync="drawerVisible"
      title="周报详情"
      size="35%"
      :with-header="true"
      direction="rtl"
      custom-class="report-drawer"
    >
      <div
        v-if="currentReport"
        class="report-detail"
      >
        <div class="detail-header">
          <h2>{{ currentReport.name }}的周报</h2>
          <div class="detail-time">
            {{ currentReport.time }}
          </div>
        </div>

        <div class="detail-section">
          <h3>系统生成内容</h3>
          <div class="data-table">
            <table>
              <tr>
                <th>纳入意向企业数</th>
                <th>外部企业录入数</th>
                <th>企业跟踪数</th>
                <th>企业委托数</th>
                <th>签约成功数</th>
                <th>签约失败数</th>
              </tr>
              <tr>
                <td>{{ currentReport.stats?.totalEnterprise || 2 }}</td>
                <td>{{ currentReport.stats?.ownEnterprise || 2 }}</td>
                <td>{{ currentReport.stats?.withFollowing || 2 }}</td>
                <td>{{ currentReport.stats?.withEntrust || 0 }}</td>
                <td>{{ currentReport.stats?.stateSign || 0 }}</td>
                <td>{{ currentReport.stats?.stateFail || 0 }}</td>
              </tr>
            </table>
          </div>
        </div>

        <div class="detail-section">
          <h3>新增项目信息</h3>
          <div class="detail-text">
            {{ currentReport.newProject || "这是一段测试数据" }}
          </div>
        </div>

        <div class="detail-section">
          <h3>来访与考察</h3>
          <div class="detail-text">
            {{ currentReport.interview || "" }}
          </div>
        </div>

        <div class="detail-section">
          <h3>其他工作</h3>
          <div class="detail-text">
            {{ currentReport.other || "" }}
          </div>
        </div>

        <div class="detail-section">
          <h3>下周计划</h3>
          <div class="detail-text">
            {{ currentReport.nextWeek || "" }}
          </div>
        </div>

        <div class="detail-section">
          <h3>需领导协助事项</h3>
          <div class="detail-text">
            {{ currentReport.assistance || "" }}
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="确认删除"
      :visible.sync="deleteDialogVisible"
      width="30%"
    >
      <span>确定要删除该周报吗？此操作不可逆。</span>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button
          type="danger"
          @click="handleDelete"
        >确定</el-button>
      </span>
    </el-dialog>

    <!-- 写周报抽屉 -->
    <el-drawer
      :visible.sync="writeReportDrawerVisible"
      title="写周报"
      size="35%"
      :with-header="true"
      direction="rtl"
      custom-class="report-drawer"
    >
      <div class="write-report-form">
        <h3 class="section-title">
          系统生成内容
        </h3>
        <div class="data-table">
          <table>
            <tr>
              <th>纳入意向企业数</th>
              <th>外部企业录入数</th>
              <th>企业跟踪数</th>
              <th>企业委托数</th>
              <th>签约成功数</th>
              <th>签约失败数</th>
            </tr>
            <tr>
              <td>{{ newReport.stats.totalEnterprise }}</td>
              <td>{{ newReport.stats.ownEnterprise }}</td>
              <td>{{ newReport.stats.withFollowing }}</td>
              <td>{{ newReport.stats.withEntrust }}</td>
              <td>{{ newReport.stats.stateSign }}</td>
              <td>{{ newReport.stats.stateFail }}</td>
            </tr>
          </table>
        </div>

        <div class="form-section">
          <h3 class="section-title">
            <span class="required-star">*</span>
            新增项目信息
          </h3>
          <el-input
            v-model="newReport.newProjects"
            type="textarea"
            rows="3"
            placeholder="请输入"
          />
        </div>

        <div class="form-section">
          <h3 class="section-title">
            <span class="required-star">*</span>
            来访与考察
          </h3>
          <el-input
            v-model="newReport.visits"
            type="textarea"
            rows="3"
            placeholder="请输入："
          />
        </div>

        <div class="form-section">
          <h3 class="section-title">
            <span class="required-star">*</span>
            其他工作
          </h3>
          <el-input
            v-model="newReport.otherWork"
            type="textarea"
            rows="3"
            placeholder="请输入："
          />
        </div>

        <div class="form-section">
          <h3 class="section-title">
            <span class="required-star">*</span>
            下周计划
          </h3>
          <el-input
            v-model="newReport.nextWeekPlan"
            type="textarea"
            rows="3"
            placeholder="请输入："
          />
        </div>

        <div class="form-section">
          <h3 class="section-title">
            <span class="required-star">*</span>
            需领导协助事项
          </h3>
          <el-input
            v-model="newReport.leadershipHelp"
            type="textarea"
            rows="3"
            placeholder="请输入："
          />
        </div>

        <div class="form-actions">
          <el-button @click="writeReportDrawerVisible = false">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="submitReport"
          >
            提交周报
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  getWeeklyReports,
  getWeeklyReportDetial,
  getWeeklyReportDelete,
  getWeeklyStats,
  addWeeklyReport,
} from "@/api/weeklyReport"; // 假设有这个 API 导入

export default {
  name: "WeeklyReport",
  props: {
    isXiezhoubao: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      options: [
        {
          value: "",
          label: "全部",
        },
        {
          value: "me",
          label: "我发出的",
        },
        {
          value: "all",
          label: "我收到的",
        },
        {
          value: "team",
          label: "我的团队",
        },
      ],
      value: "",
      searchText: "",
      currentPage: 1,
      pageSize: 10,
      total: 4,
      reports: [],
      loading: false,
      drawerVisible: false,
      deleteDialogVisible: false,
      currentReport: null,
      reportToDelete: null,
      userId: "",
      reportGroups: [],
      writeReportDrawerVisible: false,
      newReport: {
        stats: {
          totalEnterprise: 2,
          ownEnterprise: 2,
          withFollowing: 2,
          withEntrust: 0,
          stateSign: 0,
          stateFail: 0,
        },
        newProjects: "",
        visits: "",
        otherWork: "",
        nextWeekPlan: "",
        leadershipHelp: "",
      },
      defaultLogo:"https://static.idicc.cn/cdn/pangu/yonghu.png",
    };
  },
  created() {
    // 如果需要从API获取数据，可以取消下面注释
    this.fetchReports();

    // 使用mock数据时，设置总数
    this.total = this.calculateTotal();
  },
  methods: {
    // 计算总项目数
    calculateTotal() {
      return this.reportGroups.reduce(
        (total, group) => total + group.items.length,
        0
      );
    },

    // 获取周报数据
    async fetchReports() {
      let userId = JSON.parse(localStorage.getItem("userInfo") || "{}")
        ?.userId;
      this.userId = userId;
      try {
        this.loading = true;

        // 构建请求参数
        const params = {
          page: this.currentPage,
          pageSize: 999,
          scope: this.scope,
          // keyword: this.searchText
        };

        // 调用API获取数据
        const res = await getWeeklyReports(params);

        if (res && res.code === "SUCCESS") {
          // 处理返回的数据，按照日期分组
          this.processReportData(res.result.records);
          this.total = Number(res?.result?.total || 0) || 0;
        } else {
          this.$message.error(res?.message || "获取周报数据失败");
          this.reports = [];
          this.total = 0;
        }
      } catch (error) {
        console.error("获取周报列表出错:", error);
        this.$message.error("获取周报列表失败，请稍后重试");
        this.reports = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },

    // 处理周报数据，按日期分组
    processReportData(list) {
      if (!Array.isArray(list)) {
        this.reports = [];
        return;
      }

      // 将原始数据转换为需要的格式
      this.reports = list.map((item) => ({
        avatar: item.userhead || this.defaultLogo,
        name: item.username || "",
        time: item.reportDatetime || "",
        content: item.newProject || "",
        date: item.reportDate || "",
        ...item,
        // updateTitle: item.updateTitle || ''
      }));

      // 按日期分组
      const groups = {};
      this.reports.forEach((report) => {
        const dateKey = report.date;
        if (!groups[dateKey]) {
          groups[dateKey] = {
            date: report.date,
            // updateTitle: report.updateTitle,
            items: [],
          };
        }
        groups[dateKey].items.push(report);
      });

      // 转换为数组形式
      this.reportGroups = Object.values(groups);
    },

    // 搜索处理
    handleSearch(e) {
      this.currentPage = 1;
      this.scope = e;
      this.fetchReports();
    },

    // 清除搜索内容
    handleClear() {
      this.searchText = "";
      this.currentPage = 1;
      this.fetchReports();
    },

    // 页码变化
    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchReports();
    },

    // 每页条数变化
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.fetchReports();
    },

    // 显示周报详情
    async showDetails(report) {
      try {
        this.loading = true;

        // 调用获取详情API
        const res = await getWeeklyReportDetial({
          reportCode: report.reportCode, // 假设详情API需要id参数
        });

        if (res && res.code === "SUCCESS") {
          // 处理返回的详情数据
          this.currentReport = {
            ...report,
            ...res.result,
            // 确保基础信息存在
            name: res.result?.username || report.username || "未命名周报",
            time: res.result?.reportDatetime || report.reportDatetime || "",
            stats: res.result?.weeklyStats,
            // 详情字段
            // newProjects: res.result?.newProject || '',
            // visits: res.result?.visit || '',
            // otherWork: res.result?.otherWork || '',
            // nextWeekPlan: res.result?.nextWeekPlan || '',
            // leadershipHelp: res.result?.leadershipHelp || ''
          };
        } else {
          this.$message.error(res?.message || "获取周报详情失败");
          this.currentReport = { ...report };
        }
      } catch (error) {
        console.error("获取周报详情出错:", error);
        this.$message.error("获取周报详情失败，请稍后重试");
        this.currentReport = { ...report }; // 使用列表中的简略信息作为备选
      } finally {
        this.loading = false;
        this.drawerVisible = true;
      }
    },

    // 确认删除
    confirmDelete(report) {
      this.reportToDelete = report;
      this.deleteDialogVisible = true;
    },

    // 执行删除
    async handleDelete() {
      if (this.reportToDelete) {
        const res = await getWeeklyReportDelete({
          reportCode: this.reportToDelete.reportCode, // 假设详情API需要id参数
        });
        if (res.code === "SUCCESS") {
          // 在实际应用中，这里应该调用删除API
          // 模拟删除操作
          this.reportGroups.forEach((group) => {
            const index = group.items.findIndex(
              (item) =>
                item.name === this.reportToDelete.name &&
                item.time === this.reportToDelete.time
            );

            if (index !== -1) {
              group.items.splice(index, 1);

              // 如果组内没有项目了，移除整个组
              if (group.items.length === 0) {
                const groupIndex = this.reportGroups.findIndex(
                  (g) => g.date === group.date
                );
                if (groupIndex !== -1) {
                  this.reportGroups.splice(groupIndex, 1);
                }
              }
            }
          });

          // 更新总数
          this.total = this.calculateTotal();
          this.$message({
            type: "stateSign",
            message: "删除成功",
          });
        }
      }

      this.deleteDialogVisible = false;
      this.reportToDelete = null;
    },

    // 打开写周报抽屉
    openWriteReport() {
      getWeeklyStats().then((res) => {
        if (res.code === "SUCCESS") {
          // 重置表单
          this.newReport = {
            stats: {
              ...res.result,
            },
            newProjects: "",
            visits: "",
            otherWork: "",
            nextWeekPlan: "",
            leadershipHelp: "",
          };
          this.writeReportDrawerVisible = true;
        }
      });
    },

    // 提交周报
    submitReport() {
      // 表单验证
      if (
        !this.newReport.newProjects ||
        !this.newReport.visits ||
        !this.newReport.otherWork ||
        !this.newReport.nextWeekPlan ||
        !this.newReport.leadershipHelp
      ) {
        this.$message.warning("请填写所有必填项");
        return;
      }

      // 构建新周报数据

      const newReportItem = {
        assistance: this.newReport.leadershipHelp,
        interview: this.newReport.visits,
        newProject: this.newReport.newProjects,
        nextWeek: this.newReport.nextWeekPlan,
        other: this.newReport.otherWork,
      };

      addWeeklyReport(newReportItem).then((_res) => {
        if (_res.code === "SUCCESS") {
          // 关闭抽屉并显示成功消息
          this.writeReportDrawerVisible = false;
          this.$message.success("周报提交成功");
          this.fetchReports();
        } else {
          this.$message.warning("周报提交失败");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.weekly-report {
  padding: 20px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .search-box {
      display: flex;
      gap: 10px;

      .el-input {
        width: 200px;
      }
    }
  }

  .report-list {
    .report-list-item {
      margin-bottom: 20px;
      background-color: #fff;
      border-radius: 10px;
      padding: 0;
      padding: 16px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    }
    .date-group {
      padding: 10px 20px;
      font-size: 14px;
      font-weight: 500;
      .date-label {
        color: rgba(61, 61, 61, 0.6);
        font-size: 14px;
      }
    }

    .report-item {
      display: flex;
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;
      align-items: flex-start;

      &:last-child {
        border-bottom: none;
      }

      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 15px;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .report-content {
        flex: 1;

        .report-title {
          display: flex;
          justify-content: flex-start;
          margin-bottom: 10px;

          .reporter-name {
            font-size: 20px;
            font-weight: 500;
            font-family: puhuiti;
            color: rgba(0, 0, 0, 0.85);
            margin-right: 40px;
          }

          .report-time {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
          }
        }

        .report-body {
          color: #4e5969;
          font-size: 14px;
          line-height: 1.6;
          word-break: break-all;
          // 超过两行。。。
          // 超过两行显示省略号
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .report-actions {
        margin-left: 20px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        flex-direction: row;
        width: 100px;
        font-size: 14px;
        .el-button {
          font-size: 14px;
          margin-left: 0;
          margin-bottom: 5px;
          color: #3370ff;
        }

        .delete-btn {
          font-size: 14px;
          color: #f56c6c;
        }
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
  }
}

// 自定义分页器的活跃页样式
::v-deep .el-pagination.is-background .el-pager li.active {
  background-color: #3370ff;
}

.loading-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 100;
}

// 详情抽屉样式
.report-detail {
  padding: 0 20px;

  .detail-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;

    h2 {
      margin: 0 0 10px;
      font-size: 20px;
      color: #333;
    }

    .detail-time {
      color: #999;
    }
  }

  .detail-section {
    margin-bottom: 25px;

    h3 {
      font-size: 16px;
      color: #333;
      font-weight: 500;
      margin: 0 0 10px;
      padding-left: 10px;
      border-left: 3px solid #3370ff;
    }

    .detail-text {
      color: #666;
      line-height: 1.6;
      min-height: 30px;
    }

    .data-table {
      width: 100%;
      overflow-x: auto;

      table {
        width: 100%;
        border-collapse: collapse;

        th,
        td {
          padding: 12px 8px;
          text-align: center;
          border: 1px solid #ebeef5;
        }

        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: normal;
        }

        td {
          color: #606266;
        }
      }
    }
  }
}

// 写周报表单样式
.write-report-form {
  padding: 0 20px;

  .section-title {
    margin: 20px 0 10px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    display: flex;
    align-items: center;

    .required-star {
      color: #f56c6c;
      margin-right: 4px;
    }
  }

  .form-section {
    margin-bottom: 20px;
  }

  .data-table {
    margin: 15px 0;
    width: 100%;
    overflow-x: auto;

    table {
      width: 100%;
      border-collapse: collapse;

      th,
      td {
        padding: 12px 8px;
        text-align: center;
        border: 1px solid #ebeef5;
        white-space: nowrap;
      }

      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: normal;
        font-size: 12px;
      }

      td {
        color: #606266;
      }
    }
  }

  .form-actions {
    margin-top: 40px;
    display: flex;
    justify-content: center;

    .el-button {
      min-width: 100px;
      margin: 0 20px;
    }
  }
}

// 自定义抽屉样式
::v-deep.report-drawer {
  .el-drawer__header {
    padding: 15px 20px;
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 500;
    border-bottom: 1px solid #eee;
  }
}
</style>

<style>
#app .pagination .el-pagination {
  background: transparent;
  display: flex;
  justify-content: center;
}
</style>
