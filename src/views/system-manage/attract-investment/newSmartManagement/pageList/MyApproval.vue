<template>
  <div class="my-approval">
    <div class="approval-header">
      <h3 class="title">
        共有<span class="total">{{ totalCount }}</span>条申请
      </h3>
    </div>

    <!-- 审批列表表格 -->
    <el-table
      :data="approvalList"
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column
        prop="enterprise"
        label="企业名称"
        min-width="180"
      >
        <template slot-scope="scope">
          <div
            class="name"
            @click="goDetails(scope.row)"
          >
            {{ scope.row.enterprise }}
          </div>
          <div
            v-if="scope.row.enterpriseLabelNames&&scope.row.enterpriseLabelNames.length"
            class="EnterpriseLabel"
          >
            <div
              v-if="scope.row.enterpriseLabelNames"
              class="tags"
            >
              <div
                v-for="(it, ind) in scope.row.enterpriseLabelNames"
                :key="ind"
                class="firmTag"
              >
                {{ it }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="enterpriseUniCode"
        label="统一社会信用代码"
        min-width="180"
      />

      <el-table-column
        prop="applyDatetime"
        label="申请时间"
        min-width="150"
      />

      <el-table-column
        prop="userName"
        label="申请人"
        min-width="100"
      />

      <el-table-column
        prop="exceptedDatetime"
        label="期望对接时间"
        min-width="150"
      />

      <el-table-column
        prop="amount"
        label="金额"
        min-width="100"
      />

      <el-table-column
        label="当前进度"
        min-width="120"
      >
        <template slot-scope="scope">
          <div
            class="status-tag"
            :class="getStatusClass(scope.row.status)"
          >
            <i class="status-dot" />
            <span>{{ getStatusText(scope.row.status) }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        min-width="150"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="viewDetail(scope.row)"
          >
            详情
          </el-button>

          <el-button
            v-if="scope.row.isCanAudit"
            type="text"
            class="approve-btn"
            @click="approve(scope.row)"
          >
            审批
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        :current-page.sync="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 详情抽屉 -->
    <el-drawer
      title="申请详情"
      :visible.sync="detailDrawerVisible"
      direction="rtl"
      size="30%"
      custom-class="detail-drawer"
    >
      <div
        v-if="currentDetail"
        class="detail-content"
      >
        <h3 class="section-title">
          委托信息
        </h3>

        <div class="info-group">
          <div class="info-item">
            <span class="label">委托申请时间：</span>
            <span class="value">{{ currentDetail.applyDatetime }}</span>
          </div>

          <div class="info-item">
            <span class="label">委托人：</span>
            <span class="value">{{ currentDetail.userName }}</span>
          </div>

          <div class="info-item">
            <span class="label">申请理由：</span>
            <span class="value">{{ currentDetail?.reason || '-' }}</span>
          </div>

          <div class="info-item">
            <span class="label">意向企业：</span>
            <span class="value">{{ currentDetail.enterprise || '-' }}</span>
          </div>

          <div class="info-item">
            <span class="label">企业社会信用代码：</span>
            <span class="value">{{ currentDetail.enterpriseUniCode || '-' }}</span>
          </div>

          <div class="info-item">
            <span class="label">期望对接时间：</span>
            <span class="value">{{ currentDetail.exceptedDatetime || '-' }}</span>
          </div>

          <div class="info-item">
            <span class="label">招商对接人：</span>
            <span class="value">{{ currentDetail?.contact || '-' }}</span>
          </div>

          <div class="info-item">
            <span class="label">联系方式：</span>
            <span class="value">{{ currentDetail?.contactPhone || '-' }}</span>
          </div>

          <div class="info-item">
            <span class="label">招商要求：</span>
            <span class="value">{{ currentDetail?.note || '-' }}</span>
          </div>

          <div class="info-item">
            <span class="label">金额：</span>
            <span class="value">{{ currentDetail.amount || '-' }}</span>
          </div>
        </div>

        <h3 class="section-title">
          流程
        </h3>
        <!-- paymentApplyFlowList -->
        <div
          v-for="(item, index) in currentDetail.paymentApplyFlowList"
          :key="index"
          class="workflow-timeline"
        >
          <div class="timeline-item completed">
            <div class="timeline-icon">
              <i
                :class="
                  ['el-icon-more', 'el-icon-check', 'el-icon-close'][
                    item.status
                  ]
                "
              />
            </div>
            <div class="timeline-content">
              <div class="timeline-title">
                <span class="title"> {{ item.who ? item.who : '审批人' }}</span>
                <span class="time"> {{ item.gmtModify }}</span>
              </div>
              <div class="timeline-user">
                {{ item.userName }}
                <span :class="getStatusClass(item.status)">
                  {{ ['审批中', '审批通过', '审批驳回'][item.status] }}</span>

                <!-- </span> -->
              </div>
              <div
                v-if="item.instruction !== ''"
                class="timeline-comment"
              >
                {{ item.instruction }}
              </div>
            </div>
          </div>
        </div>
        <div class="workflow-timeline">
          <div class="timeline-item1 completed">
            <div class="timeline-icon">
              <i class="el-icon-more" />
            </div>
            <div class="timeline-content">
              <div class="timeline-title">
                <span class="title"> 发起人</span>
                <span class="time"> {{ currentDetail.applyDatetime }}</span>
              </div>
              <div class="timeline-user">
                {{ currentDetail.userName }}
              </div>
              <!-- <div class="timeline-comment" v-if="item.instruction!==''">
                {{ item.instruction }}
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 审批抽屉 -->
    <el-drawer
      title="审批"
      :visible.sync="approvalDrawerVisible"
      direction="rtl"
      size="50%"
      :before-close="handleCloseApprovalDrawer"
      custom-class="approval-drawer"
    >
      <div
        v-if="currentApproval"
        class="approval-form"
      >
        <div class="form-section">
          <h3 class="section-title">
            委托信息
          </h3>

          <div class="info-item">
            <span class="label">委托申请时间：</span>
            <span class="value">{{ currentApproval.applyDatetime }}</span>
          </div>

          <div class="info-item">
            <span class="label">委托人：</span>
            <span class="value">{{ currentApproval.userName }}</span>
          </div>

          <div class="info-item">
            <span class="label">申请理由：</span>
            <span class="value">{{ currentApproval.reason }}</span>
          </div>

          <div class="info-item">
            <span class="label">意向企业：</span>
            <span class="value">{{ currentApproval.enterprise }}</span>
          </div>

          <div class="info-item">
            <span class="label">企业社会信用代码：</span>
            <span class="value">{{ currentApproval.enterpriseUniCode }}</span>
          </div>

          <div class="info-item">
            <span class="label">期望对接时间：</span>
            <span class="value">{{ currentApproval.exceptedDatetime || '-' }}</span>
          </div>

          <div class="info-item">
            <span class="label">招商对接人：</span>
            <span class="value">{{ currentApproval.contact || '-' }}</span>
          </div>

          <div class="info-item">
            <span class="label">联系方式：</span>
            <span class="value">{{ currentApproval.contactPhone || '-' }}</span>
          </div>

          <div class="info-item">
            <span class="label">招商要求：</span>
            <span class="value">{{ currentApproval.note || '-' }}</span>
          </div>

          <div class="info-item">
            <span class="label">金额：</span>
            <span class="value">{{ currentApproval.amount || '-' }}</span>
          </div>
        </div>

        <div class="form-section approval-opinion">
          <div class="option-title">
            <span class="required-mark">*</span>
            <span>是否通过</span>
          </div>

          <div class="radio-group">
            <el-radio
              v-model="approvalResult"
              label="approve"
            >
              通过
            </el-radio>
            <el-radio
              v-model="approvalResult"
              label="reject"
            >
              驳回
            </el-radio>
          </div>

          <div class="option-title">
            <span
              v-if="approvalResult === 'reject'"
              class="required-mark"
            >*</span>审批意见
          </div>

          <el-input
            v-model="approvalOpinion"
            placeholder="请输入审批意见"
            rows="4"
            type="textarea"
            maxlength="200"
            show-word-limit
          />
        </div>

        <div class="form-actions">
          <el-button @click="handleCloseApprovalDrawer">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="submitApproval"
          >
            保存
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  getAuditList,
  getAuditListDetial,
  getApplyAudit,
  getApplyAuditCheck,
} from '@/api/weeklyReport';
export default {
  name: 'MyApproval',
  data() {
    return {
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,

      // 审批列表数据
      approvalList: [],

      // 审批抽屉相关
      approvalDrawerVisible: false,
      currentApproval: null,
      approvalResult: 'approve',
      approvalOpinion: '',

      // 详情抽屉相关
      detailDrawerVisible: false,
      currentDetail: null,
    };
  },

  created() {
    // 组件创建时初始化数据
    this.initApprovalList();
  },

  methods: {
    // 初始化审批列表数据
    async initApprovalList() {
      try {
        const res = await getAuditList({
          pageNum: this.currentPage,
          pageSize: this.pageSize,
        });

        if (res.code === 'SUCCESS') {
          this.approvalList = res.result.records || [];
          this.totalCount = Number(res.result.total) || 0;
        } else {
          this.$message.error(res.message || '获取审批列表失败');
        }
      } catch (error) {
        console.error('获取审批列表出错', error);
        this.$message.error('获取审批列表失败');
      }
    },

    // 获取状态对应的CSS类
    getStatusClass(status) {
      //  status 0 dai 1 tongg 2 shibai
      switch (status) {
        case '0':
          return 'status-pending';
        case '1':
          return 'status-approved';
        case '2':
          return 'status-rejected';
        default:
          return '';
      }
    },
    // 获取状态对应的文字
    getStatusText(status) {
      switch (status) {
        case '0':
          return '待审批';
        case '1':
          return '审批通过';
        case '2':
          return '审批驳回';
        default:
          return '未知状态';
      }
    },

    // 查看详情
    async viewDetail(row) {
      try {
        const res = await getAuditListDetial({
          entrustId: row.entrustId,
        });

        if (res.code === 'SUCCESS') {
          this.currentDetail = res.result || { ...row };
        } else {
          this.$message.error(res.message || '获取详情失败');
          this.currentDetail = { ...row };
        }

        this.detailDrawerVisible = true;
      } catch (error) {
        console.error('获取详情出错', error);
        this.$message.error('获取详情失败');
        this.currentDetail = { ...row };
        this.detailDrawerVisible = true;
      }
    },

    // 审批操作
    async approve(row) {
      try {
        const res = await getAuditListDetial({
          entrustId: row.entrustId,
        });

        if (res.code === 'SUCCESS') {
          this.currentApproval = res.result || { ...row };
        } else {
          this.$message.error(res.message || '获取详情失败');
          this.currentApproval = { ...row };
        }

        this.approvalDrawerVisible = true;
        this.approvalResult = 'approve';
        this.approvalOpinion = '';
      } catch (error) {
        // console.error('获取详情出错', error);
        this.$message.error('获取详情失败');
        this.currentDetail = { ...row };
        this.approvalDrawerVisible = true;
        this.approvalResult = 'approve';
        this.approvalOpinion = '';
      }
    },

    // 关闭审批抽屉
    handleCloseApprovalDrawer() {
      this.$confirm('关闭后填写的内容将丢失，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.approvalDrawerVisible = false;
        })
        .catch(() => {});
    },

    // 提交审批
    submitApproval() {
      if (!this.approvalResult) {
        this.$message.warning('请选择是否通过');
        return;
      }
      if (this.approvalResult === 'reject' && this.approvalOpinion === '') {
        this.$message.warning('请输入审批意见');
        return;
      }
      // 在实际应用中，这里应该调用API提交审批结果
      const result = this.approvalResult === 'approve' ? '1' : '2';
      let data = {
        entrustId: this.currentApproval.entrustId,
        auditNote: this.approvalOpinion,
        status: result,
      };
      getApplyAuditCheck({ entrustId: this.currentApproval.entrustId }).then(
        (res) => {
          if (res.code == 'SUCCESS') {
            if (res.result) {
              getApplyAudit(data).then((_res) => {
                this.approvalDrawerVisible = false;
                    this.$message.success(
                '操作成功'
              );
                // 更新当前行的状态
                this.initApprovalList();
              });
            } else {
              this.$message.warning(
                '当前您所在机构余额不足，请联系机构负责人充值。'
              );
            }
          }
        }
      );
    
    },

    // 改变每页显示数量
    handleSizeChange(size) {
      this.currentPage = 1; // 切换每页数量时重置为第一页
      this.initApprovalList();
    },
    
    // 改变当前页码
    handleCurrentChange(page) {
      this.currentPage = page;
      this.initApprovalList();
    },
  },
};
</script>

<style lang="scss" scoped>
  .EnterpriseLabel {
      padding: 13px 0;
      .tags {
        display: flex;
        flex-wrap: wrap;
        .firmTag {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11px;
          padding: 0 6px;
          color: #ff7d00;
          background: #fff2e6;
          margin-right: 8px;
          margin-bottom: 8px;
          height: 20px;
          border-radius: 2px 2px 2px 2px;
          line-height: 20px;
        }
      }
    }
  .name{
    // cursor: pointer;
  }
.my-approval {
  .approval-header {
    margin-bottom: 20px;

    .title {
      font-size: 16px;
      font-weight: normal;
      color: #333;
      margin: 0;
    }
    .total {
      color: #3370ff;
      padding: 0px 3px;
    }
  }

  .status-tag {
    display: flex;
    align-items: center;

    .status-dot {
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 6px;
    }

    &.status-pending {
      color: #3370ff;

      .status-dot {
        background-color: #3370ff;
      }
    }

    &.status-approved {
      color: #52c41a;

      .status-dot {
        background-color: #52c41a;
      }
    }

    &.status-rejected {
      color: #f5222d;

      .status-dot {
        background-color: #f5222d;
      }
    }
  }

  .el-button.approve-btn {
    color: #3370ff;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }

  // 审批抽屉样式
  .approval-form {
    padding: 0 20px;

    .form-section {
      margin-bottom: 30px;

      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 20px;
        padding-left: 10px;
        border-left: 3px solid #3370ff;
      }

      .info-item {
        display: flex;
        margin-bottom: 15px;
        line-height: 22px;

        .label {
          width: 140px;
          text-align: right;
          margin-right: 10px;
          color: #606266;
          flex-shrink: 0;
        }

        .value {
          flex: 1;
          color: #333;
        }
      }
    }

    .approval-opinion {
      .option-title {
        margin-bottom: 10px;
        display: flex;
        align-items: center;

        .required-mark {
          color: #f56c6c;
          margin-right: 4px;
        }
      }

      .radio-group {
        margin-bottom: 20px;
      }

   
    }

    .form-actions {
      display: flex;
      justify-content: center;
      margin-top: 40px;

      .el-button {
        min-width: 100px;
        margin: 0 20px;
      }
    }
  }

  // 详情抽屉样式
  .detail-content {
    padding: 0 20px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin: 20px 0;
    }

    .info-group {
      margin-bottom: 30px;

      .info-item {
        display: flex;
        margin-bottom: 15px;
        line-height: 22px;

        .label {
          width: 140px;
          text-align: right;
          margin-right: 10px;
          color: #606266;
          flex-shrink: 0;
        }

        .value {
          flex: 1;
          color: #333;
        }
      }
    }

    .workflow-timeline {
      padding-left: 20px;

      .timeline-item1 {
        position: relative;
        padding-bottom: 25px;
      }
      .timeline-item {
        position: relative;
        padding-bottom: 25px;
        // 移除border-left，改用伪元素统一设置垂直线
        &::before {
          content: '';
          position: absolute;
          left: 0; // 将left从15px改为0
          top: 30px;
          width: 1px;
          height: calc(100% - 30px);
          background-color: #c3cdf8; // 使用与图标背景一致的颜色
          z-index: 1; // 确保在其他元素之上
        }
      }

      .timeline-icon {
        position: absolute;
        left: 0;
        top: 0;
        width: 30px;
        height: 30px;
        background-color: #c3cdf8;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #3370ff;
        transform: translateX(-50%);
        z-index: 2; // 确保图标在线的上层
      }

      .timeline-content {
        margin-left: 30px;

        .timeline-title {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;

          .title {
            font-weight: 500;
            color: #303133;
          }

          .time {
            color: #909399;
            font-size: 12px;
          }
        }

        .timeline-user {
          color: #606266;
          margin-bottom: 8px;
        }

        .timeline-comment {
          background-color: #f2f6fc;
          padding: 10px;
          border-radius: 4px;
          color: #606266;
          margin-top: 5px;
        }
      }
    }
  }
}

// 抽屉自定义样式
:deep(.approval-drawer) {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;
    color: #303133;
    font-size: 16px;
    font-weight: 500;
  }

  .el-drawer__body {
    padding-top: 20px;
  }
}

// 抽屉自定义样式
:deep(.detail-drawer) {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;
    color: #303133;
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
