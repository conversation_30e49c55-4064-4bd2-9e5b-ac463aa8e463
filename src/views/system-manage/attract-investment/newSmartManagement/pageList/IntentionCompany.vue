<template>
  <div class="intention-company">
    <!-- <h3>意向企业列表</h3> -->
    <div
      v-if="isParticulars == 1"
      class="button-container"
    >
      <el-button
        type="primary"
        size="small"
        @click="exportData"
      >
        导出
      </el-button>
      <el-button
        type="primary"
        size="small"
        @click="addNewCompany"
      >
        自行添加
      </el-button>
    </div>
    <div
      v-if="isParticulars == 1"
      class="intention-company-list"
    >
      <el-table
        :data="companyList"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <!-- 企业名称 -->
        <el-table-column
          prop="enterpriseName"
          label="企业名称"
          width="300"
          fixed="left"
        >
          <template slot-scope="scope">
            <div
              class="name"
              @click="goDetails(scope.row)"
            >
              {{ scope.row.enterpriseName }}
            </div>
            <div
              v-if="
                scope.row.enterpriseLabelNames &&
                  scope.row.enterpriseLabelNames.length
              "
              class="EnterpriseLabel"
            >
              <div
                v-if="scope.row.enterpriseLabelNames"
                class="tags"
              >
                <div
                  v-for="(it, ind) in scope.row.enterpriseLabelNames"
                  :key="ind"
                  class="firmTag"
                >
                  {{ it }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 所属产业链 -->
        <el-table-column
          prop="chainNames"
          label="所属产业链"
          min-width="180"
        />

        <!-- 所在地区 -->
        <el-table-column
          prop="regionName"
          label="所在地区"
          min-width="150"
        />

        <!-- 企业来源 -->
        <el-table-column
          prop="clueSourceName"
          label="企业来源"
          min-width="100"
        />

        <!-- 纳入意向日期 -->
        <el-table-column
          prop="intentionDate"
          label="纳入意向日期"
          min-width="150"
        />

        <!-- 纳入意向人 -->
        <el-table-column
          prop="intentionPerson"
          label="纳入意向人"
          min-width="100"
        />

        <!-- 当前跟进人 -->
        <el-table-column
          prop="beAssignPerson"
          label="当前跟进人"
          min-width="100"
        />

        <!-- 当前进度 -->
        <el-table-column
          label="当前进度"
          min-width="120"
        >
          <template slot-scope="scope">
            <div
              class="status-tag"
              :class="getStatusClass(scope.row.clueDealState)"
            >
              <i class="status-dot" />
              <span>{{
                getStatusText(scope.row.clueDealState) +
                  (scope.row.isNotFollowMonth ? "(近一个月未跟进)" : "")
              }}</span>
            </div>
          </template>
        </el-table-column>
        <!-- 操作 -->
        <el-table-column
          label="操作"
          width="300"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              v-if="isZhipai"
              type="text"
              :disabled="scope.row.clueDealState !== '0'"
              @click="assignClue(scope.row)"
            >
              线索指派
            </el-button>

            <el-button
              type="text"
              :disabled="
                scope.row.clueDealState !== '0' || scope.row.entrustOrNot
              "
              @click="delegateRecruitment(scope.row)"
            >
              {{ scope.row.entrustOrNot ? "已委托" : "委托招商" }}
            </el-button>

            <el-button
              type="text"
              :disabled="
                scope.row.clueDealState !== '0' ||
                  scope.row.beAssignPersonId != $store.getters.user.userId
              "
              @click="addFollowRecord(scope.row)"
            >
              添加跟进记录
            </el-button>

            <el-button
              v-if="
                scope.row.clueDealState == '0' &&
                  !scope.row.isHavingFollow &&
                  !scope.row.entrustOrNot
              "
              type="text"
              style="color: #f56c6c"
              @click="deleteRecord(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          :current-page.sync="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size.sync="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Number(totalCount)"
          @size-change="init"
          @current-change="init"
        />
      </div>
    </div>
    <add-new-company
      ref="addNewCompany"
      @updateList="updateList"
    />
    <assign-clue
      ref="assignClue"
      @updateList="updateList"
    />
    <add-follow-record
      ref="addFollowRecord"
      @updateList="updateList"
    />
    <addEntrust
      v-if="isDrawer"
      :echo-item="echoItem"
      :is-drawer.sync="isDrawer"
      :echo="true"
      @updataList="updateList"
    />
    <Detailsenterprise
      v-if="isParticulars == 2"
      :enterprise-i-d="enterpriseID"
      :company-detial="companyDetial"
      @goback="goback"
    />
  </div>
</template>

<script>
import AddNewCompany from "./pop/addNewCompany.vue";
import AssignClue from "./pop/assignClue.vue";
import AddFollowRecord from "./pop/addFollowRecord.vue";
import addEntrust from "../../entrust/components/Drawer/addEntrust.vue";
import Detailsenterprise from "../../IntelligentSearch/components/Detailsenterprise.vue";
import {
  enterpriseListAPI_investment,
  exportEnterpriseListAPI_investment,
  removeEnterpriseAPI_investment,
} from "@/api/attractInvestment";

export default {
  name: "IntentionCompanyList",
  components: {
    AddNewCompany,
    AssignClue,
    AddFollowRecord,
    addEntrust,
    Detailsenterprise,
  },
  props: {
    isZhipai: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isParticulars: 1,
      echoItem: {},
      isDrawer: false,
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      // 企业列表数据
      companyList: [],
      enterpriseID: "",
      clueSource: "", // 意向企业来源
      clueId: "",
      companyDetial: "",
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      enterpriseListAPI_investment({
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      }).then((res) => {
        if (res.code === "SUCCESS") {
          this.companyList = res.result.records.map((item) => {
            return {
              ...item,
              clueSourceName: item.clueSource == 1 ? "系统推荐" : "自行添加",
            };
          });
          this.totalCount = Number(res.result.total);
        }
      });
    },
    goDetails(row) {
      this.companyDetial = row;
      this.isParticulars = 2;
      this.enterpriseID = row.enterpriseId;
      this.$emit("changeTab", 4);
    },
    goback() {
      this.isParticulars = 1;
      this.$emit("changeTab", 1);
    },
    updateList() {
      this.init();
    },
    // 获取状态对应的CSS类
    getStatusClass(status) {
      switch (status) {
        case "0":
          return "status-following";
        case "1":
          return "status-signed";
        case "2":
          return "status-failed";
        default:
          return "";
      }
    },
    // 获取状态对应的文字
    getStatusText(status) {
      switch (status) {
        case "0":
          return "跟进中";
        case "1":
          return "签约成功";
        case "2":
          return "签约失败";
        default:
          return "未知状态";
      }
    },
    // 线索指派
    assignClue(row) {
      this.$refs.assignClue.openDialog(row);
    },
    // 委托招商
    delegateRecruitment(item) {
      this.echoItem = item;
      this.isDrawer = true;
    },
    // 添加跟进记录
    addFollowRecord(row) {
      this.$refs.addFollowRecord.openDialog(row);
    },
    // 删除记录
    deleteRecord(row) {
      this.$confirm("确认取消纳入该企业?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 在这里执行删除操作
          removeEnterpriseAPI_investment({
            id: row.id,
          }).then((res) => {
            this.$message.success("删除成功");
            this.init();
          });
        })
        .catch(() => {});
    },
    // 导出数据
    async exportData() {
      if (Number(this.totalCount) === 0) {
        this.$message.error("暂无数据");
        return;
      }
      // this.xzloading = true;
      try {
        const res = await exportEnterpriseListAPI_investment();
        // let blob = new Blob([res], {
        //   type: 'text/csv,charset=UTF-8',
        // });
        let objectUrl = res.result; //URL.createObjectURL(blob);
        const fileName = `招商智管意向企业列表.xlsx`;
        const downloadLink = document.createElement("a");
        downloadLink.href = objectUrl;
        downloadLink.download = fileName;
        downloadLink.click();
      } finally {
        // this.xzloading = false;
      }
    },
    // 格式化 JSON 数据
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          if (j === "status") {
            return this.getStatusText(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
    // 自行添加
    addNewCompany() {
      this.$refs.addNewCompany.openDialog();
    },
  },
};
</script>

<style lang="scss" scoped>
.intention-company {
  .EnterpriseLabel {
    padding: 13px 0;
    .tags {
      display: flex;
      flex-wrap: wrap;
      .firmTag {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        padding: 0 6px;
        color: #ff7d00;
        background: #fff2e6;
        margin-right: 8px;
        margin-bottom: 8px;
        height: 20px;
        border-radius: 2px 2px 2px 2px;
        line-height: 20px;
      }
    }
  }
  .name {
    cursor: pointer;
  }
  .button-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
  }

  .intention-company-list {
    .status-tag {
      display: flex;
      align-items: center;

      .status-dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-right: 6px;
      }

      &.status-following {
        color: #ff4d4f;

        .status-dot {
          background-color: #ff4d4f;
        }
      }

      &.status-signed {
        color: #52c41a;

        .status-dot {
          background-color: #52c41a;
        }
      }

      &.status-failed {
        color: #fadb14;

        .status-dot {
          background-color: #fadb14;
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      text-align: center;
    }
  }
}
</style>