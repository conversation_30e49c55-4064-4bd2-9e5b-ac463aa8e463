<template>
  <div class="my-collection">
    <div v-if="isParticulars === 1">
      <div class="header">
        <h3 class="title">
          共<span class="total">{{ Number(total||0) }}</span>家企业
        </h3>
        <!-- <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入企业名称搜索"
            prefix-icon="el-icon-search"
            size="small"
            clearable
            @keyup.enter.native="handleSearch"
            @clear="handleClear"
          />
          <el-button
            type="primary"
            size="small"
            @click="handleSearch"
          >
            搜索
          </el-button>
        </div> -->
      </div>

      <!-- 企业列表表格 -->
      <el-table
        v-loading="loading"
        :data="companyList"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        @row-click="handleRowClick"
      >
        <el-table-column
          label="企业名称"
          min-width="200"
        >
          <template slot-scope="scope">
            <div class="company-name-cell">
              <span class="company-name-text">{{
                scope.row.enterpriseName
              }}</span>
              <!-- 企业标签，只有存在标签时才显示 -->
              <div
                v-if="
                  scope.row.enterpriseLabelNames &&
                    scope.row.enterpriseLabelNames.length
                "
                class="company-tags"
              >
                <span
                  v-for="(tag, index) in scope.row.enterpriseLabelNames"
                  :key="`tag-${index}`"
                  class="company-tag"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="industry"
          label="所属产业链"
          min-width="150"
        >
          <template slot-scope="scope">
            {{
              (scope.row?.chainNames && scope.row.chainNames.join(',')) || '-'
            }}
          </template>
        </el-table-column>

        <el-table-column
          label="所在地区"
          min-width="200"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <div>
              <span>
                {{ scope.row.province + scope.row.city + scope.row.area }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="collectionTime"
          label="收藏日期"
          min-width="120"
        />

        <el-table-column
          label="是否已纳入招商库"
          min-width="150"
        >
          <template slot-scope="scope">
            {{ scope.row.isClue ? '是' : '否' }}
          </template>
        </el-table-column>

        <el-table-column
          fixed="right"
          label="操作"
          width="100"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              class="cancel-btn"
              @click.stop="handleCancelCollection(scope.row)"
            >
              取消收藏
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size.sync="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 企业详情组件 -->
    <Detailsenterprise
      v-if="isParticulars === 2"
      :enterprise-i-d="enterpriseId"
      :recommend-region-code="recommendRegionCode"
      :chain-id="chainId"
      :model-type="modelType"
      :parent-chain-id="chainId"
      :company-detial="companyDetial"
      @goback="goback"
    />
  </div>
</template>

<script>
import Detailsenterprise from '@/views/system-manage/attract-investment/IntelligentSearch/components/Detailsenterprise.vue';
import { getCollectList } from '@/api/weeklyReport';
import { collectAPI } from '@/views/system-manage/attract-investment/apiUrl.js';
export default {
  name: 'MyCollection',
  components: {
    Detailsenterprise,
  },
  data() {
    return {
      isParticulars: 1, // 1:列表页面 2:详情页面
      enterpriseId: '',
      recommendRegionCode: '',
      chainId: '',
      modelType: '',
      loading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      defaultLogo:  'https://static.idicc.cn/cdn/pangu/yonghu.png',
      companyList: [],
      companyDetial: {},
    };
  },
  created() {
    this.fetchCollectionList();
  },
  methods: {
    // 获取收藏列表数据
    async fetchCollectionList() {
      this.loading = true;
      try {
        // 这里应该调用API获取数据
        const res = await getCollectList({
          pageNum: this.currentPage,
          pageSize: this.pageSize,
        });
        if (res.code === 'SUCCESS') {
          this.companyList = res.result?.records;
          this.total = Number(res?.result?.total);
          this.loading = false;
        }
        this.loading = false;
        // 模拟API返回数据
      } catch (error) {
        console.error('获取收藏列表失败', error);
        this.$message.error('获取收藏列表失败，请稍后重试');
        this.loading = false;
      }
    },

    // // 搜索
    // handleSearch() {
    //   this.currentPage = 1;
    //   this.fetchCollectionList();
    // },

    // 清除搜索条件
    // handleClear() {
    //   this.searchKeyword = '';
    //   this.handleSearch();
    // },

    // 查看企业详情
    handleRowClick(row) {
      this.companyDetial = row;
      this.enterpriseId = row.enterpriseId;
      this.chainId = row.chainId || '';
      this.recommendRegionCode = '';
      this.modelType = '';
      this.isParticulars = 2;
    },

    // 取消收藏
    handleCancelCollection(row) {
      this.$confirm('确定要取消收藏该企业吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          try {
            // 这里应该调用API取消收藏
            await collectAPI({
              status: false,
              enterpriseUniCode: row?.enterpriseUniCode,
            });

            // 模拟API操作
            // setTimeout(() => {
            // 从列表中移除
            const index = this.companyList.findIndex(
              (item) => item.id === row.id
            );
            if (index !== -1) {
              this.companyList.splice(index, 1);
              this.total -= 1;
            }
            this.$message.success('取消收藏成功');
            // }, 300);
          } catch (error) {
            this.$message.error('取消收藏失败，请稍后重试');
          }
        })
        .catch(() => {});
    },

    // 从详情返回列表
    goback() {
      this.isParticulars = 1;
      // 可能需要刷新数据
      this.fetchCollectionList();
    },

    // 改变每页显示数量
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.fetchCollectionList();
    },

    // 改变当前页码
    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchCollectionList();
    },
  },
};
</script>

<style lang="scss" scoped>
.my-collection {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .title {
      font-size: 16px;
      font-weight: normal;
      color: #333;
      margin: 0;
    }
.total{
  color: #3370ff;
  padding:0px 3px;
}
    .search-box {
      display: flex;
      gap: 10px;

      .el-input {
        width: 220px;
      }
    }
  }

  .company-name-cell {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .company-name-text {
      color: #3370ff;
      cursor: pointer;
      margin-right: 8px;

      &:hover {
        text-decoration: underline;
      }
    }

    .company-tags {
      display: flex;
      flex-wrap: wrap;

      .company-tag {
        background-color: #fff7e6;
        border: 1px solid #ffe7ba;
        color: #fa8c16;
        font-size: 12px;
        padding: 0 6px;
        border-radius: 2px;
        margin-right: 6px;
        margin-bottom: 4px;
        line-height: 18px;
        display: inline-block;
      }
    }
  }

  .cancel-btn {
    color: #f56c6c;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }
}

// 让表格行可点击的样式
::v-deep .el-table tbody tr {
  cursor: pointer;
}
</style>
