<template>
  <div class="data-board">
    <!-- 添加日期筛选 -->
    <div class="filter-section">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        :picker-options="pickerOptions"
        size="small"
        @change="handleDateChange"
      />
    </div>

    <!-- 顶部数据卡片区域 -->
    <div class="status-cards">
      <div class="status-card">
        <div class="icon-container success" />
        <div class="status-name">
          签约成功
        </div>
        <div class="status-count">
          {{ reportData?.stateSign || 0 }}
        </div>
      </div>
      <div class="status-card">
        <div class="icon-container processing" />
        <div class="status-name">
          跟进中
        </div>
        <div class="status-count">
          {{ reportData?.stateFollow || 0 }}
        </div>
      </div>
      <div class="status-card">
        <div class="icon-container failed" />
        <div class="status-name">
          签约失败
        </div>
        <div class="status-count">
          {{ reportData?.stateFail || 0 }}
        </div>
      </div>
    </div>

    <!-- 中间部分，纳入客户和线索统计 -->
    <div class="middle-section">
      <div class="stats-card customer-stats">
        <div class="stats-icon document" />
        <div class="stats-content">
          <div class="stats-title">
            纳入意向企业
          </div>
          <div class="stats-number">
            {{ reportData?.totalEnterprise || 0 }}
          </div>
        </div>
        <div class="stats-details">
          <div class="stats-item">
            <span class="label">系统推荐：</span>
            <span class="value">{{ reportData?.systemEnterprise || 0 }}</span>
          </div>
          <div class="stats-item">
            <span class="label">自行添加：</span>
            <span class="value">{{ reportData?.ownEnterprise || 0 }}</span>
          </div>
          <div class="stats-item" />
        </div>
      </div>

      <div class="stats-card leads-stats">
        <div class="stats-icon data-line" />
        <div class="stats-content">
          <div class="stats-title">
            线索处理状态统计
          </div>
          <!-- <div class="stats-number">{{ reportData?.stateSign || 0 }}</div> -->
        </div>
        <div class="stats-details">
          <div class="stats-item">
            <span class="label">自行跟进：</span>
            <span class="value">{{ reportData?.withFollowing || 0 }}</span>
          </div>
          <div class="stats-item">
            <span class="label">委托招商：</span>
            <span class="value">{{ reportData?.withEntrust || 0 }}</span>
          </div>
          <div class="stats-item">
            <span class="label">未跟踪企业：</span>
            <span class="value">{{ reportData?.withNotFollow || 0 }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部图表区域 -->
    <div class="chart-section">
      <div class="chart-card">
        <div class="chart-title">
          跟进人员排行榜
        </div>
        <div class="bar-chart">
          <div
            ref="barChart"
            style="width: 100%; height: 300px;  padding: 10px 50px;"
          />
        </div>
        <!-- 移除自定义图例，使用echarts内置图例 -->
      </div>

      <div class="chart-card">
        <div class="chart-title">
          意向企业产业链分布
        </div>
        <div class="pie-chart">
          <div
            ref="pieChart"
            style="width: 100%; height: 300px"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { getDataReport } from '@/api/weeklyReport';
import dayjs  from "dayjs";

export default {
  name: 'DataBoard',
  data() {
    return {
      // 添加日期范围和日期选择器配置
      dateRange: [],
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 1);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 3);
              picker.$emit('pick', [start, end]);
            },
          },
        ],
      },
      barChartData: [
        { name: '张三', total: 50, success: 25, processing: 15, failed: 10 },
        { name: '李四', total: 45, success: 22, processing: 15, failed: 8 },
        { name: '王五', total: 30, success: 15, processing: 10, failed: 5 },
        { name: '赵六', total: 28, success: 12, processing: 10, failed: 6 },
        { name: '钱七', total: 20, success: 10, processing: 6, failed: 4 },
      ],
      pieChartData: [
        { value: 76, name: '医疗食品' },
        { value: 24, name: '功能食品' },
        { value: 24, name: '休闲食品' },
        { value: 18, name: '保健食品' },
        { value: 10, name: '其他' },
      ],
      barChart: null,
      pieChart: null,
      reportData: {},
      timeStart: '',
      timeEnd: '',
    };
  },
  // mounted() {
  //   this.initBarChart();
  //   this.initPieChart();

  //   // 添加窗口大小变化时重绘图表

  // },
  beforeDestroy() {
    // 组件销毁前移除事件监听
    window.removeEventListener('resize', this.resizeCharts);

    // 销毁图表实例
    if (this.barChart) {
      this.barChart.dispose();
      this.barChart = null;
    }
    if (this.pieChart) {
      this.pieChart.dispose();
      this.pieChart = null;
    }
  },
  mounted() {
    this.init();
    window.addEventListener('resize', this.resizeCharts);
  },
  methods: {
    init() {
      getDataReport({
        timeStart: this.timeStart,
        timeEnd: this.timeEnd,
      }).then((res) => {
        if (res.code === 'SUCCESS') {
          this.reportData = res.result?.briefing || {};

          this.barChartData = res.result?.personCount.map((e) => {
            let { name, stateFail, stateFollow, stateSign, withFollowing } = e;
            let total =
              Number(stateSign || 0) +
              Number(stateFail || 0) +
              Number(stateFollow || 0);
            return {
              name,
              total,
              success: stateSign,
              processing: stateFollow,
              failed: stateFail,
            };
          });
          this.pieChartData = res.result?.chainGroupCount.map((e) => {
            return {
              name: e.name,
              value: e.total,
            };
          });
          this.initBarChart();
          this.initPieChart();
        }
      });
    },
    // 添加日期变更处理方法
    handleDateChange() {
      // 当日期变更时，重新加载数据
      this.fetchData();
    },

    // 添加数据获取方法
    fetchData() {
      // 这里可以根据日期范围获取数据
      // 获取成功后更新以下图表
      // 示例：如何处理日期范围
      // if (this.dateRange && this.dateRange.length === 2) {
        const [startDate, endDate] = this.dateRange;
        this.timeStart = dayjs(startDate).format('YYYY-MM-DD HH:mm:ss');
        this.timeEnd = dayjs(endDate).format('YYYY-MM-DD HH:mm:ss');
        // console.log('选择的日期范围:', startDate, '至', endDate);
        // 使用日期范围调用API获取数据
      // }
      this.init();
    },
    initBarChart() {
      // 初始化柱状图
      this.barChart = echarts.init(this.$refs.barChart);
let data=this.barChartData.reverse()
      // 准备数据
      const names = data.map((item) => item.name);
      const successData = data.map((item) => item.success);
      const processingData = data.map((item) => item.processing);
      const failedData = data.map((item) => item.failed);

      // 配置项
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          // 添加echarts图例并设置在右上角
          data: ['签约成功', '跟进中', '签约失败'],
          right: 10,
          top: 0,
          itemWidth: 12,
          itemHeight: 12,
          textStyle: {
            color: '#606266',
            fontSize: 12,
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '40px', // 为顶部图例留出空间
          containLabel: true,
        },
        xAxis: [
          {
            type: 'value',
            axisLine: { 
              show: false,
            },
            axisTick: {
              show: false,
            },
               axisLabel: {
                interval: 0,
                rotate: 30,
                textStyle: {
                  color: '#4D4D4D',
                  fontSize: '12',
                  itemSize: '',
                  marginLeft: '2px',
                },
              },
         splitLine: {
                show: true, // 显示网格线
                lineStyle: {
                  color: '#DEE3E9', // 网格线颜色
                  width: 1,
                  type: 'dashed',
                },
              },
          },
        ],
        yAxis: [
          {
            type: 'category',
            data: names,
            axisTick: {
              show: false,
            },
             axisLabel: {
              interval: 0,
              rotate: 0,
              textStyle: {
                color: '#4D4D4D',
                fontSize: '12',
                itemSize: '',
                marginLeft: '2px',
              },
            },
              splitArea: {
              show: false,
              areaStyle: {
                // color: [' rgba(255, 255, 255, 0.08)', ''], // 交替背景色
                // opacity: 0.3,
              },
            },
              axisLine: {
              lineStyle: {
                color: '#DEE3E9',
                fontSize: '20px',
              },
            },
          },
        ],
        series: [
          {
            name: '签约成功',
            type: 'bar',
            stack: 'Total',
             barWidth: 8,
            barGap: '0%',
            barCategoryGap: '50%',
            emphasis: {
              focus: 'series',
            },
            itemStyle: {
              color: '#6CCE26',
              // 设置为直角
              borderRadius: [0, 0, 0, 0],
            },
            data: successData,
          },
          {
            name: '跟进中',
            type: 'bar',
            stack: 'Total',
             barWidth: 8,
            emphasis: {
              focus: 'series',
            },
            itemStyle: {
              color: '#3370FF',
              // 设置为直角
              borderRadius: [0, 0, 0, 0],
            },
            data: processingData,
          },
          {
            name: '签约失败',
            type: 'bar',
            stack: 'Total',
             barWidth: 8,
            emphasis: {
              focus: 'series',
            },
            itemStyle: {
              color: '#E04848',
              // 为红色柱子设置右侧圆角
                  borderRadius: [0, 0, 0, 0],
            },
            data: failedData,
          },
        ],
      };

      // 应用配置项
      this.barChart.setOption(option);
    },

    initPieChart() {
      // 初始化饼图
      this.pieChart = echarts.init(this.$refs.pieChart);

      // 配置项
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 30,
          top: 'center',
          data: this.pieChartData.map((item) => item.name),
          // 移除formatter，使图例只显示名称不显示数值
          formatter: undefined,
          // 设置图例样式为10px的圆点
          icon: 'circle',
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 15,
          textStyle: {
            fontSize: 14,
            color: '#606266',
            padding: [0, 0, 0, 5], // 文本与图标的间距
          },
        },

        color:   ['#F0C92E' ,
       '#3370FF',  
       '#6CCE26',  
       '#987EFB',  
       '#F0722E', 
       '#6CCBFF', 
       '#31A9FF', 
       '#EE97FD', 
       '#ACE2DD',  
       '#E04848', 
       '#5A39D8',  
       '#E9F148', 
       '#FC4907',  
       '#771DFF', 
       '#1DFFF7',  
       '#FFAC46'],
        series: [
          {
            name: '产业环节',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['30%', '50%'],
            avoidLabelOverlap: false,
            // label: {
            //   show: true, // 开启标签显示
            //   position: 'center', // 设置为居中显示
            //   formatter: '{c}%', // 只显示百分比值
            //   fontSize: 14,
            //   fontWeight: 'bold',
            //   color: '#303133',
            //   // 每个数据项都显示标签，而不仅仅是鼠标悬停项
            //   alignTo: 'none',
            //   // 使用回调函数控制不同数据项的标签显示
            //   formatter: function(params) {
            //     // 只在第一个扇形(医疗食品)显示数值
            //     if (params.dataIndex === 0) {
            //       return params.value + '%';
            //     } else {
            //       return '';
            //     }
            //   }
            // },
            emphasis: {
              scale: false, // 禁用鼠标悬停时的放大效果
              label: {
                show: true,
                fontSize: 16,
                fontWeight: 'bold',
                // formatter: '{b}: {d}%' // 悬停时显示名称和百分比
              },
            },
            labelLine: {
              show: true,
            },
            data: this.pieChartData,
          },
        ],
      };

      // 应用配置项
      this.pieChart.setOption(option);
    },

    resizeCharts() {
      if (this.barChart) {
        this.barChart.resize();
      }
      if (this.pieChart) {
        this.pieChart.resize();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.data-board {
  // padding: 20px;
  // background-color: #f5f7fa;
    margin-top: -35px;
  .filter-section {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
  }

  .status-cards {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    .status-card {
      flex: 1;
      display: flex;
      align-items: center;
      background-color: #fff;
      border-radius: 10px;
      padding: 15px 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      height: 120px;
      .icon-container {
        width: 60px;
        height: 60px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 16px;
        margin-right: 24px;

        &.success {
          background: center / contain no-repeat
            url('~@/assets/attract/dataBoard1.webp');
          background-size: 120%;
        }

        &.processing {
          background: center / contain no-repeat
            url('~@/assets/attract/dataBoard2.webp');
          background-size: 120%;
          // background-color: rgba(64, 158, 255, 0.1);
          // color: #409eff;
          // border: 1px solid rgba(64, 158, 255, 0.2);
        }

        &.failed {
          background: center / contain no-repeat
            url('~@/assets/attract//dataBoard3.webp');
          background-size: 120%;
          // background-color: rgba(245, 108, 108, 0.1);
          // color: #f56c6c;
          // border: 1px solid rgba(245, 108, 108, 0.2);
        }
      }

      .status-name {
        font-family: puhuiti;
        color: #1d2129;
        font-size: 16px;
        margin-right: 32px;
      }

      .status-count {
        font-family: puhuiti;
        font-size: 22px;
        font-weight: 600;
        color: #1d2129;
      }
    }
  }

  .middle-section {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    height: 200px;

    .stats-card {
      flex: 1;
      display: flex;
      position: relative;
      flex-wrap: wrap;
      background-color: #fff;
      border-radius: 10px;
      padding: 50px 60px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .stats-icon {
        width: 50px;
        height: 50px;
        // border-radius: 4px;
        // display: flex;
        // align-items: center;
        // justify-content: center;
        // font-size: 24px;
        margin-right: 20px;
      }
      .document {
        background: center / contain no-repeat
          url('~@/assets/attract/dataBoard4.webp');
        // background-size: 120%;
      }
      .data-line {
        background: center / contain no-repeat
          url('~@/assets/attract/dataBoard5.webp');
        // background-size: 120%;
      }

      .stats-content {
        flex: 1;

        .stats-title {
          color: #292929;
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 5px;
        }

        .stats-number {
          font-size: 22px;
          font-weight: 600;
          color: #1d2129;
        }
      }

      .stats-details {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        margin-top: 15px;
        justify-content: space-between;
        // padding-left: 65px; /* 与图标对齐，65px = 图标宽度50px + 右边距15px */

        .stats-item {
          margin-right: 20px;

          .label {
            color: #1d2129;
            font-weight: 500;
            font-size: 14px;

            margin-right: 5px;
          }

          .value {
            font-size: 14px;
            color: #3370ff;
            font-weight: bold;
          }
        }
      }
    }
  }

  .chart-section {
    display: flex;
    gap: 20px;

    .chart-card {
      flex: 1;
      background-color: #fff;
      border-radius: 10px;
      padding: 20px 40px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .chart-title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 20px;
        font-family: puhuiti;
      }
    }

    // 移除自定义图例样式
  }
}
</style>
