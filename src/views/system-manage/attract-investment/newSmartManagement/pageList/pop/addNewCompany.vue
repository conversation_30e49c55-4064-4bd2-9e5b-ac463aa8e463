<template>
  <div class="add-new-company">
    <el-drawer
      title="添加企业"
      :visible.sync="dialogVisible"
      direction="rtl"
      size="30%"
      :before-close="() => (dialogVisible = false)"
      custom-class="drawer-custom"
    >
      <el-form
        ref="companyForm"
        :model="company"
        :rules="rules"
        label-width="150px"
        class="drawer-content"
      >
        <el-form-item
          label="企业名称"
          prop="enterpriseName"
        >
          <el-autocomplete
            ref="myAutocomplete"
            v-model="company.enterpriseName"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入企业名称"
            :popper-append-to-body="false"
            @select="handleSelect"
          />
        </el-form-item>
        <el-form-item
          label="统一社会信用代码"
          prop="uniCode"
        >
          <el-input
            v-model="company.uniCode"
            :disabled="uniCodeInput"
            placeholder="请输入统一社会信用代码"
          />
        </el-form-item>
        <el-form-item
          label="企业简介"
          prop="introduction"
        >
          <el-input
            v-model="company.introduction"
            type="textarea"
            placeholder="请输入企业简介"
            :rows="4"
          />
        </el-form-item>
        <el-form-item
          label="联系电话"
          prop="mobile"
        >
          <el-input
            v-model="company.mobile"
            placeholder="请输入联系电话"
          />
        </el-form-item>
        <el-form-item
          label="注册地址"
          prop="enterpriseAddress"
        >
          <el-input
            v-model="company.enterpriseAddress"
            placeholder="请输入注册地址"
          />
        </el-form-item>
        <el-form-item
          label="所属行业"
          prop="nationalStandardIndustry"
        >
          <el-input
            v-model="company.nationalStandardIndustry"
            placeholder="请输入所属行业"
          />
        </el-form-item>
        <el-form-item
          label="经营范围"
          prop="businessScope"
        >
          <el-input
            v-model="company.businessScope"
            type="textarea"
            placeholder="请输入经营范围"
            :rows="4"
          />
        </el-form-item>
        <div class="form-actions">
          <el-button @click="dialogVisible = false">
            取 消
          </el-button>
          <el-button
            type="primary"
            @click="submitForm"
          >
            提 交
          </el-button>
        </div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script>
import {
  newEnterpriseSearchAPI,
  addEnterpriseAPI_investment,
} from "@/api/attractInvestment";
export default {
  name: "AddNewCompany",
  data() {
    return {
      pageNum: 1,
      dialogVisible: false,
      company: {
        clueSource: "2",
        enterpriseName: "",
        uniCode: "",
        introduction: "",
        mobile: "",
        enterpriseAddress: "",
        nationalStandardIndustry: "",
        businessScope: "",
      },
      uniCodeInput: false,
      rules: {
        enterpriseName: [
          { required: true, message: "请输入企业名称", trigger: "blur" },
        ],
        uniCode: [
          {
            required: true,
            message: "请输入统一社会信用代码",
            trigger: "blur",
          },
        ],
        introduction: [
          { required: true, message: "请输入企业简介", trigger: "blur" },
        ],
        mobile: [
          { required: true, message: "请输入联系电话", trigger: "blur" },
        ],
        enterpriseAddress: [
          { required: true, message: "请输入注册地址", trigger: "blur" },
        ],
        nationalStandardIndustry: [
          { required: true, message: "请输入所属行业", trigger: "blur" },
        ],
        businessScope: [
          { required: true, message: "请输入经营范围", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    querySearchAsync(queryString, cb) {
      if (!queryString) {
        return;
      }
      newEnterpriseSearchAPI({
        keyword: queryString,
        pageNum: 1,
        pageSize: 10,
      })
        .then((response) => {
          const companies = response.result.records.map((item) => {
            return {
              ...item,
              value: item.enterpriseName,
              companyId: item.id,
              uniCode: item.unifiedSocialCreditCode,
            };
          });
          cb(companies);
          this.$refs.myAutocomplete.$refs.suggestions.popperJS.update();
        })
        .catch((error) => {
          console.error("Error fetching companies:", error);
        });
    },
    handleSelect(item) {
      this.uniCodeInput = true;
      this.company = item;
      this.company.uniCode = item.uniCode;
    },
    openDialog() {
      this.dialogVisible = true;
    },
    submitForm() {
      this.$refs.companyForm.validate((valid) => {
        if (valid) {
          addEnterpriseAPI_investment({
            ...this.company,
            clueSource: "2",
          }).then((response) => {
            if (response.result) {
              this.$message.success(
                "该企业属于哒达招商收集企业，已帮自动您添加企业相关信息"
              );
            } else {
              this.$message.success("添加企业成功！");
            }
            this.dialogVisible = false;
            this.resetForm();
            this.uniCodeInput = false;
            this.$emit("updateList");
          });
        } else {
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.companyForm.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.add-new-company {
  // 抽屉内容样式
  .drawer-content {
    padding: 0 20px;

    .el-form-item {
      margin-bottom: 20px;

      .el-input,
      .el-textarea {
        width: 100%;
      }
    }

    .form-actions {
      display: flex;
      justify-content: center;
      margin-top: 40px;

      .el-button {
        min-width: 100px;
        margin: 0 20px;
      }
    }
  }
}

::v-deep {
  .el-popper {
    position: absolute !important;
    top: 0;
    left: 0;
  }
}
</style>
