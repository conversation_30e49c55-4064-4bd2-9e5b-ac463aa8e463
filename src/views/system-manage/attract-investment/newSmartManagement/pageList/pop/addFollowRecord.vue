<template>
  <el-drawer
    title="添加跟进记录"
    :visible.sync="dialogVisible"
    direction="rtl"
    size="30%"
    :before-close="()=>dialogVisible = false"
    custom-class="drawer-custom"
  >
    <el-form
      ref="followForm"
      :model="followRecord"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="公司名称">
        <el-input
          v-model="followRecord.enterpriseName"
          disabled
        />
      </el-form-item>
      <el-form-item
        label="当前进度"
        prop="status"
      >
        <el-radio-group v-model="followRecord.status">
          <el-radio label="0">
            跟进中
          </el-radio>
          <el-radio label="1">
            签约成功
          </el-radio>
          <el-radio label="2">
            签约失败
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="跟进日期"
        prop="followDate"
      >
        <el-input
          v-model="followRecord.followDate"
          disabled
        />
      </el-form-item>
      <el-form-item
        label="跟进人"
        prop="follower"
      >
        <el-input
          v-model="followRecord.follower"
          disabled
        />
      </el-form-item>
      <el-form-item
        label="跟进概述"
        prop="description"
      >
        <el-input
          v-model="followRecord.description"
          type="textarea"
          placeholder="请输入跟进概述"
          :rows="4"
        />
      </el-form-item>
      <div class="form-actions">
        <el-button @click="dialogVisible = false">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="submitForm"
        >
          提 交
        </el-button>
      </div>
    </el-form>
  </el-drawer>
</template>

<script>
import { addFollowUpRecordAPI_investment } from '@/api/attractInvestment';
export default {
  name: 'AddFollowRecord',
  data() {
    return {
      dialogVisible: false,
      followRecord: {
        id: '',
        enterpriseName: '',
        status: '0',
        followDate: '',
        follower: '',
        description: ''
      },
      rules: {
        status: [
          { required: true, message: '请选择当前进度', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请输入跟进概述', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    openDialog(row) {
      this.followRecord.id = row.id;
      this.followRecord.enterpriseName = row.enterpriseName;
      this.followRecord.follower = row.beAssignPerson
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      // 格式化时间
      this.followRecord.followDate = today.toLocaleDateString();
      this.dialogVisible = true;
    },
    submitForm() {
      this.$refs.followForm.validate((valid) => {
        if (valid) {
          addFollowUpRecordAPI_investment({
          "clueId": this.followRecord.id,
          "clueDealState": this.followRecord.status,
          "overview": this.followRecord.description
        }).then((response) => {
            this.$message.success('添加跟进记录成功！');
            this.dialogVisible = false;
            this.resetForm();
            this.$emit('updateList')
          });
        } else {
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.followForm.resetFields();
    }
  }
};
</script>

<style scoped lang="scss">
    .form-actions {
      display: flex;
      justify-content: center;
      margin-top: 40px;
      
      .el-button {
        min-width: 100px;
        margin: 0 20px;
      }
    }
</style>