<template>
  <el-drawer
    title="线索指派"
    :visible.sync="dialogVisible"
    direction="rtl"
    size="30%"
    :before-close="() => (dialogVisible = false)"
    custom-class="drawer-custom"
  >
    <el-form
      ref="clueForm"
      :model="clue"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item
        label="企业名称"
        prop="enterpriseName"
      >
        <el-input
          v-model="clue.enterpriseName"
          disabled
        />
      </el-form-item>
      <el-form-item
        label="招商经理"
        prop="recruitmentManager"
      >
        <el-select
          v-model="clue.recruitmentManager"
          placeholder="请选择招商经理"
          :popper-append-to-body="false"
        >
          <el-option
            v-for="item in personList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="线索流转备注"
        prop="clueTransferRemark"
      >
        <el-input
          v-model="clue.clueTransferRemark"
          type="textarea"
          placeholder="请输入线索流转备注"
          :rows="4"
        />
      </el-form-item>
      <div class="form-actions">
        <el-button @click="dialogVisible = false">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="submitForm"
        >
          提 交
        </el-button>
      </div>
    </el-form>
  </el-drawer>
</template>

<script>
import {
  changeAssignPersonAPI_investment,
  listAllChildUserAPI_investment,
} from "@/api/attractInvestment";
export default {
  name: "AssignClue",
  data() {
    return {
      dialogVisible: false,
      clue: {
        id: "",
        enterpriseName: "",
        recruitmentManager: "",
        clueTransferRemark: "",
      },
      rules: {
        recruitmentManager: [
          { required: true, message: "请选择招商经理", trigger: "change" },
        ],
        clueTransferRemark: [
          { required: true, message: "请输入线索流转备注", trigger: "blur" },
        ],
      },
      personList: [],
    };
  },
  methods: {
    openDialog(row) {
      this.clue.enterpriseName = row.enterpriseName;
      this.clue.id = row.id;
      listAllChildUserAPI_investment().then((res) => {
        if (res.code === "SUCCESS") {
          this.personList = res.result.map((item) => {
            item.label = item.key;
            return item;
          });
          this.clue.recruitmentManager = this.personList[0].value;
        } else {
          this.$message.error("无可选的招商经理");
        }
      });
      this.dialogVisible = true;
    },
    submitForm() {
      this.$refs.clueForm.validate((valid) => {
        if (valid) {
          changeAssignPersonAPI_investment({
            clueId: this.clue.id,
            assignUserId: this.clue.recruitmentManager,
            assignUserName: this.personList.find(
              (item) => item.value == this.clue.recruitmentManager
            ).key,
            remark: this.clue.clueTransferRemark,
          }).then((res) => {
            this.$message.success("提交成功");
            this.dialogVisible = false;
            this.resetForm();
            this.$emit("updateList");
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.clueForm.resetFields();
    },
  },
};
</script>

<style scoped lang="scss">
.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 40px;

  .el-button {
    min-width: 100px;
    margin: 0 20px;
  }
}
::v-deep {
  .el-popper {
    position: absolute !important;
    top: 0;
    left: 0;
  }
}
</style>
