<template>
  <div>
    <div class="radar">
      <div class="title">
        招商雷达
        <div
          class="attract"
          @click="go"
        >
          产业招商
        </div>
      </div>
      <div class="content">
        <div class="left">
          <!-- <div class="isoparaffin">
            <div class="name">
              ·强链延链补链推荐情况
            </div>
            <div
              id="it1"
              class="it1"
            />
          </div> -->
          <div class="intention">
            <div class="name">
              ·推荐企业纳入意向率
            </div>
            <div class="intentionBox">
              <div class="intentionItem">
                <img
                  class="IncludedImg"
                  src="https://static.idicc.cn/cdn/pangu/IncludedNum.png"
                >
                <span class="text">纳入意向</span>
                <span class="num"> {{ IncludedNum }}</span>
              </div>
              <div class="gang" />
              <div class="intentionItem">
                <img
                  class="IncludedImg"
                  src="https://static.idicc.cn/cdn/pangu/NotincludedNum.png"
                >
                <span class="text">未纳入意向</span>
                <span class="num">{{ NotincludedNum }}</span>
              </div>
            </div>
          </div>
          <div class="recommend">
            <div class="name">
              ·新增企业推荐情况（月度）
            </div>
            <div
              v-if="!!showBar"
              id="it2"
              class="it2"
            />
            <div
              v-else
              class="it2"
            >
              <el-empty
                size="small"
                :image-size="50"
              />
            </div>
          </div>
        </div>
        <div class="middle">
          <div class="radar-e">
            <div class="name">
              数智招商雷达引擎
            </div>
            <div class="Radar">
              <div
                id="it3"
                class="it3"
              />
            </div>
          </div>
        </div>
        <div class="right">
          <div class="characteristic">
            <div class="name">
              ·推荐企业产业环节分布
            </div>
            <div
              v-if="!!showPie"
              id="it4"
              class="it4"
            />
            <!-- <div v-else class="it4"> -->
            <el-empty
              v-else
              size="small"
              class="it4"
            />
            <!-- </div> -->
          </div>
        </div>
      </div>
    </div>
    <div class="pattern">
      <div class="title">
        招商模式 <span class="t2">5大招商模式，精准推荐企业！</span>
      </div>
      <div class="modeselection">
        <div
          v-for="(item, index) in classifyList"
          :key="index"
          class="single"
          @click="gopage(item.id)"
        >
          <img :src="item.icon">
          <span class="tit">{{ item.name }}</span>
          <el-tooltip
            class="item"
            effect="light"
            :content="item.text"
            placement="top"
          >
            <span class="text">{{ item.text }}</span>
          </el-tooltip>
        </div>
      </div>
    </div>
    <div
      v-if="EnterpriseList.length > 0"
      class="recommendq"
    >
      <div class="title">
        最新推荐
        <div
          class="gd"
          @click="golist"
        >
          更多<i class="el-icon-arrow-right" />
        </div>
      </div>
      <enterprise
        v-loading="ListLoading"
        :enterprise-list="EnterpriseList"
        @particulars="particulars"
        @updataList="updataList"
      />
    </div>
  </div>
</template>

<script>
import enterprise from "./components/enterprise2.vue";
import * as echarts from "echarts";
// import NoData from '@/views/overview/components/component/noData';

//import Word3D from "./components/wordcloud3D.vue";
import "@/utils/echarts-wordcloud.min.js";
import {
  recommendedStatisticsAPI,
  intentionRateAPI,
  characteristicAPI,
  /*   homeListAPI, */
  merchantstypeCountAPI,
  merchantsAPI,
} from "../apiUrl";
import {
  recommendedStatisticsAPI_V2,
  characteristicAPI_V2,
  merchantstypeCountAPI_V2,
  intentionRateAPI_V2,
  merchantsAPI_V2,
  InformationStatisticsV2API,
} from "../apiUrl_v2";
export default {
  name: "RadarHome",
  components: {
    //Word3D,
    enterprise,
  },
  props: {
    industryId: {
      type: String,
      default: "",
    },
    parentChainId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      intention: 0, //意向率
      characteristicList: [], //词云图
      //industryId: "", //跳转到数智招商
      classifyList: [
        {
          icon: "https://static.idicc.cn/cdn/pangu/pro-business.png",
          name: "亲缘招商",
          text: "通过大数据挖掘企业高管的籍贯、母校、履历等数据信息，与招商需求地进行精准关联匹配",
          id: 1,
        },
        {
          icon: "https://static.idicc.cn/cdn/pangu/Chainowner.png",
          name: "链式招商",
          text: "深度挖掘招商需求地企业的上下游及潜在合作伙伴，并通过动迁模型计算，形成潜在招商目标企业",
          id: 3,
        },
        {
          icon: "https://static.idicc.cn/cdn/pangu/policy.png",
          name: "舆情招商",
          text: "实时追踪政府产业政策、企业成长“事件”、其他政府招商动向，深度分析舆情情报",
          id: 5,
        },
        {
          icon: "https://static.idicc.cn/cdn/pangu/zs.png",
          name: "人才招商",
          text: "构建国家级领军人才、省级领军人才、市级领军人才三类创新创业人才库，AI智能构建人才和产业的关系，寻找符合地方产业发展的高级人才。旨在协助通过吸引和引进高素质人才，以促进地方经济发展",
          id: 7,
        },
        {
          icon: "https://static.idicc.cn/cdn/pangu/resource.png",
          name: "资本招商",
          text: "针对特定产业链，寻找成立时间短，且具有高成长型、高培育型的“早”、“小”创业型企业，或新专利转化项目，旨在为实现投早、投小的资本招商目标",
          id: 8,
        },
        // {
        //   icon: require("@/views/system-manage/attract-investment/img/AI.png"),
        //   name: "AI+招商",
        //   text: "基于对海量历史投资数据训练学习，预测企业未来投资意向",
        //   id: 6,
        // },
      ],
      graphic: [
        {
          type: "rect",
          left: "center",
          top: "middle",
          shape: {
            width: 160,
            height: 70,
            radius: 20,
          },
          style: {
            fill: "#cdd1d7",
            stroke: "#ccc",
          },
          z: 10,
        },
        {
          type: "text",
          left: "center",
          top: "middle",
          style: {
            text: "暂无推荐企业",
            textAlign: "center",
            textVerticalAlign: "middle",
            fill: "#fff",
            fontSize: 18,
          },
          z: 10,
        },
      ],
      List: [],
      EnterpriseList: [],
      ListLoading: false,
      NotincludedNum: 0,
      IncludedNum: 0,
      showPie: true,
      showBar: true,
    };
  },
  created() {
    //this.getList();
    this.merchants();
    this.getIntentionrate(); //意向率
  },
  mounted() {
    // this.dynamicstate();
    this.tendencyFn();
    //this.a()
    //this.getIint();
    this.radarFn();
  },
  methods: {
    updataPage() {
      this.merchants();
      this.getIntentionrate();
      this.tendencyFn();
      //this.getIint();
      this.radarFn();
    },
    // 操作后刷新列表
    updataList() {
      this.merchants();
    },
    async merchants() {
      try {
        this.ListLoading = true;
        const res = await merchantsAPI_V2({
          pageNum: 1,
          pageSize: 5,
          chainId: this.parentChainId,
        });
        this.EnterpriseList = res.result.records;
        let result = this.EnterpriseList.reduce((acc, cur) => {
          if (!acc[cur.recommendedDate]) {
            acc[cur.recommendedDate] = {
              recommendedDate: cur.recommendedDate,
              list: [],
            };
          }
          acc[cur.recommendedDate].list.push(cur);
          return acc;
        }, {});
        this.EnterpriseList = Object.values(result);
        //console.log(this.EnterpriseList);
      } catch (error) {
        console.log(error);
      } finally {
        this.ListLoading = false;
      }
    },
    //雷达
    async radarFn() {
      const res = await merchantstypeCountAPI_V2({
        chainId: this.parentChainId,
      });

      let data = Object.keys(res.result)
        .filter(
          (key) =>
            key.includes("亲缘") ||
            key.includes("舆情") ||
            key.includes("人才") ||
            key.includes("资本") ||
            key.includes("链式")
        )
        .map((key) => ({
          //瘦身只展示这三个数据
          name: key,
          value: res.result[key],
        }));
      let maxVal = Math.max(...data.map((item) => item.value));
      let minVal = Math.min(...data.map((item) => item.value));
      if (maxVal == 0 && minVal == 0) {
        maxVal = 1;
      }
      data = data.map((item) => ({
        name: item.name,
        value: item.value,
        id: 1,
        offsetData:
          Math.round(((item.value - minVal) / (maxVal - minVal)) * (90 - 55)) +
          55,
      }));
      if (!data || !data.length) {
        this.showBar = false;
        return;
      }
      this.showBar = true;
      var chartDom = document.getElementById("it3");
      var myChart = echarts.init(chartDom);
      var option;
      var offsetDataList = [
        [
          [30, 65],
          [20, 40],
          [40, 33],
          [72, 30],
          [60, 70],
          [70, 52],
        ],
        [
          [50, 75],
          [20, 60],
          [30, 40],
          [60, 25],
          [70, 60],
          [80, 40],
        ],
        [
          [50, 75],
          [20, 60],
          [40, 50],
          [40, 25],
          [70, 60],
          [75, 40],
        ],
        [
          [30, 45], //qin
          [60, 70], //zi
          [40, 66], //lian
          [65, 55], //zc
          [70, 38], //yv
          [40, 26],
        ],
      ];
      var randomNumber = Math.floor(Math.random() * 4);
      let offsetData = offsetDataList[randomNumber];
      var datas = [];
      for (var i = 0; i < data.length; i++) {
        var item = data[i];
        var color =
          i % 2 === 0 ? "rgba(20, 126, 255,1)" : "rgba(51, 221, 213,1)";
        datas.push({
          name: "{a|" + item.name + "}\n{b|" + item.value + "}",
          value: offsetData[i],
          symbolSize: data[i].offsetData,
          label: {
            normal: {
              rich: {
                a: {
                  fontSize: 12,
                  padding: [5, 0, 0, 0],
                },
                b: {
                  fontSize: 12,
                  padding: [10, 0, 0, 15],
                },
              },
            },
          },
          itemStyle: {
            normal: {
              color,
              opacity: 1,
            },
          },
        });
      }

      option = {
        grid: {
          show: false,
          top: 10,
          bottom: 10,
        },
        xAxis: {
          type: "value",
          show: false,
          min: 0,
          max: 100,
          nameLocation: "middle",
          nameGap: 5,
        },
        yAxis: {
          min: 0,
          show: false,
          max: 100,
          nameLocation: "middle",
          nameGap: 30,
        },

        series: [
          {
            type: "scatter",
            symbol: "circle",
            symbolSize: 120,
            label: {
              normal: {
                show: true,
                formatter: "{b}",
                color: "#fff",
                textStyle: {
                  fontSize: "12",
                },
              },
            },
            itemStyle: {
              normal: {
                color: "red",
              },
              fontSize: "12",
            },
            data: datas,
          },
        ],
      };
      myChart.off("click"); // 这里很重要！！！
      let self = this;
      myChart.on("click", function (params) {
        const obj = self.classifyList.find((item) =>
          params.data.name.includes(item.name)
        );
        if (obj) {
          let id = obj.id;
          self.gopage(id);
        }
      });
      option && myChart.setOption(option);
    },
    a() {
      this.$refs.word3D.setTag(); // 数据集
    },
    async getIint(res) {
      let chartDom = document.getElementById("it4");
      let myChart = echarts.init(chartDom);
      let option;
      if (!res) {
        this.showPie = false;
        if (chartDom) {
          myChart.clear();
        }
        return;
      }
      this.showPie = true;

      // echarts参数设置
      let allNum = res?.all || 0;
      let data = Object.entries(res?.nodeCount).map(([name, value]) => ({
        name: name,
        value: value,
      }));
      /*  let data = [
         { value: 1048, name: 'Search Engine' },
         { value: 735, name: 'Direct' },
         { value: 580, name: 'Email' },
         { value: 484, name: 'Union Ads' },
         { value: 300, name: 'Video Ads' }
       ] */
      option = {
        grid: {
          left: "20%",
        },
        color: [
          "#0098FA",
          "#0CD9B5",
          "#3B72AD",
          "#FDCC00",
          "#B780E1",
          "#F27629",
        ],
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          right: "5%",
          top: "middle",
          itemGap: 12,
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            padding: [0, 0, 0, 4], // 缩小文字左侧间距
            fontSize: 12, // 缩小字体
          },
          formatter: function (name) {
            // 最多显示6个字符，超出部分用...表示
            return name.length > 6 ? name.substring(0, 6) + "..." : name;
          },
        },
        graphic: [
          {
            type: "text",
            left: "32%",
            top: "42%",
            style: {
              text: `{labelStyle|总数}\n{valueStyle|${allNum}}`,
              rich: {
                labelStyle: {
                  fontSize: 12,
                  fill: "rgba(0, 0, 0, 0.4)",
                  lineHeight: 30,
                },
                valueStyle: {
                  fontSize: 28,
                  fill: "#000",
                  fontWeight: "bold",
                  lineHeight: 40,
                },
              },
              textAlign: "center",
              lineHeight: 60,
            },
          },
        ],
        series: [
          {
            name: "",
            type: "pie",
            radius: ["32%", "55%"], // 恢复较大半径
            center: ["35%", "50%"], // 垂直位置上移
            avoidLabelOverlap: true,
            itemStyle: {
              borderWidth: 0,
              borderColor: "#fff",
            },
            label: {
              show: true,
              position: "inside",
              formatter: "{d}%",
              fontSize: 13,
              fontWeight: "normal",
              color: "#fff",
              //textBorderColor: 'rgba(0,0,0,0.3)',
              //textBorderWidth: 1
            },
            emphasis: {
              scale: true,
              scaleSize: 8,
              itemStyle: {
                shadowBlur: 20,
                shadowColor: "rgba(0, 0, 0, 0.3)",
              },
            },
            data,
          },
        ],
      };
      option && myChart.setOption(option);
    },
    gopage(id) {
      this.$emit("demodel", id);
      // document.documentElement.scrollTop = 0;
      document.body.style.overflow = "auto";
      this.$nextTick(() => {
        document.body.scrollTop = 0;
        document.body.style.overflow = "visible";
      });
    },
    particulars(item) {
      this.$emit("particulars", item, 2);
    },
    golist() {
      this.$emit("cut", 2);
    },
    /*     async getList() {
      const res = await homeListAPI();
      if (res.result.length >= 1) {
        this.industryId = res.result[0].id;
      }
    }, */
    //意向率
    async getIntentionrate() {
      const res = await InformationStatisticsV2API({
        chainId: this.parentChainId,
      });
      this.IncludedNum = res.result?.followUpStatistics?.纳入意向 || 0;
      this.NotincludedNum = res.result?.followUpStatistics?.未纳入意向 || 0;

      this.getIint(res.result?.industrialLinkDistributionStatistics);
    },
    //强延补
    async dynamicstate() {
      const res = await recommendedStatisticsAPI_V2({
        type: 1,
      });
      let obj = res.result;
      var chartDom = document.getElementById("it1");
      var myChart = echarts.init(chartDom);
      var option;
      // 总数
      let sum = Object.values(obj).reduce((acc, cur) => acc + parseInt(cur), 0);
      // 各种类型
      let data = [
        {
          name: "延链",
          value: obj.延链 || 0,
        },
        {
          name: "补链",
          value: obj.补链 || 0,
        },
        {
          name: "强链",
          value: obj.强链 || 0,
        },
      ];

      option = {
        /*  graphic: [
     {
       type: 'rect',
       left: 'center',
       top: 'middle',
       shape: {
         width: 200,
         height: 100
       },
       style: {
         fill: '#fff',
         stroke: '#ccc'
       }
     },
     {
       type: 'text',
       left: 'center',
       top: 'middle',
       style: {
         text: '暂无数据',
         textAlign: 'center',
         textVerticalAlign: 'middle'
       }
     }
   ], */
        tooltip: {
          trigger: "item",
          backgroundColor: "#00112dbf", // 提示框浮层的背景颜色。
          borderColor: "#0066FF", // 提示框浮层的边框颜色。
          borderWidth: 1, // 提示框浮层的边框宽。
          textStyle: {
            // 提示框浮层的文本样式。
            color: "#fff",
            fontStyle: "normal",
            fontWeight: "normal",
            fontFamily: "sans-serif",
            fontSize: 12,
          },
          //实例
          formatter: function (params) {
            let res =
              params.data.name +
              "： " +
              '<span style="color: #00FFF0;font-size: 12px;">' +
              params.data.value +
              "</span> 家";
            return res;
          },
        },
        // 圆心文字
        title: [
          {
            text: "按类型分布",
            top: "42%",
            textAlign: "center",
            left: "34%",
            textStyle: {
              color: "rgba(0,0,0,1)",
              fontSize: 14,
              fontWeight: "400",
              fontFamily: "Source Han Sans CN-Regular, Source Han Sans CN",
            },
          },
          {
            text: sum,
            top: "53%",
            textAlign: "center",
            left: "34%",
            textStyle: {
              color: "rgba(96, 98, 102,1)",
              fontSize: 20.3,
              fontWeight: "400",
              fontFamily: "Source Han Sans CN-Regular, Source Han Sans CN",
            },
          },
        ],
        legend: {
          textStyle: {
            color: "rgba(0,0,0,0.65)",
            fontSize: 12,
            opacity: 0.8,
            lineHeight: 33, // 设置文字之间的上下间距
          },
          // right: "right",//调整图例位置
          left: "60%",
          orient: "vertical",
          bottom: "middle",
          //itemGap: 16, // 设置图例项之间的间距
          itemWidth: 15,
          top: "12%", //调整图例位置
          itemHeight: 7, //修改icon图形大小
          icon: "circle", //图例前面的图标形状
        },
        series: [
          {
            name: "Access From",
            type: "pie",
            radius: ["60%", "80%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 0,
              borderColor: "#fff",
              borderWidth: 0,
            },
            left: "-30%",
            top: "10%",
            label: {
              show: false,
              position: "center",
            },
            labelLine: {
              show: false,
            },
            data: data,
          },
        ],
      };

      option && myChart.setOption(option);
    },
    //新增企业推荐情况统计
    async tendencyFn() {
      const res = await recommendedStatisticsAPI_V2({
        type: 2,
        chainId: this.parentChainId,
      });
      let obj = res.result;
      let key = Object.keys(obj);
      let value = Object.values(obj);

      // if(key.length <= 1) {
      //   key.push('2250-01')
      //   key.unshift('1970-01')
      //   value.push({2250: 0})
      //   value.unshift({1970: 0})
      // }
      // console.log(key,obj);
      let chartDom = document.getElementById("it2");
      let myChart = echarts.init(chartDom);
      let option;

      option = {
        grid: {
          left: "5%",
          right: "10%",
          bottom: "5%",
          top: "15%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow", // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
            axis: "auto", // 指示器的坐标轴。
            snap: true, // 坐标轴指示器是否自动吸附到点上
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: key,
          // 刻度
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          /*  name: "家",
                    nameTextStyle: {
                      padding: [0, 28, 0, 0],
                    }, */
        },
        series: [
          {
            data: value,
            type: value.length <= 1 ? "bar" : "line",
            barWidth: "20px",

            // type: 'bar',
            smooth: true,
            showSymbol: false,
            lineStyle: {
              color: "#165DFF",
            },
            itemStyle: {
              color: "#165DFF",
              normal: {
                barBorderRadius: 4,
                // color: '#E5EDFF',
                color: value.length <= 1 ? "#3370FF" : "#E5EDFF",
              },
            },
            areaStyle: {},
          },
        ],
      };

      option && myChart.setOption(option);
    },
    go() {
      if (this.industryId) {
        localStorage.setItem('routerQuery', this.industryId);
        const baseURL = process.env.VUE_APP_PORT_URL;
        let newPath = `${baseURL}#/attractInvestment?id=${this.industryId}`;
        window.open(newPath, '_blank');
      } else {
        this.$message.error("未获取到产业链");
      }
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep {
  .el-progress--circle .el-progress__text,
  .el-progress--dashboard .el-progress__text {
    top: 58%;
  }
}

.radar {
  height: 588px;
  margin: 24px 0;
  background-color: #fff;
  box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
  border-radius: 10px 10px 10px 10px;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 51px;
    border-bottom: 1px solid #e9e9e9;
    padding-left: 24px;
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    //padding-top: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);

    .attract {
      width: 109px;
      margin-right: 24px;
      height: 32px;
      background: #fbfcfe;
      box-shadow: 0px 2px 1px 0px rgba(64, 72, 82, 0.05);
      opacity: 1;
      border: 1px solid #dde4f0;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #3370ff;
    }
  }

  .content {
    display: flex;
    justify-content: space-around;
    padding: 24px 0;

    .left {
      width: 28%;

      .isoparaffin {
        height: 230px;
        width: 100%;
        background: #ffffff;
        border-radius: 10px 10px 10px 10px;
        opacity: 1;
        border: 1px solid #dde4f0;

        .name {
          padding-top: 20px;
          padding-left: 20px;
          font-size: 14px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #1d2129;
        }

        .it1 {
          width: 100%;
          height: 180px;
        }
      }

      .intention {
        height: 230px;
        width: 100%;
        background: #ffffff;
        border-radius: 10px 10px 10px 10px;
        opacity: 1;
        border: 1px solid #dde4f0;

        .intentionBox {
          display: flex;
          margin-top: 44px;

          .gang {
            width: 1px;
            height: 94px;
            background-color: #d8d8d8;
          }

          .intentionItem {
            width: 42%;
            padding-left: 50px;
            display: flex;
            justify-content: center;
            flex-direction: column;

            .num {
              font-size: 28px;
              font-weight: bold;
              color: #000000;
            }

            .text {
              font-size: 14px;
              font-weight: 500;
              color: rgba(29, 33, 41, 0.8);
              margin-bottom: 8px;
            }

            .IncludedImg {
              width: 28px;
              height: 28px;
              margin-bottom: 8px;
            }
          }
        }

        .name {
          padding-top: 20px;
          padding-left: 20px;
          font-size: 14px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #1d2129;
        }

        .it5 {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 180px;
          position: relative;

          .text {
            position: absolute;
            font-size: 14px;
            color: rgba(0, 0, 0, 1);
            top: 40%;
            left: 50%;
            transform: translate(-50%, -50%);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
          }
        }
      }

      .recommend {
        height: 230px;
        width: 100%;
        margin-top: 24px;
        background: #ffffff;
        border-radius: 10px 10px 10px 10px;
        opacity: 1;
        border: 1px solid #dde4f0;

        .name {
          padding-top: 20px;
          padding-left: 20px;
          font-size: 14px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #1d2129;
        }

        .it2 {
          width: 100%;
          height: 180px;
        }
      }
    }

    .middle {
      width: 36%;

      .radar-e {
        .name {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 30px;
          font-family: Source Han Sans CN-Medium, Source Han Sans CN;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
        }

        .Radar {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .it3 {
          width: 380px;
          height: 380px;
          margin-top: 23px;
          background: url("../img/Radarbackground.png") no-repeat;
          background-size: 380px 380px;
        }
      }
    }

    .right {
      width: 28%;

      .characteristic {
        height: 484px;
        width: 100%;
        background: #ffffff;
        border-radius: 10px 10px 10px 10px;
        opacity: 1;
        border: 1px solid #dde4f0;

        .name {
          padding-top: 20px;
          padding-left: 20px;
          font-size: 14px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #1d2129;
        }

        .it4 {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 98%;
          height: 444px;
        }
      }
    }
  }
}

.pattern {
  height: 450px;
  background: #ffffff;
  box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
  border-radius: 10px 10px 10px 10px;
  margin: 24px 0;
  opacity: 1;

  .title {
    display: flex;
    height: 51px;
    border-bottom: 1px solid #e9e9e9;
    padding-left: 24px;
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    padding-top: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);

    .t2 {
      font-size: 12px;
      padding-left: 10px;
      padding-top: 4px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .modeselection {
    display: flex;
    justify-content: space-around;
    padding: 0px 20px;

    .single {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 276px;
      height: 335px;
      margin-left: 20px;
      background: #fbfcfe;
      box-shadow: 0px 2px 1px 0px rgba(64, 72, 82, 0.05);
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid rgba(221, 228, 240, 0.6);
      margin-top: 24px;
      flex-direction: column;
      cursor: pointer;

      img {
        width: 67px;
        height: 67px;
        margin-bottom: 16px;
      }

      .tit {
        font-size: 20px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }

      .text {
        margin-top: 20px;
        font-size: 14px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        width: 65%;
        line-height: 18px;
        color: rgba(0, 0, 0, 0.65);

        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 5;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}

.recommendq {
  background-color: #fff;
  box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
  border-radius: 10px 10px 10px 10px;

  //margin: 24px;
  .title {
    display: flex;
    justify-content: space-between;
    text-align: center;
    align-items: center;
    height: 51px;
    border-bottom: 1px solid #e9e9e9;
    padding-left: 24px;
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);

    .gd {
      margin-right: 24px;
      font-size: 12px;
      padding-top: 4px;
      cursor: pointer;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #4e5969;
    }
  }
}
</style>
