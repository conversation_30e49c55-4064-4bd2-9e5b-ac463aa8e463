<template>
  <!-- 招商企业推荐 -->
  <div>
    <div class="search">
      <div class="using">
        <div class="left">
          关键词：
        </div>
        <div class="antistop">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键词"
            @input="achangeindata"
          />
        </div>
      </div>
      <div class="using">
        <div class="left">
          招商模式：
        </div>
        <div class="antistop">
          <div
            v-for="(item, index) in appearList"
            :key="index"
            :class="form.market == item.id ? 'pitchon' : 'pitch'"
            @click="screenappear(item)"
          >
            {{ item.labelName }}
          </div>
        </div>
      </div>
      <div class="using">
        <div class="left">
          推荐时间：
        </div>
        <div class="antistop">
          <el-date-picker
            v-model="form.daterange"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="——"
            popper-class="poperstyle"
            format="yyyy-MM-dd"
            value-format="timestamp"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            @change="achangeindata"
          />
        </div>
      </div>
      <div class="find">
        <div
          class="cz"
          @click="reset"
        >
          重置
        </div>
        <div
          class="seek"
          style="margin-left: 20px"
          :class="Prohibit ? '' : 'no'"
          @click="inquire"
        >
          查询
        </div>
      </div>
    </div>
    <div
      v-if="showTO"
      class="EnterpriseList"
    >
      <div class="headline">
        <div class="total">
          <span>共找到{{ total }}家企业</span>
          <el-tooltip
            effect="dark"
            content="默认导出前5000条数据"
            placement="top"
          >
            <div
              v-loading="xzloading"
              class="export"
              @click="exportex"
            >
              <img
                src="https://static.idicc.cn/cdn/pangu/interactive.png"
                class="up"
              >导出数据
            </div>
          </el-tooltip>
        </div>
      </div>
      <EnterpriseList
        v-loading="ListLoading"
        :enterprise-list="EnterpriseList"
        @particulars="particulars"
        @updataList="updataList"
      />
      <span
        v-if="EnterpriseList.length==0"
        style="display: flex;align-items: center;justify-content: center;margin-top: 50px;font-size: 18px;"
      >暂无数据</span>
      <div class="ye">
        <el-pagination
          :current-page.sync="form.pageNum"
          :page-sizes="[5, 10, 20, 50]"
          :page-size.sync="form.pageSize"
          :total="+total"
          layout="sizes, prev, pager, next, jumper"
          @size-change="search"
          @current-change="search"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { merchantsAPI } from "../apiUrl";
import { merchantsAPI_V2 } from "../apiUrl_v2";
import { investment_enterprisedownloadAPI } from '@/api/export';
import EnterpriseList from "./components/enterprise.vue";
export default {
  name: "AttRecommend",
  components: {
    EnterpriseList,
  },
  props: {
    parentChainId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      EnterpriseList: [],
      form: {
        keyword: "", //关键词
        pageNum: 1,
        pageSize: 5,
        daterange: [],
        market: -1,
      },
      total: 0,
      Prohibit: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      ListLoading: false,
      showTO:false,
      appearList: [
        //{ labelName: "全部", id: -1 },
        { labelName: "亲缘招商", id: 1 },
        { labelName: "链式招商", id: 3 },
        { labelName: "舆情招商", id: 5 },
        { labelName: "人才招商", id: 7 },
        { labelName: "资本招商", id: 8 },
        
        // { labelName: "AI+招商", id: 6 },
      ],
      xzloading:false
    };
  },
  created(){
    this.achangeindata()
    this.search()
  },
  methods: {
     updataPage() {
      this.search()
    },
    inquire() {
      if (!this.Prohibit) {
        return false;
      }
      this.form.pageNum = 1;
      this.search();
    },
    async exportex(){
    if(this.xzloading){
      return this.$message.warning("正在导出中，请耐心等待")
    }
    if(this.total==0){
      return this.$message.warning("这里还什么都没有~")
    }
        let startDateStamp = "";
        let endDateStamp = "";
        if (this.form.daterange == null) {
          this.form.daterange = [];
        }
        if (this.form.daterange.length > 1) {
          startDateStamp = this.form.daterange[0];
          endDateStamp = this.form.daterange[1];
        }
        let data = {
          pageNum: 1,
          pageSize: 5000,
          keyword: this.form.keyword,
          type: this.form.market,
          startDateStamp,
          endDateStamp,
          chainId: this.parentChainId
        };
        try {
        this.xzloading=true
        const res=   await investment_enterprisedownloadAPI(data)
        if(res.msg){
          return this.$message.error(res.msg)
        }
        let blob = new Blob([res], {
          type: "text/csv,charset=UTF-8",
        });
        let objectUrl = URL.createObjectURL(blob);
        const fileName = `招商企业推荐列表.xlsx`;
        const downloadLink = document.createElement("a");
        downloadLink.href = objectUrl;
        downloadLink.download = fileName;
        downloadLink.click(); 
        } finally  {
          this.xzloading=false
        }

    },
    // 企业详情
    particulars(item) {
      this.$emit("particulars", item, 2);
    },
    // 查询按钮状态
    achangeindata() {
      if (this.form.daterange == null) {
        this.form.daterange = [];
      }
      if (
        this.form.keyword !== "" ||
        this.form.market !== -1 ||
        this.form.daterange.length > 0
      ) {
        this.Prohibit = true;
      } else {
        this.Prohibit = false;
      }
    },
    // 操作后刷新列表
    updataList() {
      this.search();
    },
    reset() {
      this.form = {
        keyword: "", //关键词
        pageNum: 1,
        pageSize: 5,
        daterange: [],
        market: -1,
      };
      this.achangeindata();
      this.search()
    },
    async search() {
      this.showTO=true
      try {
        this.ListLoading = true;
        let startDateStamp = "";
        let endDateStamp = "";
        if (this.form.daterange == null) {
          this.form.daterange = [];
        }
        if (this.form.daterange.length > 1) {
          startDateStamp = this.form.daterange[0];
          endDateStamp = this.form.daterange[1];
        }
        let data = {
          pageNum: this.form.pageNum,
          pageSize: this.form.pageSize,
          keyword: this.form.keyword,
          type: this.form.market,
          startDateStamp,
          endDateStamp,
          chainId: this.parentChainId
        };
        const res = await merchantsAPI_V2(data);
        this.total = res.result.total;
        this.EnterpriseList = res.result.records;
      } finally {
        this.ListLoading = false;
      }
    },
    screenappear(it) {
      if (this.form.market == it.id) {
        this.form.market = -1;
      } else {
        this.form.market = it.id;
      }
      this.achangeindata();
    },
  },
};
</script>
<style lang="scss">
.poperstyle{
  margin-left: 100px !important;
}
</style>
<style lang="scss" scoped>
.poperstyle{
  margin-left: 100px;
}
  .ye{
    padding: 0 10px;
  }
.no {
  background: #d9d9d9 !important;
  border: 0px solid #d9d9d9 !important;
  color: #ffffff !important;
  cursor: no-drop !important;
}
.cz {
  width: 65px;
  height: 32px;
  background: #ffffff;
  border-radius: 4px;
  opacity: 1;
  border: 1px solid #d9d9d9;
  text-align: center;
  line-height: 32px;
  margin-right: 10px;
  font-size: 14px;
  font-family: Source Han Sans CN-Normal, Source Han Sans CN;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
  cursor: pointer;
}
.search {
  height: 270px;
  margin: 16px 0;
    box-shadow: 0px 4px 12px 0px #EEF1F8;
  padding: 24px;
  border-radius: 10px;
  background-color: #fff;

  .using {
    display: flex;
    margin-bottom: 20px;

    .left {
      width: 70px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
    }

    .antistop {
      width: 80%;
      margin-left: 14px;
      display: flex;
      flex-wrap: wrap;

      .pitchon {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 26px;
        cursor: pointer;
        background: #3370FF;
        border-radius: 2px 2px 2px 2px;
        margin-top: 8px;
        margin-right: 20px;
        padding: 2px 8px;
        opacity: 1;
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        font-weight: 400;
        color: #ffffff;
      }

      .pitch {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 26px;
        cursor: pointer;
        border-radius: 2px 2px 2px 2px;
        margin-top: 8px;
        padding: 2px 8px;
        margin-right: 20px;
        opacity: 1;
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
      }

      .multi {
        margin-right: 30px;
      }
    }
  }

  .selected {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    background: #e6f7ff;
    opacity: 1;
    border: 1px solid #bae7ff;

    .left {
      display: flex;

      .yet {
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        font-weight: 400;
        margin-left: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        color: rgba(0, 0, 0, 0.65);
      }

      .tag {
        font-size: 12px;
        font-family: Source Han Sans CN-Normal, Source Han Sans CN;
        font-weight: 400;
        color: #3370FF;
        padding: 2px 8px;
        margin-left: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 22px;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #3370FF;
      }
    }
    .reset {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 22px;
      font-size: 14px;
      font-family: Abel-Regular, Abel;
      font-weight: 400;
      color: #3370FF;
    }
  }
  .find {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
    .seek {
      width: 65px;
      border-radius: 4px;
      height: 32px;
      background: #3370FF;
      opacity: 1;
      display: flex;
      align-items: center;
      cursor: pointer;
      justify-content: center;
      font-size: 14px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }
  }
}
.EnterpriseList {
  background-color: #fff;
  height: auto;
  border-radius: 10px;
  margin: 16px 0;
    box-shadow: 0px 4px 12px 0px #EEF1F8;

  .headline {
    display: flex;
    height: 64px;
    justify-content: space-between;
    border-bottom: 1px solid #e9e9e9;

    .total {
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
      font-size: 16px;
      padding: 22px 0px 13px 24px;
      .export{
        cursor: pointer;
        margin-left: 16px;
        width: 82px;
        height: 24px;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px solid #CED4DB;
        font-size: 12px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #3F4A59;
        line-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        .up{
        width: 14px;
        height: 14px;
        margin-right: 5px;
      }
      }
    }

    .orderingRule {
      padding: 12px 24px 9px 0px;
    }
  }
}
</style>