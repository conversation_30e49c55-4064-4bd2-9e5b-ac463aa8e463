<template>
  <div>
    <div class="title">
      <div class="iconarrow">
        <i
          style="cursor: pointer"
          class="el-icon-arrow-left"
          @click="gobacka"
        />
      </div>
      <div class="scan">
        <div class="scan-top">
          <div class="scan-tab-con">
            <div class="scan-tab">
              <div
                v-for="(it, index) in tabList"
                :key="index"
                class="scan-tab-list"
                :class="whichs == it.id ? 'on' : ''"
                @click="cut(it.id)"
              >
                {{ it.name }}({{ it.sum }})
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="industrys">
      <div class="head">
        <span class="span">推荐企业产业链分布</span>
        <div class="industryXZ">
          <!--           <div
            v-for="(item, index) in industryList"
            :key="index"
            :class="industryId == item.industryChainId ? 'pitchs' : 'nopitch'"
            @click="industrycut(item)"
          >
            {{ item.chainName.replace("产业金脑·", "") }}
          </div> -->
          <!--           <el-select
            ref="chain"
            v-model="industryId"
            placeholder="请选择"
            :popper-append-to-body="false"
            @change="industrycut"
          >
            <el-option
              v-for="item in industryList"
              :key="item.industryChainId"
              :label="item.chainName"
              :value="item.industryChainId"
            />
          </el-select> -->
        </div>
      </div>
      <div class="distribution">
        <div
          v-loading="industryLoading"
          class="left"
        >
          <!--  {{ ChainList }} -->
          <industrydistribution
            ref="industrydistribution"
            :chain-list="ChainList"
          />
        </div>
        <div class="right">
          <el-table
            v-loading="industryListLoading"
            :border="true"
            :data="tableData2"
            style="width: 100%"
          >
            <el-table-column
              prop="nodeName"
              label="产业环节"
              width="200"
              align="center"
            />
            <el-table-column
              label="推荐企业数量"
              prop="relationEnterpriseCount"
              align="center"
            />
            <el-table-column
              prop="ratio"
              align="center"
              label="占比"
            />
          </el-table>
          <el-pagination
            :current-page.sync="industryform.pageNum"
            :page-size.sync="industryform.pageSize"
            :total="+industryform.total"
            layout="prev, pager, next"
            background
            @current-change="currentChange2"
          />
        </div>
      </div>
    </div>
    <div class="industrys">
      <div class="head">
        <span class="span">推荐地区企业分布</span>
        <div class="industryXZ appendToBodyFalse">
          <el-cascader
            ref="cascader"
            v-model="regionVal"
            clearable
            :props="regionProp"
            placeholder="选择地区"
            @change="regionChange"
          />
        </div>
      </div>
      <div class="distribution">
        <div class="left">
          <mapMain
            ref="map"
            class="map"
          />
        </div>
        <div class="right">
          <el-table
            v-loading="regionListLoading"
            :border="true"
            :data="tableData"
            style="width: 100%"
          >
            <el-table-column
              prop="name"
              label="地区"
              width="200"
              align="center"
            />
            <el-table-column
              label="推荐企业数量"
              prop="count"
              align="center"
            />
            <el-table-column
              prop="ratio"
              align="center"
              label="占比"
            />
          </el-table>
          <el-pagination
            :current-page.sync="regionform.pageNum"
            :page-size.sync="regionform.pageSize"
            :total="+regionform.total"
            layout="prev, pager, next"
            background
            @current-change="currentChange"
          />
        </div>
      </div>
    </div>
    <div
      class="industrys"
      style="height: auto"
    >
      <div class="head">
        <div class="intimate">
          <span class="span">推荐企业列表 </span>
          <el-tooltip
            effect="dark"
            content="默认导出前5000条数据"
            placement="top"
          >
            <div
              v-loading="xzloading"
              class="export"
              @click="exportex"
            >
              <img
                src="https://static.idicc.cn/cdn/pangu/interactive.png"
                class="up"
              >导出数据
            </div>
          </el-tooltip>
          <!-- <div
            v-if="whichs == 1"
            class="atlas"
            @click="Correlationmap"
          >
            亲商关联图谱
          </div> -->
        </div>
        <div class="industryXZ appendToBodyFalse">
          <!-- <el-cascader
              v-model="secondNodeIds"
              :append-to-body="false"
              :options="oneList"
              :props="{ checkStrictly: true }"
              clearable
              @change="refreshList"
            /> -->
          <el-input
            v-model="keyword"
            placeholder="请输入企业名称关键词"
            @input="refreshList2"
          />
        </div>
      </div>
      <list
        v-loading="ListLoading"
        :tab-list="recommendList"
        :whichs="whichs"
        @userdetails="userdetails"
        @godelenterprise="godelenterprise"
      />
      <div class="ye">
        <el-pagination
          :current-page.sync="recommendform.pageNum"
          :page-size.sync="recommendform.pageSize"
          :page-sizes="[5, 10, 20, 50]"
          background
          :total="+recommendform.total"
          layout="total,prev, pager, next,sizes,jumper"
          @size-change="listByModelTypeAPI"
          @current-change="listByModelTypeAPI"
        />
      </div>
    </div>
    <atlasdig
      v-if="showatlas"
      :showatlas.sync="showatlas"
    />
  </div>
</template>

<script>
import {
  typeCountSumAPI,
  homeListAPI,
  industryChainDistributionAPI,
  industryChainDistributionListAPI,
  getRegion,
  areaDistributionAPI,
  areaDistributionListAPI,
  listByModelTypeAPI,
  getAllSecondLevelChainNodeByIndustryChainIdAPI,
} from "../../apiUrl";
import { 
  listByModelTypeAPI_V2,
  industryChainDistributionAPI_V2,
  industryChainDistributionListAPI_V2,
  areaDistributionAPI_V2,
  areaDistributionListAPI_V2,
  merchantstypeCountAPI_V2,
 } from "../../apiUrl_v2"
import { listByModelTypedownloadAPI_V2 } from "@/api/export";
import atlasdig from "../components/atlasc.vue";
import { verificationAPI } from "@/api/user";
import mapMain from "./map.vue";
import industrydistribution from "./industrydistribution.vue";
import list from "./list.vue";
import { filterData } from "@/utils/utils";
export default {
  name: "InvestmentModel",
  components: {
    mapMain,
    list,
    industrydistribution,
    atlasdig,
  },
  props: {
    tabId: {
      type: Number,
      default: 1,
    },
    parentChainId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      secondNodeIds: [],
      keyword: "",
      tabList: [
        {
          name: "亲缘招商",
          id: 1,
        },
        {
          name: "链式招商",
          id: 3,
        },
        {
          name: "舆情招商",
          id: 5,
        },
        {
          name: "人才招商",
          id: 7,
        },
        {
          name: "资本招商",
          id: 8,
        },
        // {
        //   name: "AI+招商",
        //   id: 6,
        // },
      ],
      xzloading: false,
      regionVal: [],
      oneList: [],
      regionProp: {
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level, data } = node;
          if (level > 2) {
            resolve([]);
            return;
          }
          let type = level + 1;
          let param = { type };
          if (data && data.id) {
            param.parentId = data.id;
          }
          getRegion(param).then((res) => {
            res.result.map((e) => {
              e.value = [e.name, e.code];
              e.parentId = e.id;
              e.label = e.name;
              return e;
            });
            resolve(res.result);
          });
        },
      },
      whichs: 1,
      industryList: [],
      industryId: "",
      ChainList: [], //产业链图
      fnbindustryList: [], //产业链列表
      regionList: [], //地区分布列表
      industryform: {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      },
      regionform: {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      },
      recommendform: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      recommendList: [],
      tableData: [],
      tableData2: [],
      industryLoading: false, //推荐企业产业链分布图
      industryListLoading: false, //推荐企业产业链分布列表
      regionLoading: false, //地区分布
      regionListLoading: false, //地区分布
      ListLoading: false, //列表
      time: null,
      showatlas: false,
    };
  },
  created() {
    //this.whichs = this.tabId;
    //this.getsum();
    //this.getListindustry();
  },
  methods: {
    initialize(id) {
      this.whichs = id;
      this.getsum();
      this.getListindustry();
    },
    Correlationmap() {
      //this.$emit("userdetails",);
      this.showatlas = true;
      //let res = this.$router.resolve({ path:'/ChartsRelation',});
      //window.open(res.href, "_blank");
    },
    async exportex() {
      if (this.xzloading) {
        return this.$message.warning("正在导出中，请耐心等待");
      }
      if (this.recommendform.total == 0) {
        return this.$message.warning("这里还什么都没有~");
      }
      let data = {
        modelType: this.whichs,
        keyword: this.keyword,
        secondNodeIds: this.secondNodeIds,
        pageNum: 1,
        pageSize: 5000,
        chainId: this.parentChainId
      };
      filterData(data);
      try {
        this.xzloading = true;
        const res = await listByModelTypedownloadAPI_V2(data);
        if (res.msg) {
          return this.$message.error(res.msg);
        }
        let blob = new Blob([res], {
          type: "text/csv,charset=UTF-8",
        });
        let objectUrl = URL.createObjectURL(blob);
        const fileName = `六大招商模型企业列表.xlsx`;
        const downloadLink = document.createElement("a");
        downloadLink.href = objectUrl;
        downloadLink.download = fileName;
        downloadLink.click();
      } finally {
        this.xzloading = false;
      }
    },
    init() {
      (this.industryform = {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      }),
        (this.regionform = {
          pageNum: 1,
          pageSize: 5,
          total: 0,
        }),
        (this.recommendform = {
          pageNum: 1,
          pageSize: 10,
          total: 0,
        }),
        (this.regionVal = []);
      this.recommendList = [];
      this.secondNodeIds = [];
      this.keyword = "";
      this.industryId =  this.parentChainId
      this.getgetTypeNodeLinkPlace(); //产业链分布图
      this.getgetTypeNodeLinkPlaceList(); //产业链分布列表
      this.areaDistributionList(); //推荐企业地区分布列表
      this.getMapdistributed(); //推荐企业地区分布图
      this.listByModelTypeAPI(); //推荐企业列表
    },
    //地区变化
    regionChange() {
      this.areaDistributionList(); //推荐企业地区分布列表
      this.getMapdistributed(); //推荐企业地区分布图
    },
    //切换招商模式
    cut(id) {
      this.whichs = id;
      this.init();
    },
    refreshList() {
      this.recommendform = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      };
      this.listByModelTypeAPI();
    },
    refreshList2() {
      if (this.time != null) {
        clearTimeout(this.time);
      }
      this.time = setTimeout(async () => {
        this.recommendform = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      };
        this.listByModelTypeAPI();
      }, 400);
    },
    // 切换产业链
    async industrycut() {
      /*  const is = await verificationAPI({
  industryChainId:item.industryChainId,
}) 
if(is){ */
      //this.industryId = item.industryChainId;
      this.industryform = {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      };
      this.getgetTypeNodeLinkPlace();
      this.getgetTypeNodeLinkPlaceList();
      /*       }else{
  this.$message.error('暂无该产业链权限')
  setTimeout(()=>{
    location.reload()
  },500)
} */
    },
    // 企业列表
    async listByModelTypeAPI() {
      try {
        this.ListLoading = true;
        let data = {
          modelType: this.whichs,
          keyword: this.keyword,
          secondNodeIds: this.secondNodeIds,
          pageNum: this.recommendform.pageNum,
          pageSize: this.recommendform.pageSize,
          chainId: this.parentChainId
        };
        filterData(data);
        const res = await listByModelTypeAPI_V2(data);
        this.recommendList = res.result.records;
        // console.log(this.recommendList);
        
        this.recommendform.total = res.result.total;
      } finally {
        this.ListLoading = false;
      }
    },
    //推荐企业地区分布列表
    async areaDistributionList() {
      try {
        this.regionListLoading = true;
        let province = "";
        let city = "";
        let area = "";
        if (this.regionVal.length == 1) {
          province = this.regionVal[0][0];
        } else if (this.regionVal.length == 2) {
          province = this.regionVal[0][0];
          city = this.regionVal[1][0];
        } else if (this.regionVal.length > 2) {
          province = this.regionVal[0][0];
          city = this.regionVal[1][0];
          area = this.regionVal[2][0];
        }
        const governDirectly = ["重庆市", "北京市", "天津市", "上海市"];
        if (governDirectly.includes(province)) {
          city = province;
        }
        const res = await areaDistributionListAPI_V2({
          modelType: this.whichs,
          province,
          city,
          area,
          chainId: this.parentChainId
        });
        this.regionList = res.result;
        this.regionform.total = this.regionList.length;
        this.getTabelData();
      } finally {
        this.regionListLoading = false;
      }
    },
    //获取企业产业链分布列表
    async getgetTypeNodeLinkPlaceList() {
      try {
        this.industryListLoading = true;
        const res = await industryChainDistributionListAPI_V2({
          modelType: this.whichs,
          chainId: this.industryId,
        });
        this.fnbindustryList = res.result;
        this.industryform.total = this.fnbindustryList.length;
        this.getTabelData2();
      } finally {
        this.industryListLoading = false;
      }
    },
    getTabelData() {
      let data = JSON.parse(JSON.stringify(this.regionList));
      this.tableData = data.splice(
        (this.regionform.pageNum - 1) * this.regionform.pageSize,
        this.regionform.pageSize
      );
    },
    //自行分页
    getTabelData2() {
      let data = JSON.parse(JSON.stringify(this.fnbindustryList));
      this.tableData2 = data.splice(
        (this.industryform.pageNum - 1) * this.industryform.pageSize,
        this.industryform.pageSize
      );
    },
    //产业分布页码改变时
    currentChange(val) {
      this.regionform.pageNum = val;
      this.getTabelData();
    },
    //产业分布页码改变时
    currentChange2(val) {
      this.industryform.pageNum = val;
      this.getTabelData2();
    },
    //获取企业产业链分布图
    async getgetTypeNodeLinkPlace() {
      try {
        this.industryLoading = true;
        const res = await industryChainDistributionAPI_V2({
          modelType: this.whichs,
          chainId: this.industryId,
        });
        this.ChainList = res.result;
        this.$refs.industrydistribution.init(this.ChainList);
      } finally {
        this.industryLoading = false;
      }
    },
    // 企业地区分布图
    async getMapdistributed() {
      let province = "";
      let city = "";
      let area = "";
      if (this.regionVal.length == 1) {
        province = this.regionVal[0][0];
      } else if (this.regionVal.length == 2) {
        province = this.regionVal[0][0];
        city = this.regionVal[1][0];
      } else if (this.regionVal.length > 2) {
        province = this.regionVal[0][0];
        city = this.regionVal[1][0];
        area = this.regionVal[2][0];
      }
      const governDirectly = ["重庆市", "北京市", "天津市", "上海市"];
      if (governDirectly.includes(province)) {
        city = province;
      }
      const res = await areaDistributionAPI_V2({
        modelType: this.whichs,
        province,
        city,
        area,
        chainId: this.parentChainId
      });
      const arr = Object.keys(res.result).map((key) => ({
        regionName: key,
        number: res.result[key],
      }));
      if (this.regionVal.length > 0) {
        const info = {
          regionCode: this.regionVal[this.regionVal.length - 1][1],
          divisionLevel: this.regionVal.length,
        };
        this.$refs.map.init(arr, info);
      } else {
        const info = {
          regionCode: 100000,
          divisionLevel: 1,
        };
        this.$refs.map.init(arr, info);
      }
    },
    // 获取当前用户产业链
    async getListindustry() {
      const res = await homeListAPI();
      this.industryList = res.result;
      if (this.industryList.length < 1) {
        this.$message.error("未获取到产业链");
      } else {
        this.industryId = this.industryList[0].industryChainId;
        this.init();
      }
/*       this.oneList = await Promise.all(
        this.industryList.map(async (item) => {
          let NodeTree =
            await getAllSecondLevelChainNodeByIndustryChainIdAPI({
              industryChainId: item.industryChainId,
            });
          return {
            value: item.id,
            disabled: true,
            label: item.chainName.replace("产业金脑·", ""),
            children: NodeTree.result.map((e) => {
              return {
                value: e.id,
                label: e.nodeName,
              };
            }),
          };
        })
      ); */
    },
    godelenterprise(row) {
      this.$emit("particulars", row, 2);
    },
    // 获取对应数量
    async getsum() {
      const res = await merchantstypeCountAPI_V2({
        chainId: this.parentChainId
      });
      this.tabList = this.tabList.map((item) => {
        return {
          ...item,
          sum: res.result[item.name],
        };
      });
    },
    userdetails(it) {
      this.$emit("userdetails", it);
    },
    //返回首页
    gobacka() {
      this.$emit("gobacks");
    },
  },
};
</script>

<style lang="scss" scoped>
.export {
  cursor: pointer;
  margin-left: 16px;
  width: 82px;
  height: 24px;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px solid #ced4db;
  font-size: 12px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #3f4a59;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  .up {
    width: 14px;
    height: 14px;
    margin-right: 5px;
  }
}

.ye {
  margin: 0px 10px;
}

::v-deep {
  .el-pagination {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
}

.industryss {
  margin: 24px;
  background: #ffffff;
  box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
}

.industrys {
  margin: 24px;
  //height: 461px;
  background: #ffffff;
  box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;

  .head {
    height: 55px;
    border-bottom: 1px solid #e9e9e9;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .intimate {
      display: flex;
      align-items: center;
      justify-content: center;

      .atlas {
        width: 109px;
        height: 32px;
        border-radius: 5px;
        margin-left: 10px;
        border: #f1f4f9 1px solid;
        display: flex;
        align-items: center;
        cursor: pointer;
        justify-content: center;
        font-size: 14px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #3370ff;
      }
    }

    .span {
      font-size: 16px;
      padding-left: 24px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .distribution {
    display: flex;
    height: 100%;
    padding: 24px;

    .left {
      width: 60%;

      .map {
        width: 100%;
        height: 360px;
      }
    }

    .right {
      width: 40%;
    }
  }
}

.title {
  // height: 100px;
  // background-color: #fff;
}

.iconarrow {
  color: #666;
  font-size: 20px;
  background: white;
  padding: 2px;
  width: 25px;
  height: 25px;
  border-radius: 16px;
}

.scan {
  width: 100%;
  margin-top: 16px;

  // background-color: #fff;
  &-top {
    padding: 16px 0px;
    // background: #fff;
    padding-bottom: 12px;
    //border-bottom: 1px solid #E8E8E8
  }

  &-tab {
    display: flex;
    padding-left: 16px;

    &-con {
      display: flex;
      justify-content: space-between;
    }

    &-select {
      display: flex;
    }

    &-list {
      margin-right: 64px;
      font-size: 16px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      cursor: pointer;

      &.on {
        font-weight: bold;
        color: #3370ff;
        position: relative;

        &::after {
          content: "";
          width: 28px;
          height: 2px;
          background: #3370ff;
          position: absolute;
          left: 26px;
          bottom: -8px;
        }
      }
    }
  }

  &-con {
    margin: 16px 24px;
  }
}

.industryXZ {
  display: flex;
  padding-right: 16px;

  .pitchs {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 88px;
    padding: 0 5px;
    height: 32px;
    background: #ffffff;
    cursor: pointer;
    border-radius: 2px 0px 0px 2px;
    opacity: 1;
    font-size: 14px;
    font-weight: 400;
    z-index: 99;
    color: #3370ff;
    border: 1px solid #3370ff;
  }

  .nopitch {
    display: flex;
    align-items: center;
    cursor: pointer;
    justify-content: center;
    min-width: 88px;
    padding: 0 5px;
    height: 32px;
    margin-left: -1px;
    background: #ffffff;
    border-radius: 2px 2px 2px 2px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    opacity: 1;
    border: 1px solid #d9d9d9;
  }
}
</style>
