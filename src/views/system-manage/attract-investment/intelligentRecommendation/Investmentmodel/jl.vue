<template>
  <div>
    <el-dialog
      width="40%"
      title="推荐记录"
      :visible="showPath"
      :close-on-click-modal="false"
      center
      @close="cancel"
    >
      <span class="enterpriseName">{{ enterpriseName }}</span>
      <div
        v-if="pathList.length >= 1"
        class="const"
      >
        <div
          v-for="(item, index) in pathList"
          :key="index"
          class="contents"
        >
          <div class="text-box">
            <span class="titletext">{{ item.recommendedDate }}</span>
          </div>
          <div class="assignor">
            <div
              v-if="whichs == 1"
              class="time"
            >
              关联亲商姓名：<span>{{ item.relationUserName }}</span>
            </div>
            <div
              v-if="whichs == 2"
              class="time"
            >
              资源需求：<span>{{ item.resourceNeeds }}</span>
            </div>
            <div
              v-if="whichs == 3"
              class="time"
            >
              关联本地企业：<span>{{ item.associateLocalEnterpriseName }}</span>
            </div>
            <div
              v-if="whichs == 4"
              class="time"
            >
              关联政策：<span>{{ item.associatePolicy }}</span>
            </div>
            <div
              v-if="whichs == 5"
              class="time"
            >
              关联资讯：<a
                style="color: #3370FF;"
                :href="item.associateInformationUrl"
                target="_blank"
              >{{ item.informationTitle }}</a>
            </div>
            <div
              v-if="whichs == 6"
              class="time"
            >
              对外投资意愿
              <span>{{ item.outsideInvestSatisfaction }}</span>
            </div>
            <span v-if="whichs == 1">关联关系：{{ item.associationRelationship }}</span>
            <span v-if="whichs == 3">供应关系：{{ item.supplyRelation }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
          
    <script>
export default {
  name: "EntrustA",
  props: {
    enterpriseName: {
      type: String,
      default: "",
    },
    showPath: {
      type: Boolean,
      default: false,
    },
    pathList: {
      type: Array,
      default: () => [],
    },
    whichs: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("update:showPath", false);
    },
    individual(item) {
      this.$emit("update:showPath", false);
      this.$emit("individual", item);
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep{
  .el-dialog{
    border-radius:10px
  }

}

.enterpriseName {
  margin-left: 24px;
  margin-top: 20px;
  font-size: 14px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 600;
  color: #14243d;
}
.indiv {
  cursor: pointer;
  color: #3370FF;
}
.const {
  height: 300px;
  display: block;
  overflow-y: scroll;
  .contents {
    position: relative;
    //height: auto;
    height: 80px;
    border-radius: 10px;
    margin-left: 42px;
    margin-top: 10px;
    padding: 0px 10px;
    font-size: 28px;
    color: #4e5969;

    .titletext {
      display: flex;
      padding-top: 8px;
      font-size: 15px;
    }

    .assignor {
      display: flex;
      margin-top: 20px;
      font-size: 13px;

      .time {
        width: 50%;
      }
    }
  }
}
.contents::before {
  background-color: #3e80ff;
  content: "";
  position: absolute;
  top: 6px;
  left: -20px;
  border: 4px solid #bedaff;
  width: 16px;
  height: 16px;
  z-index: 6;
  border-radius: 50%;
}

.contents:not(:last-child)::after {
  background-color: #f0f0f0;
  content: "";
  position: absolute;
  top: 19px;
  z-index: 5;
  left: -13px;
  width: 2px;
  height: 80px;
}
</style>