<template>
  <div class="list">
    <div class="table">
      <el-table
        :data="tabList"
        style="width: 100%"
      >
        <el-table-column
          :key="Math.random()"
          prop="enterpriseName"
          label="企业名称"
          width="300"
        >
          <template slot-scope="{ row }">
            <div
              style="cursor: pointer"
              @click="goparticulars(row)"
            >
              {{ row.enterpriseName }}
            </div>
            <div
              v-if="row.enterpriseLabelNameList"
              class="tabs"
            >
              <div
                v-for="(item, index) in row.enterpriseLabelNameList"
                v-show="index <= 4"
                :key="index"
                class="firmTag"
              >
                {{ item }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :key="Math.random()"
          label="推荐产业链"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            {{ row.recommendChain }}
          </template>
        </el-table-column>
        <el-table-column
          :key="Math.random()"
          label="所在地区"
        >
          <template slot-scope="{ row }">
            {{ row.province
            }}<span v-if="row.province !== row.city">{{ row.city }}</span>{{ row.area }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="whichs==1"
          :key="Math.random()"
          label="关联人/毕业院校"
          prop="destinedRelationship"
          width="240"
        />
        <el-table-column
          v-if="whichs==1"
          :key="Math.random()"
          prop="contact"
          label="联系方式"
          width="240"
        />
        <el-table-column
          v-if="whichs==3"
          :key="Math.random()"
          prop="contact"
          label="触达方式"
          show-overflow-tooltip
        />
        <!-- <el-table-column
          v-if="whichs==3"
          label="推荐理由"
          prop="recommendationReasonDetail"
          show-overflow-tooltip
          width="240"
        /> -->
        <!-- <el-table-column
          v-if="whichs==5"
          prop="newsContent"
          label="关联舆情"
          show-overflow-tooltip
          width="240"
        /> -->
        <el-table-column
          v-if="whichs==7"
          :key="Math.random()"
          prop="talentName"
          label="关联人才"
        />
        <el-table-column
          v-if="whichs==7"
          :key="Math.random()"
          prop="researchField"
          label="研究方向"
          show-overflow-tooltip
          width="240"
        />
        <el-table-column
          v-if="whichs==8"
          :key="Math.random()"
          prop="projectName"
          label="项目名称"
          show-overflow-tooltip
          width="240"
        />
        <el-table-column
          v-if="whichs==8"
          :key="Math.random()"
          prop="projectDescription"
          label="项目介绍"
          width="240"
          show-overflow-tooltip
        />
        <el-table-column
          :key="Math.random()"
          prop="recommendedDate"
          label="推送日期"
          width="120"
        />
        <!-- <el-table-column label="更多">
          <template slot-scope="{ row }">
            <div>
              <div
                class="Youcanclick"
                @click="record(row)"
              >
                推荐记录
              </div>
            </div>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <tjjl
      v-if="showPath"
      :show-path.sync="showPath"
      :whichs="whichs"
      :enterprise-name="enterpriseName"
      :path-list="pathList"
      @individual="individual"
    />
  </div>
</template>

<script>
import tjjl from './jl.vue'
export default {
  name: "IntentionQ",
  components:{
    tjjl
  },
  props: {
    tabList: {
      type: Array,
      default: () => [],
    },
    whichs: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      pathList:[],
      showPath:false,
      enterpriseName:''
    };
  },
  created() {
  },
  methods: {
    // 去企业详情
    goparticulars(row) {

      // console.log(row);
      this.$emit("godelenterprise", row, 2);
      // this.$emit("godelenterprise", {...row,enterpriseId:'316404'}, 2);
      document.documentElement.scrollTop = 0;
    },
    individual(item) {
      this.$emit('userdetails',item)
      document.documentElement.scrollTop = 0;
    },
    record(row){
      this.enterpriseName=row.enterpriseName
      this.pathList=row.relationModelList || []
      this.showPath=true
    }
  },
};
</script>
      
<style scoped lang="scss">
.person {
  cursor: pointer;
  color: #3370FF;
}
.Youcanclick {
  color: #3370FF;
  cursor: pointer;
}
.chainNameList {
  display: flex;
  //justify-content: center;
  //align-items: center;
}
::v-deep {
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #3370FF;
    color: #fff;
  }
  .el-pagination {
    -webkit-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.1) !important;
  }
  .el-button {
    color: #3370FF;
  }
}
.ye {
  margin-top: 20px;
}

.list {
  .title {
    height: 50px;
    border-bottom: 1px solid #e9e9e9;
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
  }
  .red {
    background: #f53f3f;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .blue {
    background: #417fff;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .green {
    background: #10aa38;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .gray {
    background: #c9cdd4;
    content: "";
    position: absolute;
    width: 8px;
    height: 8px;
    top: 29px;
    left: 26px;
    border-radius: 50%;
  }

  .tabs {
    display: flex;
    //align-items: center;
    //justify-content: center;
    white-space: nowrap;

    .firmTag {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      margin-top: 6px;
      padding: 0 6px;
      color: #ff7d00;
      background: #fff2e6;
      margin-right: 8px;
      height: 18px;
      border-radius: 2px 2px 2px 2px;
    }
  }

  .table {
    margin: 0 24px;
    margin-top: 24px;
  }
}
</style>