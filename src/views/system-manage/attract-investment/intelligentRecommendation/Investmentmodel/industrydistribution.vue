<template>
  <div class="radar">
    <div class="radar-con">
      <div class="radar-item">
        <div
          style="display: flex; flex-wrap: wrap"
          class="tree"
          :style="`height: ${treeH};`"
        >
          <div
            v-for="(item, i) in detail"
            :key="i"
            class="tree-list"
          >
            <div class="tree-btn">
              {{ item.nodeName }}
              {{ item.childNodes ? '' : `(${item.relationEnterpriseCount})` }}
            </div>
            <div class="tree-item">
              <div
                v-for="(item1, j) in item.childNodes"
                :key="j"
                class="tree-btn a1"
                :class="
                  item1.isLeaf && item1.relationEnterpriseCount > 0
                    ? 'mountnode'
                    : ''
                "
                :style="`margin-bottom: ${
                  (item1.childNodes && item1.childNodes?.length - 1) * 52 + 20
                }px`"
              >
                {{ item1.nodeName
                }}<span v-if="item1.isLeaf && item1.relationEnterpriseCount > 0">({{ item1.relationEnterpriseCount }})</span>
              </div>
              <div
                class="list 111"
                :style="`height: ${item.len * 52}px;`"
              />
            </div>
            <div class="tree-item">
              <div
                v-for="(item2, j) in item.childNodes"
                :key="j"
                class="item-box"
                :style="!item2.childNodes ? 'height: 52px;' : ''"
              >
                <div
                  v-for="(item3, n) in item2.childNodes"
                  :key="n"
                  class="tree-btn a2 a1"
                  :class="
                    item3.isLeaf && item3.relationEnterpriseCount > 0
                      ? 'mountnode'
                      : ''
                  "
                >
                  {{ item3.nodeName
                  }}<span
                    v-if="item3.isLeaf && item3.relationEnterpriseCount > 0"
                  >({{ item3.relationEnterpriseCount }})</span>
                </div>
                <div
                  v-if="item2 && item2.childNodes"
                  class="list 222"
                  :style="`height: ${(item2.childNodes?.length - 1) * 52}px;`"
                />
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="detail?.length > 2"
          class="show"
          @click="showEvent"
        >
          {{ treeH == "266px" ? "展开" : "收起" }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "RaDar",
  props: {
    chainList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      detail: null,
      treeH: "266px",
    };
  },
  methods: {
    filterNodes(arr) {
      const filteredArr = arr.filter((node) => {
        if (node.relationEnterpriseCount > 0) {
          return true;
        } else if (node.childNodes) {
          node.childNodes = this.filterNodes(node.childNodes);
          return node.childNodes?.length > 0;
        }
        return false;
      });
      return filteredArr;
    },
    init(chainList) {
      //this.detail = chainList
      this.detail = this.filterNodes(chainList);
      this.detail.map((e) => {
        let len = 0;
        e.childNodes?.map((e1, n) => {
          if (e.childNodes?.length > 1) {
            if (n != e.childNodes?.length - 1) {
              len = len + (e1.childNodes?.length || 0);
              if (
                e.childNodes?.length > 1 &&
                (e1.childNodes == null || e1.childNodes?.length == 0)
              ) {
                len++;
              }
            }
            e1.childNodes?.map((e2) => {
              len = len + (e2.childNodes?.length || 0);
            });
          }
        });
        e.len = len;
      });
      this.detail.map((item) => {
        if (item.childNodes && item.childNodes?.length < 1) {
          item.childNodes = null;
        }
        item.childNodes?.map((e) => {
          if (e.childNodes && e.childNodes?.length < 1) {
            e.childNodes = null;
          }
        });
      });
    },
    showEvent() {
      if (this.treeH == "auto") {
        this.treeH = "266px";
      } else {
        this.treeH = "auto";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.green {
  background: linear-gradient(33deg, #00b769 0%, #5eca9c 100%)
    rgba(0, 0, 0, 0.2) !important;
  box-shadow: inset 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
}
.orange {
  background: linear-gradient(33deg, #f87700 0%, #f59841 100%) !important;
}
.purple {
  background: linear-gradient(33deg, #7f39ef 0%, #995ef6 100%) !important;
}
.mountnode {
  background: linear-gradient(33deg, #3975ff 0%, #51abff 100%) !important;
  color: #ffffff !important;
}
.radar {
  .title {
    height: 55px;
    line-height: 55px;
    padding-left: 24px;
    border-bottom: 1px solid #e9e9e9;
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }
  &-con {
    display: flex;
  }
  &-item {
    width: 100%;
    position: relative;
    .show {
      line-height: 32px;
      text-align: center;
      color: #3975ff;
      position: absolute;
      font-size: 14px;
      bottom: 15px;
      width: 100%;
      cursor: pointer;
    }
    .p1 {
      padding-left: 66px;
      padding-top: 34px;
      padding-bottom: 30px;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(29, 33, 41, 0.85);
      line-height: 22px;
      img {
        margin-right: 8px;
      }
    }
    .tree {
      padding-left: 66px;
      margin-bottom: 50px;
      overflow: hidden;
      &-list {
        display: flex;
        padding-bottom: 30px;
      }
      &-item {
        position: relative;
        .list {
          width: 1px;
          background: #badeff;
          position: absolute;
          top: 15px;
          left: -24px;
        }
        .tree-btn {
          margin-bottom: 20px;
        }
        .item-box {
          position: relative;
        }
      }
      &-btn {
        cursor: pointer;
        min-width: 109px;
        padding: 0 5px;
        height: 32px;
        //background: linear-gradient(33deg, #3975FF 0%, #51ABFF 100%);
        background: #fbfcfe;
        border: 1px solid #dde4f0;
        box-shadow: 0px 2px 1px 0px rgba(64, 72, 82, 0.05);
        font-size: 14px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        //color: #FFFFFF;
        color: #3370FF;
        line-height: 32px;
        text-align: center;
        border-radius: 5px;
        margin-right: 48px;
        position: relative;
        &.a1 {
          &::before {
            content: "";
            width: 24px;
            height: 1px;
            background: #badeff;
            position: absolute;
            left: -24px;
            top: 15px;
          }
          &:first-child {
            &::before {
              width: 48px;
              left: -48px;
            }
          }
        }
        .a2 {
          &::before {
            content: "";
            width: 24px;
            height: 1px;
            background: #badeff;
            position: absolute;
            left: -24px;
            top: 15px;
          }
          &:first-child {
            &::before {
              width: 48px;
              left: -48px;
            }
          }
        }
        &.on1 {
          background: linear-gradient(33deg, #00b769 0%, #5eca9c 100%)
            rgba(0, 0, 0, 0.2);
          box-shadow: inset 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
        }
        &.on2 {
          // background: linear-gradient(33deg, #00B769 0%, #5ECA9C 100%);
          background: linear-gradient(33deg, #f87700 0%, #f59841 100%);
        }
        &.on3 {
          // background: linear-gradient(33deg, #F87700 0%, #F59841 100%);
          background: linear-gradient(33deg, #7f39ef 0%, #995ef6 100%);
        }
        &.on4 {
          background: linear-gradient(33deg, #f87700 0%, #eca96b 100%)
            linear-gradient(33deg, #f87700 0%, #f59841 100%);
        }
      }
    }
    .radar-dom {
      height: 378px;
      padding-top: 30px;
      box-sizing: border-box;
    }
  }
}
</style>