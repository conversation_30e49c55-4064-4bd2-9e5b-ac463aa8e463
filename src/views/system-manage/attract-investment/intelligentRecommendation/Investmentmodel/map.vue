<!--
 * @Author: jhy
 * @Date: 2023-05-22 09:43:21
 * @LastEditTime: 2023-06-06 10:54:20
 * @LastEditors: jhy
 * @Description: 
 * @FilePath: /QT-SASS-PC/src/views/system-manage/idicc-scan/enterprise/component/map.vue
-->
<template>
  <div class="map-main">
    <div
      v-if="!hideMap"
      id="map-geo"
    />
  </div>
</template>
  
  <script>
  import * as echarts from "echarts";
  import { getCityJson } from "@/views/system-manage/idicc-scan/apiUrl";
  export default {
    name: "MapMain",
    data() {
      return {
        arr: [],
        mapName: "china",
        colorList: ["#0016DF", "#165DFF", "#4080FF", "#6AA1FF", "#AECCFF"],
        hideMap: false,
      };
    },
    methods: {
      init(arr, data) {
        this.mapName = data.mapName;
        this.arr = arr;
        let regionCode = data.regionCode;
        this.getMapInfo(regionCode, data.divisionLevel);
      },
      getMapInfo(regionCode, divisionLevel) {
        // _full 是查询所以，不加full是查询单独区域
        let code = regionCode + "_full";
        if (divisionLevel == 3) {
          code = regionCode;
        }
        let writeName = new Set(["120100", "110100", "310100", "500100"]);
        if (writeName.has(regionCode)) {
          code = code.replace("_full", "");
        }
        getCityJson(code).then((res) => {
          if (!res) {
            this.hideMap = true;
            this.$nextTick(() => {
              this.showBar = true;
              setTimeout(() => {
                //this.getMapBar(this.arr);
              }, 900);
            });
            return;
          } else {
            this.hideMap = false;
          }
          const cityData = JSON.parse(res);
          this.$nextTick(() => {
            this.showBar = true;
            this.showGeoMap(cityData);
            setTimeout(() => {
              //this.getMapBar(this.arr);
            }, 900);
          });
        });
      },
      // eslint-disable-next-line no-unused-vars
      regionsData() {
        let list = [];
        this.arr.sort((a, b) => b.number - a.number);
        const maxVal = Math.max(...this.arr.map((item) => item.number));
        const minVal = Math.min(...this.arr.map((item) => item.number));
        this.arr.map((e) => {
          let  suij = Math.round(((e.number - minVal) / (maxVal - minVal)) * (0 - 4)) + 4
          list.push({
            name: e.regionName, // 地图区域的名称
            itemStyle: {
              borderColor: "#3c8bff", // 图形的描边颜色。
              borderWidth: 1, // 描边线宽。为 0 时无描边。
              borderType: "solid", // 描边类型。
              areaColor: this.colorList[suij],
              //areaColor: this.colorList[i],
            },
          });
        });
        return list;
      },
      showGeoMap(cityData) {
        let mapName = this.mapName;
        let chartDom = document.getElementById("map-geo");
        let myChart = echarts.init(chartDom);
        function tooltipWay(nodeData) {
          return {
            show: true, // 是否显示提示框组件
            backgroundColor: "rgb(3 21 60 / 80%)", // 提示框浮层的背景颜色
            padding: 5, // 提示框浮层内边距，单位px
            textStyle: {
              color: "#FFF", // 文字的颜色
              fontStyle: "normal", // 文字字体的风格（'normal'，无样式；'italic'，斜体；'oblique'，倾斜字体）
              fontWeight: "normal", // 文字字体的粗细（'normal'，无样式；'bold'，加粗；'bolder'，加粗的基础上再加粗；'lighter'，变细；数字定义粗细也可以，取值范围100至700）
              fontSize: "14", // 文字字体大小
              lineHeight: "50", // 行高
            },
            formatter: (params) => {
              const node =
                nodeData?.find((item) => item.regionName === params.name) || null;
              var dotHtml = "";
              if (!node) {
                dotHtml = `<div><div>${params.name}</div><div>企业数量：暂无数据</div></div>`;
              } else {
                dotHtml = `<div><div>${params.name}</div><div>企业数量：${node.number}</div></div>`;
              }
              // var dotHtml = '<span style="display:inline-block;margin-right:5px;border-radius:2px;width:30px;height:30px;background-color:#F1E67F"></span>'    // 定义第一个数据前的圆点颜色
              // var dotHtml2 = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:30px;height:30px;background-color:#2BA8F1"></span>'    // 定义第二个数据前的圆点颜色
              // result += params[0].axisValue + "</br>" + dotHtml + ' 数据名称 ' + params[0].data + "</br>" + dotHtml2 + ' 数据名称 ' + params[1].data;
              return dotHtml;
            }, //数据格式化
          };
        }
        let option = {
          tooltip: {
            trigger: "item",
            formatter: "aaa",
          },
  
          geo: [
            {
              backgroundColor: "#C5DCFF",
              map: mapName,
              // 在地图中对特定的区域配置样式
              regions: this.regionsData(),
              tooltip: tooltipWay(this.arr),
              top: 200,
              aspectScale: 0.75, // 3d
              zoom: 1, // 默认显示级别
              zlevel: 12,
              roam: false, // 是否允许缩放
              layoutSize: mapName !== "china" ? "90%" : "100%",
              layoutCenter: mapName !== "china" ? ["45%", "50%"] : ["48%", "60%"],
              // 图形上的文本标签，可用于说明图形的一些数据信息，比如值，名称等。
              // label: {
              //   emphasis: {
              //       show: !true,
              //       color: '#fff',
              //   },
              //   show: !true, // 是否显示标签。
              //   fontSize: '12',
              //   color: '#fff',
              // },
              // 地图背景样式
              itemStyle: {
                areaColor: "#C5DCFF",
                borderColor: "#0066FF",
              },
              // 悬浮高亮样式 - 背景样式
              emphasis: {
                // disabled: !false,
                focus: "none", //在高亮图形时，是否淡出其它数据的图形已达到聚焦的效果。'none' 不淡出其它图形，默认使用该配置。'self' 聚焦当前高亮图形，淡出其它图形。
                // 高亮状态下文本标签
                label: {
                  show: 1,
                  color: "#ffffff",
                },
                // 高亮状态下图形样式
                itemStyle: {
                  areaColor: "#2a5aaf", // 高亮区域的颜色
                  // areaColor: '#00fff0', // 高亮区域的颜色
                  shadowBlur: 3,
                  borderWidth: 2, // 描边线宽。为 0 时无描边。
                  shadowColor: "#19A8FF",
                  shadowOffsetX: 0,
                  shadowOffsetY: 0,
                },
              },
              selectedMode: false,
              // 选中状态下的多边形和标签样式。
  
              select: {
                disabled: false,
                // disabled: true, // 是否可以被选中。在开启selectedMode的时候有效，可以用于关闭部分数据。
                // 选中区域文本标签
                label: {
                  show: !true,
                  color: "#fff",
                },
                // 高亮状态下图形样式
                itemStyle: {
                  areaColor: "#2a5aaf", //'#2a5aaf', // 高亮区域的颜色。
                  color: "#333", // 高亮区域的颜色。areaColor和color都设置，高亮区域渲染areaColor的值
                  borderWidth: 1, // 描边线宽。为 0 时无描边。
                  borderType: "solid", // 描边类型。
                },
              },
            },
          ],
        };
        myChart.showLoading();
        myChart.hideLoading();
        echarts.registerMap(this.mapName, cityData);
        myChart.setOption(option);
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .map-main {
    display: flex;
    height: 100%;
    box-sizing: border-box;
    div {
      width: 100%;
      &.on {
        width: 100%;
      }
    }
  }
  </style>