<template>
  <div>
    <div class="title">
      <div class="iconarrow">
        <i
          style="cursor: pointer"
          class="el-icon-arrow-left"
          @click="gobacka"
        />
      </div>
    </div>
    <div class="industrys">
      <div class="head">
        <span class="span">{{ userobj.name }}个人简介</span>
      </div>
      <div class="distribution">
        <div
          v-if="userobj.name"
          class="portrait"
        >
          {{ userobj.name[0] }}
        </div>
        <div class="message">
          <span class="name">{{ userobj.name }}</span>
          <div class="brief">
            {{ userobj.resume }}
          </div>
        </div>
      </div>
    </div>
    <div class="industrys">
      <div class="head">
        <span class="span">关联企业产业链分布</span>
        <div class="industry">
          <div
            v-for="(item, index) in industryList"
            :key="index"
            :class="industryId == item.id ? 'pitchs' : 'nopitch'"
            @click="industrycut(item)"
          >
            {{ item.chainName.replace("产业金脑·", "") }}
          </div>
        </div>
      </div>
      <div
        v-loading="industryLoading"
        class="nodes"
      >
        <industrydistribution
          ref="industrydistribution"
          :chain-list="ChainList"
        />
      </div>
    </div>
    <div class="industrys">
      <div class="head">
        <span class="span">关联企业列表·{{ total }}</span>
        <div class="industry">
          <el-select
            v-model="relevance"
            clearable
            placeholder="关联关系"
            @change="refresh"
          >
            <el-option
              v-for="item in relevanceList"
              :key="item.label"
              :label="item.label"
              :value="item.label"
            />
          </el-select>
          <el-input
            v-model="keyword"
            placeholder="请输入企业名称关键词"
            @input="refreshList2"
          />
        </div>
      </div>
      <div>
        <div class="table">
          <el-table
            :data="tabList"
            style="width: 100%"
          >
            <el-table-column
              prop="enterpriseName"
              label="企业名称"
              width="300"
            >
              <template slot-scope="{ row }">
                <div
                  style="cursor: pointer"
                  @click="goparticulars(row)"
                >
                  {{ row.enterpriseName }}
                </div>
                <div
                  v-if="row.enterpriseLabelNames"
                  class="tabs"
                >
                  <div
                    v-for="(item, index) in row.enterpriseLabelNames"
                    v-show="index <= 4"
                    :key="index"
                    class="firmTag"
                  >
                    {{ item }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="所在环节"
              show-overflow-tooltip
              width="240"
            >
              <template slot-scope="{ row }">
                {{ row.chainNames }}
              </template>
            </el-table-column>
            <el-table-column
              label="所在地区"
              width="240"
            >
              <template slot-scope="{ row }">
                {{ row.province
                }}<span v-if="row.province !== row.city">{{ row.city }}</span>{{ row.area }}
              </template>
            </el-table-column>
            <el-table-column
              prop="relationName"
              label="关联亲商姓名"
            />
            <el-table-column
              prop="relation"
              label="关联关系"
            />
          </el-table>
        </div>
        <el-pagination
          :current-page.sync="form.pageNum"
          :page-size.sync="form.pageSize"
          :page-sizes="[5, 10, 20, 50]"
          background
          :total="+total"
          layout="total,prev, pager, next,sizes,jumper"
          @size-change="refreshList"
          @current-change="refreshList"
        />
      </div>
    </div>
  </div>
</template>
  
  <script>
  import industrydistribution from './Investmentmodel/industrydistribution2.vue'
  import {
    personalInformationAPI,
    industryListAPI,
    industryDistributionAPI,
    pageEnterpriseAPI,
  } from "../apiUrl";
  export default {
    name: "UserPage",
    components:{
      industrydistribution
    },
    props: {
      uniqId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        userobj: {},
        industryList: [], //关联产业链
        industryId: "", //当前关联产业链
        ChainList: [],
        relevance: "", //关联关系
        relevanceList: [
          { label: "股东" },
          { label: "法定代表人" },
          { label: "高管" },
          { label: "公司骨干" },
        ],
        total: 0,
        tabList: [],
        form: {
          pageNum: 1,
          pageSize: 10,
        },
        keyword: "",
        time:null,
        industryLoading:false
      };
    },
    created() {
      this.getUser();
      this.getinList();
      this.refreshList();
    },
    methods: {
      refresh() {
        this.form = {
          pageNum: 1,
          pageSize: 10,
        };
        this.refreshList();
      },
      async refreshList() {
        let data = {
          pageNum: this.form.pageNum,
          pageSize: this.form.pageSize,
          uniqId: this.uniqId,
          keyword: this.keyword,
          relation: this.relevance,
        };
        const res = await pageEnterpriseAPI(data);
        this.tabList = res.result.records;
        this.total = res.result.totalNum;
      },
      refreshList2() {
        if (this.time != null) {
          clearTimeout(this.time);
        }
        this.time = setTimeout(async () => {
          this.refresh()
        }, 400);
      },
      // 个人简介
      async getUser() {
        const res = await personalInformationAPI({
          uniqId: this.uniqId,
        });
        if(res.result!==null){
          this.userobj = res.result;
        }
      },
      // 切换产业链
      industrycut(item) {
        this.industryId = item.id;
        this.getNodes();
      },
      // 关联企业产业链及分布
      async getinList() {
        const res = await industryListAPI({
          uniqId: this.uniqId,
        });
        this.industryList = res.result;
        if (this.industryList.length < 1) {
          this.$message.error("未获取到产业链");
        } else {
          this.industryId = this.industryList[0].id;
          this.getNodes();
        }
      },
      async getNodes() {
        try {
          this.industryLoading=true
          const res = await industryDistributionAPI({
          uniqId: this.uniqId,
          chainId: this.industryId,
        });
        this.ChainList = res.result.childNodes;
        this.$refs.industrydistribution.init(this.ChainList)
        } catch (error) {
          console.log(error);
        } finally{
          this.industryLoading=false
        }
        
      },
      gobacka() {
        this.$emit("gono");
      },
      goparticulars(row){
        row.enterpriseId=row.id     
        document.documentElement.scrollTop = 0;
        this.$emit("particulars", row, 2);
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .table {
    margin: 0 24px;
    margin-top: 24px;
  }
  .tabs {
      display: flex;
      //align-items: center;
      //justify-content: center;
      white-space: nowrap;
  }
  .firmTag {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    margin-top: 6px;
    padding: 0 6px;
    color: #ff7d00;
    background: #fff2e6;
    margin-right: 8px;
    height: 18px;
    border-radius: 2px 2px 2px 2px;
  }
  .title {
    // height: 36px;
    // background-color: #fff;
  }
  .iconarrow {
    color: #666;
    font-size: 20px;
    background: white ;
    padding: 2px;
    width: 25px;
    height: 25px;
    border-radius: 16px;
  }
  .industry {
    display: flex;
    padding-right: 16px;
    .pitchs {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 88px;
      padding: 0 5px;
      height: 32px;
      background: #ffffff;
      cursor: pointer;
      border-radius: 2px 0px 0px 2px;
      opacity: 1;
      font-size: 14px;
      font-weight: 400;
      color: #3370FF;
      border: 1px solid #3370FF;
    }
    .nopitch {
      display: flex;
      align-items: center;
      cursor: pointer;
      justify-content: center;
      min-width: 88px;
      padding: 0 5px;
      height: 32px;
      background: #ffffff;
      border-radius: 2px 2px 2px 2px;
      font-size: 14px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      opacity: 1;
      border: 1px solid #d9d9d9;
    }
  }
  .industrys {
    margin: 24px;
    background: #ffffff;
    box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
    border-radius: 10px 10px 10px 10px;
    opacity: 1;
    .head {
      height: 55px;
      border-bottom: 1px solid #e9e9e9;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .span {
        font-size: 16px;
        padding-left: 24px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
    }
    .nodes {
      //height: 353px;
      padding-top: 30px;
    }
    .distribution {
      display: flex;
      height: 100%;
      padding: 24px;
      .portrait {
        width: 80px;
        height: 80px;
        min-width: 80px;
        min-height: 80px;
        border-radius: 50%;
        background-color: #4080ff;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 24px;
      }
      .message {
        padding-left: 30px;
        padding-top: 10px;
        .name {
          font-size: 16px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
        }
        .brief {
          width: 80%;
          margin-top: 16px;
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.65);
          display: flex;
          flex-wrap: wrap;
        }
      }
    }
  }
  </style>