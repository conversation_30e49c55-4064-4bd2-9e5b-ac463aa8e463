<template>
  <div v-if="examine">
    <div v-if="showcontent">
      <div class="scan">
        <!--     <div class="scan-top">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          数智招商
        </el-breadcrumb-item>
        <el-breadcrumb-item>招商智推</el-breadcrumb-item>
      </el-breadcrumb>
    </div> -->
        <div
          v-show="isParticulars == 1"
          class="scan"
        >
          <el-dropdown
            trigger="click"
            @command="handleCommand"
          >
            <span class="el-dropdown-link">
              {{ chainName }}<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu
              slot="dropdown"
              style="max-height: 400px; overflow-y: scroll; margin-left: 20px"
            >
              <el-dropdown-item
                v-for="(item, index) in chainList"
                :key="index"
                :command="item"
              >
                {{ item.chainName }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <div
            class="scan-top"
            style="margin-top: 20px"
          >
            <div class="scan-tab-con">
              <div class="scan-tab">
                <div
                  v-for="(it, index) in tabList"
                  :key="index"
                  class="scan-tab-list"
                  :class="CurrentSelectio == it.id ? 'on' : ''"
                  @click="cut(it.id)"
                >
                  {{ it.name }}
                </div>
              </div>
            </div>
          </div>
          <radarHome
            v-if="CurrentSelectio == 1"
            ref="radarHome"
            :industry-id="industryId"
            :parent-chain-id="chainId"
            @particulars="particulars"
            @cut="cut"
            @demodel="demodel"
          />
          <recommend
            v-if="CurrentSelectio == 2"
            ref="recommend"
            :parent-chain-id="chainId"
            @particulars="particulars"
          />
          <!-- <isoparaffin
            v-if="CurrentSelectio == 3"
            :parent-chain-id="chainId"
            @particulars="particulars"
          /> -->
        </div>
        <!-- 企业详情 -->
        <Detailsenterprise
          v-if="isParticulars == 2"
          :enterprise-i-d="enterpriseID"
          :recommend-region-code="recommendRegionCode"
          :chain-id="chainId"
          :model-type="modelType"
          :parent-chain-id="chainId"
          :company-detial="companyDetial"
          @goback="goback"
        />
        <!--六大招商模型 -->
        <modelList
          v-show="isParticulars == 3"
          ref="modelList"
          :tab-id="modeid"
          :parent-chain-id="chainId"
          @gobacks="gobacks"
          @userdetails="userdetails"
          @particulars="particulars"
        />
        <!-- 个人主页 -->
        <userPage
          v-if="isParticulars == 4"
          :uniq-id="uniqId"
          :parent-chain-id="chainId"
          @particulars="particulars"
          @gono="gono"
        />
      </div>
    </div>
    <div
      v-else
      class="nodata"
    >
      <div class="con">
        <img src="https://static.idicc.cn/cdn/SSO/zhaoshangnodata.png">
        <span class="p1">暂无产业链权限</span>
        <span class="p2">请联系机构管理员配置权限功能</span>
      </div>
    </div>
  </div>
</template>

<script>
import radarHome from "./radar.vue";
import recommend from "./recommend.vue";
// import isoparaffin from "./isoparaffin.vue";
import modelList from "./Investmentmodel/index.vue";
import userPage from "./user.vue";
import Detailsenterprise from "../IntelligentSearch/components/Detailsenterprise.vue";
import { homeListAPI } from "../apiUrl";
export default {
  name: "IntelligentRecommendation",
  components: {
    radarHome,
    recommend,
    Detailsenterprise,
    // isoparaffin,
    modelList,
    userPage,
  },
  data() {
    return {
      chainList: [],
      tabList: [
        {
          name: "首页",
          id: 1,
        },
        {
          name: "招商企业推荐",
          id: 2,
        },
        // {
        //   name: "强链补链延链推荐",
        //   id: 3,
        // },
      ],
      CurrentSelectio: 1,
      isParticulars: 1,
      enterpriseID: "",
      modeid: 1,
      uniqId: "",
      industryId: "",
      examine: false,
      showcontent: false,
      modelType: "",
      chainName: "",
      chainId: "",
      recommendRegionCode: "",
      companyDetial: {},
    };
  },
  created() {
    this.getList();
  },
  mounted() {},
  methods: {
    // tab切换
    cut(id) {
      document.body.style.overflow = "auto";
      this.$nextTick(() => {
        document.body.scrollTop = 0;
        document.body.style.overflow = "visible";
      });
      this.CurrentSelectio = id;
    },
    handleCommand(e) {
      this.chainName = e.chainName;
      this.chainId = e.industryChainId;
      this.industryId = e.id;
      this.$nextTick(() => {
        if (this.CurrentSelectio == 1) {
          this.$refs.radarHome.updataPage();
        } else if (this.CurrentSelectio == 2) {
          this.$refs.recommend.updataPage();
        }
      });
    },
    async getList() {
      try {
        const res = await homeListAPI();
        if (res.result == null || res.result.length < 1) {
          this.showcontent = false;
        } else {
          this.showcontent = true;
        }
        if (res.result.length >= 1) {
          this.chainList = res.result;
          if (this.chainList.length != 0) {
            this.chainName = this.chainList[0].chainName;
            this.chainId = this.chainList[0].industryChainId;
            this.industryId = this.chainList[0].id;
          }
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.examine = true;
      }
    },
    // 企业详情
    particulars(item) {
      this.companyDetial = item;
      this.recommendRegionCode = item.recommendRegionCode || "";
      this.startpoint = this.isParticulars;
      this.enterpriseID = item.enterpriseId;
      this.modelType = item.modelType;
      this.isParticulars = 2;
    },
    // 返回原页面
    goback() {
      /* if(this.startpoint==4){
        this.startpoint=3
      } */
      this.isParticulars = this.startpoint;
    },
    gono() {
      this.isParticulars = 3;
    },
    gobacks() {
      this.isParticulars = 1;
    },
    demodel(id) {
      this.modeid = id;
      this.isParticulars = 3;
      this.$refs.modelList.initialize(this.modeid);
    },
    userdetails(id) {
      this.startpoint = this.isParticulars;
      this.uniqId = id;
      this.isParticulars = 4;
    },
  },
};
</script>

<style lang="scss" scoped>
.el-dropdown-link {
  font-size: 20px;
  font-weight: bold;
  color: #1d2129;
  cursor: pointer;
}

::-webkit-scrollbar {
  display: none;
}

.nodata {
  min-width: 88.5vw;
  height: 100vh;
  display: flex;
  justify-content: center;

  .con {
    margin-top: 10%;
    width: 170px;
    height: 300px;
    display: flex;
    align-items: center;
    flex-direction: column;

    img {
      width: 160px;
      height: 165.03px;
    }

    .p1 {
      margin-top: 38px;
      font-size: 14px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }

    .p2 {
      margin-top: 16px;
      font-size: 12px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}

.scan {
  // background: #fafcff;
  min-height: 100vh;
  box-sizing: border-box;
  min-width: 1110px;
  padding-bottom: 10px;

  &-top {
    padding: 16px 0px;
    padding-bottom: 12px;
  }

  &-tab {
    display: flex;
    padding-left: 16px;

    &-con {
      display: flex;
      justify-content: space-between;
    }

    &-select {
      display: flex;
    }

    &-list {
      margin-right: 64px;
      font-size: 16px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      cursor: pointer;

      &.on {
        font-weight: bold;
        color: #3370ff;
        position: relative;

        &::after {
          content: "";
          width: 28px;
          height: 2px;
          background: #3370ff;
          position: absolute;
          left: 50%;
          bottom: -8px;
          transform: translateX(-50%);
        }
      }
    }
  }

  &-con {
    margin: 16px 24px;
  }
}
</style>