<template>
  <div style="width: 100%;">
    <div class="title">
      <div class="iconarrow">
        <i
          style="cursor: pointer"
          class="el-icon-arrow-left"
          @click="gobacka"
        />
      </div>
    </div>
    <div class="industrys">
      <div class="head">
        <span class="span">亲商关联图谱</span>
      </div>
      <div class="box">
        <div
          v-if="!nodata"
          id="it"
          v-loading="echartsLoading"
          class="atlas"
        />
        <div
          v-else
          class="nodata"
        >
          <div class="con">
            <img src="https://static.idicc.cn/cdn/pangu/vacancy.png">
            <span class="p1">暂无数据</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { proBusinessImageAPI } from "../apiUrl";
export default {
  name: "UserPage",
  data() {
    return {
      nodata: false,
      echartsLoading: false,
    };
  },
  mounted() {
    this.open();
  },

  methods: {
    open() {
      ////console.log("???");
      this.$nextTick(() => {
        document.documentElement.scrollTop = 0;
        this.atlasFn();
      });
    },
    async atlasFn() {
      try {
        this.echartsLoading = true;
        const res = await proBusinessImageAPI({
          count: 30,
        });
        //res.result.dataSet = [];
        //res.result.relationSet = [];
        let data = res.result.dataSet.map((item) => {
          let itemStyle = {
            normal: {
              color: "#ffca00",
            },
          };
          if (item.type == 1) {
            itemStyle.normal.color = "#FDA41F";
          } else if (item.type == 2) {
            itemStyle.normal.color = "#3370FF";
          } else {
            itemStyle.normal.color = "#4EC30F";
          }
          return {
            name: item.name,
            id: item.uniqueId,
            itemStyle,
          };
        });
        let links = res.result.relationSet.map((item) => {
          let lineStyle = {
            normal: {
              color: "#ffca00",
            },
          };
          if (item.targetType == 1) {
            lineStyle.normal.color = "#FDA41F";
          } else if (item.targetType == 2) {
            lineStyle.normal.color = "#3370FF";
          } else {
            lineStyle.normal.color = "#4EC30F";
          }
          return {
            source: item.source,
            target: item.target,
            name: item.relation == null ? "" : item.relation,
            lineStyle,
          };
        });
        if (links.length == 0 && data.length == 0) {
          this.nodata = true;
        }
        ////console.log(data, links);
        var chartDom = document.getElementById("it");
        var myChart = echarts.init(chartDom);
        var option;
        option = {
          tooltip: {
            formatter: function (x) {
              return x.data;
            },
          },
          series: [
            {
              type: "graph",
              layout: "force",
              symbolSize: 120,
              roam: true,
              zoom: 0.3,
              scaleLimit: {
                min: 0.3,
                max: 1,
              },
              emphasis: {
                focus: "adjacency",
                lineStyle: {
                  width: 5,
                },
              },
              edgeSymbol: ["circle", "arrow"],
              edgeSymbolSize: [6, 6],
              edgeLabel: {
                color: "#fff",
                normal: {
                  show: true,
                  formatter: function (x) {
                    return x.data.name;
                  },
                  textStyle: {
                    fontSize: 12,
                  },
                },
              },
              force: {
                repulsion: 8000,
                edgeLength: [200, 200],
                avoidOverlap: true,
                layoutAnimation: false,
              },
              //draggable: true,
              label: {
                normal: {
                  show: true,
                  color: "#fff",
                  fontSize: 11,
                  textStyle: {},
                  formatter: function (params) {
                    var splitText = [];
                    var str = params.name;
                    for (var i = 0, len = str.length; i < len; i += 4) {
                      splitText.push(str.substr(i, 4));
                    }
                    return splitText.join("\n");
                  },
                },
              },
              data,
              links,
            },
          ],
        };
        option && myChart.setOption(option);
      } catch (error) {
        console.log(error);
      } finally {
        this.echartsLoading = false;
      }
    },
    gobacka() {
      this.$emit("gono");
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  height: 36px;
  margin-left: 30px;
  background-color: #fff;
}
.industrys {
  margin: 24px;
  height: 100%;
  //height: 461px;
  background: #ffffff;
  box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
  .head {
    height: 55px;
    border-bottom: 1px solid #e9e9e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .intimate {
      display: flex;
      align-items: center;
      justify-content: center;
      .atlas {
        width: 109px;
        height: 32px;
        border-radius: 5px;
        margin-left: 10px;
        border: #f1f4f9 1px solid;
        display: flex;
        align-items: center;
        cursor: pointer;
        justify-content: center;
        font-size: 14px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #3370FF;
      }
    }
    .span {
      font-size: 16px;
      padding-left: 24px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
  }
  .box {
    width: 100%;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;

    .atlas {
      width: 100%;
      height: 600px;
    }
    .nodata {
      display: flex;
      width: 200px;
      height: 600px;
      align-items: center;
      justify-content: center;
      .con {
        margin-top: 10%;
        width: 170px;
        height: 300px;
        display: flex;
        align-items: center;
        flex-direction: column;
        img {
          width: 216px;
          height: 177px;
        }
        .p1 {
          margin-top: 38px;
          font-size: 14px;
          font-family: Source Han Sans CN-Medium, Source Han Sans CN;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }
  }
}
</style>