<template>
  <div :style="{ display: 'flex', justifyContent: 'center' }">
    <svg
      :width="width"
      :height="height"
    >
      <a
        v-for="(tag, index) in tags"
        :key="index"
        href="#"
      >
        <text
          :x="tag.x"
          :y="tag.y"
          :font-size="12 * (1000 / (800 - tag.z * 2))"
          :font-weight="550"
          :fill-opacity="(600 + tag.z) / 800"
          :style="style(tag)"
        >
          {{ tag.text }}
        </text>
      </a>
    </svg>
  </div>
</template>
   
  <script>
export default {
  props: {
    width: {
      type: Number,
      default: 600,
    },
    height: {
      type: Number,
      default: 600,
    },
    radius: {
      type: Number,
      default: 120,
    },
  },
  data() {
    return {
      speedX: Math.PI / 1800,
      speedY: Math.PI / 1800,
      tags: [],
      colorList: [
        "#2fc7c9",
        "#b6a2de",
        "#6db9f0",
        "#88343b",
        "#ffc597",
        "#e5cf20",
        "#8fb92a",
        "#df74ae",
        "#ab978f",
      ],
      CXNum: 2,
      CYNum: 2,
    };
  },
  computed: {
    CX() {
      return this.width / this.CXNum;
    },
    CY() {
      return this.height / this.CYNum;
    },
  },
  mounted() {
    let _this = this;
    window.addEventListener(
      "resize",
      () => {
        let normalWidth = document.body.scrollWidth;
        _this.screenWidth = normalWidth;
        if (normalWidth <= 1550) {
          _this.CXNum = 2.7;
          _this.CYNum = 1.9;
        } else {
          _this.CXNum = 2.5;
          _this.CYNum = 1.8;
        }
        _this.width = normalWidth;
        _this.CX = _this.width / _this.CXNum;
        _this.CY = _this.height / _this.CYNum;
      },
      false
    );
    setInterval(() => {
      this.rotateX(this.speedX);
      this.rotateY(this.speedY);
    }, 17);
  },
  methods: {
    rotateX(angleX) {
      var cos = Math.cos(angleX);
      var sin = Math.sin(angleX);
      //console.log(cos,sin);
      for (let tag of this.tags) {
        var y1 = (tag.y - this.CY) * cos - tag.z * sin + this.CY;
        var z1 = tag.z * cos + (tag.y - this.CY) * sin;
        tag.y = y1;
        tag.z = z1;
      }
    },
    rotateY(angleY) {
      var cos = Math.cos(angleY);
      var sin = Math.sin(angleY);
      for (let tag of this.tags) {
        var x1 = (tag.x - this.CX) * cos - tag.z * sin + this.CX;
        var z1 = tag.z * cos + (tag.x - this.CX) * sin;
        tag.x = x1;
        tag.z = z1;
      }
    },
    style(tag) {
      return `fill:${tag.color};`;
    },
    calculation3DWord(radius = "") {
      let tags = [];
      for (let i = 0; i < this.tags.length; i++) {
        let tag = {};
        let k = -1 + (2 * (i + 1) - 1) / this.tags.length;
        let a = Math.acos(k);
        let b = a * Math.sqrt(this.tags.length * Math.PI);
        tag.text =
          typeof this.tags[i] === "string" ? this.tags[i] : this.tags[i].text;
        if (radius === "") {
          tag.x = this.width / 2.5 + this.radius * Math.sin(a) * Math.cos(b);
          tag.y = this.CY + this.radius * Math.sin(a) * Math.sin(b);
          tag.z = this.radius * Math.cos(a);
        } else {
          tag.x = this.width / 2.5 + radius * Math.sin(a) * Math.cos(b);
          tag.y = this.CY + radius * Math.sin(a) * Math.sin(b);
          tag.z = radius * Math.cos(a);
        }
        if (i <= this.colorList.length - 1) {
          tag.color = this.colorList[i];
        } else {
          tag.color =
            i % this.colorList.length === 0
              ? this.colorList[0]
              : this.colorList[i % this.colorList.length];
        }
        tags.push(tag);
      }
      this.tags.splice(0);
      this.tags = tags;
    },
    setTag(tags) {
      this.tags.splice(0);
      this.tags.push(...tags);
      this.calculation3DWord();
    },
  },
};
</script>
   
  <style>
</style>