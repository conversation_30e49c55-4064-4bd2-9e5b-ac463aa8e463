<template>
  <!-- 搜索结果列表 -->
  <div class="outermost">
    <div
      v-for="(it, ind) in enterpriseList"
      :key="ind"
    >
      <div class="time">
        {{ it.recommendedDate }}
        <div
          v-if="ind + 1 != enterpriseList.length"
          :style="{ height: it.list.length * 169 + 20 + 'px' }"
          class="wire"
        />
      </div>
      <div
        v-for="(item, index) in it.list"
        :key="index"
        class="single"
      >
        <img
          :src="getIconByType(item.showLabelType)"
          class="iconImg"
        >
        <div class="content">
          <div class="name">
            <div style="display: flex">
              <span
                style="cursor: pointer"
                @click="Entrydetails(item)"
              >{{
                item.enterpriseName
              }}</span>
              <div
                v-if="item.enterpriseLabelNameList"
                class="tag"
              >
                <div
                  v-for="(its, index1) in item.enterpriseLabelNameList"
                  v-show="ind < 4"
                  :key="index1"
                  class="industryTag"
                >
                  {{ its }}
                </div>
              </div>
              <div
                v-if="item.clueDealState"
                class="state"
              >
                <div
                  :class="[
                    '',
                    ['blue', 'success', 'gray'][item?.clueDealState],
                  ]"
                />

                <span
                  :class="[
                    '',
                    ['blueText', 'successText', 'grayText'][item?.clueDealState],
                  ]"
                >
                  {{ ['跟进中', '签约成功', '签约失败'][item?.clueDealState] }}
                </span>
              </div>
            </div>
            <div v-if="item.allocationState != 2">
              <div
                v-if="!item.isInvestClue"
                class="operation"
              >
                <div
                  class="bring"
                  @click="bringinto(item)"
                >
                  纳入招商意向
                </div>
                <!--                 <div
                  class="abierto"
                  @click="Leaveaside(item)"
                >
                  暂不处理
                </div> -->
              </div>
              <el-dropdown
                v-else
                placement="bottom"
              >
                <span class="bringinto">
                  <span style="color: #3370ff">已纳入招商意向</span>
                  <!-- <i
                    style="color: #3370FF"
                    class="el-icon-arrow-down el-icon--right"
                  /> -->
                </span>
                <!-- <el-dropdown-menu
                  slot="dropdown"
                  :append-to-body="false"
                >
                  <el-dropdown-item
                    v-if="
                      !item.merchantsStatusByEnterpriseDTO.isAssign &&
                        $store.getters.Assignauthority
                    "
                    @click.native="
                      designate(
                        item.merchantsStatusByEnterpriseDTO.clueId,
                        item.enterpriseName
                      )
                    "
                  >
                    <span class="dropdow">线索指派</span> 
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="
                      item.merchantsStatusByEnterpriseDTO.isAssign &&
                        $store.getters.Assignauthority
                    "
                  >
                    <span class="dropdow"> 已指派</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="!item.merchantsStatusByEnterpriseDTO.entrustOrNot"
                    @click.native="
                      entrustment(
                        item.merchantsStatusByEnterpriseDTO.clueId,
                        item.enterpriseName
                      )
                    "
                  >
                    <span class="dropdow">委托招商</span> 
                  </el-dropdown-item>
                  <el-dropdown-item v-else>
                    <span class="dropdow">已委托</span> 
                  </el-dropdown-item>
                  <el-dropdown-item
                    @click.native="
                      addsheet(
                        item.merchantsStatusByEnterpriseDTO.clueId,
                        item.enterpriseName
                      )
                    "
                  >
                    <span class="dropdow">添加走访记录</span> 
                  </el-dropdown-item>
                </el-dropdown-menu> -->
              </el-dropdown>
            </div>
            <div v-if="item.allocationState == 2">
              <div
                class="abierto"
                @click="remove(item)"
              >
                删除推荐
              </div>
            </div>
          </div>
          <div class="content-one">
            <div class="one-a">
              <span class="key">成立日期：</span>
              <span class="value">{{ item.registerDate }}</span>
            </div>
            <div class="one-b">
              <span class="key">注册资本：</span>
              <el-tooltip
                effect="dark"
                :visible-arrow="false"
                placement="top"
                :content="item.registeredCapital"
              >
                <span class="value">{{ item.registeredCapital }}</span>
              </el-tooltip>
            </div>
            <div class="one-c">
              <span class="key">所在地区：</span>
              <el-tooltip
                effect="dark"
                :visible-arrow="false"
                placement="top"
                :content="
                  item.city == item.province
                    ? item.province + item.area
                    : item.province + item.city + item.area
                "
              >
                <span class="value">{{ item.province
                }}<span v-if="item.city !== item.province">{{
                  item.city
                }}</span>{{ item.area }}</span>
              </el-tooltip>
            </div>
            <div class="one-d">
              <span class="key">所在产业：</span>
              <el-tooltip
                effect="dark"
                :visible-arrow="false"
                placement="top-start"
                :content="item.linkPlace"
              >
                <span class="value">{{ item.linkPlace }}</span>
              </el-tooltip>
            </div>
          </div>
          <div class="content-two">
            <div class="two-a">
              <span class="key">推荐日期：</span>
              <span class="value">{{ item.recommendedDate }}</span>
            </div>
            <div class="two-b">
              <span class="key">推荐招商模式：</span>
              <span class="value">{{ item?.modelTypeStr || '' }}</span>
              <!-- <el-tooltip
                effect="dark"
                :visible-arrow="false"
                placement="top"
                :content="item.modelTypeStr"
              >
                <span class="value">{{ item.modelTypeStr }}</span>
              </el-tooltip> -->
            </div>
            <div class="two-c">
              <span class="key">联系方式：</span>

              <el-tooltip
                effect="dark"
                :visible-arrow="false"
                placement="top"
                :content="item.enterpriseContact"
              >
                <span class="value">{{ item?.enterpriseContact || '' }}</span>
              </el-tooltip>
            </div>
            <div class="two-d">
              <span class="key">推送地区：</span>
              <span class="value">{{ item?.recommendRegionName || '' }}</span>
            </div>
          </div>
          <div v-if="item.modelTypeStr">
            <div
              v-if="item.modelTypeStr.includes('舆情招商')"
              class="content-three"
            >
              <div class="three">
                <span class="key">相关资讯：</span>
                <a
                  v-if="item.information"
                  :href="item.information.url"
                  class="value"
                  target="_blank"
                >{{ item.information.title }}</a>
                <span
                  v-else
                  class="noinformation"
                >暂无相关资讯</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <entrust
      v-if="entrustball"
      :entrustball="entrustball"
      :clue-id="clueId"
      :enterprise-name="enterpriseName"
      @closeentrust="closeentrust"
      @getList="getList"
    />
    <designate
      v-if="assignball"
      :assignball="assignball"
      :clue-id="clueId"
      :enterprise-name="enterpriseName"
      @closeassignball="closeassignball"
      @getList="getList"
    />
    <addrecord
      v-if="recordball"
      :recordball="recordball"
      :clue-id="clueId"
      :enterprise-name="enterpriseName"
      @closerecord="closerecord"
      @getList="getList"
    />
    <AlertDialog
      :show-dialog="showDialog"
      @onClose="onClose"
    />
  </div>
</template>
<script>
import { getEnterpriseIconByType } from '@/utils/utils';
import { inclusionIntentionAPI, leaveAsideAPI, deleteAPI } from '../../apiUrl';
import entrust from '../../manage/components/operation/entrust.vue';
import AlertDialog from '@/views/system-manage/attract-investment/manage/components/alertDialog.vue';
import designate from '../../manage/components/operation/designate.vue';
import addrecord from '../../manage/components/operation/addinterview.vue';
export default {
  name: 'EnterpriseList',
  components: {
    entrust,
    designate,
    addrecord,
    AlertDialog,
  },
  props: {
    enterpriseList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      AttentionLoading: false, //关注loading
      isIntention: false, //是否纳入意向
      showDialog: false,
      entrustOrNot: false, //是否委托
      isAssign: false, //是否指派
      clueId: '', //线索id
      entrustball: false, //委托招商弹层
      assignball: false, //线索指派弹层
      recordball: false, //线索指派弹层
      enterpriseName: '测试企业名称',
    };
  },
  methods: {
    getIconByType(type) {
      return getEnterpriseIconByType(type, 'webp');
    },
    // 企业详情
    Entrydetails(item) {
      this.$emit('particulars', item, 2);
      document.documentElement.scrollTop = 0;
    },
    // 刷新列表
    getList() {
      this.$emit('updataList');
    },
    // 关闭委托招商弹层
    closeentrust() {
      this.entrustball = false;
    },
    //纳入意向
    async bringinto(item) {
      await inclusionIntentionAPI({
        clueSource: 1,
        uniCode: item.unifiedSocialCreditCode,
      });
      this.$message.success('纳入招商意向成功');
      this.$emit('updataList');
    },
    async Leaveaside(item) {
      const res = await leaveAsideAPI({
        id: item.id, //招商企业记录id
      });
      if (res.code === 'SUCCESS') {
        this.$message.success('暂不处理成功');
        this.$emit('updataList');
      }
    },
    async remove(item) {
      this.$confirm('确认后将在招商列表中删去此条推荐', '确认删除推荐？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          await deleteAPI({
            id: item.id,
          });
          this.$message.success('删除推荐成功');
          this.$emit('updataList');
        })
        .catch(() => {});
    },
    // 关闭线索指派弹层
    closeassignball() {
      this.assignball = false;
    },
    // 关闭线索指派弹层
    closerecord() {
      this.recordball = false;
    },
    // 委托招商
    entrustment(id, name) {
      this.showDialog = true;
      //this.clueId = id;
      //this.enterpriseName = name;
      //this.entrustball = true;
    },
    onClose() {
      this.showDialog = false;
    },
    // 线索指派
    designate(id, name) {
      this.clueId = id;
      this.enterpriseName = name;
      this.assignball = true;
    },
    // 添加走访记录
    addsheet(id, name) {
      this.clueId = id;
      this.enterpriseName = name;
      this.recordball = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.dropdow {
  white-space: nowrap;
}
.noinformation {
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}
.outermost {
  //margin-top: 24px;
  //margin-bottom: 24px;
  padding-bottom: 24px;
  .time {
    font-size: 13px;
    margin-top: 17px;
    margin-bottom: 7px;
    //margin-left: 51px;
    margin-left: 3.2%;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #4e5969;
  }
  .wire {
    position: absolute;
    width: 1px;
    margin-top: -2px;
    left: 2.18%;
    background-color: #f3f3f6;
  }
  .time::after {
    background-color: #3e80ff;
    content: '';
    position: absolute;
    left: 1.8%;
    border: 3px solid #bedaff;
    width: 12px;
    height: 12px;
    z-index: 6;
    border-radius: 50%;
  }
}
.abierto {
  width: 100px;
  height: 32px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 2px 2px 2px 2px;
  font-size: 14px;
  font-family: Source Han Sans CN-Normal, Source Han Sans CN;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  border: 1px solid rgba(0, 0, 0, 0.25);
  cursor: pointer;
}
.abierto:hover {
  background-color: #f4faff;
}
.single {
  width: 94%;
  margin-left: 3%;
  height: 162px;
  padding-left: 26px;

  display: flex;
  background-color: #f7f8fa;
  margin-bottom: 7px;
  //border-bottom: 1px solid #e9e9e9;
  .iconImg {
    width: 32px;
    height: 32px;
    margin-top: 28px;
    margin-right: 22px;
  }
  .content {
    width: calc(100% - 52px);
    margin-top: 25px;
    padding-right: 26px !important;
    .name {
      width: 100%;
      max-width: 100%;
      display: flex;
      height: 18px;
      justify-content: space-between;
      font-size: 18px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      .tag {
        display: flex;
        padding-left: 10px;
        margin-top: 3px;
        .industryTag {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11px;
          padding: 0 6px;
          color: #ff7d00;
          background: #fff2e6;
          margin-right: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
        }
      }
      .state {
        width: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 4px;
        margin-left: 8px;
        font-size: 13px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        .blue {
          background: #417fff;
        }
        .gray {
          background: #e04848;
        }
        .success {
          background: #4cc169;
        }
        .success,
        .gray,
        .blue {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          margin-right: 5px;
          margin-bottom: 2px;
          opacity: 1;
        }
        .blueText {
          color: #3370ff;
        }
        .grayText {
          color: #e04848;
        }
        .successText {
          color: #4cc169;
        }
      }
      .operation {
        display: flex;
        .abierto {
          width: 100px;
          height: 32px;
          background-color: rgba(255, 255, 255, 0.5);
          border-radius: 2px 2px 2px 2px;
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 1;
          border: 1px solid rgba(0, 0, 0, 0.25);
          cursor: pointer;
        }
        .abierto:hover {
          background-color: #f4faff;
        }
        .bring {
          width: 126px;
          height: 32px;
          background: rgba(28, 145, 255, 0.95);
          border-radius: 2px 2px 2px 2px;
          opacity: 1;
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
        .bring:hover {
          background: rgba(28, 145, 255, 1);
        }
      }
      .bringinto {
        width: 126px;
        height: 32px;
        background-color: #ffffff;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #3370ff;
        opacity: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
      .bringinto:hover {
        background-color: #f4faff;
      }
    }
    .EnterpriseLabel {
      padding: 13px 0;
      .tags {
        display: flex;
      }
    }
    .content-one {
      display: flex;
      margin-top: 20px;
      .one-a {
        width: 20%;
      }
      .one-b {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }
      .one-c {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }
      .one-d {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }
      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }
      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }
    .content-two {
      display: flex;
      margin-top: 14px;
      .two-a {
        width: 20%;
      }
      .two-b {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }
      .two-c {
        width: 40%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }
      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }
      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }
    .content-three {
      display: flex;
      margin-top: 14px;
      .three {
        width: 100%;
      }
      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }
      .value {
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        cursor: pointer;
        font-weight: 400;
        color: #3370ff;
      }
    }
  }
}
</style>
