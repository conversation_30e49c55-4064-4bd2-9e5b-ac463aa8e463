<template>
  <!-- 弹窗提示 -->
  <div
    v-if="showDialog"
    class="dialogView"
  >
    <el-dialog
      :visible="showDialog"
      width="400px"
      top="20%"
      title="提示"
      :modal-append-to-body="false"
      @close="onClose"
    >
      <div class="dialog_content">
        web端不支持委托招商，请前往“哒达招商”APP或小程序哒达助招模块委托
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <div
          class="cancelBtn"
          @click="onClose"
        >
          取消
        </div>
        <div
          class="confirmBtn"
          @click="onClose"
        >
          确定
        </div>
      </div>
    </el-dialog>
  </div>
</template>
      
<script>
export default {
  name: "AlertDialog",
  props: {
    showDialog: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  methods: {
    onClose() {
      this.$emit("onClose");
    },
  },
};
</script>
      
<style scoped lang="scss">
::v-deep {
  .el-dialog__header {
    background: white !important;
  }
}
.dialogView {
  padding: 20px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}
.dialog_content {
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  color: #3f4a59;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
.cancelBtn {
  width: 76px;
  height: 38px;
  border-radius: 4px;
  align-items: center;
  padding: 8px 4px;
  gap: 12px;
  box-sizing: border-box;
  border: 1px solid #ced4db;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  color: #3f4a59;
  text-align: center;
  cursor: pointer;
  margin-right: 12px;
}
.confirmBtn {
  width: 76px;
  height: 38px;
  border-radius: 4px;
  align-items: center;
  padding: 8px 4px;
  gap: 12px;
  box-sizing: border-box;
  border: 1px solid #3370ff;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  color: #fff;
  cursor: pointer;
  text-align: center;
  background: #3370ff;
}
</style>