<template>
  <div>
    <el-dialog
      width="50%"
      title="委托招商"
      :visible="entrustball"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="form"
        label-position="top"
        :model="form"
        :rules="rules"
        label-width="140px"
      >
        <el-form-item label="企业名称">
          {{ enterpriseName }}
        </el-form-item>
        <el-form-item
          label="委托招商备注"
          prop="remark"
        >
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入委托招商备注"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          style="background-color: #f5f5f5;color: rgba(0, 0, 0, 0.85);"
          @click="cancel"
        >
          取 消
        </el-button>
        <el-button
          v-loading="loading"
          :disabled="loading ? true : false"
          type="primary"
          :rows="4"
          style="color: #fff"
          @click="preserve"
        >
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
      
<script>
import { commissionConnectionAPI } from "../../../apiUrl";
export default {
  name: "EntrustA",
  props: {
    entrustball: {
      type: Boolean,
      default: false,
    },
    enterpriseName: {
      type: String,
      default: null,
    },
    clueId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      loading: false,
      form: {
        remark: "",
      },
      rules: {
        remark: [{ required: true, message: "委托招商备注不能为空" }],
      },
    };
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("closeentrust");
    },
    async preserve() {
      try {
        await this.$refs.form.validate();
        this.loading = true;
        let data = {
          clueId: this.clueId,
          remark: this.form.remark,
        };
       const res =  await commissionConnectionAPI(data);
       if(res.code === 'SUCCESS'){
        this.$message.success("委托招商成功");
        this.cancel();
        this.$emit("getList");
      }
        
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
    <style scoped lang="scss">
::v-deep {
  .el-form-item {
    margin-bottom: 5px !important;
  }
  .el-form--label-top .el-form-item__label{
    padding: 0px !important;
  }
}
.inline-input {
  width: 100%;
}
</style>