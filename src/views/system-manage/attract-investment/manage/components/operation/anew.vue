<template>
  <div>
    <el-dialog
      width="50%"
      title="线索指派"
      :visible="assignball"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="form"
        label-position="top"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="企业名称">
          {{ enterpriseName }}
        </el-form-item>
        <el-form-item
          label="招商经理"
          prop="followUpPersonId"
          class="appendToBodyFalse"
        >
          <el-select
          :popper-append-to-body="false"
            v-model="form.followUpPersonId"
            placeholder="请选择招商经理"
            filterable
          >
            <el-option
              v-for="item in assignList"
              :key="item.id"
              :label="item.realName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="线索流转备注"
          prop="remark"
        >
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入线索流转备注"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          style="background-color: #f5f5f5;color: rgba(0, 0, 0, 0.85);"
          @click="cancel"
        >
          取 消
        </el-button>
        <el-button
          v-loading="loading"
          type="primary"
          style="color: #fff"
          :disabled="loading ? true : false"
          @click="preserve"
        >
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
          
    <script>
  import { listAllChildDeptUsersAPI,listAllDeptAPI } from "@/api/CattractInvestment";
  import { reAssignmentClueAPI } from "../../../apiUrl";
  export default {
    name: "EntrustA",
    props: {
      assignball: {
        type: Boolean,
        default: false,
      },
      enterpriseName: {
        type: String,
        default: null,
      },
      clueId: {
        type: String,
        default: null,
      },
    },
    data() {
      return {
        loading: false,
        assignList: [],
        form: {
          remark: "",
          followUpPersonId: "",
        },
        rules: {
          remark: [{ required: true, message: "线索流转备注不能为空" }],
          followUpPersonId: [{ required: true, message: "招商经理不能为空" }],
        },
      };
    },
    created() {
      this.assign();
    },
    methods: {
      async assign() {
        const res = await listAllDeptAPI();
        this.assignList = res.result;
      },
      cancel() {
        this.$emit("closeassignball");
      },
      async preserve() {
        try {
          await this.$refs.form.validate();
          this.loading = true;
          let data = {
            clueId: this.clueId,
            remark: this.form.remark,
            followUpPersonId: this.form.followUpPersonId,
          };
          await reAssignmentClueAPI(data);
          this.$message.success("线索重新指派成功");
          this.cancel();
          this.$emit("getList");
        } finally {
          this.loading = false;
        }
      },
    },
  };
  </script>
  <style scoped lang="scss">
        ::v-deep {
    .el-form-item {
      margin-bottom: 5px !important;
    }
    .el-form--label-top .el-form-item__label{
      padding: 0px !important;
    }
  }
  .inline-input {
    width: 100%;
  }
  </style>