<template>
  <div>
    <el-dialog
      width="40%"
      title="指派路径"
      :visible="showPath"
      :close-on-click-modal="false"
      center
      @close="cancel"
    >
      <div
        v-if="pathList.length >= 1"
        class="const"
      >
        <div
          v-for="(item, index) in pathList"
          :key="index"
          class="contents"
        >
          <div class="text-box">
            <span class="titletext">{{ item.assignDateStr }}</span>
          </div>
          <div class="assignor">
            <div class="time">
              指派人：{{ item.assignPerson }}
            </div>
            跟进人：{{ item.beAssignPerson }}
          </div>
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          style="color: #fff"
          @click="cancel"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
        
  <script>
export default {
  name: "EntrustA",
  props: {
    showPath: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
    pathList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    cancel() {
      this.$emit("update:showPath", false);
    },
  },
};
</script>
      <style scoped lang="scss">
.const {
  height: 300px;
  display: block;
  overflow-y: scroll;
  .contents {
    position: relative;
    //height: auto;
    height: 80px;
    border-radius: 10px;
    margin-left: 42px;
    margin-top: 10px;
    padding: 0px 10px;
    font-size: 28px;
    color: #4e5969;

    .titletext {
      display: flex;
      padding-top: 8px;
      font-size: 15px;
    }

    .assignor {
      display: flex;
      margin-top: 20px;
      font-size: 13px;

      .time {
        width: 50%;
      }
    }
  }
}
.contents::before {
  background-color: #3e80ff;
  content: "";
  position: absolute;
  top: 6px;
  left: -20px;
  border: 4px solid #bedaff;
  width: 16px;
  height: 16px;
  z-index: 6;
  border-radius: 50%;
}

.contents:not(:last-child)::after {
  background-color: #f0f0f0;
  content: "";
  position: absolute;
  top: 19px;
  z-index: 5;
  left: -13px;
  width: 2px;
  height: 80px;
}
</style>