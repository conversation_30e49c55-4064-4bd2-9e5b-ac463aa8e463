<template>
  <div>
    <el-dialog
      width="50%"
      title="任务处理"
      :visible="recordball"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="form"
        label-position="top"
        label-width="80px"
        :model="form"
        :rules="rules"
      >
        <el-form-item
          label="公司名称"
        >
          {{ enterpriseName }}
        </el-form-item>
        <el-form-item
          prop="havaInvestmentIntention"
          label="任务状态"
        >
          <el-radio
            v-model="form.havaInvestmentIntention"
            label="0"
          >
            待处理
          </el-radio>
          <el-radio
            v-model="form.havaInvestmentIntention"
            label="1"
          >
            处理中
          </el-radio>
          <el-radio
            v-model="form.havaInvestmentIntention"
            label="2"
          >
            已完成
          </el-radio>
        </el-form-item>
        <el-form-item
          label="处理日期"
        >
          {{ formattedDate }}
        </el-form-item>
        <el-form-item
          label="处理人"
        >
          {{ $store.getters.user.realName }}
        </el-form-item>
        <el-form-item
          prop="remark"
          label="任务处理概述"
        >
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入任务处理概述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          style="background-color: #f5f5f5;color: rgba(0, 0, 0, 0.85);"
          @click="cancel"
        >
          取 消
        </el-button>
        <el-button
          v-loading="loading"
          type="primary"
          style="color: #fff"
          :disabled="loading ? true : false"
          @click="preserve"
        >
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
            
  <script>
  import { dealAPI } from "../../../apiUrl";
  export default {
    name: "EntrustA",
    props: {
      recordball: {
        type: Boolean,
        default: false,
      },
      enterpriseName: {
        type: String,
        default: null,
      },
      clueId: {
        type: String,
        default: null,
      },
      state:{
        type: Number,
        default: null,
      }
    },
    data() {
      return {
        loading: false,
        pickerOptions: {
          disabledDate(time) {
              return time.getTime() > Date.now();
            },
        },
        formattedDate:'',
        form: {
          remark: "",
          havaInvestmentIntention:''
        },
        rules: {
          remark: [{ required: true, message: "任务处理概述不能为空" }],
          havaInvestmentIntention:[{ required: true, message: "任务状态" }],
        },
      };
    },
    created() {
			const currentDate = new Date();
			const year = currentDate.getFullYear();
			const month = ("0" + (currentDate.getMonth() + 1)).slice(-2);
			const date = ("0" + currentDate.getDate()).slice(-2);
			const formattedDate = `${year}-${month}-${date}`;
			this.formattedDate = formattedDate
      this.form.havaInvestmentIntention=this.state
		},
    methods: {
      cancel() {
        this.$emit("closetask");
      },
      async preserve() {
        try {
          await this.$refs.form.validate();
          this.loading = true;
          let data = {
            clueId: this.clueId,
            dealDesc: this.form.remark,
            delaState:this.form.havaInvestmentIntention
          };
          const res = await dealAPI(data);
          if(res.code === 'SUCCESS'){
            this.$message.success("任务处理成功");
            this.cancel();
            this.$emit("getList");
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.loading = false;
        }
      },
    },
  };
  </script>
  <style scoped lang="scss">
          ::v-deep {
    .el-form-item {
      margin-bottom: 5px !important;
    }
    .el-form--label-top .el-form-item__label{
      padding: 0px !important;
    }
  }
  .inline-input {
    width: 100%;
  }
  </style>