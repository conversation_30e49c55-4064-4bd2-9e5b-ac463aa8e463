<template>
  <div>
    <el-dialog
      width="50%"
      title="添加走访记录"
      :visible="recordball"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="form"
        label-position="top"
        label-width="80px"
        :model="form"
        :rules="rules"
      >
        <el-form-item
          label="公司名称"
        >
          {{ enterpriseName }}
        </el-form-item>
        <el-form-item
          prop="followUpDate"
          label="跟进日期"
          class="appendToBodyFalse"
        >
          <div class="block">
            <el-date-picker
              v-model="form.followUpDate"
              :popper-append-to-body="false"
              value-format="timestamp"
              popper-class="timePick"
              type="date"
              placeholder="选择日期"
              :picker-options="pickerOptions"
            />
          </div>
        </el-form-item>
        <el-form-item
          prop="followUpPersonId"
          label="跟进人"
        >
          {{ $store.getters.user.realName }}
        </el-form-item>
        <el-form-item
          prop="havaInvestmentIntention"
          label="投资意向"
        >
          <el-radio
            v-model="form.havaInvestmentIntention"
            label="true"
          >
            有投资意向
          </el-radio>
          <el-radio
            v-model="form.havaInvestmentIntention"
            label="false"
          >
            无投资意向
          </el-radio>
        </el-form-item>
        <el-form-item
          prop="remark"
          label="跟进概述"
        >
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入走访概述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          style="background-color: #f5f5f5;color: rgba(0, 0, 0, 0.85);"
          @click="cancel"
        >
          取 消
        </el-button>
        <el-button
          v-loading="loading"
          type="primary"
          style="color: #fff"
          :disabled="loading ? true : false"
          @click="preserve"
        >
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
          
<script>
import { addfollowUpRecordAPI } from "../../../apiUrl";
export default {
  name: "EntrustA",
  props: {
    recordball: {
      type: Boolean,
      default: false,
    },
    enterpriseName: {
      type: String,
      default: null,
    },
    clueId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      loading: false,
      pickerOptions: {
        disabledDate(time) {
            return time.getTime() > Date.now();
          },
      },
      form: {
        remark: "",
        followUpPersonId: "",
        followUpDate:'',
        havaInvestmentIntention:''
      },
      rules: {
        remark: [{ required: true, message: "走访概述不能为空" }],
        followUpDate:[{ required: true, message: "跟进日期不能为空" }],
        havaInvestmentIntention:[{ required: true, message: "投资意向不能为空" }],
      },
    };
  },
  methods: {
    cancel() {
      this.$emit("closerecord");
    },
    async preserve() {
      try {
        await this.$refs.form.validate();
        this.loading = true;
        let data = {
          clueId: this.clueId,
          followUpDate:this.form.followUpDate,
          overview: this.form.remark,
          havaInvestmentIntention:this.form.havaInvestmentIntention
        };
        await addfollowUpRecordAPI(data);
        this.$message.success("添加走访记录成功");
        /* const h = this.$createElement
        this.$message({
          message: h('div', null, [
            h('span',{ style:"color:gray" }, '添加走访记录成功，该企业已进入'),
            h('span', { style:"color:green;cursor:pointer",on:{
              click:()=>{
                this.skip()
              }
            } }, '数智招商-招商智管-我的接洽企业'),
            h('span',{ style:'color:gray',}, '中')
          ]),
          type: 'success',
          duration: 5000,
        }); */
        this.cancel();
        this.$emit("getList");
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    skip(){
      this.$message.closeAll(); //关闭message弹窗
      this.$router.push({ path: '/attract/manage', query: { show:2} });
    },
  },
};
</script>
<style scoped lang="scss">
        ::v-deep {
  .el-form-item {
    margin-bottom: 5px !important;
  }
  .el-form--label-top .el-form-item__label{
    padding: 0px !important;
  }
}
.inline-input {
  width: 100%;
}
</style>
<style lang="scss">
.timePick{
  margin-left: 260px !important;
  margin-top: 60px !important;
}
</style>