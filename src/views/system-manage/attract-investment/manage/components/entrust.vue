<template>
  <!-- 委托企业 -->
  <div class="list">
    <div class="title">
      共有 {{ total }} 家企业
    </div>
    <div class="table">
      <el-table
        v-loading="tabLoading"
        :data="tabList"
        style="width: 100%"
      >
        <el-table-column
          prop="enterpriseName"
          label="企业名称"
          width="250"
        >
          <template slot-scope="{ row }">
            <div
              class="enterpriseNames"
              @click="goparticulars(row)"
            >
              {{ row.enterpriseName }}
            </div>
            <div
              v-if="row.enterpriseLabelNames"
              class="tabs"
            >
              <div
                v-for="(item, index) in row.enterpriseLabelNames"
                v-show="index <= 1"
                :key="index"
                class="firmTag"
              >
                {{ item }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="所属产业链"
          width="200"
        >
          <template slot-scope="{ row }">
            <el-tooltip
              class="item"
              effect="dark"
              :visible-arrow="false"
              placement="top"
            >
              <div slot="content">
                <div v-if="row.chainNameList">
                  <span
                    v-for="(item, index) in row.chainNameList"
                    :key="index"
                  >
                    {{ item.replace('产业金脑·', '')
                    }}<span v-if="row.chainNameList.length != index + 1">;</span>
                  </span>
                </div>
              </div>
              <div
                v-if="row.chainNameList"
                class="chainNameList"
              >
                <span
                  v-for="(item, index) in row.chainNameList"
                  :key="index"
                >
                  {{ item.replace('产业金脑·', '')
                  }}<span v-if="row.chainNameList.length != index + 1">;</span>
                </span>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="所在地区"
          width="200"
        >
          <template slot-scope="{ row }">
            {{ row.province
            }}<span v-if="row.province !== row.city">{{ row.city }}</span>{{ row.area }}
          </template>
        </el-table-column>
        <el-table-column
          prop="entrustDateStr"
          label="委托日期"
          width="120"
        />
        <el-table-column
          prop="entrustPerson"
          label="委托人"
        />
        <el-table-column
          prop="clueDealState"
          label="当前进度"
        >
          <template slot-scope="{ row }">
            <div style="display: flex; align-items: center">
              <div
                :class="
                  row.entrustDealState == 0
                    ? 'red'
                    : row.entrustDealState == 2
                      ? 'green'
                      : 'blue'
                "
              />
              {{
                row.entrustDealState == 0
                  ? '未开始'
                  : row.entrustDealState == 2
                    ? '已完成'
                    : '进行中'
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="{ row }">
            <div>
              <div
                class="Youcanclick"
                @click="detail(row)"
              >
                详情
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="ye">
      <el-pagination
        :current-page.sync="pageNum"
        :page-sizes="[5, 10, 20, 50]"
        :page-size.sync="pageSize"
        background
        :total="+total"
        layout="prev, pager, next,sizes,  jumper"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
  </div>
</template>

<script>
import { entrustPageAPI } from '../../apiUrl';
export default {
  name: 'IntentionQ',
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      total: 0,
      tabList: [],
      tabLoading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    detail(row) {
      this.$emit('godel', row.id, row.enterpriseId, 4);
    },
    // 去企业详情
    goparticulars(row) {
      this.$emit('godelenterprise', row, row.enterpriseId, 2);
    },
    //委托企业列表
    async getList() {
      try {
        this.tabLoading = true;

        let data = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          queryType: 2, //查询全部类型
        };
        const res = await entrustPageAPI(data);
        this.total = res.result.totalNum;
        this.tabList = res.result.records;
      } catch (error) {
        console.log(error);
      } finally {
        this.tabLoading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.enterpriseNames {
  cursor: pointer;
  width: 250px;
  white-space: nowrap;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}
.Youcanclick {
  color: #3370ff;
  cursor: pointer;
}
.chainNameList {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
::v-deep {
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #3370ff;
    color: #fff;
  }
  .el-pagination {
    -webkit-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.1) !important;
  }
  .el-button {
    color: #3370ff;
  }
}
.ye {
  margin-top: 20px;
}

.list {
  .title {
    height: 50px;
    border-bottom: 1px solid #e9e9e9;
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
  }
  .red {
    background: #f53f3f;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .blue {
    background: #417fff;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .green {
    background: #10aa38;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .gray {
    background: #c9cdd4;
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    top: 29px;
    left: 26px;
    border-radius: 50%;
  }

  .tabs {
    display: flex;
    //align-items: center;
    //justify-content: center;
    white-space: nowrap;

    .firmTag {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      margin-top: 6px;
      padding: 0 6px;
      color: #ff7d00;
      background: #fff2e6;
      margin-right: 8px;
      height: 18px;
      border-radius: 2px 2px 2px 2px;
    }
  }

  .table {
    margin-top: 24px;
  }
}
</style>
