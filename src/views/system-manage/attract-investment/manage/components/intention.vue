<template>
  <!-- 意向企业 -->
  <div class="list">
    <div class="title">
      共有 {{ total }} 家企业
    </div>
    <div class="table">
      <el-table
        v-loading="tabLoading"
        :data="tabList"
        style="width: 100%"
      >
        <el-table-column
          prop="enterpriseName"
          label="企业名称"
          width="250"
        >
          <template slot-scope="{ row }">
            <div
              class="enterpriseNames"
              @click="goparticulars(row)"
            >
              {{ row.enterpriseName }}
            </div>
            <div
              v-if="row.enterpriseLabelNames"
              class="tabs"
            >
              <div
                v-for="(item, index) in row.enterpriseLabelNames"
                v-show="index <= 1"
                :key="index"
                class="firmTag"
              >
                {{ item }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="所属产业链"
          width="200"
        >
          <template slot-scope="{ row }">
            <el-tooltip
              class="item"
              effect="dark"
              :visible-arrow="false"
              placement="top"
            >
              <div slot="content">
                <div
                  v-if="row.chainNameList"
                >
                  <span
                    v-for="(item, index) in row.chainNameList"
                    :key="index"
                  >
                    {{ item.replace("产业金脑·", "")
                    }}<span v-if="row.chainNameList.length != index + 1">;</span>
                  </span>
                </div>
              </div>
              <div
                v-if="row.chainNameList"
                class="chainNameList"
              >
                <span
                  v-for="(item, index) in row.chainNameList"
                  :key="index"
                >
                  {{ item.replace("产业金脑·", "")
                  }}<span v-if="row.chainNameList.length != index + 1">;</span>
                </span>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="所在地区"
          width="200"
        >
          <template slot-scope="{row}">
            {{ row.province }}<span v-if="row.province!==row.city">{{ row.city }}</span>{{ row.area }}
          </template>
        </el-table-column>
        <el-table-column
          prop="intentionDateStr"
          width="140"
        >
          <template
            slot="header"
          >
            <el-tooltip
              class="item"
              effect="dark"
              content="纳入意向日期"
              placement="top"
            >
              <span>纳入意向日期</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="是否系统推荐"
        >
          <template
            slot="header"
          >
            <el-tooltip
              class="item"
              effect="dark"
              content="是否系统推荐"
              placement="top"
            >
              <span>是否系统推荐</span>
            </el-tooltip>
          </template>
          <template slot-scope="{row}">
            {{ row.isSystemRecommend ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="280"
        >
          <template slot-scope="{ row }">
            <div style="display: flex;">
              <div class="rule">
                <div
                  v-if="
                    $store.getters.Assignauthority &&
                      row.clueState == 0 &&
                      row.clueDealState !== '2'
                  "
                  class="Youcanclick"
                  @click="designate(row)"
                >
                  线索指派
                </div>
                <div
                  v-else-if="$store.getters.Assignauthority"
                  class="Noclick"
                >
                  已指派
                </div>
              </div>
              <el-divider
                v-if="$store.getters.Assignauthority"
                direction="vertical"
              />
              <div class="rule">
                <div
                  v-if="row.entrustOrNot == 0"
                  class="Youcanclick"
                  @click="entrustment()"
                >
                  委托招商
                </div>
                <div
                  v-else
                  class="Noclick"
                >
                  已委托
                </div>
              </div>
              <el-divider direction="vertical" />
              <div
                class="Youcanclick"
                @click="addsheet(row)"
              >
                添加走访记录
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="ye">
      <el-pagination
        :current-page.sync="pageNum"
        :page-sizes="[5, 10, 20, 50]"
        :page-size.sync="pageSize"
        background
        :total="+total"
        layout="prev, pager, next,sizes,  jumper"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
    <entrust
      v-if="entrustball"
      :entrustball="entrustball"
      :clue-id="clueId"
      :enterprise-name="enterpriseName"
      @closeentrust="closeentrust"
      @getList="getList"
    />
    <designate
      v-if="assignball"
      :assignball="assignball"
      :clue-id="clueId"
      :enterprise-name="enterpriseName"
      @closeassignball="closeassignball"
      @getList="getList"
    />
    <addrecord
      v-if="recordball"
      :recordball="recordball"
      :clue-id="clueId"
      :enterprise-name="enterpriseName"
      @closerecord="closerecord"
      @getList="getList"
    />
    <AlertDialog
      :show-dialog="showDialog"
      @onClose="onClose"
    />
  </div>
</template>

<script>
import AlertDialog from "./alertDialog.vue";
import { intentionPageAPI } from "../../apiUrl";
import entrust from "././operation/entrust.vue";
import designate from "././operation/designate.vue";
import addrecord from "././operation/addinterview.vue";
export default {
  name: "IntentionQ",
  components: {
    entrust,
    designate,
    addrecord,
    AlertDialog,
  },
  data() {
    return {
      showDialog: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      tabList: [],
      tabLoading: false,
      clueId: "", //线索id
      enterpriseName: "", //企业名称
      entrustball: false, //委托招商弹层
      assignball: false, //线索指派弹层
      recordball: false, //线索指派弹层
    };
  },
  created() {
    this.getList();
  },
  methods: {
    onClose() {
      this.showDialog = false;
    },
    // 关闭委托招商弹层
    closeentrust() {
      this.entrustball = false;
    },
    // 关闭线索指派弹层
    closeassignball() {
      this.assignball = false;
    },
    // 关闭线索指派弹层
    closerecord() {
      this.recordball = false;
    },
    // 委托招商
    entrustment() {
     this.showDialog  =true;
    },
    // 线索指派
    designate(row) {
      this.clueId = row.id;
      this.enterpriseName = row.enterpriseName;
      this.assignball = true;
    },
    // 添加走访记录
    addsheet(row) {
      this.clueId = row.id;
      this.enterpriseName = row.enterpriseName;
      this.recordball = true;
    },
    // 去企业详情
    goparticulars(row) {
      this.$emit("godelenterprise",row, row.enterpriseId, 2);
    },
    //意向企业列表
    async getList() {
      try {
        this.tabLoading = true;
        let data = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          queryType: 2, //查询全部
        };
        const res = await intentionPageAPI(data);
        this.total = res.result.totalNum;
        this.tabList = res.result.records;
      } catch (error) {
        console.log(error);
      } finally {
        this.tabLoading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.enterpriseNames{
  cursor: pointer;
  width: 250px;
  white-space: nowrap;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}
.rule{
  width:60px;
  display: flex;
  justify-content: center;
}
.Youcanclick {
  color: #3370FF;
  cursor: pointer;
}
.Noclick {
  color: #000000;
  opacity: 0.6;
  cursor: not-allowed;
}
.chainNameList {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
::v-deep {
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #3370FF;
    color: #fff;
  }
  .el-pagination {
    -webkit-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.1) !important;
  }
  .el-button {
    color: #3370FF;
  }
  .el-divider--vertical{
    height: 1.5em !important;
  }
}
.ye {
  margin-top: 20px;
}

.list {
  .title {
    height: 50px;
    border-bottom: 1px solid #e9e9e9;
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
  }

  .tabs {
    display: flex;
    white-space: nowrap;

    .firmTag {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      margin-top: 6px;
      padding: 0 6px;
      color: #ff7d00;
      background: #fff2e6;
      margin-right: 8px;
      height: 18px;
      border-radius: 2px 2px 2px 2px;
    }
  }

  .table {
    margin-top: 24px;
  }
}
</style>