<template>
  <!-- 展开/收起 -->
  <div class="textContent">
    <div
      :id="`id_${id}`"
      :class="['content', textOver && !openLine ? 'contentOneLine' : '']"
    >
      {{ content }}
    </div>
    <div
      v-if="textOver"
      class="openLineBtn"
      @click="openLineClick"
    >
      {{ openLine ? "收起" : "展开" }}
      <i :class="openLine ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
    </div>
  </div>
</template>
<script>
export default {
  name: "LongText",
  props: {
    content: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      textOver: false,
      openLine: false,
    };
  },
  //   created() {
  //     this.getH();
  //   },
  onBeforeMount() {
    this.getH();
  },
  watch: {
    content() {
      this.getH();
    },
  },
  methods: {
    getH() {
      this.$nextTick(() => {
        const domRef = document.getElementById(`id_${this.id}`);
        if (domRef) {
          const height = window
            .getComputedStyle(domRef)
            .height.replace("px", "");
          if (+height > 21) {
            this.textOver = true;
          } else {
            this.textOver = false;
          }
        } else {
          this.textOver = false;
        }
      });
    },
    openLineClick() {
      this.openLine = !this.openLine;
    },
  },
};
</script>

<style scoped lang="scss">
.textContent {
  width: 100%;
}
.content {
  width: 100%;
  font-size: 14px;
  font-weight: 400;
  color: #4e5969;
  line-height: 20px;
}
.contentOneLine {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.openLineBtn {
  display: flex;
  width: 60px;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  color: #3370ff;
  align-items: center;
  cursor: pointer;
  margin-top: 4px;
}
.openLineBtn2 {
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  color: #3370ff;
  align-items: center;
  cursor: pointer;
  margin-top: 4px;
}
</style>
