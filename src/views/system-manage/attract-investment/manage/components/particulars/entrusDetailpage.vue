<template>
  <!-- 委托企业详情 -->
  <div class="box">
    <div class="title">
      <div class="iconarrow">
        <i
          style="cursor: pointer"
          class="el-icon-arrow-left"
          @click="gobacka"
        />
      </div>
    </div>
    <div class="headA">
      <img
        :src="getIconByType(essential.showLabelType)"
        class="iconImg"
      >
      <div class="content">
        <div>
          <div class="name">
            <span>{{ essential.enterpriseName }}</span>
            <div
              v-if="essential.chainNameList"
              class="tag"
            >
              <div
                v-for="(it, ind) in essential.chainNameList"
                :key="ind"
                class="industryTag"
              >
                {{ it.replace("产业金脑·", "") }}
              </div>
            </div>
          </div>
          <div class="EnterpriseLabel">
            <div
              v-if="essential.enterpriseLabelNames"
              class="tags"
            >
              <div
                v-for="(it, ind) in essential.enterpriseLabelNames"
                :key="ind"
                class="firmTag"
              >
                {{ it }}
              </div>
            </div>
          </div>
        </div>

        <div class="content-two">
          <div class="two-a">
            <span class="key">委托日期：</span>
            <span class="value">{{ essential.entrustDateStr }}</span>
          </div>
          <div class="two-b">
            <span class="key">委托人：</span>
            <span class="value">{{ essential.entrustPerson }}</span>
          </div>
          <div class="two-c">
            <span class="key">委托招商备注：</span>
            <el-tooltip
              effect="dark"
              :visible-arrow="false"
              placement="top"
              :content="essential.entrustTaskRemark"
            >
              <span class="value">{{ essential.entrustTaskRemark }}</span>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
    <div class="processing">
      <div class="title">
        <span class="text">委托跟进记录</span>
      </div>
      <div class="interview">
        <div class="total">
          当前共<span class="tot"> {{ total }} </span>条跟进记录
        </div>
      </div>
      <div class="record">
        <div
          v-for="(item, index) in cluepages"
          :key="index"
          class="single"
        >
          <div class="head">
            <span class="time">{{ item.followUpDate }}</span>
            <i
              v-if="item.dealState"
              :class="
                item.dealState == 0
                  ? 'red'
                  : item.dealState == 2
                    ? 'green'
                    : 'blue'
              "
            />
            <span
              v-if="item.dealState"
              class="letter"
            >{{
              item.dealState == 0
                ? "待处理"
                : item.dealState == 2
                  ? "已完成"
                  : "处理中"
            }}</span>
          </div>
          <div class="box">
            <div class="one">
              <i class="yuan" />
              <div class="describe">
                跟进人
              </div>
              <span class="jg"> {{ item.followUpPerson }}</span>
            </div>
            <div class="ones">
              <i class="yuan" />
              <div class="describe">
                跟进概述
              </div>
              <span class="jg"> {{ item.followUpOverview }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="ye">
        <el-pagination
          :current-page.sync="pageNum"
          :page-size.sync="pageSize"
          :total="+total"
          layout="prev, pager, next"
          @size-change="getcluepages"
          @current-change="getcluepages"
        />
      </div>
    </div>
    <entrust
      v-if="entrustball"
      :entrustball="entrustball"
      :clue-id="threadID"
      :enterprise-name="essential.enterpriseName"
      @closeentrust="closeentrust"
      @getList="getList"
    />
    <designate
      v-if="assignball"
      :assignball="assignball"
      :clue-id="threadID"
      :enterprise-name="essential.enterpriseName"
      @closeassignball="closeassignball"
      @getList="getList"
    />
    <addrecord
      v-if="recordball"
      :recordball="recordball"
      :clue-id="threadID"
      :enterprise-name="essential.enterpriseName"
      @closerecord="closerecord"
      @getList="getList"
    />
  </div>
</template>
    
    <script>
import { getEnterpriseIconByType } from '@/utils/utils'
import {
  clueDetailAPI,
  pageByClueIdAPI,
  getMerchantsStatusAPI,
  collectAPI,
  cancelCollectAPI,
} from "../../../apiUrl";
import entrust from "../operation/entrust.vue";
import designate from "../operation/designate.vue";
import addrecord from "../operation/addinterview.vue";
export default {
  name: "TaskDetai",
  components:{
    entrust,
    designate,
    addrecord
  },
  props: {
    threadID: {
      type: String,
      default: null,
    },
    enterpriseID: {
      type: String,
      default: null,
    },
    companyDetial: {
      type: Object,
      default: ()=>{},
    },
  },
  data() {
    return {
      essential: {},
      pathList: [], //指派线索
      cluepages: [], //线索处理
      showPath: false,
      total: 0,
      pageNum: 1,
      pageSize: 5,
      task: false,
      clueId: "",
      enterpriseName: "",
      state: "",
      isAttention: false, //是否关注
      AttentionLoading: false, //关注loading
      isIntention: false, //是否纳入意向
      entrustOrNot: false, //是否委托
      isAssign: false, //是否指派
      entrustball: false, //委托招商弹层
      assignball: false, //线索指派弹层
      recordball: false, //线索指派弹层
    };
  },
  created() {
    this.getparticulars();
    this.getMerchantsStatusAPI(); //招商相关状态
  },
  methods: {
    getIconByType (type) {
      return getEnterpriseIconByType(type, 'webp');
    },
    getList(){
      this.getMerchantsStatusAPI()
    },
    async getMerchantsStatusAPI() {
      const res = await getMerchantsStatusAPI({
        enterpriseId: this.enterpriseID,
      });
      (this.isAttention = res.result.isAttention), //是否关注
        (this.isIntention = res.result.isIntention), //是否纳入意向
        (this.entrustOrNot = res.result.entrustOrNot), //是否委托
        (this.isAssign = res.result.isAssign); //是否指派
    },
    closetask() {
      this.task = false;
    },
    // 关闭委托招商弹层
    closeentrust() {
      this.entrustball = false;
    },
    // 关闭线索指派弹层
    closeassignball() {
      this.assignball = false;
    },
    // 关闭线索指派弹层
    closerecord() {
      this.recordball = false;
    },
    // 委托招商
    entrustment() {
      this.entrustball = true;
    },
    // 线索指派
    designate() {
      this.assignball = true;
    },
    // 添加走访记录
    addsheet() {
      this.recordball = true;
    },
    // 获取详情
    async getparticulars() {
      let data = {
        clueId: this.threadID,
      };
      const res = await clueDetailAPI(data);
      this.essential = res.result;
      this.getcluepages();
    },
    // 获取委托跟进记录
    async getcluepages() {
      let data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        clueId: this.threadID,
      };
      const res = await pageByClueIdAPI(data);
      this.cluepages = res.result.records;
      if(this.pageNum==1 &&  this.cluepages.length>0){
        this.cluepages[0].dealState=this.essential.entrustDealState
      }
      this.total = res.result.totalNum;
    },
    // 返回原页面
    gobacka() {
      this.$emit("goback");
    },
    // 关注企业
    async attention() {
      try {
        this.AttentionLoading = true;
        const res = await collectAPI({
         status:!this.companyDetial?.isCollect ,
        
         enterpriseUniCode: this.companyDetial?.unifiedSocialCreditCode,
        });
        if(res.code === 'SUCCESS'){
          // this.getMerchantsStatusAPI(); //招商相关状态
          this.$message.success("关注企业成功！");
        }
       
      } catch (error) {
        console.log(error);
      } finally {
        this.AttentionLoading = false;
      }
    },
    // 取消关注
    async cancel() {
      try {
        this.AttentionLoading = true;
        const res = await cancelCollectAPI({
          enterpriseId: this.enterpriseID,
        });
        if(res.code === 'SUCCESS'){
          this.getMerchantsStatusAPI(); //招商相关状态
          this.$message.success("取消关注成功！");
        }
        
      } catch (error) {
        console.log(error);
      } finally {
        this.AttentionLoading = false;
      }
    },
 
  },
};
</script>
    
    <style lang="scss" scoped>
::v-deep {
  .el-pagination {
    display: flex;
    justify-content: center;
    -webkit-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.1) !important;
  }
}
.iconarrow {
  color: #666;
    font-size: 20px;
    background: white ;
    padding: 2px;
    width: 25px;
    height: 25px;
    border-radius: 16px;
}
.box {
  .manag {
    padding: 24px;
  }
}
.title {
  height: 50px;
  background-color: #fff;
}
.headA {
  width: 100%;
  margin-top: 16px;
  height: 130px;
  display: flex;
  background: #ffffff;
  border-radius: 10px;
  opacity: 1;
  .operation {
    position: absolute;
    display: flex;
    margin-left: 75%;
    margin-top: 22px;
    .attention {
      width: 70px;
      height: 32px;
      background: #ffffff;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid #3370FF;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      img {
        width: 18px;
        height: 18px;
        margin-right: 8px;
        margin-bottom: 2px;
      }
      span {
        color: #3370FF;
        font-weight: 400;
        font-size: 14px;
      }
    }
    .bringinto {
      width: 126px;
      height: 32px;
      background: #3370FF;
      border-radius: 2px 2px 2px 2px;
      margin-left: 8px;
      opacity: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      img {
        width: 18px;
        height: 18px;
        margin-right: 8px;
        margin-bottom: 2px;
      }
      span {
        font-weight: 400;
        color: #ffffff;
        font-size: 14px;
      }
    }
  }
  .iconImg {
    width: 32px;
    height: 32px;
    margin-top: 28px;
    margin-left: 26px;
    margin-right: 22px;
  }
  .content {
    margin-top: 22px;
    width: 100%;
    .name {
      display: flex;
      width: 70%;
      font-size: 18px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      .tag {
        display: flex;
        padding-left: 10px;
        margin-top: 3px;
        .industryTag {
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #3370FF;
          color: #3370FF;
          font-size: 11px;
          padding: 0 6px;
          margin-left: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
        }
      }
    }
    .EnterpriseLabel {
      padding: 13px 0;
      width: 70%;
      .tags {
        display: flex;
        .firmTag {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11px;
          padding: 0 6px;
          color: #ff7d00;
          background: #fff2e6;
          margin-right: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
        }
      }
    }
    .content-two {
      display: flex;
      margin-top: 4px;
      .two-a {
        width: 20%;
      }
      .two-b {
        width: 20%;
      }
      .two-c {
        width: 40%;
      }
      .path {
        cursor: pointer;
        margin-left: 10px;
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        font-weight: 400;
        color: #3370FF;
      }
      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }
      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }
  }
}
.processing {
  width: 100%;
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 10px;
  opacity: 1;
  .title {
    border-bottom: 1px solid #e9e9e9;
    font-size: 16px;
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    border-radius: 10px;
    color: rgba(0, 0, 0, 0.85);
    .taskmanagement {
      cursor: pointer;
      width: 80px;
      height: 32px;
      background: #3370FF;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
    }
  }
}
.green {
  display: flex;
  background: #10aa38;
  margin-left: 25px;
  content: "";
  position: relative;
  width: 6px;
  right: 5px;
  top: 5px;
  height: 6px;
  border-radius: 50%;
}
.blue {
  display: flex;
  background: #3370FF;
  margin-left: 25px;
  content: "";
  position: relative;
  width: 6px;
  right: 5px;
  top: 5px;
  height: 6px;
  border-radius: 50%;
}
.red {
  display: flex;
  background: #ff2034;
  content: "";
  margin-left: 25px;
  position: relative;
  width: 6px;
  right: 5px;
  top: 5px;
  height: 6px;
  border-radius: 50%;
}

.yuan {
  display: flex;
  background: #c9cdd4;
  content: "";
  position: relative;
  width: 6px;
  right: 3px;
  top: 6px;
  height: 6px;
  border-radius: 50%;
}

.interview {
  .total {
    font-size: 14px;
    margin-left: 16px;
    margin-top: 16px;
    font-family: Source Han Sans CN-Normal, Source Han Sans CN;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);

    .tot {
      color: #3370FF;
    }
  }
}
.record {
  margin-left: 16px;
  margin-top: 16px;

  .head {
    display: flex;

    .time {
      margin-left: 25px;
      font-size: 13px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #4e5969;
    }

    .letter {
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #1d2129;
    }
  }

  .box {
    width: 95%;
    margin-left: 25px;
    margin-top: 6px;
    margin-bottom: 25px;
    background: #f7f8fa;
    padding: 20px 18px;

    .one {
      display: flex;
      margin-bottom: 16px;
    }
    .ones {
      display: flex;
    }
    .describe {
      font-size: 14px;
      width: 56px;
      margin-right: 10px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #1d2129;
    }

    .jg {
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #4e5969;
    }
  }
}
.head::after {
  background-color: #3e80ff;
  content: "";
  position: absolute;
  left: 45px;
  border: 4px solid #bedaff;
  width: 16px;
  height: 16px;
  z-index: 6;
  border-radius: 50%;
}

.single:not(:last-child)::before {
  background-color: #f0f0f0;
  content: "";
  position: absolute;
  left: 52px;
  z-index: 5;
  width: 2px;
  height: 136px;
}
</style>