<template>
  <!-- 我的企业管理 -->
  <div>
    <div v-if="isParticulars == 1">
      <div class="title">
        <div class="iconarrow">
          <i
            style="cursor: pointer"
            class="el-icon-arrow-left"
            @click="gobacka"
          />
        </div>
        <div class="scan">
          <div class="scan-top">
            <div class="scan-tab-con">
              <div class="scan-tab">
                <div
                  v-for="(it, index) in tabList"
                  :key="index"
                  class="scan-tab-list"
                  :class="whichs == it.id ? 'on' : ''"
                  @click="cut(it.id)"
                >
                  {{ it.name }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="list">
        <intention
          v-if="whichs == 0"
          @godel="godel"
          @godelenterprise="godelenterprise"
        />
        <task
          v-if="whichs == 1"
          @godel="godel"
          @godelenterprise="godelenterprise"
        />
        <matterWith
          v-if="whichs == 2"
          @godel="godel"
          @godelenterprise="godelenterprise"
        />
        <entrust
          v-if="whichs == 3"
          @godel="godel"
          @godelenterprise="godelenterprise"
        />
        <designate
          v-if="whichs == 4"
          @godel="godel"
          @godelenterprise="godelenterprise"
        />
        <attention
          v-if="whichs == 5"
          @godel="godel"
          @godelenterprise="godelenterprise"
        />
      </div>
    </div>
    <Detailsenterprise
      v-if="isParticulars == 2"
      :enterprise-i-d="enterpriseID"
      :company-detial="companyDetial"
      @goback="goback"
    />
    <taskSp
      v-if="isParticulars == 3"
      :thread-i-d="rowId"
      :enterprise-i-d="enterpriseID"
      :company-detial="companyDetial"
      @goback="goback"
      @godel="godel"
    />
    <entrus
      v-if="isParticulars == 4"
      :thread-i-d="rowId"
      :enterprise-i-d="enterpriseID"
      :company-detial="companyDetial"
      @goback="goback"
      @godel="godel"
    />
    <designateSp
      v-if="isParticulars == 5"
      :enterprise-i-d="enterpriseID"
      :thread-i-d="rowId"
      :company-detial="companyDetial"
      @goback="goback"
      @godel="godel"
    />
  </div>
</template>

<script>
import intention from "./intention.vue"; //意向企业
import task from "./task.vue"; //任务企业
import matterWith from "./matterWith.vue"; //接洽企业
import entrust from "./entrust.vue"; //委托企业
import designate from "./designate.vue"; //指派企业
import attention from "./attention.vue"; //指派企业
import Detailsenterprise from "../../IntelligentSearch/components/Detailsenterprise.vue";//企业详情
import taskSp from './particulars/taskDetailpage.vue'// 任务详情
import entrus from './particulars/entrusDetailpage.vue'// 委托详情
import designateSp from './particulars/designateDetailpage.vue'// 指派详情
export default {
  name: "IntentionQ",
  components: {
    intention,
    task,
    matterWith,
    entrust,
    designate,
    attention,
    Detailsenterprise,
    taskSp,
    entrus,
    designateSp
  },
  props: {
    which: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      whichs: 0,
      tabList: [
      ],
      isParticulars: 1,
      enterpriseID: "", //企业id
      rowId: '',//线索id
      companyDetial:{}
    };
  },
  created() {
    this.whichs = this.which;
    if(this.$store.getters.Assignauthority){
      this.tabList= [
      {
          name: "我的意向企业",
          id: 0,
        },
        {
          name: "我的任务企业",
          id: 1,
        },
        {
          name: "我的接洽企业",
          id: 2,
        },
        // {
        //   name: "我的委托企业",
        //   id: 3,
        // },
        {
          name: "我的指派企业",
          id: 4,
        },
        {
          name: "我的关注企业",
          id: 5,
        },
      ]
    }else{
      this.tabList= [
      {
          name: "我的意向企业",
          id: 0,
        },
        {
          name: "我的任务企业",
          id: 1,
        },
        {
          name: "我的接洽企业",
          id: 2,
        },
        // {
        //   name: "我的委托企业",
        //   id: 3,
        // },
        {
          name: "我的关注企业",
          id: 5,
        },
      ]
    }
  },
  methods: {
    goback() {
      this.isParticulars = 1;
    },
    // 进入详情
    godel(item,id, enterpriseId, go) {
      this.companyDetial=item
      this.rowId = id;
      this.enterpriseID = enterpriseId;
      this.isParticulars = go;
      document.documentElement.scrollTop = 0;
    },
    godelenterprise(id,go){
      this.enterpriseID = id;
      this.isParticulars = go;
      document.documentElement.scrollTop = 0;
    },
    // 返回原页面
    gobacka() {
      this.$emit("gobacka");
    },
    cut(id) {
      this.whichs = id;
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  margin: 16px 0;
  border-radius: 10px;
  background-color: #fff;

box-shadow: 0px 4px 12px 0px #EEF1F8;
.list{
  padding:0 16px;
}
}

.iconarrow {
  color: #666;
    font-size: 20px;
    background: white ;
    padding: 2px;
    width: 25px;
    height: 25px;
    border-radius: 16px;
}

.scan {
  width: 100%;
  // margin-top: 24px;
  // background-color: #fff;
  &-top {
    padding: 16px 0px;
    // background: #fff;
    padding-bottom: 12px;
    //border-bottom: 1px solid #E8E8E8
  }
  &-tab {
    display: flex;
    padding-left: 16px;
    &-con {
      display: flex;
      justify-content: space-between;
    }
    &-select {
      display: flex;
    }
    &-list {
      margin-right: 64px;
      font-size: 16px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      cursor: pointer;
      &.on {
        font-weight: bold;
        color: #3370FF;
        position: relative;
        &::after {
          content: "";
          width: 28px;
          height: 2px;
          background: #3370FF;
          position: absolute;
          left: 32px;
          bottom: -8px;
        }
      }
    }
  }
  &-con {
    margin: 16px 24px;
  }
}
</style>