<template>
  <!-- 任务企业 -->
  <div class="list">
    <div class="title">
      共有 {{ total }} 家企业
    </div>
    <div class="table">
      <el-table
        v-loading="tabLoading"
        :data="tabList"
        style="width: 100%"
      >
        <el-table-column
          prop="enterpriseName"
          label="企业名称"
          width="250"
        >
          <template slot-scope="{ row }">
            <div
              class="enterpriseNames"
              @click="goparticulars(row)"
            >
              {{ row.enterpriseName }}
            </div>
            <div
              v-if="row.enterpriseLabelNames"
              class="tabs"
            >
              <div
                v-for="(item, index) in row.enterpriseLabelNames"
                v-show="index <= 1"
                :key="index"
                class="firmTag"
              >
                {{ item }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="所属产业链"
          width="200"
        >
          <template slot-scope="{ row }">
            <el-tooltip
              class="item"
              effect="dark"
              :visible-arrow="false"
              placement="top"
            >
              <div slot="content">
                <div
                  v-if="row.chainNameList"
                >
                  <span
                    v-for="(item, index) in row.chainNameList"
                    :key="index"
                  >
                    {{ item.replace("产业金脑·", "")
                    }}<span v-if="row.chainNameList.length != index + 1">;</span>
                  </span>
                </div>
              </div>
              <div
                v-if="row.chainNameList"
                class="chainNameList"
              >
                <span
                  v-for="(item, index) in row.chainNameList"
                  :key="index"
                >
                  {{ item.replace("产业金脑·", "")
                  }}<span v-if="row.chainNameList.length != index + 1">;</span>
                </span>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          label="所在地区"
          width="180"
        >
          <template slot-scope="{row}">
            {{ row.province }}<span v-if="row.province!==row.city">{{ row.city }}</span>{{ row.area }}
          </template>
        </el-table-column>
        <el-table-column
          prop="assignDateStr"
          label="指派日期"
          width="120"
        />
        <el-table-column
          prop="assignPerson"
          label="指派人"
        />
        <el-table-column
          prop="clueDealState"
          label="当前进度"
        >
          <template slot-scope="{ row }">
            <div
              style="
                display: flex;
                align-items: center;
              "
            >
              <div
                :class="
                  row.clueDealState == 0
                    ? 'red'
                    : row.clueDealState == 1
                      ? 'blue'
                      : 'green'
                "
              />
              {{
                row.clueDealState == 0
                  ? "待处理"
                  : row.clueDealState == 1
                    ? "处理中"
                    : "已完成"
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="220"
        >
          <template slot-scope="{ row }">
            <div style="display: flex">
              <div
                class="Youcanclick"
                @click="detail(row)"
              >
                详情
              </div>
              <el-divider
                v-if="row.clueDealState!=2"
                direction="vertical"
              />
              <div
                v-if="row.clueDealState!=2 && $store.getters.Assignauthority"
                class="rule"
              >
                <div
                  v-if="
                    $store.getters.Assignauthority &&
                      row.beAssignPersonId==$store.getters.user.userId && row.clueDealState!=='2'
                  "
                  class="Youcanclick"
                  @click="designate(row)"
                >
                  线索指派
                </div>
                <div
                  v-else-if="
                    $store.getters.Assignauthority && row.clueDealState=='2'
                  "
                  class="Noclick"
                >
                  线索指派
                </div>
                <div
                  v-else-if="
                    $store.getters.Assignauthority && row.beAssignPersonId!==$store.getters.user.userId
                  "
                  class="Noclick"
                >
                  已指派
                </div>
              </div>
              <el-divider
                v-if="row.clueDealState!=2"
                direction="vertical"
              />
              <div
                v-if="row.clueDealState!=2"
                class="rule"
              >
                <div
                  v-if="
                    row.clueDealState !== '2' &&
                      row.beAssignPersonId == $store.getters.user.userId
                  "
                  class="Youcanclick"
                  @click="disposeFn(row)"
                >
                  任务处理
                </div>
                <div
                  v-else
                  class="Noclick"
                >
                  任务处理
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="ye">
      <el-pagination
        :current-page.sync="pageNum"
        :page-sizes="[5, 10, 20, 50]"
        :page-size.sync="pageSize"
        background
        :total="+total"
        layout="prev, pager, next,sizes,  jumper"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
    <taskdispose
      v-if="task"
      :recordball="task"
      :clue-id="clueId"
      :enterprise-name="enterpriseName"
      :state="state"
      @closetask="closetask"
      @getList="getList"
    />
    <designate
      v-if="assignball"
      :assignball="assignball"
      :clue-id="clueId"
      :enterprise-name="enterpriseName"
      @closeassignball="closeassignball"
      @getList="getList"
    />
  </div>
</template>
  
  <script>
import { myTaskPageAPI } from "../../apiUrl";
import  taskdispose from './operation/taskdispose.vue'// 任务处理
import designate from "././operation/designate.vue";
export default {
  name: "IntentionQ",
  components:{
    taskdispose,
    designate
  },
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      total: 0,
      clueId:"",
      tabList: [],
      tabLoading: false,
      enterpriseName:'',
      task:false,
      state:'',
      assignball:false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 去任务详情
    detail(row){
      this.$emit("godel", row.id,row.enterpriseId, 3);
    },
    // 去企业详情
    goparticulars(row) {
      this.$emit("godelenterprise", row,row.enterpriseId, 2);
    },
    // 关闭任务处理详情
    closetask(){
      this.task = false;
    },
    //任务处理
    disposeFn(row) {
      this.state=row.clueDealState
      this.clueId = row.id;
      this.enterpriseName = row.enterpriseName;
      this.task = true;
    },
      // 线索指派
    designate(row) {
      this.clueId = row.id;
      this.enterpriseName = row.enterpriseName;
      this.assignball = true;
    },
    // 关闭线索指派弹层
    closeassignball() {
      this.assignball = false;
    },
    //任务企业列表
    async getList() {
      try {
        this.tabLoading = true;
        let data = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        };
        const res = await myTaskPageAPI(data);
        this.total = res.result.totalNum;
        this.tabList = res.result.records;
      } catch (error) {
        console.log(error);
      } finally {
        this.tabLoading = false;
      }
    },
  },
};
</script>
  
  <style scoped lang="scss">
 .enterpriseNames{
  cursor: pointer;
  width: 250px;
  white-space: nowrap;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}
  
.rule{
  width:60px;
  display: flex;
  justify-content: center;
}
.Youcanclick {
  color: #3370FF;
  cursor: pointer;
}
.Noclick {
  color: #000000;
  opacity: 0.6;
  cursor: not-allowed;
}
.Youcan{
  color: #f5f5f5;
}
.chainNameList {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
::v-deep {
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #3370FF;
    color: #fff;
  }
  .el-pagination {
    -webkit-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.1) !important;
  }
  .el-button {
    color: #3370FF;
  }
  .el-divider--vertical{
    height: 1.5em !important;
  }
}
.ye {
  margin-top: 20px;
}

.list {
  .title {
    height: 50px;
    border-bottom: 1px solid #e9e9e9;
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
  }
  .red {
    background: #f53f3f;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .blue {
    background: #417fff;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .green {
    background: #10aa38;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .gray {
    background: #c9cdd4;
    content: "";
    position: absolute;
    width: 8px;
    height: 8px;
    top: 29px;
    left: 26px;
    border-radius: 50%;
  }

  .tabs {
    display: flex;
    //align-items: center;
    //justify-content: center;
    white-space: nowrap;

    .firmTag {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      margin-top: 6px;
      padding: 0 6px;
      color: #ff7d00;
      background: #fff2e6;
      margin-right: 8px;
      height: 18px;
      border-radius: 2px 2px 2px 2px;
    }
  }

  .table {
    margin-top: 24px;
  }
}
</style>