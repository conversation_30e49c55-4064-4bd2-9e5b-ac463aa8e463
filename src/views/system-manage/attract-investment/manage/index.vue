<template>
  <!-- 招商智管 -->
  <div v-if="examine">
    <div v-if="showcontent">
      <div class="scan">
        <div v-show="which == -1">
          <!--       <div class="scan-top">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item> 数智招商 </el-breadcrumb-item>
          <el-breadcrumb-item>招商智管</el-breadcrumb-item>
        </el-breadcrumb>
      </div> -->
          <!--           <div class="head">
          <div class="title">
              <div class="span">
                我的企业
              </div>
            </div> 
          </div> -->
          <div class="typeselect">
            <div
              v-for="(item, index) in classifyList"
              :key="index"
              :style="{ width: $store.getters.Assignauthority ? '16%' : '19%' }"
              class="single"
              @click="firmType(item.id)"
            >
              <div class="wordage">
                <span class="text">{{ item.name }}</span>
                <span class="sum">{{ item.sum }}</span>
              </div>
              <img :src="item.icon">
            </div>
          </div>
          <div class="one">
            <div class="left">
              <div class="title">
                <div class="span">
                  我的招商漏斗
                  <el-tooltip
                    effect="dark"
                    placement="right"
                  >
                    <div slot="content">
                      纳入意向企业：推荐企业中我纳入意向的企业
                      <br>
                      已接洽企业：推荐企业中我纳入意向的并接洽的企业
                      <br>
                      有投资意向企业：推荐企业中我纳入意向的并接洽的有投资意向的企业
                    </div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </div>
                <div
                  id="funnels"
                  class="funnels"
                />
              </div>
            </div>
            <div class="right">
              <div class="title">
                <div class="span">
                  我的任务进度
                </div>
              </div>
              <div class="schedule">
                <div class="annular">
                  <el-progress
                    type="circle"
                    :stroke-width="15"
                    :width="165"
                    color="#165DFF"
                    :percentage="+dataInfo.intention"
                  />
                  <span>我的意向企业
                    <el-tooltip
                      effect="dark"
                      content="我的意向企业中已走访接洽企业占比"
                      placement="top"
                    >
                      <i class="el-icon-info" />
                    </el-tooltip>
                  </span>
                </div>
                <div class="annular">
                  <el-progress
                    type="circle"
                    :stroke-width="15"
                    :width="165"
                    color="#14C9C9"
                    :percentage="+dataInfo.task"
                  />
                  <span>
                    我的任务企业
                    <el-tooltip
                      effect="dark"
                      content="我的任务企业中已完成占比"
                      placement="top"
                    >
                      <i class="el-icon-info" />
                    </el-tooltip>
                  </span>
                </div>
                <!-- <div class="annular">
                  <el-progress type="circle" :stroke-width="15" :width="165" color="#F7BA1E"
                    :percentage="+dataInfo.entrust" />
                  <span>我的委托企业
                    <el-tooltip effect="dark" content="我的委托企业中已完成占比" placement="top">
                      <i class="el-icon-info" />
                    </el-tooltip>
                  </span>
                </div> -->
              </div>
            </div>
          </div>
          <div class="two">
            <div class="left">
              <div class="title">
                <div class="span">
                  招商经理排行榜
                </div>
                <div class="invent">
                  <div
                    v-for="(item, index) in patenttable"
                    :key="index"
                    :class="profit == item.id ? 'xzoption' : 'option'"
                    @click="profitFn(item.id)"
                  >
                    {{ item.name }}
                  </div>
                </div>
              </div>
              <div class="firmType">
                <el-radio
                  v-model="radio"
                  label=""
                >
                  接洽企业
                </el-radio>
                <el-radio
                  v-model="radio"
                  label="1"
                >
                  有投资意向企业
                </el-radio>
              </div>
              <div
                id="rankinglist"
                class="rankinglist"
              />
            </div>
            <div class="right">
              <div class="title">
                <div class="span">
                  关注企业动态
                </div>
              </div>
              <!-- <div class="Dynamicquantity">
                <div class="dynamicS">
                  <div class="text">
                    ·动态数量（月）
                  </div>
                  <div
                    id="dynamic"
                    class="dynamic"
                  />
                </div>
                <div class="feelingsS">
                  <div class="text">
                    ·舆情数量（月）
                  </div>
                  <div
                    id="feelings"
                    class="feelings"
                  />
                </div>
              </div> -->
              <!-- <div
                class="invent"
                style="padding-left: 24px"
              >
                <div
                  v-for="(item, index) in incidenttable"
                  :key="index"
                  :class="incid == item.id ? 'xzoption' : 'option'"
                  @click="incidFn(item.id)"
                >
                  {{ item.name }}
                </div>
              </div> -->
              <div class="List">
                <div class="event">
                  <div class="record">
                    <div
                      v-for="(item, index) in incidentList"
                      :key="index"
                      class="recordItem"
                    >
                      <div
                        class="timeView"
                        @click="toDetail(item, item.enterpriseId)"
                      >
                        <span class="name">{{ item.enterpriseName }}</span>
                        <span class="time">{{
                          formatTimestamp(Number(item.publishDate))
                        }}</span>
                      </div>
                      <div class="boxView">
                        <div class="titleView">
                          <span class="titleText"> {{ item.eventTitle }}</span>
                        </div>
                        <!-- 展开 -->
                        <div class="contentView">
                          <!-- <span :id="`id_${id}`" class="content"> {{ item.eventContent }}                       
                          </span> -->
                          <LongText
                            :id="item.id"
                            :content="item.eventContent"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  v-show="incidentpage.total"
                  class="ye"
                >
                  <el-pagination
                    :current-page.sync="incidentpage.pageNum"
                    :page-size.sync="incidentpage.pageSize"
                    background
                    :total="+incidentpage.total"
                    layout="prev, pager, next"
                    @size-change="incidentListFn"
                    @current-change="incidentListFn"
                  />
                </div>
              </div>
              <!-- <div
                  v-if="incid == 1"
                  class="event"
                >
                  <a
                    v-for="(item, index) in publicList"
                    :key="index"
                    :href="item.url"
                    target="_blank"
                    class="popularfeelings"
                  >
                    <div class="above">
                      <div
                        v-for="(it, ind) in item.themes"
                        v-show="ind < 2"
                        :key="ind"
                        class="tag"
                      >
                        {{ it }}
                      </div>
                      <div
                        :style="{'max-width': xp ? '300px' : ''}"
                        class="xq"
                      >{{ item.title }}</div>
                    </div>
                    <div class="time">
                      {{ item.publishDate }}
                    </div>
                  </a>
                  <div class="ye">
                    <el-pagination
                      :current-page.sync="publicpage.pageNum"
                      :page-size.sync="publicpage.pageSize"
                      background
                      :total="+publicpage.total"
                      layout="prev, pager, next"
                      @size-change="publicListtFn"
                      @current-change="publicListtFn"
                    />
                  </div>
                </div> -->
            </div>
          </div>
        </div>
      </div>
      <index
        v-if="which !== -1 && which !== 99"
        :which="which"
        @gobacka="gobacka"
      />
      <Detailsenterprise
        v-if="enterpriseID"
        :enterprise-i-d="enterpriseID"
        @goback="gobacka"
      />
    </div>
  </div>
  <div
    v-else
    class="nodata"
  >
    <div class="con">
      <img src="https://static.idicc.cn/cdn/SSO/zhaoshangnodata.png">
      <span class="p1">暂无产业链权限</span>
      <span class="p2">请联系机构管理员配置权限功能</span>
    </div>
  </div>
</template>

<script>
import LongText from "./components/longText.vue";
import Detailsenterprise from "../IntelligentSearch/components/Detailsenterprise.vue";
import {
  myFunnelAPI,
  myTaskProgressStatusAPI,
  investmentManagerRankListAPI,
  enterpriseDynamicAPI,
  informationAPI,
  homeListAPI,
  getMyCountAPI,
  collectEnterpriseEventListAPI,
} from "../apiUrl";
import * as echarts from "echarts";
import index from "./components/index.vue";
export default {
  name: "ManaGe",
  components: {
    index,
    Detailsenterprise,
    LongText,
  },
  data() {
    return {
      enterpriseID: "",
      openLine: false,
      radio: "",
      graphic: [
        {
          type: "rect",
          left: "center",
          top: "middle",
          shape: {
            width: 160,
            height: 70,
            radius: 20,
          },
          style: {
            fill: "#cdd1d7",
            stroke: "#ccc",
          },
          z: 10,
        },
        {
          type: "text",
          left: "center",
          top: "middle",
          style: {
            text: "暂无数据",
            textAlign: "center",
            textVerticalAlign: "middle",
            fill: "#fff",
            fontSize: 18,
          },
          z: 10,
        },
      ],
      incid: 0,
      incidenttable: [
        {
          name: "动态事件",
          id: 0,
        },
        {
          name: "舆情资讯",
          id: 1,
        },
      ],
      // 招商经理排行榜
      patenttable: [
        {
          name: "本月累计",
          id: 0,
        },
        {
          name: "本年累计",
          id: 1,
        },
      ],
      profit: 0,
      //企业类型枚举
      classifyList: [],
      which: -1,
      // 我的任务进度
      dataInfo: {
        intention: 0,
        task: 0,
        entrust: 0,
      },
      //事件列表
      incidentList: [],
      //事件分页
      incidentpage: {
        total: 0,
        pageNum: 1,
        pageSize: 5,
      },
      //舆情列表
      publicList: [],
      //舆情分页
      publicpage: {
        total: 0,
        pageNum: 1,
        pageSize: 5,
      },
      examine: false,
      showcontent: false,
      numberList: {},
      xp: false,
    };
  },
  watch: {
    radio() {
      this.sponsorshipmanager();
    },
  },
  created() {
    //this.getList();
  },
  mounted() {
    this.getList();
    this.getscreenWidth();
    /*  this.sponsorshipmanager(); //招商经理排行榜
    this.enterpriseDynami();
    this.funnelFn(); //招商漏斗 */
  },
  methods: {
    toDetail(item, id) {
      this.which = 99;
      this.enterpriseID = id;
      this.$emit("godelenterprise", item, id, 2);
    },
    formatTimestamp(timestamp) {
      const date = new Date(timestamp); // 直接使用毫秒级时间戳
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从 0 开始，所以 +1
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");

      return `${year}-${month}-${day}`;
    },
    getscreenWidth() {
      var screenWidth = window.innerWidth;
      if (screenWidth <= 1440) {
        this.xp = true;
      } else {
        this.xp = false;
      }
    },
    async getList() {
      try {
        const res = await homeListAPI();
        if (res.result == null || res.result.length < 1) {
          this.showcontent = false;
        } else {
          this.showcontent = true;
          this.getSum();
          this.taskProgress(); //任务进度
          this.incidentListFn(); //动态事件
          this.sponsorshipmanager(); //招商经理排行榜
          this.enterpriseDynami(); //关注企业动态
          this.funnelFn(); //招商漏斗
        }
      } finally {
        this.examine = true;
      }
    },
    async enterpriseDynami() {
      const res = await enterpriseDynamicAPI();
      this.dynamicList = res.result;
      this.dynamicstate(); //关注企业动态
      this.feelingsSum();
    },
    async getSum() {
      const res = await getMyCountAPI();
      let sumList = res.result;
      if (this.$store.getters.Assignauthority) {
        this.classifyList = [
          {
            icon: require("@/views/system-manage/attract-investment/img/icon1.png"),
            name: "我的意向企业",
            id: 0,
            sum: sumList.intentionCount || 0,
          },
          {
            icon: require("@/views/system-manage/attract-investment/img/icon2.png"),
            name: "我的任务企业",
            id: 1,
            sum: sumList.taskCount || 0,
          },
          {
            icon: require("@/views/system-manage/attract-investment/img/icon3.png"),
            name: "我的接洽企业",
            id: 2,
            sum: sumList.followCount || 0,
          },
          // {
          //   icon: require("@/views/system-manage/attract-investment/img/icon4.png"),
          //   name: "我的委托企业",
          //   id: 3,
          //   sum: sumList.entrustCount || 0,
          // },
          {
            icon: require("@/views/system-manage/attract-investment/img/icon5.png"),
            name: "我的指派企业",
            id: 4,
            sum: sumList.assignCount || 0,
          },
          {
            icon: require("@/views/system-manage/attract-investment/img/icon6.png"),
            name: "我的关注企业",
            id: 5,
            sum: sumList.attentionCount || 0,
          },
        ];
      } else {
        this.classifyList = [
          {
            icon: require("@/views/system-manage/attract-investment/img/icon1.png"),
            name: "我的意向企业",
            id: 0,
            sum: sumList.intentionCount || 0,
          },
          {
            icon: require("@/views/system-manage/attract-investment/img/icon2.png"),
            name: "我的任务企业",
            id: 1,
            sum: sumList.taskCount || 0,
          },
          {
            icon: require("@/views/system-manage/attract-investment/img/icon3.png"),
            name: "我的接洽企业",
            id: 2,
            sum: sumList.followCount || 0,
          },
          // {
          //   icon: require("@/views/system-manage/attract-investment/img/icon4.png"),
          //   name: "我的委托企业",
          //   id: 3,
          //   sum: sumList.entrustCount || 0,
          // },
          {
            icon: require("@/views/system-manage/attract-investment/img/icon6.png"),
            name: "我的关注企业",
            id: 5,
            sum: sumList.attentionCount || 0,
          },
        ];
      }
    },
    //关注的动态事件
    async incidentListFn() {
      const res = await collectEnterpriseEventListAPI({
        pageNum: this.incidentpage.pageNum,
        pageSize: this.incidentpage.pageSize,
      });
      if (res.result.records !== null) {
        this.incidentList = res.result.records.map((item) => {
          item.textOver = false;
          item.openLine = false;
          return item;
        });
        this.incidentpage.total = res.result.total;
      }
    },
    //舆情资讯
    async publicListtFn() {
      const res = await informationAPI({
        pageNum: this.publicpage.pageNum,
        pageSize: this.publicpage.pageSize,
      });
      this.publicList = res.result.records;
      this.publicpage.total = res.result.totalNum;
    },
    //舆情数量
    feelingsSum() {
      let data = this.dynamicList.publicOpinion.map((item) => {
        return { name: item.typeName, value: item.typeCount };
      });
      const sum = this.dynamicList.publicOpinion.reduce(
        (sum, opinion) => sum + Number(opinion.typeCount),
        0
      );
      var chartDom = document.getElementById("feelings");
      var myChart = echarts.init(chartDom);
      var option;
      // 总数
      // 各种类型
      option = {
        tooltip: {
          trigger: "item",
          backgroundColor: "#00112dbf", // 提示框浮层的背景颜色。
          borderColor: "#0066FF", // 提示框浮层的边框颜色。
          borderWidth: 1, // 提示框浮层的边框宽。
          textStyle: {
            // 提示框浮层的文本样式。
            color: "#fff",
            fontStyle: "normal",
            fontWeight: "normal",
            fontFamily: "sans-serif",
            fontSize: 12,
          },
          //实例
          formatter: function (params) {
            let res =
              params.data.name +
              "： " +
              '<span style="color: #00FFF0;font-size: 12px;">' +
              params.data.value +
              "</span> 个";
            return res;
          },
        },
        // 圆心文字
        title: [
          {
            text: "按舆情主题",
            top: "46%",
            textAlign: "center",
            left: "34%",
            textStyle: {
              color: "rgba(0,0,0,0.45)",
              fontSize: 14,
              fontWeight: "400",
              fontFamily: "Source Han Sans CN-Regular, Source Han Sans CN",
            },
          },
          {
            text: sum,
            top: "53%",
            textAlign: "center",
            left: "34%",
            textStyle: {
              color: "rgba(0,0,0,0.85)",
              fontSize: 26,
              fontWeight: "400",
              fontFamily: "Source Han Sans CN-Regular, Source Han Sans CN",
            },
          },
        ],
        legend: {
          type: "scroll",
          pageIconInactiveColor: "rgba(44,132,251,0.40)",
          pageIconColor: "#2C86FF",
          pageIconSize: 10, //翻页按钮大小
          textStyle: {
            color: "rgba(0,0,0,0.65)",
            fontSize: 12,
            opacity: 0.8,
            lineHeight: 33, // 设置文字之间的上下间距
          },
          // right: "right",//调整图例位置
          left: "70%",
          orient: "vertical",
          bottom: "middle",
          //itemGap: 16, // 设置图例项之间的间距
          itemWidth: 15,
          top: "12%", //调整图例位置
          itemHeight: 7, //修改icon图形大小
          icon: "circle", //图例前面的图标形状
        },
        series: [
          {
            type: "pie",
            radius: ["50%", "70%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 0,
              borderColor: "#fff",
              borderWidth: 0,
            },
            left: "-30%",
            top: "10%",
            label: {
              show: false,
              position: "center",
            },
            labelLine: {
              show: false,
            },
            data: data,
          },
        ],
      };
      if (data.length == 0) {
        let graphic = [];
        (graphic = this.graphic),
          (option = {
            graphic,
          });
      }

      option && myChart.setOption(option);
    },
    dynamicstate() {
      let data = this.dynamicList.dynamicStatistics.map((item) => {
        return { name: item.typeName, value: item.typeCount };
      });
      const sum = this.dynamicList.dynamicStatistics.reduce(
        (sum, opinion) => sum + Number(opinion.typeCount),
        0
      );
      var chartDom = document.getElementById("dynamic");
      var myChart = echarts.init(chartDom);
      var option;
      // 各种类型
      option = {
        tooltip: {
          trigger: "item",
          backgroundColor: "#00112dbf", // 提示框浮层的背景颜色。
          borderColor: "#0066FF", // 提示框浮层的边框颜色。
          borderWidth: 1, // 提示框浮层的边框宽。
          textStyle: {
            // 提示框浮层的文本样式。
            color: "#fff",
            fontStyle: "normal",
            fontWeight: "normal",
            fontFamily: "sans-serif",
            fontSize: 12,
          },
          //实例
          formatter: function (params) {
            let res =
              params.data.name +
              "： " +
              '<span style="color: #00FFF0;font-size: 12px;">' +
              params.data.value +
              "</span> 个";
            return res;
          },
        },
        // 圆心文字
        title: [
          {
            text: "按动态类型",
            top: "46%",
            textAlign: "center",
            left: "34%",
            textStyle: {
              color: "rgba(0,0,0,0.45)",
              fontSize: 14,
              fontWeight: "400",
              fontFamily: "Source Han Sans CN-Regular, Source Han Sans CN",
            },
          },
          {
            text: sum,
            top: "53%",
            textAlign: "center",
            left: "34%",
            textStyle: {
              color: "rgba(0,0,0,0.85)",
              fontSize: 30,
              fontWeight: "400",
              fontFamily: "Source Han Sans CN-Regular, Source Han Sans CN",
            },
          },
        ],
        legend: {
          type: "scroll",
          pageIconInactiveColor: "rgba(44,132,251,0.40)",
          pageIconColor: "#2C86FF",
          pageIconSize: 10, //翻页按钮大小
          textStyle: {
            color: "rgba(0,0,0,0.65)",
            fontSize: 12,
            opacity: 0.8,
            lineHeight: 33, // 设置文字之间的上下间距
          },
          // right: "right",//调整图例位置
          left: "70%",
          orient: "vertical",
          bottom: "middle",
          //itemGap: 16, // 设置图例项之间的间距
          itemWidth: 15,
          top: "12%", //调整图例位置
          itemHeight: 7, //修改icon图形大小
          icon: "circle", //图例前面的图标形状
        },
        series: [
          {
            type: "pie",
            radius: ["50%", "70%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 0,
              borderColor: "#fff",
              borderWidth: 0,
            },
            left: "-30%",
            top: "10%",
            label: {
              show: false,
              position: "center",
            },
            labelLine: {
              show: false,
            },
            data: data,
          },
        ],
      };
      if (data.length == 0) {
        let graphic = [];
        (graphic = this.graphic),
          (option = {
            graphic,
          });
      }

      option && myChart.setOption(option);
    },
    // 我的招商漏斗
    async funnelFn() {
      const res = await myFunnelAPI();
      /*  const sortedEntries = Object.entries(res.result).sort(
        (a, b) => a[1] - b[1]
      ); */
      /*  const sortedDict = {};
      sortedEntries.forEach((entry) => (sortedDict[entry[0]] = entry[1])); */
      //const dataY = Object.keys(sortedDict);
      let dataYX = Object.values(res.result);
      //dataYX = [9, 8,2, 1];
      // 排序
      dataYX.sort(function (a, b) {
        return a - b;
      });
      // 右侧展示的字段
      let a = dataYX.slice();
      let data2 = a.reverse();
      // 找到最大值
      let max = dataYX[dataYX.length - 1];
      // 给其余值不为0的值增加最大值的10%
      if (max !== 0) {
        dataYX = dataYX.map((item, index) => {
          let add = Math.round(max * 0.1);
          // 最后一项不加
          if (index !== dataYX.length - 1) {
            // 加上去等于上一项的不加
            if (Number(dataYX[index]) + add !== Number(dataYX[index + 1])) {
              //0和已经等于最大值的不加
              if (item !== max && item != 0) {
                item = Number(item) + add;
              }
            }
          }
          return item;
        });
      }
      let data = ["有投资意向企业", "已接洽企业", "纳入意向企业", "推荐企业"];
      // 原数组取反
      let b = dataYX.slice();
      let dataXY = b.map((num) => -1 * Math.abs(num));
      let borList = ["#aeccff", "#6aa1ff", "#4080ff", "#165dff"];
      // 添加颜色
      if (max !== 0) {
        dataYX = dataYX.map((item, index) => {
          return {
            value: item,
            itemStyle: {
              borderColor: borList[index],
              //borderColor : item ==0  ? '#fff' : borList[index]
            },
          };
        });
        dataXY = dataXY.map((item, index) => {
          return {
            value: item,
            itemStyle: {
              borderColor: borList[index],
              //borderColor : item ==0  ? '#fff' : borList[index]
            },
          };
        });
      }

      var chartDom = document.getElementById("funnels");
      var myChart = echarts.init(chartDom);
      var option;
      option = {
        tooltip: {
          show: false,
        },
        grid: {
          left: "8%",
          top: "5%", // grid布局设置适当调整避免X轴文字只能部分显示
          button: "0%",
          right: "15%",
          containLabel: true,
        },
        yAxis: [
          {
            type: "category",
            data,
            axisTick: {
              alignWithLabel: false,
              show: false,
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              padding: [0, 0, 0, -100],
              textStyle: {
                align: "left",
              },
            },
          },
          {
            gridIndex: 0,
            type: "category",
            position: "right",
            inverse: true,
            data: data,
            axisTick: { show: false },
            axisLine: { show: false },
            splitLine: { show: false },
            axisLabel: {
              align: "right",
              padding: [22, -50, 20, 0],
              formatter: (_, index) => {
                return `{value|${data2[index]}}`;
              },
              rich: {
                value: {
                  color: "#00000",
                  fontSize: 14,
                  fontWeight: 500,
                },
              },
            },
          },
        ],
        xAxis: [
          {
            type: "value",
            show: false,
          },
        ],
        series: [
          {
            type: "bar",
            barWidth: 20,
            data: dataXY,
            silent: true,
            hoverAnimation: false,
            itemStyle: {
              normal: {
                borderRadius: [4, 0, 0, 4],
                formatter: function () {},
                //borderColor: 'rgba(22, 93, 255, 1)', // 设置描边的颜色
                color: function (params) {
                  let colorList = [
                    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                      { offset: 1, color: "rgba(174, 204, 255, 1)" }, //柱图渐变色
                    ]),
                    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                      { offset: 1, color: "rgba(106, 161, 255, 1)" }, //柱图渐变色
                    ]),
                    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                      { offset: 1, color: "rgba(64, 128, 255, 1)" }, //柱图渐变色
                    ]),

                    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                      { offset: 1, color: "rgba(22, 93, 255, 1)" }, //柱图渐变色
                    ]),
                  ];
                  return colorList[params.dataIndex];
                },
              },
            },
          },
          {
            type: "bar",
            barWidth: 20,
            silent: true,
            hoverAnimation: false,
            itemStyle: {
              normal: {
                borderRadius: [0, 4, 4, 0],
                //borderColor: 'rgba(22, 93, 255, 1)', // 设置描边的颜色
                color: function (params) {
                  let colorList = [
                    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                      { offset: 1, color: "rgba(174, 204, 255, 1)" }, //柱图渐变色
                    ]),
                    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                      { offset: 1, color: "rgba(106, 161, 255, 1)" }, //柱图渐变色
                    ]),
                    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                      { offset: 1, color: "rgba(64, 128, 255, 1)" }, //柱图渐变色
                    ]),

                    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                      { offset: 1, color: "rgba(22, 93, 255, 1)" }, //柱图渐变色
                    ]),
                  ];
                  return colorList[params.dataIndex];
                },
              },
            },
            data: dataYX,
            barGap: "-100%",
          },
        ],
      };

      option && myChart.setOption(option);
    },
    // 我的任务进度
    async taskProgress() {
      const res = await myTaskProgressStatusAPI();
      this.dataInfo = {
        intention: res.result.recommendPercentage,
        task: res.result.taskPercentage,
        entrust: res.result.entrustPercentage,
      };
    },
    // 招商经理排行
    async sponsorshipmanager() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const monthStr = month < 10 ? `0${month}` : month;
      const yearMonth = `${year}-${monthStr}`;
      const res = await investmentManagerRankListAPI({
        date: this.profit == 0 ? yearMonth : year,
        haveInvestmentIntention: this.radio == 1 ? "true" : "",
      });
      let dataY = res.result.map((item) => {
        return item.rankName;
      });
      let dataSeries = res.result.map((item) => {
        return item.rankCount;
      });
      let graphic = [];
      if (dataY.length == 0) {
        graphic = this.graphic;
      }
      var chartDom = document.getElementById("rankinglist");
      var myChart = echarts.init(chartDom);
      var option;
      option = {
        graphic,
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "none", // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
            axis: "auto", // 指示器的坐标轴。
            snap: true, // 坐标轴指示器是否自动吸附到点上
          },
        },
        grid: {
          left: "4%",
          top: "2%", // grid布局设置适当调整避免X轴文字只能部分显示
          button: "0%",
          right: "8%",
          containLabel: true,
        },
        axisLabel: {
          show: true,
          fontSize: 12,
          color: "#86909C",
          formatter: function (params) {
            if (params.length > 3) {
              return params.substring(0, 3) + "...";
            } else {
              return params;
            }
          },
        },
        xAxis: {
          type: "value",
          show: true,
          // 不显示轴线
          axisLine: {
            show: false,
          },
          // 不显示刻度线
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            // 网格线为虚线
            show: false,
          },
        },
        yAxis: [
          {
            gridIndex: 0,
            type: "category",
            inverse: true,
            position: "left",
            data: dataY,
            axisTick: { show: false },
            axisLine: { show: false },
            splitLine: { show: false },
            axisLabel: {
              width: 80,
              padding: [0, 0, 0, -80],
              align: "left",
              formatter: (name, index) => {
                const id = index + 1;
                if (id < 4) {
                  return `{icon|${id}}`;
                } else {
                  return `{count|${id}}`;
                }
              },
              rich: {
                icon: {
                  padding: [0, 20, 0, 0],
                  width: 35,
                  height: 15,
                  color: "#3370FF",
                  align: "center",
                  fontSize: 20,
                  fontWeight: 800,
                },
                count: {
                  padding: [0, 20, 0, 0],
                  width: 35,
                  height: 15,
                  align: "center",
                  fontSize: 14,
                  fontWeight: 500,
                },
              },
            },
          },
          {
            type: "category",
            inverse: true,
            position: "left",
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              color: "#00000",
              fontSize: 11,
            },
            data: dataY,
          },
          {
            gridIndex: 0,
            type: "category",
            position: "right",
            inverse: true,
            data: dataY,
            axisTick: { show: false },
            axisLine: { show: false },
            splitLine: { show: false },
            axisLabel: {
              align: "right",
              padding: [25, -20, 20, 0],
              formatter: (_, index) => {
                return `{value|${dataSeries[index]}}`;
              },
              rich: {
                value: {
                  color: "#00000",
                  fontSize: 14,
                  fontWeight: 500,
                },
              },
            },
          },
        ],
        series: [
          {
            type: "bar",
            barWidth: 8,
            itemStyle: {
              normal: {
                borderRadius: [4, 4, 4, 4],
                color: function (params) {
                  let colorList = [
                    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                      { offset: 0, color: "rgba(255, 255, 255, 1)" }, //柱图渐变色
                      { offset: 1, color: "rgba(22, 93, 255, 0.8)" }, //柱图渐变色
                    ]),
                    new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                      { offset: 0, color: "rgba(254, 254, 254, 1)" }, //柱图渐变色
                      { offset: 1, color: "rgba(54, 202, 202, 1)" }, //柱图渐变色
                    ]),
                  ];
                  if (params.dataIndex < 3) {
                    return colorList[0];
                  } else {
                    return colorList[1];
                  }
                },
              },
            },
            data: dataSeries,
          },
        ],
      };
      option && myChart.setOption(option, true);
    },
    // 招商经理排行榜维度切换
    profitFn(i) {
      this.profit = i;
      this.sponsorshipmanager();
    },
    incidFn(i) {
      this.incid = i;
      if (i == 0) {
        (this.incidentpage = {
          total: 0,
          pageNum: 1,
          pageSize: 5,
        }),
          this.incidentListFn();
      } else {
        this.publicpage = {
          total: 0,
          pageNum: 1,
          pageSize: 5,
        };
        this.publicListtFn();
      }
    },
    // 企业列表跳转
    firmType(i) {
      this.which = i;
    },
    // 返回首页
    gobacka() {
      this.enterpriseID = "";
      this.which = -1;
    },
  },
};
</script>

<style lang="scss" scoped>
.nodata {
  min-width: 88.5vw;
  height: 100vh;
  display: flex;
  justify-content: center;

  .con {
    margin-top: 10%;
    width: 170px;
    height: 300px;
    display: flex;
    align-items: center;
    flex-direction: column;

    img {
      width: 160px;
      height: 165.03px;
    }

    .p1 {
      margin-top: 38px;
      font-size: 14px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }

    .p2 {
      margin-top: 16px;
      font-size: 12px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}

::v-deep {
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #3370ff;
    color: #fff;
  }

  .el-pagination {
    -webkit-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.1) !important;
    width: 100%;
    display: flex;
    justify-content: center;
  }
}

.ye {
  margin-top: 20px;
}

.invent {
  display: flex;
  margin-right: 24px;
  z-index: 99;

  .xzoption {
    width: 88px;
    height: 32px;
    color: #3370ff;
    font-size: 14px;
    background: #ffffff;
    border-radius: 2px 0px 0px 2px;
    opacity: 1;
    border: 1px solid #3370ff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  .option {
    width: 88px;
    height: 32px;
    font-size: 14px;
    background: #ffffff;
    border-radius: 2px 0px 0px 2px;
    opacity: 1;
    border: 1px solid #d9d9d9;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
}

.scan {
  background: #fafcff;
  // min-height: 100vh;
  box-sizing: border-box;
  min-width: 1110px;
  //padding-top: 5px;
  padding-bottom: 10px;

  &-top {
    padding: 16px 0px;
    background: #fff;
    padding-bottom: 12px;
  }

  .typeselect {
    display: flex;
    justify-content: space-between;
    // padding: 0px 16px;
    // padding-top: 16px;

    img {
      //margin-left: 20%;
      width: 48px;
      height: 48px;
      margin-top: 20px;
      margin-right: 17px;
    }

    /*   img:hover {
          width: 78px;
          height: 78px;
          margin-bottom: 0px;
      } */

    .single {
      display: flex;
      justify-content: space-between;
      //justify-content: center;
      //align-items: center;
      //width: 15%;
      background-color: #fff;
      box-shadow: 0px 4px 14px 0px #eef1f8;
      border-radius: 10px 10px 10px 10px;
      height: 94px;
      //flex-direction: column;
      cursor: pointer;

      .wordage {
        display: flex;
        flex-direction: column;

        .text {
          padding-top: 16px;
          padding-left: 17px;
          font-size: 14px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.65);
        }

        .sum {
          padding-top: 15px;
          padding-left: 17px;
          font-size: 24px;
          font-family: Source Han Sans CN-Medium, Source Han Sans CN;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }

    .single:hover {
      box-shadow: 0px 4px 14px 0px #eef1f8;
    }
  }

  .head {
    height: 190px;
    margin: 24px;
    background-color: #fff;
    border-radius: 10px;

    .title {
      height: 51px;
      border-bottom: 1px solid #e9e9e9;

      .span {
        font-size: 16px;
        padding: 15px 0 12px 24px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
    }

    .typeselect {
      display: flex;
      justify-content: space-around;
      // padding: 0px 16px;

      /*   img:hover {
          width: 78px;
          height: 78px;
          margin-bottom: 0px;
      } */

      .single {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 15%;

        height: 138px;
        //flex-direction: column;
        cursor: pointer;

        .wordage {
          display: flex;
          flex-direction: column;

          .text {
            font-size: 14px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.65);
          }

          .sum {
            font-size: 24px;
            font-family: Source Han Sans CN-Medium, Source Han Sans CN;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
          }
        }
      }
    }
  }

  .one {
    height: 308px;
    margin: 16px 0;
    border-radius: 10px;
    display: flex;
    justify-content: space-between;

    .left {
      width: 49%;
      background-color: #fff;
      border-radius: 10px;

      .funnels {
        height: 320px;
      }
    }

    .left,
    .right {
      box-shadow: 0px 4px 12px 0px #eef1f8;
    }

    .title {
      height: 51px;
      border-bottom: 1px solid #e9e9e9;

      .span {
        font-size: 16px;
        padding: 15px 0 12px 24px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
    }

    .left {
      width: 49%;
      background-color: #fff;
      border-radius: 10px;

      .funnels {
        height: 320px;
        width: 100%;
      }
    }

    .right {
      width: 49%;
      margin-left: 16px;
      border-radius: 10px;
      background-color: #fff;

      .schedule {
        display: flex;
        height: 257px;

        .annular {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          flex-direction: column;

          span {
            font-size: 14px;
            //margin-top: 27px;
            padding-top: 27px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.65);
          }
        }
      }
    }
  }

  .two {
    margin: 16px 0;
    border-radius: 10px;
    padding-bottom: 100px;
    display: flex;
    justify-content: space-between;

    .left,
    .right {
      box-shadow: 0px 4px 12px 0px #eef1f8;
    }

    .title {
      height: 51px;
      border-bottom: 1px solid #e9e9e9;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .span {
        font-size: 16px;
        padding: 15px 0 12px 24px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
    }

    .left {
      width: 49%;
      height: 560px;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0px 4px 12px 0px #eef1f8;

      .firmType {
        width: 100%;
        height: 60px;
        padding-left: 20px;
        display: flex;
        align-items: center;
      }

      .rankinglist {
        width: 100%;
        height: 500px;
      }
    }

    .right {
      width: 49%;
      margin-left: 16px;
      border-radius: 10px;
      background-color: #fff;
      box-shadow: 0px 4px 12px 0px #eef1f8;

      .Dynamicquantity {
        width: 100%;
        height: 250px;
        display: flex;

        .text {
          font-size: 14px;
          height: 40px;
          padding-left: 80px;
          padding-top: 40px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: rgba(29, 33, 41, 0.85);
        }

        .feelingsS {
          width: 50%;
          height: 250px;

          .feelings {
            width: 100%;
            height: 210px;
          }
        }

        .dynamicS {
          width: 50%;
          height: 250px;

          .dynamic {
            width: 100%;
            height: 210px;
          }
        }
      }

      .List {
        padding-bottom: 10px;
        box-sizing: border-box;
      }
    }
  }
}

.popularfeelings {
  display: flex;
  justify-content: space-between;
  width: 94%;
  margin-top: 24px;
  margin-left: 24px;
  .above {
    display: flex;
  }

  .tag {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #3370ff;
    color: #3370ff;
    min-width: 62px;
    font-size: 11px;
    background-color: #e7eef8;
    padding: 0 6px;
    margin-right: 8px;
    padding-top: 2px;
    height: 18px;
    border-radius: 2px 2px 2px 2px;
  }

  .xq {
    font-size: 14px;
    white-space: nowrap;
    display: block;
    overflow: hidden;
    max-width: 340px;
    text-overflow: ellipsis;
    font-family: Abel-Regular, Abel;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
  }

  .xq:hover {
    color: #3370ff;
  }

  .time {
    font-size: 14px;
    font-family: Source Han Sans CN-Normal, Source Han Sans CN;
    font-weight: 400;
    min-width: 80px;
    color: rgba(0, 0, 0, 0.45);
  }
}

.record {
  padding: 16px;
  box-sizing: border-box;
  min-height: calc(100% - 80px);
  .timeView {
    display: flex;
    height: 20px;
    justify-content: space-between;
    cursor: pointer;
    .name {
      margin-left: 25px;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      color: #3370ff;
    }

    .time {
      line-height: 20px;
      font-size: 13px;
      font-weight: 400;
      color: #4e5969;
    }
  }
  .timeView::after {
    background-color: #3e80ff;
    content: "";
    position: absolute;
    border: 4px solid #bedaff;
    width: 16px;
    height: 16px;
    z-index: 6;
    border-radius: 50%;
    top: 0px;
  }

  .boxView {
    margin-left: 25px;
    margin-top: 6px;
    margin-bottom: 25px;
    height: auto;
    background: #f7f8fa;
    padding: 11px 15px;
    box-sizing: border-box;
    overflow: hidden;

    .titleView {
      display: flex;
      margin-bottom: 6px;
      .titleText {
        font-size: 14px;
        font-weight: 400;
        color: #1d2129;
        line-height: 20px;
      }
    }
    .contentView {
      display: flex;
      overflow: hidden;
      .content {
        width: 100%;
        font-size: 14px;
        font-weight: 400;
        color: #4e5969;
        line-height: 20px;
      }
      .contentOneLine {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .openLineBtn {
        display: flex;
        width: 60px;
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        color: #3370ff;
        align-items: center;
        cursor: pointer;
        margin-left: 4px;
      }
      .openLineBtn2 {
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        color: #3370ff;
        align-items: center;
        cursor: pointer;
        margin-left: 4px;
      }
    }
  }

  .recordItem {
    position: relative;
  }
  .recordItem:not(:last-child)::before {
    background-color: #f0f0f0;
    content: "";
    position: absolute;
    left: 7px;
    z-index: 5;
    width: 2px;
    height: calc(100% + 25px);
  }
}
</style>