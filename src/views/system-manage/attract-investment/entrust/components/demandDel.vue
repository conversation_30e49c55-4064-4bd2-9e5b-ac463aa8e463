<template>
  <!-- 企业详情 -->
  <div
    v-if="dataInfo.userName"
    class="box"
  >
    <div class="title">
      <div class="iconarrow">
        <i
          style="cursor: pointer"
          class="el-icon-arrow-left"
          @click="gobacka"
        />
      </div>
    </div>
    <div class="head">
      <img
        src="https://static.idicc.cn/cdn/pangu/demand.png"
        class="iconImg"
      >
      <div class="content">
        <div class="enterprise">
          {{ dataInfo.demander }}
        </div>
        <div class="content-two">
          <div class="two-a">
            <span class="key">需求发布人：</span>
            <span class="value">
              {{ dataInfo.userName }}
            </span>
          </div>
          <div class="two-a">
            <span class="key">需求发布时间：</span>
            <span class="value">
              {{ dataInfo.gmtCreate }}</span>
          </div>
          <div class="two-a">
            <span class="key">招商需求所需产业链：</span>
            <span
              v-if="dataInfo.chainNames"
              class="value"
            >
              {{ dataInfo.chainNames.join('、') }}
            </span>
          </div>
        </div>
        <div class="content-two">
          <div class="two-a">
            <span class="key">招商需求企业所属区域：</span>
            <span
              v-if="dataInfo.addressName"
              class="value"
            >
              {{ dataInfo.addressName.join('、') }}
            </span>
          </div>
          <div class="two-a">
            <span class="key">招商需求企业类型：</span>
            <span
              v-if="dataInfo.enterpriseType"
              class="value"
            >{{ dataInfo.enterpriseType.join('、') }}</span>
          </div>
          <div class="two-a">
            <span class="key">招商需求融资阶段：</span>
            <span
              v-if="dataInfo.enterpriseFinance"
              class="value"
            >{{ dataInfo.enterpriseFinance.join('、') }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="listBox">
      <span class="titleText">反馈企业列表</span>
      <div
        v-for="(item, index) in myentrustList"
        :key="index"
        class="single"
      >
        <div class="content">
          <div class="names">
            <img
              src="https://static.idicc.cn/cdn/pangu/enterpriseIcon.png"
              class="iconImgs"
            >
            <div style="display: flex">
              <span>{{ item.enterpriseName }}</span>
              <div
                v-if="item.chainNames"
                class="tagItem"
              >
                {{ item?.chainNames[0] }}
              </div>
            </div>
          </div>
          <div class="content-one">
            <div class="one-a">
              <span class="key">推荐理由：</span>
              <span class="value">{{ item.auditReason }}</span>
            </div>
            <div class="one-a">
              <span class="key">反馈时间：</span>
              <span class="value">{{ item.auditTime }}</span>
            </div>
          </div>
          <div class="content-two">
            <div class="two-a">
              <span class="key">企业概况：</span>
              <span class="value">{{ item.enterpriseDetail }}</span>
            </div>
          </div>
          <div class="operationBtn">
            <el-button
              type="text"
              @click="deleteFn(item)"
            >
              删除
            </el-button>
            <el-button
              v-if="item.status!=3"
              type="text"
              @click="clickCheck(item)"
            >
              一键委托
            </el-button>
          </div>
        </div>
      </div>
      <div
        class="ye"
      >
        <el-pagination
          :current-page.sync="formInline.pageNum"
          :page-sizes="[5, 10, 20, 50]"
          :page-size.sync="formInline.pageSize"
          :total="+total"
          layout="total,sizes, prev, pager, next, jumper"
          @size-change="getDetailList"
          @current-change="getDetailList"
        />
      </div>
    </div>
    <addEntrust
      v-if="isDrawer"
      :is-drawer.sync="isDrawer"
      :one-click="true"
      :operation-item="operationItem"
      @updataList="getDetailList"
    />
  </div>
</template>

<script>
import { demandListAPI, recommendlistAPI ,recommenddeleteAPI} from "@/api/xiaoAI";
import  addEntrust from './Drawer/addEntrust.vue'
export default {
  name: "DetailsEnterprise",
  components: {
    addEntrust
  },
  props: {
    demandID: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      dataInfo: {},
      isDrawer:false,
      myentrustList: [],
      operationItem:{},
      formInline: {
        pageNum: 1,
        pageSize: 5
      },
      total:'',
    };
  },
  created() {
    document.body.style.overflow = "auto";
    this.$nextTick(() => {
      document.body.scrollTop = 0
      document.body.style.overflow = 'visible'
    })
    this.getDetail();
    this.getDetailList();
  },
  methods: {
    clickCheck(item){
      this.operationItem=item
      this.isDrawer=true
    },
     deleteFn(item) {
      this.$confirm("确认删除该反馈？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await recommenddeleteAPI({
            recommendId: item.recommendId,
          });
          this.$message.success("删除成功！");
          this.getDetailList();
        })
        .catch(() => {
        });
    },
    async getDetailList() {
      const res = await recommendlistAPI({
        demandId: this.demandID,
        pageNum: this.formInline.pageNum,
        pageSize: this.formInline.pageSize,
      });
      this.myentrustList = res.result.records
      this.total = res.result.total
    },
    async getDetail() {
      const res = await demandListAPI({
        demandId: this.demandID
      });
      let demandList = res.result.records
      demandList.map(it => {
        if (it.id == this.demandID) {
          this.dataInfo = it
        }
      })
    },
    gobacka() {
      this.$emit("goback");
    },
  },
};
</script>

<style lang="scss" scoped>
.listBox {
  background-color: #fff;
  border-radius: 10px;
  opacity: 1;
  margin-top: 30px;
  box-shadow: 0px 4px 12px 0px #EEF1F8;

  .titleText {
    display: flex;
    justify-content: space-between;
    padding: 16px 30px;
    align-items: center;
    font-size: 16px;
    border-bottom:  1px solid #e9e9e9;
  }

  .operationBtn {
    position: absolute;
    top: 51px;
    right: 8%;
    display: flex;
  }

  .single {
    padding: 30px 0;
    box-sizing: border-box;
    border-bottom:  1px solid #e9e9e9;
    .key {
      font-size: 14px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.45);
    }

    .value {
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;
    }

    .content-one {
      display: flex;
      margin-top: 20px;
      margin-left: 80px;

      .one-a {
        width: 40%;
      }
    }

    .content-two {
      display: flex;
      margin-top: 20px;
      margin-left: 80px;

      .two-a {
        width: 40%;
      }
    }

    .content {
      position: relative;
      .names {
        display: flex;
        align-items: center;
        font-size: 18px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);

        .iconImgs {
          width: 32px;
          height: 32px;
          margin-left: 30px;
          margin-right: 22px;
        }

        .tagItem {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11px;
          padding: 0 6px;
          color: #ff7d00;
          background: #fff2e6;
          margin-right: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
        }
      }
    }
  }
}

.dropdow {
  white-space: nowrap;
}

.box {
  padding-bottom: 20px;
  overflow-y: scroll;

  .manag {
    padding: 24px;
  }
}

.title {
  .iconarrow {
    color: #666;
    font-size: 20px;
    background: white;
    padding: 2px;
    width: 25px;
    height: 25px;
    border-radius: 16px;
  }
}

.head {
  width: 100%;
  margin-top: 16px;
  display: flex;
  background: #ffffff;
  border-radius: 10px;
  opacity: 1;

  box-shadow: 0px 4px 12px 0px #EEF1F8;

  .operation {
    position: absolute;
    display: flex;
    //margin-left: 80%;
    right: 5vw;
    margin-top: 22px;

    .attention {
      width: 70px;
      height: 32px;
      background-color: #fff;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid #3370FF;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      img {
        width: 18px;
        height: 18px;
        margin-right: 8px;
        margin-bottom: 2px;
      }

      span {
        color: #3370FF;
        font-weight: 400;
        font-size: 14px;
      }
    }

    .attention:hover {
      background-color: #F4FAFF;
    }

    .noattention {
      width: 70px;
      height: 32px;
      background: #f7f8fa;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
    }

    .noattention:hover {
      background-color: #F4FAFF;
    }
  }

  .iconImg {
    width: 32px;
    height: 32px;
    margin-top: 28px;
    margin-left: 26px;
    margin-right: 22px;
  }

  .content {
    margin-top: 25px;
    width: 100%;
    .enterprise {
      font-size: 18px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 16px;
    }

    .name {
      display: flex;
      width: 70%;
      font-size: 18px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);

      .tag {
        display: flex;
        padding-left: 10px;
        margin-top: 3px;

        .industryTag {
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #3370FF;
          color: #3370FF;
          font-size: 11px;
          padding: 0 6px;
          margin-left: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
        }
      }
    }

    .content-two {
      display: flex;
      margin-top: 4px;
      padding-bottom: 22px;

      .two-a {
        width: 30%;
      }

      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }

      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }
  }
}
</style>