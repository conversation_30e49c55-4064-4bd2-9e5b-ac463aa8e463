<template>
  <!-- 企业详情 -->
  <div
    v-if="dataInfo.enterprise"
    class="box"
  >
    <div class="title">
      <div class="iconarrow">
        <i
          style="cursor: pointer"
          class="el-icon-arrow-left"
          @click="gobacka"
        />
      </div>
    </div>
    <div class="head">
      <div class="orderSn">
        申请时间：{{ dataInfo.applyDatetime }}
      </div>
      <div class="statusBox">
        {{ stateShow(dataInfo.status) }}
      </div>
      <img
        src="https://static.idicc.cn/cdn/pangu/enterpriseIcon.png"
        class="iconImg"
      >
      <div class="content">
        <div class="enterprise">
          {{ dataInfo.enterprise }}
        </div>
        <div class="content-two">
          <div class="two-a">
            <span class="key">期望对接时间：</span>
            <span class="value">
              {{ dataInfo.exceptedDatetime }}
            </span>
          </div>
          <div class="two-a">
            <span class="key">招商对接人：</span>
            <span class="value">
              {{ dataInfo.contact }}</span>
          </div>
          <div class="two-a">
            <span class="key">联系方式：</span>
            <span class="value">
              {{ dataInfo.contactPhone }}
            </span>
          </div>
        </div>
        <div class="content-two">
          <div class="two-a">
            <span class="key">金额：</span>
            <span class="value">
              {{ dataInfo.amount }}
            </span>
          </div>
          <div class="two-a">
            <span class="key">招商需求：</span>
            <span
              class="value"
            >{{ dataInfo.note }}</span>
          </div>
          <div class="two-a">
            <span class="key">委托人：</span>
            <span class="value">{{ dataInfo.userName }}</span>
          </div>
          <div class="two-a">
            <span class="key">申请理由：</span>
            <span class="value">{{ dataInfo.reason }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="contentBox">
      <div
        v-for="(item, index) in dataInfo.paymentApplyFlowList"
        :key="index"
        class="contentItemBox"
      >
        <div class="Time">
          <div class="remindTime">
            <div :class="item.status == 2 ? 'circle2' : 'circle1'" />
            <span>{{ item.who ? item.who : '审批人' }}</span>
            <span class="typeText">
              {{ item.gmtModify }}
            </span>
          </div>
        </div>
        <div class="userName">
          {{ item.userName }}
          <span
            v-if="item.userId && index != dataInfo.paymentApplyFlowList.length - 1"
            class="status"
          >({{ item.status ==
            0 ? '审批中' : (item.status == 1 ?
              '审批通过' :
              '审批驳回') }})</span>
        </div>
        <div
          v-if="item.instruction"
          class="instruction"
        >
          {{ item.instruction }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { paymentdetailAPI } from "@/api/xiaoAI";
export default {
  name: "DetailsEnterprise",
  components: {
  },
  props: {
    entrustId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      dataInfo: {},
      myentrustList: [],
      CurrentSelectio: 1,
      tabList: [
        {
          name: "进度信息",
          id: 1,
        },
        {
          name: "催办信息",
          id: 2,
        },
      ]
    };
  },
  created() {
    document.body.style.overflow = "auto";
    this.$nextTick(() => {
      document.body.scrollTop = 0
      document.body.style.overflow = 'visible'
    })
    this.getDetail();
  },
  methods: {
    stateShow(value) {
      let stateList = {
        0: '处理中',
        1: '审批通过',
        2: '审批驳回',
      }
      return stateList[value] || '未知状态'
    },
    //tab切换
    cut(id) {
      this.CurrentSelectio = id;
    },
    async getDetail() {
      const res = await paymentdetailAPI({
        entrustId: this.entrustId
      });
      this.dataInfo = res.result;
      let userId = this.$store.getters.user.userId
      this.dataInfo.paymentApplyFlowList.push({
        gmtModify: this.dataInfo.applyDatetime,
        who: '发起人',
        userName: userId == this.dataInfo.userId ? '我' : this.dataInfo.userName,
        instruction: this.dataInfo.reason
      })
    },
    gobacka() {
      this.$emit("goback");
    },
  },
};
</script>

<style lang="scss" scoped>
.statusBox{
  position: absolute;
  right: 60px;
  top: 50px;
  color: #5070ff;
  font-size: 16px;
}
.contentBox {
  width: 100%; /* 690rpx × 0.5 = 345px */
  height: auto;
  background: #FFFFFF;
  box-shadow: 0px 4px 12px 0px #EEF1F8; /* 0rpx 8rpx 24rpx 0rpx */
  border-radius: 10px 10px 10px 10px; /* 20rpx */
  padding: 17px 15px 15px 12px; /* 34rpx 30rpx 30rpx 24rpx */
  box-sizing: border-box;
}

.contentBox .contentItemBox {
  width: 100%;
  height: auto;
  position: relative;
}

.contentBox .contentItemBox .instruction {
  width: 500px; /* 590rpx */
  background: #FAFAFA;
  padding: 9px; /* 18rpx */
  box-sizing: border-box;
  font-size: 13px; /* 26rpx */
  font-weight: normal;
  color: #3F4A59;
  margin-left: 21px; /* 42rpx */
  border-radius: 4px; /* 8rpx */
}

.contentBox .contentItemBox .userName {
  font-size: 14px; /* 28rpx */
  font-weight: normal;
  color: #3F4A59;
  margin-left: 21px; /* 42rpx */
  margin-bottom: 7px; /* 14rpx */
}

.contentBox .contentItemBox .status {
  margin-left: 6px; /* 12rpx */
  color: #3370FF;
}

.contentBox .contentItemBox:last-of-type:before {
  content: '';
  display: none;
}

.contentBox .contentItemBox:before {
  content: '';
  position: absolute;
  top: 15px;
  bottom: 0;
  left: 6px;
  width: 1px;
  height: 100%;
  background-color: #E5E6EB;
}

.contentBox .contentItem {
  width: 300px;
  height: auto;
  background: #F7F8FA;
  border-radius: 6px 6px 6px 6px; 
  padding: 15px;
  margin-left: 18px;
  box-sizing: border-box;
  padding-bottom: 3px;
  position: relative;
}

.contentBox .contentItem .Itemleft {
  display: flex;
  align-items: center;
  height: 19px; /* 38rpx */
}

.contentBox .contentItem .Itemline {
  display: flex;
  margin-bottom: 12px; /* 24rpx */
}

.contentBox .contentItem .GrayCircle {
  width: 4px; /* 8rpx */
  height: 4px; /* 8rpx */
  min-width: 4px; /* 8rpx */
  min-height: 4px; /* 8rpx */
  background: #C9CDD4;
  border-radius: 50%;
  margin-right: 6px; /* 12rpx */
}

.contentBox .contentItem .key,
.contentBox .contentItem .value {
  font-weight: 400;
  font-size: 14px; /* 28rpx */
}

.contentBox .contentItem .key {
  color: #1D2129;
  white-space: nowrap;
}

.contentBox .contentItem .value {
  color: #4E5969;
}

.contentBox .Time {
  display: flex;
  height: 30px; /* 60rpx */
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px; /* 12rpx */
}

.contentBox .Time .remindTime,
.contentBox .Time .typeText {
  font-weight: 400;
  display: flex;
  align-items: center;
}

.contentBox .Time .remindTime {
  display: flex;
  align-items: center;
  font-size: 13px; /* 26rpx */
  color: #4E5969;
  min-width: 40%;
}

.contentBox .Time .typeText {
  font-size: 14px; /* 28rpx */
  color: #1D2129;
  margin-left: 20px;
}

.contentBox .Time .dot {
  width: 6px; /* 12rpx */
  height: 6px; /* 12rpx */
  background: #3370FF;
  border-radius: 50%;
  margin-left: 16px; /* 32rpx */
  margin-right: 8px; /* 16rpx */
  min-width: 6px; /* 12rpx */
  min-height: 6px; /* 12rpx */
}
.circle1 {
  background-color: #67C23A;
  position: relative;
  /* border: 6rpx solid #bedaff; 转换后为 3px */
  width: 14px;       /* 28rpx × 0.5 = 14px */
  height: 14px;      /* 28rpx × 0.5 = 14px */
  z-index: 6;
  border-radius: 50%;
  margin-right: 7px; /* 14rpx × 0.5 = 7px */
}

.circle2 {
  background-color: #E04848;
  position: relative;
  /* border: 6rpx solid #bedaff; 转换后为 3px */
  width: 14px;       /* 28rpx × 0.5 = 14px */
  height: 14px;      /* 28rpx × 0.5 = 14px */
  z-index: 6;
  border-radius: 50%;
  margin-right: 7px; /* 14rpx × 0.5 = 7px */
}
.scan {
  width: 100%;
  margin-top: 16px;
  background-color: #fff;
  border-radius: 10px;

  box-shadow: 0px 4px 12px 0px #EEF1F8;

  &-top {
    padding: 16px 0px;
    // background: #fff;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 10px 10px 0px 0px;
  }

  &-tab {
    display: flex;
    padding-left: 16px;

    &-con {
      display: flex;
      justify-content: space-between;
    }

    &-select {
      display: flex;
    }

    &-list {
      margin-right: 64px;
      font-size: 16px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      cursor: pointer;

      &.on {
        font-weight: bold;
        color: #3370FF;
        position: relative;

        &::after {
          content: "";
          width: 28px;
          height: 2px;
          background: #3370FF;
          position: absolute;
          left: 18px;
          bottom: -8px;
        }
      }
    }
  }

  &-con {
    margin: 16px 24px;
  }
}

.listBox {
  background-color: #fff;
  border-radius: 10px;
  opacity: 1;
  margin-top: 30px;
  box-shadow: 0px 4px 12px 0px #EEF1F8;

  .titleText {
    display: flex;
    justify-content: space-between;
    padding: 16px 30px;
    align-items: center;
    font-size: 16px;
    border-bottom: 1px solid #e9e9e9;
  }

  .operationBtn {
    position: absolute;
    top: 51px;
    right: 8%;
    display: flex;
  }
}

.dropdow {
  white-space: nowrap;
}

.box {
  padding-bottom: 20px;
  overflow-y: scroll;

  .manag {
    padding: 24px;
  }
}

.title {
  .iconarrow {
    color: #666;
    font-size: 20px;
    background: white;
    padding: 2px;
    width: 25px;
    height: 25px;
    border-radius: 16px;
  }
}

.head {
  width: 100%;
  margin-top: 16px;
  padding-top: 32px;
  padding-bottom: 12px;
  display: flex;
  background: #ffffff;
  border-radius: 10px;
  opacity: 1;
  box-shadow: 0px 4px 12px 0px #EEF1F8;
  position: relative;

  .orderSn {
    position: absolute;
    left: 30px;
    top: 30px;
  }

  .operation {
    position: absolute;
    display: flex;
    //margin-left: 80%;
    right: 5vw;
    margin-top: 22px;

    .attention {
      width: 70px;
      height: 32px;
      background-color: #fff;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid #3370FF;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      img {
        width: 18px;
        height: 18px;
        margin-right: 8px;
        margin-bottom: 2px;
      }

      span {
        color: #3370FF;
        font-weight: 400;
        font-size: 14px;
      }
    }

    .attention:hover {
      background-color: #F4FAFF;
    }

    .noattention {
      width: 70px;
      height: 32px;
      background: #f7f8fa;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
    }

    .noattention:hover {
      background-color: #F4FAFF;
    }
  }

  .iconImg {
    width: 32px;
    height: 32px;
    margin-top: 28px;
    margin-left: 26px;
    margin-right: 22px;
  }

  .content {
    margin-top: 25px;
    width: 100%;

    .enterprise {
      font-size: 18px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 16px;
    }

    .name {
      display: flex;
      width: 70%;
      font-size: 18px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);

      .tag {
        display: flex;
        padding-left: 10px;
        margin-top: 3px;

        .industryTag {
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #3370FF;
          color: #3370FF;
          font-size: 11px;
          padding: 0 6px;
          margin-left: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
        }
      }
    }

    .content-two {
      display: flex;
      margin-top: 4px;
      padding-bottom: 22px;

      .two-a {
        width: 30%;
      }

      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }

      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }
  }
}
</style>