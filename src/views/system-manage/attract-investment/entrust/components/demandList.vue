<template>
  <!-- 搜索结果列表 -->
  <div>
    <div class="issue">
      <span>共{{ total }}条需求</span>
      <el-button
        type="primary"
        @click="adddemand"
      >
        发布招商需求
      </el-button>
    </div>

    <div
      v-for="(item, index) in enterpriseList"
      :key="index"
      class="single"
    >
      <div class="itemBtn">
        <img
          src="https://static.idicc.cn/cdn/pangu/demand.png"
          class="iconImg"
        >
        <div class="content">
          <div class="name">
            <div style="display: flex">
              <span>{{
                item.demander
              }}</span>
            </div>
          </div>
          <div class="content-one">
            <div class="one-a">
              <span class="key">需求发布时间：</span>
              <span class="value">{{ item.gmtCreate }}</span>
            </div>
          </div>
          <div
            class="stateBtn"
            :class="stateClass(item.recommendCount)"
          >
            {{ item.recommendCount == 0 ? '待反馈' : '已反馈' + item.recommendCount + '次' }}
          </div>
          <div class="operationBtn">
            <el-button
              type="text"
              @click="Entrydetails(item)"
            >
              详情
            </el-button>
            <el-button
              type="text"
              :disabled="item.cancel"
              @click="clickCheck(item)"
            >
              {{ !item.cancel ? '撤销' : '已撤销' }}
            </el-button>
          </div>
          <div class="content-two">
            <div class="two-b">
              <span class="key">招商需求描述：：</span>
              <el-tooltip
                effect="dark"
                :visible-arrow="false"
                placement="top"
                :content="item.demandDescribe"
              >
                <span class="value">{{ item.demandDescribe }}</span>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span
      v-if="enterpriseList.length == 0 && showNodata"
      style="display: flex;align-items: center;justify-content: center;margin-top: 30px;font-size: 18px;"
    > <span style="padding-top: 30px">暂无数据</span></span>
    <div
      v-if="showNodata"
      class="ye"
    >
      <el-pagination
        :current-page.sync="formInline.pageNum"
        :page-sizes="[5, 10, 20, 50]"
        :page-size.sync="formInline.pageSize"
        :total="+total"
        layout="total,sizes, prev, pager, next, jumper"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
    <addDemand
      v-if="isDrawer"
      :is-drawer.sync="isDrawer"
      @updataList="updataList"
    />
  </div>
</template>
<script>
import { demandListAPI,demandCancelAPI } from "@/api/xiaoAI";
import  addDemand from './Drawer/addDemand.vue'
export default {
  name: "EnterpriseList",
  components: {
    addDemand
  },
  data() {
    return {
      enterpriseList:[],
      total:0,
      Listloading:false,
      isDrawer:false,
      showNodata:false,
      formInline: {
        pageNum: 1,
        pageSize: 5
      },
    };
  },
  created() {
    this.getList()
  },
  methods: {
    adddemand(){
      this.isDrawer=true
    },
    updataList(){
      this.formInline.pageNum=1
      this.getList()
    },
    async getList(){
      try {
       const res = await demandListAPI({
        pageNum: this.formInline.pageNum,
        pageSize: this.formInline.pageSize,
       });
      this.enterpriseList = res.result.records
      this.total = res.result.total
      } finally {
        this.showNodata = true;
      }
    },
    stateClass(state) {
      if (state==0) {
        return 'gray'
      }else{
        return "blue"
      }
    },
    // 企业详情
    Entrydetails(item) {
      this.$emit("godemandDel", item.id, 2);
      document.documentElement.scrollTop = 0;
    },
    checkCanle() {
      this.check = false
    },
    clickCheck(item) {
      this.$confirm("执行撤销后将不能收到该需求相关推荐企业反馈信息，是否继续撤销", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await demandCancelAPI({
            demandId: item.demandId,
          });
          this.$message.success("撤销成功！");
          this.getList();
        })
        .catch(() => {
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.issue{
 display: flex;
 justify-content: space-between;
 padding: 16px 30px;
 align-items: center;
 font-size: 16px;
}
.operationBtn{
  position: absolute;
  top: 51px;
  left: 86%;
  display: flex;
}
.dropdow {
  white-space: nowrap;
}

.noinformation {
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}

.abierto {
  width: 100px;
  height: 32px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 2px 2px 2px 2px;
  font-size: 14px;
  font-family: Source Han Sans CN-Normal, Source Han Sans CN;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  border: 1px solid rgba(0, 0, 0, 0.25);
  cursor: pointer;
}

.abierto:hover {
  background-color: #F4FAFF;
}

.single {
  width: calc(100% - 48px);
  padding: 0 !important;
  margin-left: 24px;
  height: 136px;
  display: flex;
  border-bottom: 1px solid #e9e9e9;
  position: relative;
  flex-direction: column;
  .itemTop{
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    height: 20px;
    display: flex;
    align-items: center;
    margin-top: 10px;
    position: absolute;
  }
  .itemBtn{
    display: flex;
    //margin-top: 20px;
  }
  .stateBtn {
          width: 146px;
          height: 40px;
          z-index: 11;
          border-radius: 4px 4px 4px 4px;
          font-weight: 400;
          font-size: 14px;
          color: #FFFFFF;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          top: 50px;
          left: 60%;
        }
        .blue{
          background-color: #3370ff;
        }
        .orange{
          background-color: #fa9600;
        }
        .green{
          background-color: #19b21e;
        }
        .gray{
          background-color: #b7bfc7;
        }
        .red{
          background-color: #fc474c;
        }

  .iconImg {
    width: 32px;
    height: 32px;
    margin-top: 28px;
    margin-right: 22px;
  }

  .content {
    margin-top: 32px;
    width: calc(100% - 52px);

    .name {
      width: 100%;
      max-width: 100%;
      display: flex;
      height: 18px;
      justify-content: space-between;
      font-size: 18px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);

      .tag {
        display: flex;
        padding-left: 10px;
        margin-top: 3px;

        .industryTag {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11px;
          padding: 0 6px;
          color: #ff7d00;
          background: #fff2e6;
          margin-right: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
        }
      }

      .state {
        width: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 4px;
        margin-left: 8px;
        font-size: 13px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;

        .blue {
          width: 8px;
          height: 8px;
          background: #417fff;
          border-radius: 50%;
          margin-right: 5px;
          margin-bottom: 2px;
          opacity: 1;
        }

        .gray {
          width: 8px;
          height: 8px;
          background: #c9cdd4;
          border-radius: 50%;
          margin-right: 5px;
          margin-bottom: 2px;
          opacity: 1;
        }

        .blues {
          color: #417fff;
        }

        .grays {
          color: #86909c;
        }
      }

      .operation {
        display: flex;

        .abierto {
          width: 100px;
          height: 32px;
          background-color: rgba(255, 255, 255, 0.5);
          border-radius: 2px 2px 2px 2px;
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 1;
          border: 1px solid rgba(0, 0, 0, 0.25);
          cursor: pointer;
        }

        .abierto:hover {
          background-color: #F4FAFF;
        }

        .bring {
          width: 100px;
          height: 32px;
          background: rgba(28, 145, 255, 0.95);
          border-radius: 2px 2px 2px 2px;
          margin-right: 14px;
          opacity: 1;
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }

        .bring:hover {
          background: rgba(28, 145, 255, 1);
        }
      }

      .bringinto {
        width: 126px;
        height: 32px;
        background-color: #FFFFFF;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #3370FF;
        opacity: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }

      .bringinto:hover {
        background-color: #F4FAFF;
      }
    }

    .EnterpriseLabel {
      padding: 13px 0;

      .tags {
        display: flex;
      }
    }

    .content-one {
      display: flex;
      margin-top: 20px;

      .one-a {
        width: 20%;
      }

      .one-b {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }

      .one-c {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }

      .one-d {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
        position: relative;
        z-index: 11;
      }
      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }

      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }

    .content-two {
      display: flex;
      margin-top: 14px;
      z-index: 9;

      .two-a {
        width: 20%;
      }

      .two-b {
        width: 80%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }

      .two-c {
        width: 40%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }

      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }

      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }

    .content-three {
      display: flex;
      margin-top: 14px;

      .three {
        width: 100%;
      }

      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }

      .value {
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        cursor: pointer;
        font-weight: 400;
        color: #3370FF;
      }
    }
  }
}
</style>