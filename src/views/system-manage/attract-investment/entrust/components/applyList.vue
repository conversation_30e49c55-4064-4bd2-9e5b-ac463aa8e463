<template>
  <!-- 搜索结果列表 -->
  <div>
    <div class="issue">
      <span>共{{ total }}条申请</span>
    </div>
    <div
      v-for="(item, index) in enterpriseList"
      :key="index"
      class="single"
    >
      <div class="itemTop">
        申请时间：{{ item.applyDatetime }}
      </div>
      <div class="itemBtn">
        <img
          src="https://static.idicc.cn/cdn/pangu/enterpriseIcon.png"
          class="iconImg"
        >
        <div class="content">
          <div class="name">
            <div style="display: flex">
              <span>{{
                item.enterprise
              }}</span>
            </div>
          </div>
          <div class="content-one">
            <div class="one-a">
              <span class="key">期望对接时间：</span>
              <span class="value">{{ item.exceptedDatetime }}</span>
            </div>
            <div class="one-b">
              <span class="key">招商对接人：</span>
              <span class="value">{{ item.contact }}</span>
            </div>
            <div class="one-c">
              <span class="key">联系方式：</span>
              <span class="value">{{ item.contactPhone }}</span>
            </div>
          </div>
          <div
            class="stateBtn"
            :class="stateClass(item.status)"
          >
            {{ stateShow(item.status) }}
          </div>
          <div class="operationBtn">
            <el-button
              type="text"
              @click="Entrydetails(item)"
            >
              详情
            </el-button>
          </div>
          <div class="content-two">
            <div class="two-a">
              <span class="key">金额：</span>
              <span class="value">{{ item.amount }}</span>
            </div>
            <div class="two-b">
              <span class="key">招商需求：：</span>
              <el-tooltip
                effect="dark"
                :visible-arrow="false"
                placement="top"
                :content="item.note"
              >
                <span class="value">{{ item.note }}</span>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span
      v-if="enterpriseList.length == 0 && showNodata"
      style="display: flex;align-items: center;justify-content: center;margin-top: 30px;font-size: 18px;"
    > <span
      style="padding-top: 30px"
    >暂无数据</span></span>
    <div
      v-if="showNodata"
      class="ye"
    >
      <el-pagination
        :current-page.sync="formInline.pageNum"
        :page-sizes="[5, 10, 20, 50]"
        :page-size.sync="formInline.pageSize"
        :total="+total"
        layout="total,sizes, prev, pager, next, jumper"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
  </div>
</template>
<script>
import { stateList, enterpriseType, } from './publicOptions'
import { paymentApplyListAPI } from "@/api/xiaoAI";
export default {
  name: "EnterpriseList",
  components: {

  },
  data() {
    return {
      stateList: stateList,
      enterpriseType: enterpriseType,
      enterpriseList: [],
      commentDig: false,
      examineDig: false,
      showNodata: false,
      total: 0,
      formInline: {
        pageNum: 1,
        pageSize: 5
      },
      operationData: {},
      isDrawer: false
    };
  },
  created() {
    this.getList()
  },
  methods: {
    changPageList() {
      this.$emit("changPageList", 2);
      document.documentElement.scrollTop = 0;
    },
    // 委托详情
    Entrydetails(item) {
      this.$emit("godemandDel", item.entrustId, 4);
      document.documentElement.scrollTop = 0;
    },
    updataList() {
      this.formInline.pageNum = 1
      this.getList()
    },
    async getList() {
      try {
        const res = await paymentApplyListAPI({
          pageNum: this.formInline.pageNum,
          pageSize: this.formInline.pageSize,
        });
        this.enterpriseList = res.result.records
        this.total = res.result.total
      } finally {
        this.showNodata = true;
      }
    },
    stateShow(value) {
      let stateList = {
        0: '处理中',
        1: '审批通过',
        2: '审批驳回',
      }
      return stateList[value] || '未知状态'
    },
    stateClass(state) {
     if (state == 1) {
        return "green"
      } else if (state == 2) {
        return "gray"
      } else {
        return "blue"
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.issue {
  display: flex;
  justify-content: space-between;
  padding: 16px 30px;
  align-items: center;
  font-size: 16px;
}

.operationBtn {
  position: absolute;
  top: 51px;
  left: 86%;
  display: flex;
}

.dropdow {
  white-space: nowrap;
}

.noinformation {
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}

.abierto {
  width: 100px;
  height: 32px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 2px 2px 2px 2px;
  font-size: 14px;
  font-family: Source Han Sans CN-Normal, Source Han Sans CN;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  border: 1px solid rgba(0, 0, 0, 0.25);
  cursor: pointer;
}

.abierto:hover {
  background-color: #F4FAFF;
}

.single {
  width: calc(100% - 48px);
  padding: 0 !important;
  margin-left: 24px;
  height: 156px;
  display: flex;
  border-bottom: 1px solid #e9e9e9;
  position: relative;
  flex-direction: column;

  .itemTop {
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    height: 20px;
    display: flex;
    align-items: center;
    margin-top: 10px;
    position: absolute;
  }

  .itemBtn {
    display: flex;
    margin-top: 20px;
  }

  .stateBtn {
    width: 146px;
    height: 40px;
    z-index: 11;
    border-radius: 4px 4px 4px 4px;
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 50px;
    left: 60%;
  }

  .blue {
    background-color: #3370ff;
  }

  .orange {
    background-color: #fa9600;
  }

  .green {
    background-color: #19b21e;
  }

  .gray {
    background-color: #b7bfc7;
  }

  .red {
    background-color: #fc474c;
  }

  .iconImg {
    width: 32px;
    height: 32px;
    margin-top: 28px;
    margin-right: 22px;
  }

  .content {
    margin-top: 32px;
    width: calc(100% - 52px);

    .name {
      width: 100%;
      max-width: 100%;
      display: flex;
      height: 18px;
      justify-content: space-between;
      font-size: 18px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);

      .tag {
        display: flex;
        padding-left: 10px;
        margin-top: 3px;

        .industryTag {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11px;
          padding: 0 6px;
          color: #ff7d00;
          background: #fff2e6;
          margin-right: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
        }
      }

      .state {
        width: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 4px;
        margin-left: 8px;
        font-size: 13px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;

        .blue {
          width: 8px;
          height: 8px;
          background: #417fff;
          border-radius: 50%;
          margin-right: 5px;
          margin-bottom: 2px;
          opacity: 1;
        }

        .gray {
          width: 8px;
          height: 8px;
          background: #c9cdd4;
          border-radius: 50%;
          margin-right: 5px;
          margin-bottom: 2px;
          opacity: 1;
        }

        .blues {
          color: #417fff;
        }

        .grays {
          color: #86909c;
        }
      }

      .operation {
        display: flex;

        .abierto {
          width: 100px;
          height: 32px;
          background-color: rgba(255, 255, 255, 0.5);
          border-radius: 2px 2px 2px 2px;
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 1;
          border: 1px solid rgba(0, 0, 0, 0.25);
          cursor: pointer;
        }

        .abierto:hover {
          background-color: #F4FAFF;
        }

        .bring {
          width: 100px;
          height: 32px;
          background: rgba(28, 145, 255, 0.95);
          border-radius: 2px 2px 2px 2px;
          margin-right: 14px;
          opacity: 1;
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }

        .bring:hover {
          background: rgba(28, 145, 255, 1);
        }
      }

      .bringinto {
        width: 126px;
        height: 32px;
        background-color: #FFFFFF;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #3370FF;
        opacity: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }

      .bringinto:hover {
        background-color: #F4FAFF;
      }
    }

    .EnterpriseLabel {
      padding: 13px 0;

      .tags {
        display: flex;
      }
    }

    .content-one {
      display: flex;
      margin-top: 20px;

      .one-a {
        width: 20%;
      }

      .one-b {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }

      .one-c {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }

      .one-d {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
        position: relative;
        z-index: 11;
      }

      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }

      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }

    .content-two {
      display: flex;
      margin-top: 14px;
      z-index: 9;

      .two-a {
        width: 20%;
      }

      .two-b {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }

      .two-c {
        width: 40%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }

      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }

      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }

    .content-three {
      display: flex;
      margin-top: 14px;

      .three {
        width: 100%;
      }

      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }

      .value {
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        cursor: pointer;
        font-weight: 400;
        color: #3370FF;
      }
    }
  }
}
</style>