<template>
  <!-- 搜索结果列表 -->
  <div>
    <div class="issue">
      <span>共{{ total }}条委托</span>
      <div>
        <el-button
          type="primary"
          @click="changPageList"
        >
          我的申请
        </el-button>
        <el-button
          type="primary"
          @click="addEntrust"
        >
          发起委托招商
        </el-button>
      </div>
    </div>
    <div
      v-for="(item, index) in enterpriseList"
      :key="index"
      class="single"
    >
      <div class="itemTop">
        订单号：{{ item.orderSn }}
      </div>
      <div class="itemBtn">
        <img
          src="https://static.idicc.cn/cdn/pangu/enterpriseIcon.png"
          class="iconImg"
        >
        <div class="content">
          <div class="name">
            <div style="display: flex;align-items: center;">
              <span>{{ item.enterprise }}</span>
              <div
                class="stateBtn"
              >
                {{ stateShow(item.state) }}
              </div>
            </div>
          </div>
          <div class="content-one">
            <div class="one-a">
              <span class="key">期望对接时间：</span>
              <span class="value">{{ item.exceptedDatetime }}</span>
            </div>
            <div class="one-b">
              <span class="key">招商对接人：</span>
              <span class="value">{{ item.contact }}</span>
            </div>
            <div class="one-c">
              <span class="key">联系方式：</span>
              <span class="value">{{ item.contactPhone }}</span>
            </div>
          </div>
          <div class="operationBtn">
            <el-button
              type="text"
              @click="Entrydetails(item)"
            >
              详情
            </el-button>
            <el-button
              v-if="item.state == 1"
              type="text"
              @click="retreat(item)"
            >
              退单
            </el-button>
            <el-button
              v-if="item.state == 2"
              type="text"
              @click="todosthFn(item)"
            >
              催办
            </el-button>
            <el-button
              v-if="item.state == 3"
              type="text"
              @click="commentFn(item)"
            >
              评论
            </el-button>
            <el-button
              v-if="item.state == 9"
              type="text"
              @click="examineFn(item)"
            >
              结单审批
            </el-button>
          </div>
          <div class="content-two">
            <div class="two-a">
              <span class="key">金额：</span>
              <span class="value">{{ item.amount }}</span>
            </div>
            <div class="two-b">
              <span class="key">招商需求：：</span>
              <el-tooltip
                effect="dark"
                :visible-arrow="false"
                placement="top"
                :content="item.note"
              >
                <span class="value">{{ item.note }}</span>
              </el-tooltip>
            </div>
            <div class="two-c">
              <span class="key">所属机构：</span>
              <span class="value">{{ item.orgName }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span
      v-if="enterpriseList.length == 0 && showNodata"
      style="
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 30px;
        font-size: 18px;
      "
    >
      <span style="padding-top: 30px">暂无数据</span></span>
    <div
      v-if="showNodata"
      class="ye"
    >
      <el-pagination
        :current-page.sync="formInline.pageNum"
        :page-sizes="[5, 10, 20, 50]"
        :page-size.sync="formInline.pageSize"
        :total="+total"
        layout="total,sizes, prev, pager, next, jumper"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
    <addEntrust
      v-if="isDrawer"
      :is-drawer.sync="isDrawer"
      @updataList="updataList"
    />
    <comment
      v-if="commentDig"
      :comment-dig.sync="commentDig"
      :operation-data="operationData"
      @updataList="updataList"
    />
    <examine
      v-if="examineDig"
      :examine-dig.sync="examineDig"
      :operation-data="operationData"
      @updataList="updataList"
    />
  </div>
</template>
<script>
import { stateList, enterpriseType } from './publicOptions';
import { billingListAPI, closeAPI, reminderAPI } from '@/api/xiaoAI';
import addEntrust from './Drawer/addEntrust.vue';
import comment from './Drawer/comment.vue';
import examine from './Drawer/examine.vue';
export default {
  name: 'EnterpriseList',
  components: {
    addEntrust,
    comment,
    examine,
  },
  data() {
    return {
      stateList: stateList,
      enterpriseType: enterpriseType,
      enterpriseList: [],
      commentDig: false,
      examineDig: false,
      showNodata: false,
      total: 0,
      formInline: {
        pageNum: 1,
        pageSize: 5,
      },
      operationData: {},
      isDrawer: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    changPageList() {
      this.$emit('changPageList', 2);
      document.documentElement.scrollTop = 0;
    },
    // 委托详情
    Entrydetails(item) {
      this.$emit('godemandDel', item.id, 3);
      document.documentElement.scrollTop = 0;
    },
    examineFn(item) {
      this.operationData = item;
      this.examineDig = true;
    },
    commentFn(item) {
      this.operationData = item;
      this.commentDig = true;
    },
    todosthFn(item) {
      this.$confirm(
        `该订单已被产业顾问${item.receiveContact}认领，但未提交跟进信息，是否进行催办？`,
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          await reminderAPI({
            entrustId: item.id,
          });
          this.$message.success('催办成功！');
          this.getList();
        })
        .catch(() => {});
    },
    retreat(item) {
      this.$confirm(
        '对于尚未被认领的招商委托，您有权申请退单。一旦退单请求得到处理，原先支付的金额将在15个工作日内以原路径退还至您的账户。退单后，相应的招商流程将自动终止，不再继续进行。',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          await closeAPI({
            entrustId: item.id,
          });
          this.$message.success('退单成功！');
          this.getList();
        })
        .catch(() => {});
    },
    updataList() {
      this.formInline.pageNum = 1;
      this.getList();
    },
    addEntrust() {
      this.isDrawer = true;
    },
    async getList() {
      try {
        let investmentCompany = JSON.parse(
          localStorage.getItem('investmentCompany') || '{}'
        );
        if (investmentCompany?.uniCode||investmentCompany?.enterpriseName) {
          this.addEntrust();
        }
        const res = await billingListAPI({
          pageNum: this.formInline.pageNum,
          pageSize: this.formInline.pageSize,
        });
        this.enterpriseList = res.result.records;
        this.total = res.result.total;
      } finally {
        this.showNodata = true;
      }
    },
    stateShow(state) {
      let text = '未知状态';
      this.stateList.map((it) => {
        if (it.value == state) {
          text = it.label;
        }
      });
      return text;
    },
    showEnterpriseCategory(type) {
      let showType = '其他企业';
      this.enterpriseType.map((it) => {
        if (type == it.value) {
          showType = it.label;
        }
      });
      return showType;
    },
    stateClass(state) {
      if (state == 13 || state == 11) {
        return 'orange';
      } else if (state == 3 || state == 4 || state == 6 || state == 7) {
        return 'green';
      } else if (state == 12) {
        return 'red';
      } else if (state == 5) {
        return 'gray';
      } else {
        return 'blue';
      }
      //产业顾问申诉处理中没有确认颜色 else默认蓝色
    },
    checkCanle() {
      this.check = false;
    },
    clickCheck(item, state) {
      this.checkState = state;
      this.checkData = item;
      this.check = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.issue {
  display: flex;
  justify-content: space-between;
  padding: 16px 30px;
  align-items: center;
  font-size: 16px;
}
.operationBtn {
  position: absolute;
  top: 51px;
  left: 86%;
  display: flex;
}
.dropdow {
  white-space: nowrap;
}

.noinformation {
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}

.abierto {
  width: 100px;
  height: 32px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 2px 2px 2px 2px;
  font-size: 14px;
  font-family: Source Han Sans CN-Normal, Source Han Sans CN;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  border: 1px solid rgba(0, 0, 0, 0.25);
  cursor: pointer;
}

.abierto:hover {
  background-color: #f4faff;
}

.single {
  width: calc(100% - 48px);
  padding: 0 !important;
  margin-left: 24px;
  height: 156px;
  display: flex;
  border-bottom: 1px solid #e9e9e9;
  position: relative;
  flex-direction: column;
  .itemTop {
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    height: 20px;
    display: flex;
    align-items: center;
    margin-top: 10px;
    position: absolute;
  }
  .itemBtn {
    display: flex;
    margin-top: 20px;
  }
  .stateBtn {
    width: auto;
    padding: 0 4px;
    height: 20px;
    z-index: 11;
    background-color: #EAEAFF;
    border-radius: 2px;
    font-weight: 400;
    font-size: 12px;
    color: #3370FF;
    margin-left: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .blue {
    //background-color: #3370ff;
    color: #3370ff;
  }
  .orange {
    //background-color: #fa9600;
    color: #fa9600;
  }
  .green {
    //background-color: #19b21e;
    color: #19b21e;
  }
  .gray {
    //background-color: #b7bfc7;
    color: #b7bfc7;
  }
  .red {
    //background-color: #fc474c;
    color: #fc474c;
  }

  .iconImg {
    width: 32px;
    height: 32px;
    margin-top: 28px;
    margin-right: 22px;
  }

  .content {
    margin-top: 32px;
    width: calc(100% - 52px);

    .name {
      width: 100%;
      max-width: 100%;
      display: flex;
      height: 18px;
      justify-content: space-between;
      font-size: 18px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);

      .tag {
        display: flex;
        padding-left: 10px;
        margin-top: 3px;

        .industryTag {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11px;
          padding: 0 6px;
          color: #ff7d00;
          background: #fff2e6;
          margin-right: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
        }
      }

      .state {
        width: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 4px;
        margin-left: 8px;
        font-size: 13px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;

        .blue {
          width: 8px;
          height: 8px;
          background: #417fff;
          border-radius: 50%;
          margin-right: 5px;
          margin-bottom: 2px;
          opacity: 1;
        }

        .gray {
          width: 8px;
          height: 8px;
          background: #c9cdd4;
          border-radius: 50%;
          margin-right: 5px;
          margin-bottom: 2px;
          opacity: 1;
        }

        .blues {
          color: #417fff;
        }

        .grays {
          color: #86909c;
        }
      }

      .operation {
        display: flex;

        .abierto {
          width: 100px;
          height: 32px;
          background-color: rgba(255, 255, 255, 0.5);
          border-radius: 2px 2px 2px 2px;
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 1;
          border: 1px solid rgba(0, 0, 0, 0.25);
          cursor: pointer;
        }

        .abierto:hover {
          background-color: #f4faff;
        }

        .bring {
          width: 100px;
          height: 32px;
          background: rgba(28, 145, 255, 0.95);
          border-radius: 2px 2px 2px 2px;
          margin-right: 14px;
          opacity: 1;
          font-size: 14px;
          font-family: Source Han Sans CN-Normal, Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }

        .bring:hover {
          background: rgba(28, 145, 255, 1);
        }
      }

      .bringinto {
        width: 126px;
        height: 32px;
        background-color: #ffffff;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #3370ff;
        opacity: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }

      .bringinto:hover {
        background-color: #f4faff;
      }
    }

    .EnterpriseLabel {
      padding: 13px 0;

      .tags {
        display: flex;
      }
    }

    .content-one {
      display: flex;
      margin-top: 20px;

      .one-a {
        width: 20%;
      }

      .one-b {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }

      .one-c {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }

      .one-d {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
        position: relative;
        z-index: 11;
      }
      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }

      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }

    .content-two {
      display: flex;
      margin-top: 14px;
      z-index: 9;

      .two-a {
        width: 20%;
      }

      .two-b {
        width: 20%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }

      .two-c {
        width: 40%;
        white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 20%;
        text-overflow: ellipsis;
      }

      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }

      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }

    .content-three {
      display: flex;
      margin-top: 14px;

      .three {
        width: 100%;
      }

      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }

      .value {
        font-size: 14px;
        font-family: Abel-Regular, Abel;
        cursor: pointer;
        font-weight: 400;
        color: #3370ff;
      }
    }
  }
}
</style>
