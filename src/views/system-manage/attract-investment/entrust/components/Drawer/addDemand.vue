<template>
  <div>
    <el-drawer
      title="需求发布"
      :visible="isDrawer"
      :before-close="handleClose"
      :wrapper-closable="false"
    >
      <div style="width: 90%; margin-left: 5%">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          size="mini"
          label-width="160px"
          class="demo-ruleForm"
        >
          <el-form-item
            label="招商需求方"
            prop="demander"
          >
            <el-input
              v-model="ruleForm.demander"
              placeholder="请输入招商需求方"
            />
          </el-form-item>
          <el-form-item
            label="招商需求企业所属产业链"
            class="appendToBodyFalse"
          >
            <el-cascader
              v-model="ruleForm.chainIds"
              :props="{ multiple: true, emitPath: false }"
              collapse-tags
              :options="industryList"
              :show-all-levels="false"
              @change="handleChange"
            />
          </el-form-item>
          <el-form-item
            label="招商需求企业所属区域"
            class="appendToBodyFalse"
          >
            <el-select
              v-model="ruleForm.addressCode"
              multiple
              collapse-tags
              :multiple-limit="3"
              :popper-append-to-body="false"
              placeholder="请选择"
            >
              <el-option
                v-for="item in cityList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="招商需求企业类型"
            class="appendToBodyFalse"
          >
            <el-select
              v-model="ruleForm.enterpriseType"
              multiple
              collapse-tags
              :multiple-limit="3"
              :popper-append-to-body="false"
              placeholder="请选择"
            >
              <el-option
                v-for="item in enterpriseTypeList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="招商需求企业融资阶段"
            class="appendToBodyFalse"
          >
            <el-select
              v-model="ruleForm.enterpriseFinance"
              multiple
              collapse-tags
              :popper-append-to-body="false"
              :multiple-limit="3"
              placeholder="请选择"
            >
              <el-option
                v-for="item in enterpriseFinanceList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="招商需求描述"
            prop="demandDescribe"
          >
            <el-input
              v-model="ruleForm.demandDescribe"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="请输入招商需求描述"
            />
          </el-form-item>
          <el-form-item class="submit-button">
            <el-button @click="handleClose">
              取消
            </el-button>
            <el-button
              v-loading="btnLoading"
              class="btn"
              @click="preserve"
            >
              保存
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { cityAPI } from "@/api/city";
import {
  enumListAPI,
  industrychainListAPI,
  demandSubmitAPI
} from "@/api/xiaoAI";
export default {
  name: "DetailedLIstRight",
  props: {
    isDrawer: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ruleForm: {
        demander: "",
        demandDescribe: "",
      },
      btnLoading: false,
      enterpriseFinanceList: [],
      enterpriseTypeList: [],
      industryList: [],
      cityList: [],
      rules: {
        demander: [{ required: true, message: "请输入招商需求方名称", trigger: "blur" }],
        demandDescribe: [{ required: true, message: "请输入招商需求描述", trigger: "blur" }],
      },
    };
  },
  async created() {
    this.getEnumList()
    this.getCityList()
    this.getindustrychainList()
  },
  methods: {
    handleChange(value) {
      if (value.length > 3) {
        this.$message.warning(`最多选择3条产业链`);
        this.ruleForm.chainIds = value.slice(0, 3);
      } else {
        this.ruleForm.chainIds = value;
      }
    },
    async getindustrychainList() {
      const res = await industrychainListAPI({
        capType: 1,
      });
      this.industryList = res.result.map(item => {
        return {
          label: item.categoryName,
          value: item.categoryId,
          children: item.chainList.map(it => {
            return {
              label: it.chainName,
              value: it.chainId,
            }
          })
        }
      })
    },
    async getCityList() {
      const res = await cityAPI({
        type: 1,
      });
      this.cityList = res.result;
    },
    async getEnumList() {
      const res = await enumListAPI();
      this.enterpriseFinanceList = res.result.enterpriseFinanceList//融资阶段
      this.enterpriseTypeList = res.result.enterpriseTypeList//企业类型
    },
    // 关闭
    handleClose() {
      this.$emit("update:isDrawer", false);
    },
    // 保存
    async preserve() {
      let data = {
        demander: this.ruleForm.demander,
        demandDescribe: this.ruleForm.demandDescribe,
        chainIds: this.ruleForm.chainIds,
        chainNames: this.ruleForm.chainIds?.map(value => {
          const foundItem = this.industryList.find(item => {
            return item.children.find(child => child.value === value);
          });
          return foundItem ? foundItem.children.find(child => child.value === value).label : null;
        }),
        addressCode: this.ruleForm.addressCode,
        addressName: this.cityList?.map(it => {
          const matchedItem = this.ruleForm.addressCode.find(i => i === it.code);
          return matchedItem ? it.name : null;
        }).filter(name => name !== null),
        enterpriseType: this.ruleForm.enterpriseType,
        enterpriseFinance: this.ruleForm.enterpriseFinance,
      }
      try {
        this.btnLoading = true;
        await this.$refs.ruleForm.validate();
        const res = await demandSubmitAPI(data)
        this.$message.success("需求发布成功！");
        this.handleClose();
        this.$emit("updataList");
      } catch (error) {
        // console.log(error);
      } finally {
        this.btnLoading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}

.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}

.btn:focus {
  background: #3370FF;
  color: rgba(255, 255, 255, 0.85);
}

.submit-button {
  margin-left: 90px;
  margin-top: 20px;
}

::v-deep {
  .el-drawer__header {
    font-size: 20px;
    color: #000;
  }

  .el-input__inner {
    width: 240px;
  }
}
</style>
<style lang="scss">
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #1684fc !important;
  border-color: #1684fc !important;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #1684fc !important;
  border-color: #1684fc !important;
}

.el-radio__input.is-checked .el-radio__inner::after {
  background-color: #1684fc;
  width: 7px;
  height: 7px;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #1684fc !important;
  background: #fff !important;
}

.el-radio__input.is-disabled.is-checked .el-radio__inner::after {
  background-color: #1684fc !important;
  opacity: 0.6;
}
</style>