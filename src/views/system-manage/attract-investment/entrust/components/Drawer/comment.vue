<template>
  <div>
    <el-drawer
      title="评论"
      :visible="commentDig"
      :before-close="handleClose"
      :wrapper-closable="false"
    >
      <div style="width: 90%; margin-left: 5%">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          size="mini"
          label-width="160px"
          class="demo-ruleForm"
        >
          <el-form-item
            label="评论人:"
          >
            {{ $store.getters.user.realName }}
          </el-form-item>
          <el-form-item
            label="评论时间:"
          >
            {{ operationTime }}
          </el-form-item>
          <el-form-item
            label="评论信息:"
            prop="note"
          >
            <el-input
              v-model="ruleForm.note"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="请输入评论信息"
            />
          </el-form-item>
          <el-form-item class="submit-button">
            <el-button @click="handleClose">
              取消
            </el-button>
            <el-button
              v-loading="btnLoading"
              class="btn"
              @click="preserve"
            >
              保存
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { cityAPI } from "@/api/city";
import {
  enumListAPI,
  industrychainListAPI,
  demandSubmitAPI,
  commentAPI
} from "@/api/xiaoAI";
export default {
  name: "DetailedLIstRight",
  props: {
    commentDig: {
      type: Boolean,
      default: false,
    },
    operationData: {
      type: Object,
      default: ()=>{},
    },
  },
  data() {
    return {
      ruleForm: {
        note: "",
      },
      btnLoading: false,
      enterpriseFinanceList: [],
      enterpriseTypeList: [],
      operationTime: '',
      industryList: [],
      cityList: [],
      rules: {
        note: [{ required: true, message: "请输入评论信息", trigger: "blur" }],
      },
    };
  },
  async created() {
   this.operationTime = this.formatDate()
  },
  methods: {
    formatDate() {
      let date = new Date()
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${year}-${month}-${day}   ${hours}:${minutes}:${seconds}`;
    },
    // 关闭
    handleClose() {
      this.$emit("update:commentDig", false);
    },
    // 保存
    async preserve() {
      let data = {
        entrustId: this.operationData.id,
        content: this.ruleForm.note,
      }
      try {
        this.btnLoading = true;
        await this.$refs.ruleForm.validate();
        const res = await commentAPI(data)
        this.$message.success("评论成功！");
        this.handleClose();
        this.$emit("updataList");
      } catch (error) {
        console.log(error);
      } finally {
        this.btnLoading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}

.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}

.btn:focus {
  background: #3370FF;
  color: rgba(255, 255, 255, 0.85);
}

.submit-button {
  margin-left: 90px;
  margin-top: 20px;
}

::v-deep {
  .el-drawer__header {
    font-size: 20px;
    color: #000;
  }

  .el-input__inner {
    width: 240px;
  }
}
</style>
<style lang="scss">
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #1684fc !important;
  border-color: #1684fc !important;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #1684fc !important;
  border-color: #1684fc !important;
}

.el-radio__input.is-checked .el-radio__inner::after {
  background-color: #1684fc;
  width: 7px;
  height: 7px;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #1684fc !important;
  background: #fff !important;
}

.el-radio__input.is-disabled.is-checked .el-radio__inner::after {
  background-color: #1684fc !important;
  opacity: 0.6;
}
</style>