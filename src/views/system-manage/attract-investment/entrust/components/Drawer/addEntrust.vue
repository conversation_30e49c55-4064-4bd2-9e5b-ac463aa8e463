<template>
  <div>
    <el-drawer
      title="发起委托招商"
      :visible="isDrawer"
      :before-close="handleClose"
      :wrapper-closable="false"
    >
      <div
        v-show="!centerDialogVisible"
        style="width: 90%; margin-left: 5%"
      >
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          size="mini"
          label-width="160px"
          class="demo-ruleForm"
        >
          <el-form-item
            label="意向企业"
            prop="enterprise"
          >
            <span v-if="oneClick">{{ ruleForm.enterprise }}</span>
            <el-autocomplete
              v-else
              ref="myAutocomplete"
              v-model="ruleForm.enterprise"
              :popper-append-to-body="false"
              :fetch-suggestions="querySearchAsync"
              placeholder="请输入意向企业名称"
              :disabled="investmentCompany"
              @select="handleSelect"
            />
          </el-form-item>
          <el-form-item
            v-if="!oneClick"
            label="企业社会信用代码"
            prop="enterpriseUniCode"
          >
            <el-input
              v-model="ruleForm.enterpriseUniCode"
              :disabled="investmentCompany"
              placeholder="请输入社会信用代码"
            />
          </el-form-item>
          <el-form-item
            label="期望对接时间"
            class="appendToBodyFalse"
            prop="exceptedDatetime"
          >
            <el-date-picker
              v-model="ruleForm.exceptedDatetime"
              :append-to-body="false"
              type="date"
              value-format="timestamp"
              :picker-options="pickerOptions"
              placeholder="选择日期"
            />
          </el-form-item>
          <el-form-item
            label="招商需求"
            prop="note"
          >
            <el-input
              v-model="ruleForm.note"
              placeholder="请输招商需求"
            />
          </el-form-item>
          <el-form-item
            label="招商对接人"
            prop="contact"
          >
            <el-input
              v-model="ruleForm.contact"
              placeholder="请输招商对接人"
            />
          </el-form-item>
          <el-form-item
            label="联系方式"
            prop="contactPhone"
          >
            <el-input
              v-model="ruleForm.contactPhone"
              placeholder="联系方式"
            />
          </el-form-item>
          <div class="Operationitem">
            <div class="priceBox">
              <div class="prices">
                <span class="symbol">￥</span>{{ priceText.amount || "-"
                }}<span
                  v-if="priceText.name"
                  class="type"
                >| {{ priceText.name }}</span>
              </div>
              <div
                class="BillingRule"
                @click="BillingRuleFn()"
              >
                计费规则 >
              </div>
            </div>
          </div>
          <div class="Operationitem">
            <el-button @click="handleClose">
              取消
            </el-button>
            <el-button
              v-loading="btnLoading"
              class="btn"
              @click="submit"
            >
              发起委托
            </el-button>
          </div>
        </el-form>
      </div>
      <div
        v-if="centerDialogVisible"
        class="codeBox"
      >
        <div
          class="CancelPayment"
          @click="CancelPayment"
        >
          取消支付
        </div>
        <div class="hintText">
          请使用微信扫码支付
        </div>
        <div
          id="qrcode"
          ref="qrcode"
          class="qrcode"
        />
      </div>
      <el-drawer
        title="哒达招商招商对接须知"
        :append-to-body="true"
        size="25%"
        :before-close="canelnotice"
        :visible.sync="noticePop"
      >
        <div class="applyaffirm">
          <image
            src="https://static.idicc.cn/cdn/aiChat/applet/entrustHead.png"
            class="entrustHead"
          />
          <div class="p1">
            哒达招商招商对接须知
          </div>
          <div class="p2">
            尊敬的委托人：
            <br>感谢您选择哒达招商作为您的招商伙伴。以下是您需要了解的关键事项：<br>
            <span class="panel">1.费用匹配：</span>招商委托费用将根据您的企业类型自动匹配。费用标准：{{
              PriceCopy
            }}。<br>
            <span class="panel">2.退单自由：</span>在产业顾问认领订单前，您可随时自行退单。<br>
            <span class="panel">3.订单管理：</span>若顾问一周内未填写跟进信息，订单将自动退回待认领状态，您仍可退单。<br>
            <!-- <span class="panel">4.跟进信息自动审核：</span>顾问提交跟进信息后，若您未及时审核，我们将在五个工作日后自动审核通过，待运营人员审核。<br> -->
            <span class="panel">4.结单自动审核：</span>顾问提交结单申请后，若您未及时审核，我们将在五个工作日后自动审核通过，待运营人员审核。<br>
            <span class="panel">5.招商挑战：</span>请理解招商工作的不确定性顾问可能无法完全符合您的预期时间表。<br>
            <span class="panel">6.退费承诺：</span>订单退单后，我们将在15个工作日内将费用原路退回到您的账户上，此须知旨在确
            保您对我们的服务有清晰的认识。如您同意以上条款，我们将全力以赴协助您完成招商工作。<br>
            <span class="inscribe"> 【艾瑞数云科技】</span>
          </div>
          <div class="check">
            <img
              v-if="ischeck"
              src="https://static.idicc.cn/cdn/pangu/iconYes2.png"
              class="iconYes"
              @click="checkFn"
            >
            <div
              v-else
              class="iconOn"
              @click="checkFn"
            />
            我已阅读并同意上述信息
          </div>
          <div class="title">
            <div class="leftWire" />
            支付方式
            <div class="rightWire" />
          </div>
          <div class="typeBox">
            <div
              v-for="(item, index) in patternPaymentList"
              :key="index"
              class="typeItem"
              @click="changeType(item.value)"
            >
              <img
                class="image"
                :src="
                  patternPaymentId == item.value
                    ? 'https://static.idicc.cn/cdn/pangu/success.png'
                    : 'https://static.idicc.cn/cdn/pangu/nosuccess.png'
                "
              ><span class="name">{{ item.name }}</span>
            </div>
          </div>
          <div
            v-if="patternPaymentId == 0"
            class="followInput2"
          >
            <el-input
              v-model="reason"
              trim="all"
              :clearable="false"
              placeholder="请输入申请理由"
            />
          </div>
          <div
            class="entrustBtn"
            @click="preserve"
          >
            发起委托
          </div>
        </div>
      </el-drawer>
      <el-drawer
        title="计费规则"
        :append-to-body="true"
        size="25%"
        :before-close="canel"
        :visible.sync="innerDrawer"
      >
        <div class="page-container">
          <div class="content-wrapper">
            <div class="box">
              <div class="text-box">
                <div class="headline">
                  费用匹配
                </div>
                <div class="t2">
                  招商委托费用将根据您的企业类型自动匹配
                </div>
                <div class="t1">
                  <div
                    v-for="(item, index) in priceList"
                    :key="index"
                    class="price"
                  >
                    {{ item.name }}<span>{{ item.amount }}元</span>
                  </div>
                </div>
                <div class="t4">
                  费用说明：招商顾问费用指平台旗下产业顾问为委托方建立与目标企业的沟通渠道，并促成委托方与目标企业正式沟通洽谈，委托方支付平台的服务费。
                </div>
              </div>
              <div class="text-box">
                <div
                  style="margin-bottom: 12px"
                  class="headline"
                >
                  退单手续费规则
                </div>
                <div class="t44">
                  如产业顾问已经接受委托并跟进的情况下，因委托方单方原因导致的退单，委托人可能需要承担委托费的40%的费用，作为对产业顾问的补偿。
                </div>
                <div class="headline22">
                  您不需要承担手续费的情况
                </div>
                <div class="t4">
                  1.委托订单发布后，未认领情况下进行的退单；<br>
                  2.委托订单发布后，到了期望对接时间仍无人认领的自动退单；<br>
                  3.委托订单发布后，已认领一周内未提交跟进信息且未超过对接时间发生的自动退单。<br>
                </div>
              </div>
              <div style="height: 30px" />
            </div>
          </div>
        </div>
      </el-drawer>
    </el-drawer>
  </div>
</template>

<script>
import { cityAPI } from "@/api/city";
import { newEnterpriseSearchAPI } from "@/api/attractInvestment";
import QRCode from "qrcode2";
import {
  enumListAPI,
  industrychainListAPI,
  addSubmit,
  getEnterpriseLabelAPI,
  billingSubmit,
  paymentgetBillingStatusAPI,
  paymentApply,
  ownList,
} from "@/api/xiaoAI";
export default {
  name: "DetailedLIstRight",
  props: {
    isDrawer: {
      type: Boolean,
      default: false,
    },
    oneClick: {
      type: Boolean,
      default: false,
    },
    echo: {
      type: Boolean,
      default: false,
    },
    echoItem: {
      type: Object,
      default: () => {},
    },
    operationItem: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      innerDrawer: false,
      centerDialogVisible: false,
      reason: "",
      ischeck: false,
      ruleForm: {},
      btnLoading: false,
      noticePop: false,
      enterpriseFinanceList: [],
      patternPaymentList: [
        {
          name: "机构支付",
          value: 0,
        },
        {
          name: "个人支付",
          value: 1,
        },
      ],
      patternPaymentId: 0,
      enterpriseTypeList: [],
      industryList: [],
      cityList: [],
      priceText: {},
      priceList: [],
      billNo: "",
      packageStr: "",
      PriceCopy: "",
      intervalRef: null,
      pickerOptions: {
        disabledDate(time) {
          // 计算7天后的日期（去掉时间部分）
          const minDate = new Date();
          minDate.setDate(minDate.getDate() + 7);
          minDate.setHours(0, 0, 0, 0);

          // 禁用7天内（含今天）的所有日期
          return time.getTime() < minDate.getTime();
        },
      },
      rules: {
        enterprise: [
          { required: true, message: "请输入意向企业", trigger: "blur" },
        ],
        exceptedDatetime: [
          { required: true, message: "请选择日期", trigger: "change" },
        ],
        enterpriseUniCode: [
          {
            required: true,
            message: "请输入社会统一信用代码",
            trigger: "blur",
          },
        ],
        contact: [
          { required: true, message: "请输入对接人姓名", trigger: "blur" },
        ],
        contactPhone: [
          { required: true, message: "请输入联系方式", trigger: "blur" },
          {
            pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/,
            message: "请输入合法手机号码",
            trigger: "blur",
          },
        ],
      },
      investmentCompany: false,
    };
  },
  watch: {
    "ruleForm.enterpriseUniCode": {
      handler(newValue) {
        this.getmoney(newValue);
      },
    },
  },
  async created() {
    this.getEnumList();
    this.getCityList();
    this.getindustrychainList();
    this.getOwn();
    this.init();
    if (this.oneClick) {
      // console.log(this.operationItem);
      this.ruleForm.enterprise = this.operationItem.enterpriseName;
      this.ruleForm.recommendId = this.operationItem.recommendId;
      this.getmoney(this.operationItem.enterpriseUniCode);
    } else if (this.echo) {
      this.ruleForm.enterprise = this.echoItem.enterpriseName;
      this.ruleForm.enterpriseUniCode = this.echoItem.uniCode;
      this.getmoney(this.echoItem.uniCode);
    }
  },
  methods: {
    init() {
      let investmentCompany = JSON.parse(
        localStorage.getItem("investmentCompany") || "{}"
      );
      if (investmentCompany?.uniCode || investmentCompany?.enterpriseName) {
        this.investmentCompany = true;
        this.ruleForm.enterpriseUniCode = investmentCompany?.uniCode;
        this.ruleForm.enterprise = investmentCompany?.enterpriseName;
      } else {
        this.investmentCompany = false;
      }
    },
    async getOwn() {
      const res = await ownList();
      let orgName = res.result.selected?.orgName;
      let isDefault = res.result.selected?.isDefault;
      if (isDefault || !orgName) {
        this.patternPaymentList = [
          {
            name: "个人支付",
            value: 1,
          },
        ];
        this.patternPaymentId = 1;
      }
    },
    handleSelect(item) {
      this.ruleForm.enterpriseUniCode = item.uniCode;
      this.getmoney(this.ruleForm.enterpriseUniCode);
    },
    querySearchAsync(queryString, cb) {
      if (!queryString) {
        return cb([]);
      }
      newEnterpriseSearchAPI({
        keyword: queryString,
        pageNum: 1,
        pageSize: 10,
      })
        .then((response) => {
          const companies = response.result.records.map((item) => {
            return {
              value: item.enterpriseName,
              companyId: item.id,
              uniCode: item.unifiedSocialCreditCode,
            };
          });
          cb(companies);
          this.$refs.myAutocomplete.$refs.suggestions.popperJS.update();
        })
        .catch((error) => {
          console.error("Error fetching companies:", error);
        });
    },
    changeType(id) {
      this.patternPaymentId = id;
    },
    checkFn() {
      this.ischeck = !this.ischeck;
    },
    async getEnumList() {
      const res = await enumListAPI();
      const mergedData = this.mergeByAmount(
        res.result.enterpriseAmountTypeList
      );
      this.priceList = mergedData.reverse();
      this.PriceCopy = this.mergeByAmount2(res.result.enterpriseAmountTypeList);
    },
    mergeByAmount(data) {
      const mergedData = {};
      data.forEach((item) => {
        if (!mergedData[item.amount]) {
          mergedData[item.amount] = {
            ...item,
          };
        } else {
          mergedData[item.amount].name += "、" + item.name;
        }
      });
      return Object.values(mergedData);
    },
    //处理文案
    mergeByAmount2(data) {
      const mergedData = {};
      data.forEach((item) => {
        if (!mergedData[item.amount]) {
          mergedData[item.amount] = item.name;
        } else {
          mergedData[item.amount] += "、" + item.name;
        }
      });
      let formattedData = Object.keys(mergedData).map((amount) => {
        return `${mergedData[amount]}${amount}元`;
      });
      formattedData = formattedData.reverse();
      const resultString = formattedData.join("、");
      return resultString;
    },
    CancelPayment() {
      clearInterval(this.intervalRef);
      this.centerDialogVisible = false;
    },
    startPolling() {
      this.intervalRef = setInterval(async () => {
        const res = await paymentgetBillingStatusAPI({
          billNo: this.billNo,
        });
        if (res.code == "SUCCESS") {
          if (res.result == 2) {
            this.handleClose();
            this.$message.success("支付成功！");
            this.$emit("updataList");
          }
        }
      }, 1500);
    },
    canel() {
      this.innerDrawer = false;
    },
    canelnotice() {
      this.noticePop = false;
    },
    BillingRuleFn() {
      this.innerDrawer = true;
    },
    //获取价格
    async getmoney(newValue) {
      let data = {
        enterpriseUniCode: newValue,
      };
      const res = await getEnterpriseLabelAPI(data);
      this.priceText = res.result;
    },
    handleChange(value) {
      if (value.length > 3) {
        this.$message.warning(`最多选择3条产业链`);
        this.ruleForm.chainIds = value.slice(0, 3);
      } else {
        this.ruleForm.chainIds = value;
      }
    },
    async getindustrychainList() {
      const res = await industrychainListAPI({
        capType: 1,
      });
      this.industryList = res.result.map((item) => {
        return {
          label: item.categoryName,
          value: item.categoryId,
          children: item.chainList.map((it) => {
            return {
              label: it.chainName,
              value: it.chainId,
            };
          }),
        };
      });
    },
    async getCityList() {
      const res = await cityAPI({
        type: 1,
      });
      this.cityList = res.result;
    },
    // 关闭
    handleClose() {
      this.investmentCompany = false;
      localStorage.removeItem("investmentCompany");
      clearInterval(this.intervalRef);
      this.$emit("update:isDrawer", false);
    },
    async submit() {
      await this.$refs.ruleForm.validate();
      this.noticePop = true;
    },
    // 保存
    async preserve() {
      let data = this.ruleForm;
      if (!this.ischeck) {
        this.$message.error(`请先勾选协议`);
        return;
      }
      if (!this.reason && this.patternPaymentId == 0) {
        this.$message.error(`请输入申请理由`);
        return;
      }
      try {
        this.btnLoading = true;
        await this.$refs.ruleForm.validate();
        const res = await addSubmit(data);
        if (res.code == "SUCCESS") {
          if (this.patternPaymentId == 0) {
            let data2 = {
              entrustId: res.result,
              reason: this.reason,
            };
            let res2 = await paymentApply(data2);
            if (res2.code == "SUCCESS") {
              this.handleClose();
              this.$message.success("发起委托申请已提交成功！");
              this.$emit("updataList");
            }
          } else {
            let data2 = {
              businessCode: "entrust",
              item: {
                entrustId: res.result,
              },
              method: 4,
            };
            let res2 = await billingSubmit(data2);
            if (res2.code == "SUCCESS") {
              this.billNo = res2.result.billNo;
              this.packageStr = res2.result.packageStr;
              this.noticePop = false;
              this.centerDialogVisible = true;
              setTimeout(() => {
                this.$nextTick(() => {
                  new QRCode(document.getElementById("qrcode"), {
                    text: this.packageStr,
                    width: 160,
                    height: 160,
                    correctLevel: QRCode.CorrectLevel.H,
                  });
                  this.startPolling();
                });
              }, 1000);
            }
          }
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.btnLoading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep {
  .el-popper {
    position: absolute !important;
    top: 0;
    left: 0;
  }
}
.bright {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 26px;
  height: 19px;
  z-index: 4;
}

.success {
  position: absolute;
  bottom: 5px;
  right: 1.5px;
  width: 16px;
  height: 16px;
  z-index: 5;
}

.title {
  width: 100%;
  font-weight: 500;
  font-size: 16px;
  color: #1d2129;
  margin-bottom: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title .rightWire {
  margin-left: 12px;
  width: 60px;
  height: 1.5px;
  display: flex;
  background: linear-gradient(270deg, rgba(29, 33, 41, 0) 0%, #616366 100%);
}

.title .leftWire {
  margin-right: 12px;
  width: 60px;
  height: 1.5px;
  display: flex;
  background: linear-gradient(270deg, #616366 0%, rgba(29, 33, 41, 0) 100%);
}

.typeBox {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 22px;
  box-sizing: border-box;
  margin-bottom: 12px;
  position: relative;
}

.typeBox .name {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 100%;
}

.typeItem {
  position: relative;
  cursor: pointer;

  .image {
    width: 128px;
    height: 51px;
    position: relative;
  }

  .name {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #000;
    font-size: 14px;
    text-align: center;
    z-index: 2;
    pointer-events: none;
  }
}

.entrustBtn {
  width: 263px;
  height: 36px;
  background: linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%), #3370ff;
  border-radius: 6px 6px 6px 6px;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
  cursor: pointer;
}

.entrustHead {
  width: 311px;
  height: 85px;
  z-index: 1;
  position: absolute;
  top: 0;
  pointer-events: none;
  border-radius: 16px 16px 0 0;
}

.applyaffirm {
  //width: 311px;
  height: auto;
  background: #ffffff;
  border-radius: 16px 16px 16px 16px;
  box-sizing: border-box;
  padding: 24px;
  display: flex;
  align-items: center;
  flex-direction: column;
  position: relative;
}

.applyaffirm .p1 {
  font-weight: 500;
  font-size: 16px;
  color: #102245;
  z-index: 3;
  margin-bottom: 24px;
}

.applyaffirm .p2 {
  font-weight: 400;
  font-size: 14px;
  color: #3f4a59;
  line-height: 20px;
  height: 300px;
  //overflow-y: scroll;
}

.applyaffirm .p2 .panel {
  font-weight: 600;
}

.applyaffirm .p2 .inscribe {
  float: right;
}

.applyaffirm .check {
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 12px;
  color: #323233;
  margin-top: 62px;
  margin-bottom: 16px;
}

.applyaffirm .check .iconYes,
.applyaffirm .check .iconOn {
  cursor: pointer;
  width: 18px;
  height: 18px;
  min-width: 18px;
  min-height: 18px;
  margin-right: 5px;
}

.applyaffirm .check .iconOn {
  border-radius: 50%;
  border: 1px solid #636c78;
  box-sizing: border-box;
}

.applyaffirm .affirm {
  width: 263px;
  height: 36px;
  background: linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%), #3370ff;
  border-radius: 6px 6px 6px 6px;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.applyaffirm .popBtn {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.applyaffirm .popBtn .affirm {
  width: 125.5px;
  height: 36px;
  background: linear-gradient(174deg, #aec6ff 0%, #1f61ff 100%), #3370ff;
  border-radius: 6px 6px 6px 6px;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.applyaffirm .popBtn .canleBt {
  width: 100%;
  height: 36px;
  background: #ffffff;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #ebedf0;
  font-weight: 400;
  font-size: 16px;
  color: #323233;
  display: flex;
  align-items: center;
  justify-content: center;
}

.codeBox {
  display: flex;
  flex-direction: column;
  align-items: center;

  .CancelPayment {
    width: 100%;
    font-size: 14px;
    font-weight: 500;
    margin-left: 30px;
    cursor: pointer;
  }

  .hintText {
    font-size: 18px;
    font-weight: 500;
    margin-top: 72px;
    margin-bottom: 25px;
  }
}

.Operationitem {
  display: flex;
  padding: 0 80px;
  width: 100%;
  box-sizing: border-box;
  margin-top: 32px;
}

.priceBox {
  display: flex;
  //align-items: center;
  flex-direction: column;
  justify-content: center;

  .symbol {
    font-weight: 500;
    font-size: 13px;
    color: #1d2129;
  }

  .prices {
    font-weight: 500;
    font-size: 20px;
    color: #1d2129;
    //margin-top: 12rpx;
  }

  .BillingRule {
    font-weight: 400;
    font-size: 11px;
    color: #3370ff;
    margin-top: 3px;
    cursor: pointer;
    margin-left: 4px;
  }

  .type {
    font-weight: 400;
    font-size: 12px;
    color: #86909c;
    margin-left: 3px;
  }
}

.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #3370ff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}

.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}

.btn:focus {
  background: #3370ff;
  color: rgba(255, 255, 255, 0.85);
}

.submit-button {
  margin-left: 90px;
  margin-top: 20px;
}

::v-deep {
  .el-drawer__header {
    font-size: 20px;
    color: #000;
  }

  .el-input__inner {
    width: 240px;
  }
}
</style>
<style lang="scss">
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #1684fc !important;
  border-color: #1684fc !important;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #1684fc !important;
  border-color: #1684fc !important;
}

.el-radio__input.is-checked .el-radio__inner::after {
  background-color: #1684fc;
  width: 7px;
  height: 7px;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #1684fc !important;
  background: #fff !important;
}

.el-radio__input.is-disabled.is-checked .el-radio__inner::after {
  background-color: #1684fc !important;
  opacity: 0.6;
}

.page-container {
  background-color: #fafcff;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.box {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.text-box {
  width: 100%;
  background: #ffffff;
  box-shadow: 0 4px 12px 0 #eef1f8;
  border-radius: 10px;
  padding: 15px;
  box-sizing: border-box;
}

.headline {
  font-weight: 500;
  font-size: 16px;
  color: #1d2129;
  margin-bottom: 10px;
}

.headline22 {
  font-weight: 500;
  font-size: 14px;
  color: #1d2129;
  margin: 16px 0 10px;
}

.t1 {
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  line-height: 28px;
  border-bottom: 1px solid #eef1f5;
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.price {
  display: flex;
  justify-content: space-between;
}

.t2 {
  font-weight: 400;
  font-size: 14px;
  color: #86909c;
  line-height: 22px;
  margin-bottom: 10px;
}

.t44 {
  font-weight: 400;
  font-size: 12px;
  color: #86909c;
  line-height: 18px;
  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid #eef1f5;
}

.t4 {
  font-weight: 400;
  font-size: 12px;
  color: #86909c;
  line-height: 18px;
}
</style>
