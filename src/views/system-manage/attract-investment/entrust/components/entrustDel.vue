<template>
  <!-- 企业详情 -->
  <div
    v-if="dataInfo.enterprise"
    class="box"
  >
    <div class="title">
      <div class="iconarrow">
        <i
          style="cursor: pointer"
          class="el-icon-arrow-left"
          @click="gobacka"
        />
      </div>
    </div>
    <div class="head">
      <div class="orderSn">
        订单号：{{ dataInfo.orderSn }}
      </div>
      <img
        src="https://static.idicc.cn/cdn/pangu/enterpriseIcon.png"
        class="iconImg"
      >
      <div class="content">
        <div class="enterprise">
          {{ dataInfo.enterprise }}
        </div>
        <div class="content-two">
          <div class="two-a">
            <span class="key">期望对接时间：</span>
            <span class="value">
              {{ dataInfo.exceptedDatetime }}
            </span>
          </div>
          <div class="two-a">
            <span class="key">招商对接人：</span>
            <span class="value">
              {{ dataInfo.contact }}</span>
          </div>
          <div class="two-a">
            <span class="key">联系方式：</span>
            <span class="value">
              {{ dataInfo.contactPhone }}
            </span>
          </div>
        </div>
        <div class="content-two">
          <div class="two-a">
            <span class="key">金额：</span>
            <span class="value">
              {{ dataInfo.amount }}
            </span>
          </div>
          <div class="two-a">
            <span class="key">招商需求：</span>
            <span
              class="value"
            >{{ dataInfo.note }}</span>
          </div>
          <div class="two-a">
            <span class="key">所属机构：</span>
            <span class="value">{{ dataInfo.orgName }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="scan">
      <div class="scan-top">
        <div class="scan-tab-con">
          <div class="scan-tab">
            <div
              v-for="(it, index) in tabList"
              :key="index"
              class="scan-tab-list"
              :class="CurrentSelectio == it.id ? 'on' : ''"
              @click="cut(it.id)"
            >
              {{ it.name }}
            </div>
          </div>
        </div>
      </div>
      <div
        v-show="CurrentSelectio == 1"
        class="contentBox"
      >
        <div
          v-for="(item, index) in dataInfo.logs"
          :key="index"
          class="contentItemBox"
        >
          <div class="Time">
            <div class="circle1" />
            <span class="remindTime">{{ item.startDatetime || item.gmtCreate }}</span>
            <span class="typeText"><span class="dot" />{{ item.statusName }}
            </span>
          </div>
          <div
            v-if="item.childObjectText == 'audit' && item.tip"
            style="font-weight: 400;font-size: 30rpx;color: #FF0000;margin-left: 36rpx;
						margin-bottom: 16rpx;"
          >
            <span>（超过5天，系统自动审批通过）</span>
          </div>
          <!-- 结单申请 -->
          <div
            v-if="item.childObjectText == 'apply' && item.apply"
            class="contentItem"
          >
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">申请人：</span>
              </div>
              <span class="value">{{ item.apply.applyName }}</span>
            </div>
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">结单说明：</span>
              </div>
              <span class="value">{{ item.apply.note }}</span>
            </div>
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">附件：</span>
              </div>
              <div class="annex">
                <div
                  v-for="(it, ind) in item.apply.attachUrls"
                  :key="ind"
                  @click="previewImg(item.apply.attachUrls, ind)"
                >
                  <img
                    :src="it"
                    class="annexImg"
                  >
                </div>
              </div>
            </div>
          </div>
          <!-- 申请审批 -->
          <div
            v-if="item.childObjectText == 'audit' && item.audit"
            class="contentItem"
          >
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">审批人：</span>
              </div>
              <span class="value">{{ item.audit.auditName }}</span>
            </div>
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">是否通过：</span>
              </div>
              <span class="value">{{ item.audit.auditStatusName }}</span>
            </div>
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">审批说明：</span>
              </div>
              <span class="value">{{ item.audit.note || '无' }}</span>
            </div>
          </div>
          <!-- 评论信息 -->
          <div
            v-if="item.childObjectText == 'comment' && item.comment"
            class="contentItem"
          >
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">委托人：</span>
              </div>
              <span class="value">{{ item.comment.name }}</span>
            </div>
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">评论信息：</span>
              </div>
              <span class="value">{{ item.comment.note }}</span>
            </div>
          </div>
          <!-- 认领信息 -->
          <div
            v-if="item.childObjectText == 'claim' && item.claim"
            class="contentItem"
          >
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">认领人：</span>
              </div>

              <span class="value">{{ item.claim.contact }}</span>
            </div>
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">联系方式：</span>
              </div>
              <span class="value">{{ item.claim.contactPhone }}</span>
            </div>
          </div>
          <!-- 跟进信息 -->
          <div
            v-if="item.childObjectText == 'follow' && item.follow"
            class="contentItem"
          >
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">提交人：</span>
              </div>
              <span class="value">{{ item.follow.createBy }}</span>
            </div>
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">企业对接人：</span>
              </div>
              <span class="value">{{ item.follow.enterpriseContact }}</span>
            </div>
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">职务：</span>
              </div>
              <span class="value">{{ item.follow.contactPosition }}</span>
            </div>
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">联系方式：</span>
              </div>
              <span class="value">{{ item.follow.contactPhone }}</span>
            </div>
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">跟进概述：</span>
              </div>
              <span class="value">{{ item.follow.note }}</span>
            </div>
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">附件：</span>
              </div>
              <div
                v-if="item.follow.attachUrls"
                class="annex"
              >
                <div
                  v-for="(it, ind) in item.follow.attachUrls"
                  :key="ind"
                  @click="previewImg(item.follow.attachUrls, ind)"
                >
                  <img
                    :src="it"
                    class="annexImg"
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-show="CurrentSelectio == 1 && !dataInfo.logs.length">
        <NoData />
      </div>
      <div
        v-show="CurrentSelectio == 2"
        class="contentBox"
      > 
        <div
          v-for="(item, index) in dataInfo.reminds"
          :key="index"
          class="contentItemBox"
        >
          <div class="Time">
            <div class="circle1" />
            <span class="remindTime">{{ item.remindTimeStr }}</span>
            <span class="typeText"><span class="dot" />{{ item.type }}
            </span>
          </div>
          <div class="contentItem">
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">委托人：</span>
              </div>
              <span class="value">{{ item.createBy }}</span>
            </div>
            <div class="Itemline">
              <div class="Itemleft">
                <span class="GrayCircle" />
                <span class="key">催办信息：</span>
              </div>
              <span class="value">{{ item.note }}</span>
            </div>
          </div>
        </div>
      </div>
      <div
        v-show="CurrentSelectio == 2 && !dataInfo.reminds.length"
      >
        <NoData />
      </div>
    </div>
  </div>
</template>

<script>
import { closeDelAPI } from "@/api/xiaoAI";
import NoData from '@/views/overview/components/component/noData3.vue';
export default {
  name: "DetailsEnterprise",
  components: {
    NoData
  },
  props: {
    entrustId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      dataInfo: {},
      myentrustList: [],
      CurrentSelectio: 1,
      tabList: [
        {
          name: "进度信息",
          id: 1,
        },
        {
          name: "催办信息",
          id: 2,
        },
      ]
    };
  },
  created() {
    document.body.style.overflow = "auto";
    this.$nextTick(() => {
      document.body.scrollTop = 0
      document.body.style.overflow = 'visible'
    })
    this.getDetail();
  },
  methods: {
    //tab切换
    cut(id) {
      this.CurrentSelectio = id;
    },
    async getDetail() {
      const res = await closeDelAPI({
        entrustId: this.entrustId
      });
      this.dataInfo = res.result;
    },
    gobacka() {
      this.$emit("goback");
    },
  },
};
</script>

<style lang="scss" scoped>
.circle1 {
  background-color: #3e80ff;
  position: relative;
  border: 4px solid #bedaff;
  width: 13px;
  height: 13px;
  z-index: 6;
  border-radius: 50%;
  margin-right: 14px;
}

.contentBox {
  width: 345px;
  /* height: 325px; */
  height: auto;
  /* overflow: scroll; */
  //background: #FFFFFF;
  //box-shadow: 0px 4px 12px 0px #EEF1F8;
  //border-radius: 10px 10px 10px 10px;
  padding: 17px 15px 15px 12px;
  box-sizing: border-box;

  .annex {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    .annexImg {
      width: 76px;
      height: 76px;
      border-radius: 6px;
      margin-right: 6px;
      margin-bottom: 6px;
    }
  }

  .contentItemBox {
    width: 100%;
    height: auto;
    position: relative;
  }

  .contentItemBox:last-of-type:before {
    content: '';
    display: none;
  }

  .contentItemBox:before {
    content: '';
    position: absolute;
    top: 15px;
    bottom: 0;
    left: 6px;
    width: 1px;
    height: 100%;
    background-color: #E5E6EB;
  }

  .contentItem {
    width: 600px;
    height: auto;
    background: #F7F8FA;
    border-radius: 6px 6px 6px 6px;
    padding: 15px;
    margin-left: 18px;
    box-sizing: border-box;
    padding-bottom: 3px;
    position: relative;

    .Itemleft {
      display: flex;
      align-items: center;
      height: 19px;
    }

    .Itemline {
      display: flex;
      margin-bottom: 12px;
    }

    .GrayCircle {
      width: 4px;
      height: 4px;
      min-width: 4px;
      min-height: 4px;
      background: #C9CDD4;
      border-radius: 50%;
      margin-right: 6px;
    }

    .key {
      font-weight: 400;
      font-size: 14px;
      color: #1D2129;
      white-space: nowrap;
    }

    .value {
      font-weight: 400;
      font-size: 14px;
      color: #4E5969;
    }

  }

  .Time {
    display: flex;
    height: 30px;
    align-items: center;
    margin-bottom: 6px;

    .remindTime {
      font-weight: 400;
      font-size: 13px;
      color: #4E5969;
      min-width: 40%;
    }

    .typeText {
      font-weight: 400;
      font-size: 14px;
      color: #1D2129;
      display: flex;
      align-items: center;
    }

    .dot {
      width: 6px;
      height: 6px;
      background: #3370FF;
      border-radius: 50%;
      margin-left: 16px;
      margin-right: 8px;
      min-width: 6px;
      min-height: 6px;
    }

  }
}

.scan {
  width: 100%;
  margin-top: 16px;
  background-color: #fff;
  border-radius: 10px;

  box-shadow: 0px 4px 12px 0px #EEF1F8;

  &-top {
    padding: 16px 0px;
    // background: #fff;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 10px 10px 0px 0px;
  }

  &-tab {
    display: flex;
    padding-left: 16px;

    &-con {
      display: flex;
      justify-content: space-between;
    }

    &-select {
      display: flex;
    }

    &-list {
      margin-right: 64px;
      font-size: 16px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      cursor: pointer;

      &.on {
        font-weight: bold;
        color: #3370FF;
        position: relative;

        &::after {
          content: "";
          width: 28px;
          height: 2px;
          background: #3370FF;
          position: absolute;
          left: 18px;
          bottom: -8px;
        }
      }
    }
  }

  &-con {
    margin: 16px 24px;
  }
}

.listBox {
  background-color: #fff;
  border-radius: 10px;
  opacity: 1;
  margin-top: 30px;
  box-shadow: 0px 4px 12px 0px #EEF1F8;

  .titleText {
    display: flex;
    justify-content: space-between;
    padding: 16px 30px;
    align-items: center;
    font-size: 16px;
    border-bottom: 1px solid #e9e9e9;
  }

  .operationBtn {
    position: absolute;
    top: 51px;
    right: 8%;
    display: flex;
  }
}

.dropdow {
  white-space: nowrap;
}

.box {
  padding-bottom: 20px;
  overflow-y: scroll;

  .manag {
    padding: 24px;
  }
}

.title {
  .iconarrow {
    color: #666;
    font-size: 20px;
    background: white;
    padding: 2px;
    width: 25px;
    height: 25px;
    border-radius: 16px;
  }
}

.head {
  width: 100%;
  margin-top: 16px;
  padding-top: 32px;
  padding-bottom: 12px;
  display: flex;
  background: #ffffff;
  border-radius: 10px;
  opacity: 1;
  box-shadow: 0px 4px 12px 0px #EEF1F8;
  position: relative;

  .orderSn {
    position: absolute;
    left: 30px;
    top: 30px;
  }

  .operation {
    position: absolute;
    display: flex;
    //margin-left: 80%;
    right: 5vw;
    margin-top: 22px;

    .attention {
      width: 70px;
      height: 32px;
      background-color: #fff;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid #3370FF;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      img {
        width: 18px;
        height: 18px;
        margin-right: 8px;
        margin-bottom: 2px;
      }

      span {
        color: #3370FF;
        font-weight: 400;
        font-size: 14px;
      }
    }

    .attention:hover {
      background-color: #F4FAFF;
    }

    .noattention {
      width: 70px;
      height: 32px;
      background: #f7f8fa;
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
    }

    .noattention:hover {
      background-color: #F4FAFF;
    }
  }

  .iconImg {
    width: 32px;
    height: 32px;
    margin-top: 28px;
    margin-left: 26px;
    margin-right: 22px;
  }

  .content {
    margin-top: 25px;
    width: 100%;

    .enterprise {
      font-size: 18px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 16px;
    }

    .name {
      display: flex;
      width: 70%;
      font-size: 18px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);

      .tag {
        display: flex;
        padding-left: 10px;
        margin-top: 3px;

        .industryTag {
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #3370FF;
          color: #3370FF;
          font-size: 11px;
          padding: 0 6px;
          margin-left: 8px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
        }
      }
    }

    .content-two {
      display: flex;
      margin-top: 4px;
      padding-bottom: 22px;

      .two-a {
        width: 30%;
      }

      .key {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
      }

      .value {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
      }
    }
  }
}
</style>