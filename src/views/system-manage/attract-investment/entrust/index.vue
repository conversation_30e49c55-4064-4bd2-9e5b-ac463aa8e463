<template>
  <!-- 我的企业管理 -->
  <div>
    <div v-show="isParticulars==1">
      <div
        v-if="whichs!==2"
        class="title"
      >
        <div class="scan">
          <div class="scan-top">
            <div class="scan-tab-con">
              <div class="scan-tab">
                <div
                  v-for="(it, index) in tabList"
                  :key="index"
                  class="scan-tab-list"
                  :class="whichs == it.id ? 'on' : ''"
                  @click="cut(it.id)"
                >
                  {{ it.name }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else
        style="cursor: pointer;"
        @click="whichs=0"
      >
        返回
      </div>
      <div class="list">
        <entrustList
          v-if="whichs==0"
          @godemandDel="godemandDel"
          @changPageList="changPageList"
        />
        <demandList
          v-if="whichs==1"
          @godemandDel="godemandDel"
        />
        <applyList
          v-if="whichs==2"
          @godemandDel="godemandDel"
        />
      </div>
    </div>
    <demandDel
      v-if="isParticulars==2"
      :demand-i-d="demandID"
      @goback="goback"
    />
    <entrustDel
      v-if="isParticulars==3"
      :entrust-id="demandID"
      @goback="goback"
    />
    <applyDel
      v-if="isParticulars==4"
      :entrust-id="demandID"
      @goback="goback"
    />
  </div>
</template>

<script>
import entrustList from "./components/entrustList.vue";
import demandList from "./components/demandList.vue";
import applyList from "./components/applyList.vue";
import demandDel from "./components/demandDel.vue";
import entrustDel from "./components/entrustDel.vue";
import applyDel from "./components/applyDel.vue";
export default {
  name: "IntentionQ",
  components: {
    entrustList,
    demandList,
    demandDel,
    entrustDel,
    applyList,
    applyDel
  },
  props: {
  },
  data() {
    return {
      whichs: 0,
      tabList: [
          {
          name: "我的委托",
          id: 0,
        },
        {
          name: "我的需求",
          id: 1,
        },
      ],
      isParticulars: 1,
      demandID: "", //需求单id
      entrustId:'',//委托单id
    };
  },
  created() {
    
  },
  methods: {
    changPageList(whichs){
      this.whichs=whichs
    },
    goback(){
      this.isParticulars = 1
    },
    godemandDel(id,go){
      this.demandID = id;
      this.isParticulars = go;
      document.documentElement.scrollTop = 0;
    },
    cut(id) {
      this.whichs = id;
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  margin: 16px 0;
  border-radius: 10px;
  background-color: #fff;

box-shadow: 0px 4px 12px 0px #EEF1F8;
.list{
  padding:0 16px;
}
}

.iconarrow {
  color: #666;
    font-size: 20px;
    background: white ;
    padding: 2px;
    width: 25px;
    height: 25px;
    border-radius: 16px;
}

.scan {
  width: 100%;
  // margin-top: 24px;
  // background-color: #fff;
  &-top {
    padding: 16px 0px;
    // background: #fff;
    padding-bottom: 12px;
    //border-bottom: 1px solid #E8E8E8
  }
  &-tab {
    display: flex;
    padding-left: 16px;
    &-con {
      display: flex;
      justify-content: space-between;
    }
    &-select {
      display: flex;
    }
    &-list {
      margin-right: 64px;
      font-size: 16px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
      cursor: pointer;
      &.on {
        font-weight: bold;
        color: #3370FF;
        position: relative;
        &::after {
          content: "";
          width: 28px;
          height: 2px;
          background: #3370FF;
          position: absolute;
          left: 18px;
          bottom: -8px;
        }
      }
    }
  }
  &-con {
    margin: 16px 24px;
  }
}
</style>