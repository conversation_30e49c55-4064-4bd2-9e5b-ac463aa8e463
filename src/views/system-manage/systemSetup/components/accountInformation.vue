<template>
  <div
    ref="msMain"
    class="vue-ms-main vue-ms-main-sel"
  >
    <el-card class="box-card">
      <div
        slot="header"
        class="clearfix"
      >
        <span style="font-size: 20px">账户信息
          <i
            class="el-icon-edit"
            style="cursor: pointer"
            @click="canEdit"
          /></span>
      </div>
      <div style="margin-left: 5%; width: 90%">
        <el-form
          ref="form"
          :model="form"
          :inline="true"
          label-position="left"
          :rules="rules"
        >
          <el-row
            type="flex"
            justify="space-between"
          >
            <el-col :span="12">
              <el-form-item
                label="姓名："
                prop="realName"
              >
                <el-input
                  v-if="isEdit"
                  v-model="form.realName"
                />
                <span v-else>{{ form.realName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="账号/手机号："
                prop="username"
              >
                <span>{{ form.username }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row
            type="flex"
            justify="space-between"
          >
            <el-col :span="12">
              <el-form-item
                label="招商属地："
                prop="codeName"
              >
                <span>{{ form.codeName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="关注的产业链："
                prop="focusIndustryIds"
              >
                <el-select
                  v-if="isEdit"
                  v-model="form.focusIndustryIds"
                  :multiple-limit="3"
                  placeholder="请选择您关注的产业"
                  :options="purchaseChain"
                  :multiple="true"
                >
                  <el-option
                    v-for="item in purchaseChain"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <span v-else>{{ form.focusIndustryIdsName }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row
            type="flex"
            justify="space-between"
          >
            <el-col :span="12">
              <el-form-item
                label="邮箱："
                prop="username"
              >
                <el-input
                  v-if="isEdit"
                  v-model="form.email"
                />
                <span v-else>{{ form.email }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="所在单位："
                prop="company"
              >
                <el-input
                  v-if="isEdit"
                  v-model="form.company"
                />
                <span v-else>{{ form.company }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row
            type="flex"
            justify="space-between"
          >
            <el-col :span="12">
              <el-form-item
                label="所在单位职务："
                prop="jobTitle"
              >
                <el-input
                  v-if="isEdit"
                  v-model="form.jobTitle"
                />
                <span v-else>{{ form.jobTitle }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="所在城市："
                prop="cityCode"
              >
                <el-cascader
                  v-if="isEdit"
                  ref="cascader"
                  v-model="form.cityCode"
                  :options="areaList"
                  :props="citys2"
                  placeholder="请选择所在城市"
                />
                <span v-else>{{ form.cityName }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row
            type="flex"
            justify="space-between"
          >
            <el-col :span="12">
              <el-form-item
                label="优势资源城市："
                prop="advantageCity"
              >
                <el-input
                  v-if="isEdit"
                  v-model="form.advantageCity"
                />
                <span v-else>{{ form.advantageCity }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="优势资源简介："
                prop="resume"
              >
                <el-input
                  v-if="isEdit"
                  v-model="form.resume"
                />
                <span v-else>{{ form.resume }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row
            type="flex"
            justify="space-between"
          >
            <el-col :span="12">
              <el-form-item label="所在团队：">
                <span>{{ workerParent }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="账号已有身份：">
                <span>{{ userIdentitys }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row
            type="flex"
            justify="space-between"
          >
            <el-col :span="12">
              <el-form-item label="所属机构：">
                <span>{{ orgName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12" />
          </el-row>
          <el-form-item v-if="isEdit">
            <el-button
              type="primary"
              @click="onSubmit"
            >
              确定
            </el-button>
            <el-button @click="canEdit">
              取消
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>
  
  <script>
import { getAccountInfoAPI, workerParentAPI } from "@/api/account";
import { completeUserInfoAPI, getUserInfoAPI } from "@/api/login";
import { getAllAddress } from "@/api/user";
import { getCityListAPI, listAllPurchaseChainAPI } from "@/api/userCenter";
export default {
  name: "AccountInformation",
  components: {},
  filters: {
    status(value) {
      if (value == 0) {
        return "禁用";
      } else if (value == 1) {
        return "启用";
      } else if (value == 2) {
        return "过期";
      } else {
        return "";
      }
    },
    accountClassification(value) {
      if (value == 1) {
        return "正式";
      } else if (value == 2) {
        return "试用";
      } else if (value == 3) {
        return "其他";
      } else {
        return "";
      }
    },
  },
  data() {
    return {
      listLoading: false,
      areaList: [],
      purchaseChain: [],
      isEdit: false,
      isredact: true,
      isDrawer: false,
      form: {
        userId: "",
        realName: "", //姓名
        username: "",
        code: "", //招商属地
        focusIndustryIds: [], //关注产业
        focusIndustryIdsName: "",
        email: "", //邮箱
        company: "", //所在单位
        jobTitle: "",
        cityCode: "", //所在城市code
        cityName: "", //所在城市名称
        advantageCity: "", //优势资源城市
        resume: "", //优势资源简述
      },
      usercontent: {},
      rowId: "",
      total: "",
      userIdentitys: "",
      orgName: "",
      workerParent: "",
      citys2: {
        checkStrictly: true,
      },
      rules: {
        focusIndustryIds: [
          {
            required: true,
            message: "您的关注的产业链不能为空",
            trigger: "change",
          },
        ],
        realName: [
          { required: true, message: "您的姓名不能为空", trigger: "blur" },
        ],
        jobTitle: [
          { required: true, message: "您的职位不能为空", trigger: "blur" },
        ],
        company: [
          { required: true, message: "所在单位不能为空", trigger: "blur" },
        ],
        cityCode: [
          { required: true, message: "所在城市不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getListAll();
    this.personalDetails();
    this.getAllAddressList();
  },
  methods: {
    getAllAddressList() {
      this.listLoading = true;
      getAllAddress().then((res) => {
        let addressList = [];
        let areaList = [];
        res?.result?.map((e) => {
          if (e.parentId == 0) {
            if (e.id != "3645" && e.id != "3646" && e.id != "3647")
              e.disabled = true; //台湾、澳门、香港只有一级
            e.label = e.province;
            e.value = e.code;
            addressList.push(e);
          } else {
            e.label = e.province;
            e.value = e.code;
            areaList.push(e);
          }
        });
        // 市
        addressList.map((el) => {
          res.result.map((e) => {
            if (el.id == e.parentId) {
              e.label = e.city;
              e.value = e.code;
              if (el.children) {
                el.children.push(e);
              } else {
                el.children = [];
                el.children.push(e);
              }
            }
          });
          return el;
        });
        // 区
        addressList.map((el) => {
          if (el.children && el.children.length > 0) {
            for (let i = 0; i < el.children.length; i++) {
              let el1 = el.children[i];
              areaList.map((e) => {
                if (e.parentId == el1.id) {
                  e.label = e.area;
                  e.value = e.code;
                  if (el1.children) {
                    el1.children.push(e);
                  } else {
                    el1.children = [];
                    el1.children.push(e);
                  }
                }
              });
            }
          }
          return el;
        });
        this.areaList = addressList;
        this.listLoading = false;
      });
    },
    getListAll() {
      listAllPurchaseChainAPI({ token: this.userInfo?.token }).then((res) => {
        this.purchaseChain = res.result.map((e) => ({
          value: e.id,
          label: e.name,
        }));
      });
    },
    canEdit() {
      this.isEdit = !this.isEdit;
    },
    // 编辑
    compile() {
      this.isredact = false;
    },
    onSubmit() {
      this.onFinish();
    },
    // 表单提交处理
    onFinish() {
      let nodes = this.$refs.cascader.getCheckedNodes();
      if (nodes && nodes.length) {
        let node = this.$refs.cascader.getCheckedNodes()[0];
        if (node) {
          this.form.cityName = node.parent.label + "/" + node.label;
        }
      }
      const { focusIndustryIds } = this.form;
      const selectedLabels = this.purchaseChain
        .filter((item) => focusIndustryIds.some((i) => i == item.value))
        .map((item) => item.label);
      this.form.focusIndustryIdsName = selectedLabels.join(",");
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const data = {
            ...this.form,
            userId: this.form.userId,
            cityCode: this.form.cityCode[this.form.cityCode.length - 1],
          };
          completeUserInfoAPI(data).then((res) => {
            if (res?.code === "SUCCESS") {
              this.$message.success("个人信息编辑成功");
              this.canEdit();
              this.getAiInfo();
            } else {
              console.error(res?.msg || "服务器繁忙");
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },

    getAiInfo() {
      getUserInfoAPI()
        .then((obj) => {
          localStorage.setItem("AiUserInfo", JSON.stringify(obj.result));
          let user = JSON.parse(localStorage.getItem("userInfo"));
          user.realName = obj.result.realName;
          localStorage.setItem("userInfo", JSON.stringify(user));
          window.location.reload();
        })
        .catch((error) => {
          console.error("获取 AI 信息失败:", error);
        });
    },
    // 回显
    async personalDetails() {
      const AiUserInfo = JSON.parse(localStorage.getItem("AiUserInfo"));
      this.form.userId = AiUserInfo.userCompletionInfoDO.userId;
      this.form.realName = AiUserInfo.realName;
      this.form.email = AiUserInfo.userCompletionInfoDO.email;
      this.form.username = AiUserInfo.username;
      this.form.codeName = AiUserInfo.userCompletionInfoDO.codeName;
      this.form.focusIndustryIds =
        AiUserInfo.userCompletionInfoDO.focusIndustryIds;
      this.form.focusIndustryIdsName = AiUserInfo.userCompletionInfoDO
        .chainNames
        ? AiUserInfo.userCompletionInfoDO.chainNames.join(",")
        : "";
      this.form.company = AiUserInfo.userCompletionInfoDO.company;
      this.form.jobTitle = AiUserInfo.userCompletionInfoDO.jobTitle;
      this.form.cityCode = AiUserInfo.userCompletionInfoDO.cityCode;
      this.form.cityName = AiUserInfo.userCompletionInfoDO.cityName;
      this.form.advantageCity = AiUserInfo.userCompletionInfoDO.advantageCity;
      this.form.resume = AiUserInfo.userCompletionInfoDO.resume;
      this.userIdentitys =
        AiUserInfo.userCompletionInfoDO.userIdentity == "1"
          ? "产业运营方"
          : "产业顾问方";
      const res = await getAccountInfoAPI();
      this.orgName = res.result.orgName; //所在机构
      const res2 = await workerParentAPI();
      this.workerParent = res2.result?.realName;
    },
    // 查看清单
    permissionview(val) {
      this.total = val;
      (this.rowId = this.usercontent.id), (this.isDrawer = true);
    },
  },
};
</script>
  
  <style scoped lang="scss">
::v-deep {
  .el-input {
    width: 180px;
  }
}
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #3370ff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #3370ff;
  color: rgba(255, 255, 255, 0.85);
}
</style>