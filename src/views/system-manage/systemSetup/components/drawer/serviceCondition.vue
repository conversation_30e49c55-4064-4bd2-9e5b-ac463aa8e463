<template>
  <div>
    <el-drawer
      title="子账号清单"
      :visible="isdialogFormVisible"
      :before-close="handleClose"
      :wrapper-closable="false"
      size="32%"
    >
      <!-- <el-divider /> -->
      <div style="width: 80%; margin-left: 10%">
        <div style="display: flex">
          <div style="width: 100px">
            机构名称：
          </div>
          {{ rowId.orgName }}
        </div>
        <div style="display: flex; margin-top: 12px">
          <div style="width: 100px">
            账号：
          </div>
          {{ rowId.username }}
        </div>
        <div style="display: flex; margin-top: 12px">
          <div style="width: 100px">
            子账号使用：
          </div>
          {{ rowId.enableSubAccountNum }}/{{ rowId.subAccountNum }}
        </div>
        <div
          style="
            width: 100%;
            border: 1px solid #ededed;
            height: 470px;
            margin-top: 5%;
            display: block;
            overflow-y: scroll;
          "
        >
          <el-table
            ref="singleTable"
            :data="tableData"
            highlight-current-row
            style="width: 100%"
          >
            <el-table-column
              type="index"
              label="序号"
              width="50"
            />
            <el-table-column
              property="mobile"
              label="账号"
              width="120"
            />
            <el-table-column
              property="realName"
              label="姓名"
              width="120"
            />
            <el-table-column
              property="deptName"
              label="部门"
            />
          </el-table>
        </div>
        <el-button
          style="margin-bottom: 20px; margin-top: 20px; float: right"
          class="btn"
          @click="handleClose"
        >
          确定
        </el-button>
      </div>
    </el-drawer>
  </div>
</template>
  
  <script>
import { querySubAccountAPI } from "@/api/account";
export default {
  name: "DetailedLIstRight",
  props: {
    isdialogFormVisible: {
      type: Boolean,
      default: false,
    },
    rowId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      total: 0,
      tableData: [],
    };
  },
  watch: {},
  created() {
    this.dataListFn();
  },
  methods: {
    handleClose() {
      this.$emit("update:isdialogFormVisible", false);
    },
    async dataListFn() {
      //console.log(this.rowId);
      const res = await querySubAccountAPI({
        orgCode: this?.rowId?.orgCode,
        userId: this.rowId.id,
      });
      //console.log(res.result, "result");
      this.tableData=res.result
    },
  },
};
</script>
  
  <style scoped lang="scss">
::v-deep {
  .el-drawer__header {
    font-size: 20px;
    color: #000;
  }
}
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #3370FF;
  color: rgba(255, 255, 255, 0.85);
}
</style>