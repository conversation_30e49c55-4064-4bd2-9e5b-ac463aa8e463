<template>
  <div>
    <el-drawer
      title="功能权限清单"
      :visible="isDrawer"
      :before-close="handleClose"
      :wrapper-closable="false"
    >
      <!-- <el-divider /> -->
      <div style="width: 80%; margin-left: 10%">
        <span>共{{ total }}项功能权限</span>
        <div
          style="
            width: 100%;
            border: 1px solid #ededed;
            height: 600px;
            display: block;
            overflow-y: scroll;
            margin-top: 4px;
          "
        >
          <el-tree
            ref="refTree"
            :data="data"
            :props="defaultProps"
            show-checkbox
            default-expand-all
            :check-strictly="isCheck"
            node-key="id"
          />
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
// import { listUserBindAclAPI } from '@/api/account'
import {
  // getAclTreeByRoleTypeAPI,
  listAclByUserIdAPI
} from "@/api/role";
import {disableTree} from '@/utils/dateFormat'
export default {
  name: "DetailedLIstRight",
  props: {
    isDrawer: {
      type: Boolean,
      default: false,
    },
    rowId:{
      type: String,
      default:null
    },
    total:{
      type: String,
      default:null
    }
  },
  data() {
    return {
      data: [    
      ],
      defaultProps: {
        children: "aclDTOList",
        label: "aclName",
      },
      isCheck:false
    };
  },
  watch: {},
  created(){
    this.listUserBindAcl()
  },
  methods: {
    handleClose() {
      this.$emit("update:isDrawer", false);
    },
   async  listUserBindAcl(){
    const res =  await listAclByUserIdAPI()
    this.data = res.result
    //this.$refs.Tree.setCheckedNodes([this.data]);
    this.data= disableTree(this.data)
    this.$nextTick(()=>{
      const keys = [];
        const nodes = this.$refs.refTree.store.nodesMap;
        for (let key in nodes) {
          keys.push(key);
        }
        this.$refs.refTree.setCheckedKeys(keys);
    })
    //this.total= this.data.length
   /*   const res2 = await listUserBindAclAPI({
        id:this.rowId
      })
    //this.isCheck= true
    this.$nextTick(() => {
        const nodes = [];
        res2.result.forEach((item) => {
          const node = this.$refs.refTree.getNode(item);
          if (node?.isLeaf) {
            nodes.push(item);
          }
        });
        this.$refs.refTree.setCheckedKeys(nodes, true);
      });
      this.data= disableTree(this.data)
     this.total=res2.result.length */
    }
  },
};
</script>

<style scoped lang="scss">
::v-deep {
  .el-drawer__header {
    font-size: 20px;
    color: #000;
  }
  .el-tree-node__label{
    opacity: 0.6;
  }
  .el-checkbox__input.is-indeterminate .el-checkbox__inner{
    background-color: #f1f1f1 !important;
    border: 1px #f1f1f1 solid !important;
  }
  .el-checkbox__inner{
    background-color: #f1f1f1 !important;
    border: 1px #f1f1f1 solid !important;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #f1f1f1 !important;
    border: 1px #f1f1f1 solid !important;
}
}
</style>