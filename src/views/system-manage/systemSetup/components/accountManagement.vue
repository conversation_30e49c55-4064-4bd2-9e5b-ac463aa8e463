<template>
  <div
    ref="msMain"
    class="vue-ms-main vue-ms-main-sel"
  >
    <div v-if="isList==1">
      <table-layout
        :tab-name-list="['账号设置']"
      >
        <!-- 查询 -->
        <template slot="selBtn">
          <!--         <el-button
          type="primary"
          plain
          size="small"
          :loading="listLoading"
          icon="el-icon-search"
          @click="search"
        >
          搜索
        </el-button>
        <el-button
          size="small"
          @click="reset"
        >
          重置
        </el-button> -->
          <!--         <el-button
          class="btn"
          @click="dialogFormVisible = true"
        >
          添加
        </el-button> -->
        </template>
        <!-- 查询内容 -->
        <el-form
          slot="elForm"
          ref="params"
          label-width="82px"
          class="demo-form-inline"
          :model="pageSize"
        >
          <el-form-item label="账号">
            <el-input
              v-model="pageSize.mobile"
              placeholder="请输入账号"
            />
          </el-form-item>
          <!--           <el-form-item
            v-if="$store.getters.user.accountType == '1'"
            label="主账号"
          >
            <el-input
              v-model="pageSize.mobil"
              placeholder="请输入主账号"
            />
          </el-form-item> -->
          <el-form-item
            v-if="$store.getters.user.accountType == '1'"
            label="所在机构"
          >
            <el-input
              v-model="pageSize.orgName"
              placeholder="请输入机构名称"
            />
          </el-form-item>
          <el-form-item label="人员姓名">
            <el-input
              v-model="pageSize.realName"
              placeholder="请输入人员姓名"
            />
          </el-form-item>
          <el-form-item label="启禁用">
            <el-select
              v-model="pageSize.status"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item> 
          <el-form-item label="账号状态">
            <el-select
              v-model="pageSize.accountStatus" 
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in options2"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item> 
          <el-form-item style="width: 400px;">
            <el-button
              v-loading="listLoading"
              class="btn"
              @click="search"
            >
              查询
            </el-button>
            <el-button
              class="btn"
              @click="reset"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <div slot="selTable">
          <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
          >
            <el-table-column
              prop="username"
              align="center"
              label="账号"
              width="140"
            />
            <el-table-column
              prop="realName"
              label="姓名"
              align="center"
              width="180"
            />
            <el-table-column
              v-if="$store.getters.user.accountType == '1'"
              prop="orgName"
              align="center"
              width="160"
              label="所在机构"
            />
            <el-table-column
              v-if="$store.getters.user.accountType == '1'"
              prop="accountClassification"
              align="center"
              label="账号类型"
              width="120"
            > 
              <template slot-scope="{row}">
                {{ row.accountType | accountType }}
              </template>
            </el-table-column>
            <el-table-column
              prop="accountStatus"
              label="启禁用"
              align="center"
            >
              <template slot-scope="{ row }">
                <el-switch
                  :value="row.status"
                  :disabled="$store.getters.user.accountType == '1' ? false : true"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  @change="updataState(row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="a"
              align="center"
              label="子账号使用情况"
              width="160"
            >
              <template slot-scope="{row}">
                <span
                  style="color:#37a9ff;cursor: pointer;"
                  @click="serviceConditionFn(row)"
                >{{ row.enableSubAccountNum }}/{{ row.subAccountNum }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="accountStatus"
              align="center"
              label="账号状态"
              width="160"
            >
              <template slot-scope="{row}">
                {{ row.accountStatus | accountStatus }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="操作"
            >
              <template slot-scope="{row}">
                <el-button
                  type="text"
                  @click="allocation(row.id)"
                >
                  配置
                </el-button>
                <!--  <el-divider direction="vertical" />
                <el-button
                  type="text"
                  @click="userdeleted(row)"
                >
                  删除
                </el-button> -->
              </template>
            </el-table-column>
          </el-table>
        </div>
      </table-layout>
      <div class="ye">
        <el-pagination
          :current-page.sync="pageSize.pageNum"
          :page-sizes="[5, 10, 20, 50]"
          :page-size.sync="pageSize.pageSize"      
          :total="+total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="industryList"
          @current-change="industryList"
        />
      </div>
    </div>
    <div v-if="isList==2">
      <table-layout
        :tab-name-list="['账号设置']"
      />
      <AccountSetting
        :row-id="rowId"
        @close="close"
      />
    </div>
    <serviceCondition
      v-if="isdialogFormVisible"
      :isdialog-form-visible.sync="isdialogFormVisible"
      :row-id="rowId"
       
    />
  </div>
</template>

<script>
import TableLayout from "@/common/components/table-layout";
import serviceCondition from './drawer/serviceCondition.vue'
import AccountSetting from './AccountSetting.vue'
import {accountPageAPI} from '@/api/account'
import {userdeletedAPI,updateStatusAPI} from '@/api/userset'
export default {
  name:"AccountManagement",
  components: {
    TableLayout,
    serviceCondition,
    AccountSetting
  },
  filters:{
    accountClassification(value){
      if(value=='01'){
        return '正式'
      } else if(value=='02'){
        return '试用'
      }else if(value=='03'){
        return '过期'
      }else{
        return ''
      }
    },
    accountType(value){
      if(value=='1'){
        return '运营'
      } else if(value=='2'){
        return '用户'
      }else{
        return ''
      }
    },
    accountStatus(value){
      if(value=='1'){
        return '正常'
      } else if(value=='2'){
        return '禁用'
      }else if(value=='3'){
        return '过期'
      }else{
        return ''
      }
    }
  },
  data() {
    return {
      listLoading:false,
      loading: false,
      // 弹层
      // 联系人名称
      linkman: "",
      isList:1,
      //列表
      tableData: [],
      // 子账号使用情况
      isdialogFormVisible:false,
      options: [
        {
          value: "",
          label: "全部",
        },
        {
          value: "1",
          label: "启用",
        },
        {
          value: "0",
          label: "禁用",
        },
      ],
      options2: [
      {
          value: "",
          label: "全部",
        },
        {
          value: "1",
          label: "正常",
        },
        {
          value: "2",
          label: "禁用",
        },
        {
          value: "3",
          label: "过期",
        },
      ],
      pageSize: {        
        pageSize: 10,
        pageNum: 1,
        mobile:"",
        mobil:"",
        orgName:"",
        realName:"",
        status:"",
        accountStatus:""
      },
      //总数
      total: '',
      rowId:""
    };
  },
  computed: {
  },
  watch: {},
  created() {
    this.industryList()
  },
  methods: {
    close(value){
      this.isList =value
      this.industryList()
    },
    allocation(id) {
      this.isList=2
      this.rowId= id
      /* this.$router.push({
      path:'/business/configuration',
        query: {
        id,
      }
    }) */
    },
    serviceConditionFn(row){
     this.rowId= row
     this.isdialogFormVisible=true
    },
    async userdeleted(row){
      this.$confirm("确定删除该用户？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await userdeletedAPI({
          id:row.id
        });
        // 刷新产业链
        this.industryList();
        this.$message({
          type: "success",
          message: "删除用户成功!",
        });
      });
    },
    updataState(row){
      if(row.status){
        this.$confirm("确定禁用该账号？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await updateStatusAPI({
          id:row.id,
          status:0
        });
        // 刷新
        this.industryList();
        this.$message.success("操作成功!")
      });
      }else{
        this.$confirm("确定启用该账号？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await updateStatusAPI({
          id:row.id,
          status:1
        });
        // 刷新
        this.industryList();
        this.$message.success("操作成功!")
      });
      }
    },
    // 获取列表
    async industryList() {
      try {
        this.loading=true
        const res =  await accountPageAPI(this.pageSize)
     this.tableData=res.result.records
     this.total=res.result.total
      } catch (error) {
        console.log(error);
      } finally{
        this.loading=false
      }
    
    },
    reset() {
     this.pageSize= {
        pageSize: 10,
        pageNum: 1,
        mobile:"",
        mobil:"",
        orgName:"",
        realName:"",
        status:"",
        accountStatus:""
      }
      this.industryList()
    },
    async search(){
    try {
      this.pageSize.pageNum = 1
      this.listLoading=true
      await this.industryList()
    } catch (error) {
      console.log(error);
    }finally{
      this.listLoading=false
    }
    }
  },

}
</script>

<style lang="scss">
.el-tooltip__popper {
 max-width: 900px;
}
</style>
<style scoped lang="scss">
.ye {
  margin-bottom: 50px;
}
.btn{
  color: rgba(255,255,255,0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover{
 background: #1765AD;;
 color: rgba(255,255,255,0.85);
}
.btn:active{
 background: #52ABFF;
 color: rgba(255,255,255,0.85);
}
.btn:focus{
 background: #3370FF;
 color: rgba(255,255,255,0.85);
}
</style>