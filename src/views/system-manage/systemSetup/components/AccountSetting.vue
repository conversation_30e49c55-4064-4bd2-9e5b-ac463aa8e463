<template>
  <div>
    <el-card class="box-card">
      <div
        slot="header"
        class="clearfix"
      >
        <span style="font-size: 20px; margin-left: 20px">账号配置</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
        >
          <i
            class="el-icon-close"
            @click="close"
          />
        </el-button>
      </div>
      <div class="jurisdiction">
        <div class="menu">
          <div style="display: flex; justify-content: space-between">
            <p style="font-size: 16px">
              账号列表
            </p>
            <!--             <div style="width: 300px; margin-right: 30px">
              <el-input
                v-model="antistop"
                placeholder="请输入关键词"
              >
                <i
                  slot="suffix"
                  style="cursor: pointer"
                  class="el-input__icon el-icon-search"
                  @click="antistopFn"
                />
              </el-input>
            </div> -->
          </div>
          <div
            style="
              overflow-y: scroll;
              width: 400px;
              height: 500px;
              display: block;
              margin-top: 10px;
            "
          >
            <!-- <el-table
              :data="tableData"
              :row-key="getRowKeys"
              tooltip-effect="dark"
              @selection-change="handleSelectStockChange"
            >
              <el-table-column
                v-if="activeName !== 'first'"
                type="selection"
                align="center"
                :reserve-selection="true"
                width="55"
              />
              <el-table-column
                prop="realName"
                align="center"
                label="姓名"
                width="120"
              />
              <el-table-column
                prop="username"
                label="账号"
                align="center"
                width="120"
              />
              <el-table-column
                prop="deptName"
                label="部门"
                align="center"
                width="140"
              />
            </el-table> -->
            <el-tree
              ref="Tree"
              v-loading="treeLoading"
              :data="dataList"
              :props="defaultPropsFn"
              default-expand-all
              show-checkbox
              :check-strictly="isCheckFn"
              node-key="id"
              @check-change="checkedFn"
            />
          </div>
        </div>
        <div class="operation">
          <el-tabs
            v-model="activeName"
            stretch
            @tab-click="handleClick"
          >
            <el-tab-pane
              v-if="$store.getters.user.accountType == '1'"
              label="使用设置"
              name="first"
            >
              <div
                style="
                  display: flex;
                  width: 100%;
                  justify-content: space-between;
                "
              >
                <el-form
                  ref="ruleForm"
                  label-position="left"
                  style="margin-top: 2%; width: 75%"
                  :model="ruleForm"
                  :rules="rules"
                  label-width="140px"
                  class="demo-ruleForm"
                >
                  <el-form-item
                    label="账户类型"
                    prop="resource"
                  >
                    <el-radio-group v-model="ruleForm.resource">
                      <el-radio label="01">
                        正式账户
                      </el-radio>
                      <el-radio label="02">
                        试用账户
                      </el-radio>
                      <el-radio label="03">
                        其他账户
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item
                    label="使用期限"
                    prop="startDate"
                  >
                    <el-date-picker
                      v-model="ruleForm.startDate"
                      type="daterange"
                      unlink-panels
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      range-separator=""
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                  <el-form-item
                    label="子账号数量"
                    prop="subAccountNum"
                  >
                    <el-input v-model="ruleForm.subAccountNum" />
                  </el-form-item>
                  <el-form-item
                    label="部门层级限制"
                    prop="maxDeptLevel"
                  >
                    <el-input v-model="ruleForm.maxDeptLevel" />
                  </el-form-item>
                  <el-form-item
                    label="角色数量限制"
                    prop="maxRoleNum"
                  >
                    <el-input v-model="ruleForm.maxRoleNum" />
                  </el-form-item>

                  <el-form-item
                    label="启禁用"
                    prop="delivery"
                  >
                    <el-switch
                      v-model="ruleForm.delivery"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                    />
                  </el-form-item>
                  <el-form-item style="float: right">
                    <el-button @click="close">
                      取消
                    </el-button>
                    <el-button
                      class="btn"
                      :disabled="pitchOnId=='' ? true : false"
                      @click="submitForm('ruleForm')"
                    >
                      保存
                    </el-button>
                  </el-form-item>
                </el-form>
                <!--                 <el-button
                  class="btn"
                  style="height: 40px; margin-top: 2%"
                >
                  编辑
                </el-button> -->
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="角色设置"
              name="second"
            >
              <div style="display: flex">
                <div style="width: 30%">
                  <p>角色设置</p>
                  <el-select
                    v-model="value1"
                    multiple
                    clearable
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.id"
                      :label="item.roleName"
                      :value="item.id"
                    />
                  </el-select>
                </div>
                <div style="width: 60%">
                  <p style="margin-left: 20%">
                    权限列表:{{ sum }}/{{ sum }}
                  </p>
                  <div
                    style="
                      border: 1px solid #ededed;
                      height: 340px;
                      margin-left: 10%;
                      display: block;
                      overflow-y: scroll;
                    "
                  >
                    <el-tree
                      ref="refTree"
                      v-loading="industryLoading"
                      :data="data"
                      :props="defaultProps"
                      default-expand-all
                      show-checkbox
                      :check-strictly="isCheck"
                      node-key="id"
                      @check-change="checked"
                    />
                  </div>
                </div>
              </div>
              <div style="float: right; margin-right: 10%; margin-top: 5%">
                <el-button @click="close">
                  取消
                </el-button>
                <el-button
                  class="btn"
                  :disabled="pitchOnId=='' ? true : false"
                  @click="editUserRole()"
                >
                  保存
                </el-button>
              </div>
            </el-tab-pane>
            <!-- 产业链配置 -->
            <el-tab-pane
              label="产业链配置"
              name="third"
            >
              <div style="display: flex">
                <div>
                  <p style="margin-left: 20px">
                    新增产业链
                  </p>
                  <el-select
                    v-model="value2"
                    multiple
                    collapse-tags
                    style="margin-left: 20px; width: 240px"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in chainList"
                      :key="item.id"
                      :label="item.chainName"
                      :value="item.id"
                    />
                  </el-select>
                </div>
                <div style="margin-left: 5%">
                  <p>已选产业链</p>
                  <div
                    style="
                      overflow-y: scroll;
                      height: 320px;
                      display: block;
                      margin-top: 10px;
                    "
                  >
                    <el-table
                      :data="industryData"
                      style="width: 100%"
                    >
                      <el-table-column
                        type="index"
                        width="50"
                        align="center"
                      />
                      <el-table-column
                        prop="chainName"
                        label="产业链"
                        width="180"
                        align="center"
                      />
                      <el-table-column
                        align="center"
                        label="操作"
                      >
                        <template slot-scope="{ row }">
                          <el-button
                            type="text"
                            @click="remove(row.id)"
                          >
                            移除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
              <div style="float: right; margin-right: 50%; margin-top: 5%">
                <el-button @click="close">
                  取消
                </el-button>
                <el-button
                  class="btn"
                  :disabled="pitchOnId=='' ? true : false"
                  @click="setAccountIndustryChain"
                >
                  保存
                </el-button>
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="密码重置"
              name="fourth"
            >
              <div style="width: 80%; margin-left: 10%">
                <div v-if="resetPasswords!==''"> 
                  <p
                    style="
                      font-size: 20px;
                      text-align: center;
                      padding-top: 10%;
                    "
                  >
                    您的密码重置成功!
                  </p>
                  <div
                    style="
                      text-align: center;
                      margin-top: 10%;
                      width: 60%;
                      height: 100px;
                      margin-left: 20%;
                      background-color: #eeeeee;
                    "
                  >
                    <p style="padding-top: 10%">
                      账号：{{ resetPasswords.username }}
                    </p> 
                    <p
                      style="padding-bottom: 10%,cursor: pointer;"
                      @click="copy( resetPasswords.password )"
                    >
                      新密码：{{ resetPasswords.password }}
                    </p>
                  </div>
                </div>
                <el-button
                  v-loading="Password"
                  style="margin-left: 40%; margin-top: 10%"
                  class="btn"
                  :disabled="pitchOnId=='' ? true : false"
                  @click="resetPasswordsFn"
                >
                  密码重置
                </el-button>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-card>
  </div>
</template>
    
    <script>
import { getTreeLength } from "@/utils/dateFormat";
import {
  accountSetAPI,
  setAccountIndustryChainAPI,
  listAccountSetAPI,
  queryAccountSetAPI,
  resetPasswordAPI,
  listRoleByUserIdAPI,
  listRoleByUserIdForRoleSetAPI,
  roleAclTreeForRoleSetAPI,
  listIndustryChainAPI,
  getAccountIndustryChainAPI,
  deleteAccountIndustryChainAPI,
  editUserRoleAPI
} from "@/api/account";
//import { rolesAPI } from "@/api/role";
import { disableTree } from "@/utils/dateFormat";
export default {
  name: "JurisDiction",
  props: {
    rowId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      antistop: "",
      isCheck: false,
      isCheckFn:true,
      tableData: [],
      industryData: [],
      options: [],
      chainList: [],
      value1: "",
      value2: "",
      selected: 0,
      industryLoading:false,
      // 勾选id集合
      userId: [],
      type: 1,
      multipleSelection: [],
      treeLoading:false,
      centerDialogVisible: false,
      activeName: "first",
      ruleForm: {
        resource: "",
        startDate: [],
        subAccountNum: "", //子账号数量
        maxDeptLevel: "", //部门层级限制
        maxRoleNum: "", //角色数量限制
        delivery: true,
      },
      resetPasswords: "",
      data: [],
      defaultProps: {
        children: "aclDTOList",
        label: "aclName",
      },
      Password:false,
      dataList:[],
      defaultPropsFn:{
        children: "accountSetListDTOS",
        label: "realName",
      },
      pitchOnId:"",
      rules: {
        resource: [
          { required: true, message: "账户类型必选", trigger: "blur" },
        ],
        subAccountNum: [
          { required: true, message: "子账号数量必填", trigger: "blur" },
        ],
        startDate: [
          { required: true, message: "使用期限必填", trigger: "blur" },
        ],
        maxDeptLevel: [
          { required: true, message: "部门层级限制必填", trigger: "blur" },
        ],
        delivery: [{ required: true, message: "账户状态必填", trigger: "blur" }],
        maxRoleNum: [
          { required: true, message: "角色数量限制必填", trigger: "blur" },
        ],
      },
    };
  },
  computed: {
    // 计算总数
    sum() {
      return getTreeLength(this.data);
    },
  },
  watch: {
   async  'pitchOnId'(val){
      if(val!==''){
      this.queryAccountSet()
      this.listRoleByUserId()
      //this.roleList();
      //this.listIndustryChain();
      this.industryDataFn()
      }
    },
    "value1"(){
      this.treeList()
    }
  },
  created() {
    this.staffListFn();// 左侧列表
    //this.queryAccountSet();使用设置回显
    //this.listRoleByUserId();
    this.roleList();
    this.listIndustryChain();
    if(this.$store.getters.user.accountType == '2'){
      this.activeName='second'
    }
  },
  methods: {
    // 子账号设置
    async submitForm() {
      await this.$refs.ruleForm.validate();
      let status = this.ruleForm.delivery == false ? 0 : 1;
      //this.ruleForm.startDate = new Array();
      await accountSetAPI({
        userId: this.pitchOnId, //用户id
        startDate: this.ruleForm.startDate[0],
        endDate: this.ruleForm.startDate[1],
        subAccountNum: this.ruleForm.subAccountNum,
        maxDeptLevel: this.ruleForm.maxDeptLevel,
        maxRoleNum: this.ruleForm.maxRoleNum,
        accountClassification:this.ruleForm.resource,
        status,
      });
      this.$message.success("使用设置编辑成功");
      this.queryAccountSet();
    },
   async editUserRole(){
    if(this.value1==null){
      this.value1=[]
    }
    if(this.value1.length<1){
      return this.$message.error("角色设置不能为空")
    }
     await editUserRoleAPI({
      userId:this.pitchOnId,
      roleIds:this.value1,
     })
     this.$message.success("编辑角色成功")
    },
    // 树
    async treeList() {
      try {
        this.industryLoading =true
        if(this.value1?.length>=1){
        const res = await roleAclTreeForRoleSetAPI({
          roleIds: this.value1,
      });
      if(res.result==null){
        return
      }
      this.data = res.result;
      this.$nextTick(()=>{
      const keys = [];
      const nodes = this.$refs.refTree.store.nodesMap;
      for (let key in nodes) {
        keys.push(key);
      }
      this.$refs.refTree.setCheckedKeys(keys);
      })
      this.data = disableTree(this.data);
      }else{
        this.data = []
        this.selected=0
      }
      } finally {
        this.industryLoading =false
      }
      
     
    },
    // 产业链选择框和列表
    async listIndustryChain() {
      const arr = await listIndustryChainAPI({
        userId:this.rowId,
      });
      this.chainList = arr.result;
    },
    async industryDataFn(){
      const res = await getAccountIndustryChainAPI({
        userId:this.pitchOnId,
      });
      this.industryData = res.result;
      let arr = res.result.map((item)=>{
        return item.chainId
      })
      this.value2 =arr
    },
    // 输入关键词搜索
    antistopFn() {},
    // 左侧列表
    async staffListFn() {
      try {
      this.treeLoading=true
      const res = await listAccountSetAPI({
      userId: this.rowId,
      });
      this.dataList.push(res.result)
      if(this.$store.getters.user.accountType == '2'){
        this.dataList=res.result.accountSetListDTOS
      } 
      }  finally{
       this.treeLoading=false
      }
    },
    // 使用设置回显
    async queryAccountSet() {
      const res = await queryAccountSetAPI({
        userId:this.pitchOnId
      });
      this.ruleForm.resource = res.result.accountClassification; //账号类型
      if (res.result.startDate != null && res.result.endDate != null) {
        //this.ruleForm.startDate.push(res.result.startDate);
        //this.ruleForm.startDate.push(res.result.endDate);
        new Date(res.result.startDate)
        new Date(res.result.endDate)
        this.ruleForm.startDate=[]
        this.ruleForm.startDate.push(res.result.startDate);
        this.ruleForm.startDate.push(res.result.endDate);
      }
      //this.ruleForm.startDate[0] = new Date(res.result.startDate)
      this.ruleForm.delivery = res.result.status
      //this.ruleForm.startDate[1] =  new Date( res.result.endDate)
      this.ruleForm.subAccountNum = res.result.subAccountNum;
      this.ruleForm.maxDeptLevel = res.result.maxDeptLevel;
      this.ruleForm.maxRoleNum = res.result.maxRoleNum;
    },
    // 角色设置回显
    async listRoleByUserId() {
      const res = await listRoleByUserIdAPI({
        userId: this.pitchOnId,
      });
      this.value1 = res.result;
    },
    // 获取可选角色列表
    async roleList() {
      const res = await listRoleByUserIdForRoleSetAPI(
      );
      this.options = res.result;
    },
    // 设置账号产业链
    async setAccountIndustryChain() {
      await setAccountIndustryChainAPI({
        userId:  this.pitchOnId,
        chainIds: this.value2,
      });
      this.$message.success("设置用户产业链成功");
      this.value2=[]
      this.industryDataFn();
    },

    // 移除
    async remove(id) {
      this.$confirm("确定删除该产业链吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
          await   deleteAccountIndustryChainAPI({
            userChainId:id
        }); 
        this.industryDataFn();
        this.$message({
          type: "success",
          message: "移除产业链成功",
        });
      });
    },
    // 重置密码
    async resetPasswordsFn() {
      this.$confirm("是否确认重置密码", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
      const res =  await resetPasswordAPI({
          userId:this.pitchOnId, //已选择id,
        });
        this.resetPasswords = res.result
        this.$message({
          type: "success",
          message: "重置密码成功！",
        });
      });
    },
    copy(val){
      var cInput = document.createElement("input");
      cInput.value = val;
      document.body.appendChild(cInput);
      cInput.select(); 
      document.execCommand("copy");
      document.body.removeChild(cInput);
      this.$message.success("复制成功")
    },
    // 计算已勾选
    checked() {
      const res = this.$refs.refTree.getCheckedKeys();
      this.selected = res.length;
    },
    // 树单选
  checkedFn(data,checked){
    if(checked){
        this.$refs.Tree.setCheckedNodes([data]);
        this.pitchOnId=data.id
    }else if(this.pitchOnId==data.id){
        this.pitchOnId=''
    }
    },
    // 关闭
    close() {
      this.$emit("close", 1);
    },
    // tabs切换
    handleClick() {},
    // 表格
    getRowKeys(row) {
      return row.id;
    },
    // 表格翻页也不会丢失
    handleSelectStockChange(val) {
      let stockSelectlist = [];
      val.forEach((el) => {
        stockSelectlist.push(el.id);
      });
      this.multipleSelection = stockSelectlist;
    },
  },
};
</script>
    
    <style scoped lang="scss">
.ye {
  position: relative;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 20px;
  .el-icon-plus {
    margin-right: 10px;
  }
}
.jurisdiction {
  display: flex;
  width: 95%;
  margin-left: 2.5%;
  .menu {
    width: 40%;
  }
  .operation {
    width: 55%;
    margin-left: 5%;
  }
}
.box-card {
  height: 700px;
}

.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #3370FF;
  color: rgba(255, 255, 255, 0.85);
}
</style>