<template>
  <div>
    <el-dialog
      width="50%"
      title="导入失败原因"
      :visible="toleadfail"
      :close-on-click-modal="false"
      center
      @close="cancel"
    >
      <div
        v-if="excelEesult !=null"
        class="ovfl"
      >
        <p
          v-for="(item,index) in excelEesult"
          :key="index"
        >
          {{ item }}
        </p>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="cancel">
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
      <script>
  export default {
    name: "ExcelErrorwd",
    props: {
      toleadfail: {
        type: Boolean,
        default: false,
      },
      excelEesult: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
  
      };
      
    },
    methods:{
      cancel() {
          this.$emit("update:toleadfail",false)
        },
    }
  };
  </script>
      
  <style scoped lang="scss">
  .ovfl{
    overflow: auto;
    height: 100%;
    overflow-x: hidden;
  }
  </style>