<template>
  <div>
    <el-button
      icon="el-icon-arrow-left"
      type="text"
      @click="$router.back()"
    >
      返回
    </el-button>
    <div class="allocation">
      <div class="down-tree">
        <el-tree
          v-loading="treeload"
          class="tree"
          :data="data"
          default-expand-all="true"
          :expand-on-click-node="false"
          :props="defaultProps"
          @node-click="handleNodeClick"
        >
          <span
            slot-scope="{ node, data }"
            class="custom-tree-node"
          >
            <span :class="{ catalogue: data.isLeaf == '0' }">{{ data.nodeName }}
              <span v-if="data.isLeaf == '1'">({{ node.data.relevanceEnterpriseNumber }})</span>
            </span>
          <!-- 1是挂载企业isLeaf -->
          </span>
        </el-tree>
      </div>

      <div class="from">
        <div v-if="ishow">
          <h3>{{ formData.nodeName }}</h3>
          <p>招商企业名单</p>
          <el-form
            :inline="true"
            :model="form"
            class="demo-form-inline"
          >
            <el-form-item>
              <el-input
                v-model="form.enterpriseName"
                placeholder="请输入企业名称关键词"
              />
            </el-form-item>
            <el-form-item>
              <div class="item">
                <el-select
                  v-model="form.province"
                  placeholder="省"
                  @change="cityList2"
                >
                  <el-option
                    v-for="item in city"
                    :key="item.value"
                    :label="item.name"
                    :value="item.name"
                  />
                </el-select>
              </div>
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="form.city"
                placeholder="市"
                :no-data-text="'请先选择上一级'"
                @change="cityList3"
              >
                <el-option
                  v-for="item in city1"
                  :key="item.value"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="form.area"
                :no-data-text="'请先选择上一级'"
                placeholder="区"
              >
                <el-option
                  v-for="item in city2"
                  :key="item.value"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                class="btn"
                @click="handleNode"
              >
                查询
              </el-button>
              <el-button
                class="btn"
                @click="reset"
              >
                重置
              </el-button>
            </el-form-item>
          </el-form>
          <div class="top">
            <el-button
              class="btn"
              @click="dialogFormVisible = true"
            >
              添加
            </el-button>
            <el-button
              :disabled="multipleSelection.length > 0 ? false : true"
              class="btn"
              @click="batchDelete"
            >
              批量移除
            </el-button>
            <el-upload
            action="#"
              class="upload"
              :show-file-list="false"
              :on-change="affirmstate"
              accept="'.xlsx','.xls'"
              :auto-upload="false"
            >
              <el-button
                v-loading="batchTolead"
                class="btn"
              >
                批量导入
              </el-button>
            </el-upload>
            <el-button
              v-loading="templateLoading"
              class="btn"
              @click="downloadTemplate"
            >
              下载导入模板
            </el-button>
          </div>
          <div
            v-if="isTable"
            class="table"
          >
            <el-table
              ref="multipleTable"
              v-loading="Listload"
              :data="tableData"
              tooltip-effect="dark"
              style="width: 100%"
              :row-key="getRowKeys"
              @selection-change="handleSelectStockChange"
            >
              <el-table-column
                :reserve-selection="true"
                type="selection"
                width="55"
                align="center"
              />
              <el-table-column
                label="企业名称"
                width="200"
                show-overflow-tooltip
                align="center"
              >
                <template slot-scope="scope">
                  {{ scope.row.enterpriseName }}
                </template>
              </el-table-column>
              <el-table-column
                prop="unifiedSocialCreditCode"
                align="center"
                label="统一社会信用代码"
                width="220"
              />
              <el-table-column
                label="行政区划"
                align="center"
                show-overflow-tooltip
              >
                <template slot-scope="{ row }">
                  {{ row.province }}{{ row.city }}{{ row.area }}
                </template>
              </el-table-column>
              <el-table-column
                label="所属行业"
                align="center"
                show-overflow-tooltip
              >
                <template slot-scope="{ row }">
                  {{ row.nationalStandardIndustry }}-{{
                    row.nationalStandardIndustryBig
                  }}-{{ row.nationalStandardIndustryMiddle }}-{{
                    row.nationalStandardIndustrySmall
                  }}
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                width="100"
                show-overflow-tooltip
              >
                <template slot-scope="{ row }">
                  <el-button
                    type="text"
                    @click="dels(row.id)"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div>
              <el-pagination
                :current-page.sync="form.pageNum"
                :page-sizes="[5, 10, 20, 50]"
                :page-size.sync="form.pageSize"
                :total="+total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleNodeList(nodeId)"
                @current-change="handleNodeList(nodeId)"
              />
            </div>
            <!--             <div class="buttons">
              <div class="btnss">
                <el-button class="btn">
                  重置
                </el-button>
                <el-button
                  class="btn"
                  @click="enterpriseSave"
                >
                  保存
                </el-button>
              </div>
            </div> -->
          </div>
          <div
            v-else
            class="isTable"
          >
            <p>加载中...</p>
          </div>
          <transferCommon
            v-if="dialogFormVisible"
            :dialog-form-visible.sync="dialogFormVisible"
            :node-id="nodeId"
          />
        </div>
        <p v-else>
          请选择挂载企业节点进行招商企业设置
        </p>
      </div>
      <excer
        v-if="toleadfail"
        :toleadfail.sync="toleadfail"
        :excel-eesult="excelEesult"
      />
    </div>
  </div>
</template>

<script>
import excer from "./excerWd.vue";
import transferCommon from "./transferCommon.vue";
import { cityAPI } from "@/api/city";
import { getPathId } from '@/utils/utils'

import {
  downloadTemplateAPI,
  attractTreeAPI,
  enterpriseimportAPI,
  enterpriseListAPI,
  enterpriseSaveAPI,
  enterpriseBatchRemoveAPI,
} from "@/api/attractInvestment";
export default {
  components: {
    transferCommon,
    excer,
  },
  data() {
    return {
       actionUrl:"",
      isTable:true,
      Listload: false, // 列表loading
      treeload: false, // 树loadin
      toleadfail: false, // 弹层开关
      excelEesult: [], // 导入失败返回的的数据列表
      batchTolead: false, // 导入loading
      dialogFormVisible: false, // 添加弹层
      ishow: false, // 是否展示表格
      multipleSelection: [], // 批量移除的列表
      templateLoading: false, // 下载模板的loading
      formData: {}, // 标题
      nodeId: [], // 节点id
      form: {
        pageNum: 1, //	int	是	页面编号，默认是1
        pageSize: 10, //	int	是	页面大小，默认是20
        province: "",
        city: "",
        area: "",
        enterpriseName: "",
      },
      // 省市区
      city: [],
      city1: [],
      city2: [],
      total: 0, // 列表总数
      tableData: [], // 列表
      data: [], // 树
      defaultProps: {
        children: "childNodes",
        label: "nodeName",
      },
    };
  },
  created() {
    this.actionUrl=localStorage.getItem('originPath')
    this.cityList();
    this.attractLsit();
  },
  methods: {
    // 保存
/*     async enterpriseSave() {
      let data = {
        orgChainId: "", //机构产业链关系id
        chainNodeId: "", //产业链节点id
        enterpriseId: "", //企业id
      };
      // const res = await enterpriseSaveAPI(data)
    }, */
    // 下载导入模板
    async downloadTemplate() {
      try {
        this.templateLoading = true;
        const res = await downloadTemplateAPI();
        let blob = new Blob([res], {
          type: "text/csv,charset=UTF-8",
        });
        let objectUrl = URL.createObjectURL(blob);
        window.location.href = objectUrl;
      } catch (error) {
        console.log(error);
      } finally {
        this.templateLoading = false;
      }
    },
    // 招商企业导入
    async affirmstate(file) {
      //console.log(file);
      var name = file.name.substring(file.name.lastIndexOf(".") + 1);
      try {
        this.batchTolead = true;
        /*         if (name !== "xlsx" && name !== "xls") {
          return this.$message.error("文件格式有误");
        } */
        let data = {
          file: file.raw, //文件
          orgChainId:this.$route.query.id|| getPathId()|| null, //机构产业链关系id
          chainNodeId: this.nodeId.id, //节点id
        };
        const res = await enterpriseimportAPI(data);
       // console.log(res);
        if (res.code=='SUCCESS' && res.result.length==0) {
          this.$message.success("导入成功");
          this.attractLsit();
          this.handleNodeClick(this.nodeId);
        } else {
          this.excelEesult = res.result;
          this.toleadfail = true;
          this.$message.error("导入失败");
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.batchTolead = false;
      }
    },
    // 搜索
    async handleNode() {
      try {
        this.Listload = true;
        let props = {
          orgChainId: this.$route.query.id|| getPathId()|| null, //机构产业链关系id
          chainNodeId: this.nodeId.id, //产业链节点id
          enterpriseName: this.form.enterpriseName, //企业名称
          province: this.form.province, //省份
          city: this.form.city, //市
          area: this.form.area, //区
          pageNum: this.form.pageNum, //页码
          pageSize: this.form.pageSize, //每页条数
        };
        const res = await enterpriseListAPI(props);
        this.tableData = res.result.records;
        this.total = res.result.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.Listload = false;
      }
    },
    // 获取招商列表
    async handleNodeClick(data) {
      if (data.isLeaf == "0") {
        return this.$message.error("根节点和非挂载企业无法配置");
      }
      try {
        this.isTable=false
        //this.Listload = true;
        this.nodeId = data;
       /*  this.form.province=''
        this.form.city=''
        this.form.area=''
        this.form.enterpriseName='' */
        this.form={}
        this.multipleSelection=[]
        //需要把节点信息存起来
        let props = {
          orgChainId:this.$route.query.id|| getPathId()|| null, //机构产业链关系id
          chainNodeId: this.nodeId.id, //产业链节点id
          enterpriseName: "", //企业名称
          province: this.form.province, //省份
          city: this.form.city, //市
          area: this.form.area, //区
          pageNum: this.form.pageNum, //页码
          pageSize: this.form.pageSize, //每页条数
        };
        const res = await enterpriseListAPI(props);
        //console.log(data);
        this.ishow = true;
        this.formData = data;
        this.tableData = res.result.records;
        this.total = res.result.total;
      } catch (error) {
        console.log(error);
      } finally {
        //this.Listload = false;
        this.isTable=true
      }
    },
    // 上一页下一页
   async handleNodeList(data){
      try {
        this.Listload = true;
        this.nodeId = data;
        //需要把节点信息存起来
        let props = {
          orgChainId:this.$route.query.id|| getPathId()|| null, //机构产业链关系id
          chainNodeId: this.nodeId.id, //产业链节点id
          enterpriseName: "", //企业名称
          province: this.form.province, //省份
          city: this.form.city, //市
          area: this.form.area, //区
          pageNum: this.form.pageNum, //页码
          pageSize: this.form.pageSize, //每页条数
        };
        const res = await enterpriseListAPI(props);
        this.ishow = true;
        this.formData = data;
        this.tableData = res.result.records;
        this.total = res.result.total;
      } catch (error) {
        console.log(error);
      } finally {
      this.Listload = false;
      }
    },
    // 获取树状图
    async attractLsit() {
      try {
        this.treeload = true;
        let data = {
          orgChainId:this.$route.query.id|| getPathId()|| null,
        };
        const res = await attractTreeAPI(data);
        this.data = [];
        this.data.push(res.result);
      } catch (error) {
        console.log(error);
      } finally {
        this.treeload = false;
      }
    },
    // 勾选
    getRowKeys(row) {
      //console.log(row,"row?");
      //console.log(row,'--row--row');
      return row.id;
    },
    // 选中或者取消选中
    handleSelectStockChange(val) {
      //console.log(val, "val");
      let stockSelectlist = [];
      val.forEach((el) => {
        stockSelectlist.push(el.id);
      });
      this.multipleSelection = stockSelectlist;
      //console.log(this.SelectStock,'sele');
    },
    // 批量移除
    batchDelete() {
      this.$confirm("确认批量移除企业吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // await 函数(id)
          await enterpriseBatchRemoveAPI({
            orgChainId:this.$route.query.id|| getPathId()|| null, //机构产业链关系id
            chainNodeId: this.nodeId.id, //产业链节点id
            ids: this.multipleSelection, //需要删除的id集合
          });
          this.$message({
            type: "success",
            message: "删除成功!",
          });
          this.multipleSelection=[]
          this.handleNodeClick(this.nodeId);
        })
        .catch(() => {});
    },
    // 移除
    async dels(id) {
      this.$confirm("确认移除该企业吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          //console.log("确定");
          let ids = [];
          ids.push(id);
          await enterpriseBatchRemoveAPI({
            orgChainId:this.$route.query.id|| getPathId()|| null, //机构产业链关系id
            chainNodeId: this.nodeId.id, //产业链节点id
            ids: ids, //需要删除的id集合
          });
          this.$message({
            type: "success",
            message: "删除成功!",
          });
          this.handleNodeClick(this.nodeId);
        })
        .catch(() => {
        });
    },
    // 重置
    reset() {
      this.form = {
        pageNum: 1, //	int	是	页面编号，默认是1
        pageSize: 10, //	int	是	页面大小，默认是20
        province: "",
        city: "",
        area: "",
      };
      this.handleNodeClick(this.nodeId);
    },
    // 省
    async cityList() {
      const res = await cityAPI({
        type: 1,
      });
      this.city = res.result;
    },
    // 市
    async cityList2(value) {
      const data = this.city.filter((item) => {
        if (item.name == value) {
          return item;
        }
      });
      this.cityid = data[0].id;
      const res = await cityAPI({
        type: 2,
        parentId: this.cityid,
      });
      this.form.city = "";
      this.form.area = "";
      this.city2 = [];
      this.city1 = res.result;
    },
    // 区
    async cityList3(value) {
      const data = this.city1.filter((item) => {
        if (item.name == value) {
          return item;
        }
      });
      this.areaid = data[0].id;
      const res = await cityAPI({
        type: 3,
        parentId: this.areaid,
      });
      this.form.area = "";
      this.city2 = res.result;
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep {
  .el-input--suffix .el-input__inner {
    width: 180px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #3370FF;
    border-color: #3370FF;
  }
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #3370FF;
    border-color: #3370FF;
  }
  .el-pagination {
    //background-image: linear-gradient(to bottom right, #1b53a8, #00122e) !important;
    position: unset !important;
    //text-align: center !important;
  }
  .el-pagination .el-select .el-input {
    margin-right: 90px;
  }
}
.down-tree{
 height: 60rem;
 display: block;
 overflow-y: scroll;
 margin-right: 30px;
}
.isTable{
  position: relative;
  p{
    font-size: 20px;
    margin-top: 10rem;
    position: absolute;
    font-size: 23px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
.catalogue {
  color: #c2b0b0;
  pointer-events: none;
  cursor: not-allowed !important;
}
.top {
  display: flex;
}
.upload {
  margin: 0px 10px;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.buttons {
  width: 100%;
  display: flex;
  margin-top: 1rem;
  margin-bottom: 2rem;

  .btnss {
    margin-left: auto;
  }
}
.table {
  margin-top: 2rem;
}
.allocation {
  display: flex;
}
.tree {
  width: 250px;
  margin-top: 20px;
}
.from {
  margin-left: 30px;
}
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #3370FF;
  color: rgba(255, 255, 255, 0.85);
}
</style>