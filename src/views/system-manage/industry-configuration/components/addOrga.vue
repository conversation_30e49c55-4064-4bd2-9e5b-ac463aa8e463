<template>
  <div>
    <el-dialog
      width="40%"
      title="添加机构产业链关系"
      :visible="dialogFormVisible"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="form"
        label-position="top"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <!--         <el-form-item
          prop="orgName"
          label="机构名称"
        >
          <el-autocomplete
            v-model="form.orgName"
            class="inline-input"
            :fetch-suggestions="querySearch"
            :trigger-on-focus="false"
            placeholder="请输入机构名称"
            @blur="blur"
            @select="handleSelect"
          />
        </el-form-item> -->
        <el-form-item
          prop="orgName"
          label="机构名称:"
          class="appendToBodyFalse"
        >
          <el-select
            v-model="form.orgName"
            :popper-append-to-body="false"
            filterable  
            placeholder="请输入机构名称关键词"
          >
            <el-option
              v-for="item in organizationList"
              :key="item.id"
              :label="item.orgName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <!--         <el-form-item
          prop="chainName"
          label="产业链名称"
        >
          <el-autocomplete
            v-model="form.chainName"
            class="inline-input"
            :fetch-suggestions="querySearch1"
            :trigger-on-focus="false"
            placeholder="请输入产业链名称"
            @blur="blur1"
            @select="handleSelect1"
          />
        </el-form-item> -->
        <el-form-item
          prop="chainName"
          label="产业链名称:"
          class="appendToBodyFalse"
        >
          <el-select
            v-model="form.chainName"
            :popper-append-to-body="false"
            multiple
            collapse-tags
            filterable  
            placeholder="请输入产业链名称关键词"
            :no-data-text="form.orgName ? '无数据' : '请先选择机构'"
          >
            <el-option
              v-for="item in industryList"
              :key="item.id"
              :label="item.chainName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="form.notes"
            placeholder="请输入备注"
            type="textarea"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="cancel">
          取 消
        </el-button>
        <el-button
          v-loading="loading"
          type="primary"
          @click="preserve"
        >
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
    
    <script>
import { autoSearchAPI } from "@/api/Settled";
import { industryChainTagAPI } from "@/api/industryChain";
import { addAPI , getAllinstitutionAPI , getindustryAllAPI} from "@/api/industryConfiguration";
//import {  addIndustryAPI } from '@/api/industry'
export default {
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      organizationList:[],
      industryList:[],
      form: {
        notes:'',
        chainName:[],
        orgName:""
      },
      id: "",
      id1:"",
      rules: {
        orgName: [
          { required: true, message: "机构名称不能为空", },
          //{ max: 10, message: "机构名称应为10字符以内", trigger: "blur" },
        ],
        chainName: [
          { required: true, message: "产业链名称不能为空", },
          //{ max: 20, message: "产业链名称应为20字符以内", trigger: "blur" },
        ],
      },
    };
  },
  watch:{
    'form.orgName'(newValue){
      this.form.chainName=[]
      if(newValue){
        this.getindustryAll(newValue)
      }
    }
  },
  created() {
    this.getAllinstitution()
    //this.getindustryAll()
  },
  methods: {
    async getAllinstitution(){
     const res =  await  getAllinstitutionAPI()
     this.organizationList= res.result
    },
    async getindustryAll(id){
     const res =  await  getindustryAllAPI({
      orgId:id
     })
     this.industryList= res.result
    },
    blur() {
      this.form.orgName = "";
    },
    blur1() {
      this.form.chainName = "";
    },
    async handleSelect(item) {
      this.id = item.id;
    },
    async handleSelect1(item) {
      item.value = this.form.chainName;
      this.id1 = item.id;
    },
    async industryChainTag() {
      const res = await industryChainTagAPI();
    },
    async querySearch(queryString, cb) {
      const res = await autoSearchAPI({
        orgName: this.form.orgName,
      });
      var results = res.result.searchHits;
      let dataList = [];
      for (let i = 0; i <= results.length - 1; i++) {
        dataList[i] = {
          id: results[i].content.id,
          // 需要拼接的话在这里
          value: `${results[i].content.orgName}-(机构代码${results[i].content?.orgCode})`
        };
      }
      cb(dataList);
    },
    async querySearch1(queryString, cb) {
      const res = await industryChainTagAPI({
        organizeId: this.id,
        chainName:this.form.chainName
      });
      if ( res.result === null) {
        res.result = {
        totalHits: "0",
        totalHitsRelation: "EQUAL_TO",
        maxScore: "NaN",
        scrollId: null,
        searchHits: [],
        aggregations: null,
        empty: true
        }
      }
      var results = res.result
      let dataList = [];
      for (let i = 0; i <= results.length - 1; i++) {
        dataList[i] = {
          id: results[i].content.id,
          value: results[i].content.chainName,
        };
      }
      cb(dataList);
    },
    cancel() {
      this.$emit("update:dialogFormVisible", false);
    },
    async preserve() {
      await this.$refs.form.validate();
      this.loading = true;
      try {
         await addAPI({
          organizeId: this.form.orgName,
          industryChainIds: this.form.chainName,
          notes:this.form.notes
        });
        //const res = await addIndustryAPI(this.form)
        this.cancel();
        // 提示
        this.$message.success("新增产业链成功");
        // 调用列表接口刷新页面
        this.$emit("SettledList");
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
  <style scoped lang="scss">
.inline-input {
  width: 100%;
}
</style>