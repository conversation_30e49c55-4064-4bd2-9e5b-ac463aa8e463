<!--
  Created by CTKJ-0224 on 2021/7/20 15:34
  Description: 穿梭框组件
-->
<template>
  <el-dialog
    width="50%"
    title="添加招商企业"
    :visible="dialogFormVisible"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <el-form
      :inline="true"
      :model="form"
      class="demo-form-inline"
    >
      <el-form-item>
        <el-input
          v-model="form.user"
          placeholder="请输入企业名称关键词"
        />
      </el-form-item>
      <el-form-item>
        <div class="item">
          <el-select
            v-model="form.province"            
            placeholder="省"
            @change="cityList2"
          >
            <el-option
              v-for="item in city"
              :key="item.value"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </div>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="form.city"
          placeholder="市"
          :no-data-text="'请先选择上一级'"
          @change="cityList3"
        >
          <el-option
            v-for="item in city1"
            :key="item.value"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="form.area"
          :no-data-text="'请先选择上一级'"
          placeholder="区"
        >
          <el-option
            v-for="item in city2"
            :key="item.value"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          class="btn"
        >
          查询
        </el-button>
        <el-button
          class="btn"
          @click="reset"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <div style="padding-left:30px"> 
      <el-transfer
        v-model="value"
        class="el-transfer-info"
        :titles="['待选择', '已选择']"
        :data="dataList"
        @change="transferChange"
        @filter-method="filterMethod"
      >
        <el-pagination
          slot="left-footer"
          small
          align="right"
          :current-page="pageleft.pageNo"
          :page-size="pageleft.pageSize"
          :total="+pageleft.total"
          :pager-count="5"
          layout=" total,prev, next"
          :prev-text="'上一页'"
          :next-text="'下一页'"
          @prev-click="prevClickLeft"
          @next-click="nextClickLeft"
        />
        <el-pagination
          slot="right-footer"
          small
          :current-page="pageRight.pageNo"
          :page-size="pageRight.pageSize"
          layout="total, prev, next"
          :prev-text="'上一页'"
          :next-text="'下一页'"
          :pager-count="5"
          :total="pageRight.total"
          @prev-click="prevClickRight"
          @next-click="nextClickRight"
        /> 
      </el-transfer>
    </div>
  </el-dialog>
</template>


<script>
import { cityAPI } from "@/api/city";
import { addenterpriseListAPI , enterpriseSaveAPI} from "@/api/attractInvestment";
import { getPathId } from '@/utils/utils'

  export default {
    name:'TransferCommon',
    props: {
/*     //左侧栏总数
    dataTotal: {
      type: Array,
      default: () => []
    },
    //右侧栏总数
    valueTotal: {
      type: Array,
      default: () => []
    }, */
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
    nodeId:{
      type:Object,
      default:()=>{

      }
    }
    
  },
    data() {
      return {
        form: {
        pageNum: 1, //	int	是	页面编号，默认是1
        pageSize: 10, //	int	是	页面大小，默认是20
        province: "",
        city: "",
        area: "",
      },
      city: [],
      city1: [],
      city2: [],
        pageleft: { pageNo: 1, pageSize: 10, total: 0 },
        pageRight: { pageNo: 1, pageSize: 10, total: 20 },
        currentDatas: [],//过滤被选中的数据 */
        value:[],
        dataList:[]
      };
    },
    computed:{
    },
     mounted(){
/*       this.pageleft.total=this.dataTotal.length-this.valueTotal.length
      this.pageRight.total=this.valueTotal.length
      this.value=this.valueTotal.slice(0,20)
      this.dataList=this.dataTotal.slice(0,20)
      this.currentDatas=this.dataTotal.filter(item=>!this.valueTotal.includes(item.key)) */
    }, 
    created(){
      this.cityList()
      this.addenterpriseList()
    },
    methods:{
    async  addenterpriseList(){
       const res = await addenterpriseListAPI({
        pageNum:this.form.pageNum,
        pageSize:this.form.pageSize,
        enterpriseName:this.form.enterpriseName,
        province:this.form.province,
        city:this.form.city,
        area:this.form.area
       })
      // console.log(res);
       let arr =[]
       res.result.records.map((item)=>{
        let obj ={
          key:item.id,
          label:item.enterpriseName
        }
        arr.push(obj)
       })
       this.dataList=arr
       //console.log(res.result.total,'res.result.total');
       this.pageleft.total =  res.result.total
      },
      async cityList() {
      const res = await cityAPI({
        type: 1,
      });
      this.city = res.result;
    },
    // 市
    async cityList2(value) {
      const data = this.city.filter((item) => {
        if (item.name == value) {
          return item;
        }
      });
      this.cityid = data[0].id;
      const res = await cityAPI({
        type: 2,
        parentId: this.cityid,
      });
      this.form.city = "";
      this.form.area = "";
      this.city2 = [];
      this.city1 = res.result;
    },
    // 区
    async cityList3(value) {
      const data = this.city1.filter((item) => {
        if (item.name == value) {
          return item;
        }
      });
      this.areaid = data[0].id;
      const res = await cityAPI({
        type: 3,
        parentId: this.areaid,
      });
      this.form.area = "";
      this.city2 = res.result;
    },
       // 左侧上一页翻页
      prevClickLeft(val){
        let num1=(val-1)*this.pageleft.pageSize
        let num2=val*this.pageleft.pageSize
        let arr=this.dataTotal.filter(item=>this.value.includes(item.key))
        this.dataList=this.currentDatas.slice(num1,num2).concat(arr)
      },
      // 左侧下一页翻页
      nextClickLeft(val){
        this.form.pageNum=val
        this.addenterpriseList()
        //console.log(val,'val?');
/*         let num1=(val-1)*this.pageleft.pageSize
        let num2=val*this.pageleft.pageSize
        let arr=this.dataTotal.filter(item=>this.value.includes(item.key))
        this.dataList=this.currentDatas.slice(num1,num2).concat(arr) */
      },
      // 右侧上一页翻页
      prevClickRight(){
        let num1=(this.pageleft.pageNo-1)*this.pageleft.pageSize
        let num2=this.pageleft.pageNo*this.pageleft.pageSize
        this.value=this.valueTotal.slice(num1,num2)
      },
      // 右侧下一页翻页
      nextClickRight(){
        let num1=(this.pageRight.pageNo-1)*this.pageRight.pageSize
        let num2=this.pageRight.pageNo*this.pageRight.pageSize
        this.value=this.valueTotal.slice(num1,num2)
      },
      // 穿梭框改变
/*       transferChange(current, direction, move){
        if (direction == "left") {
          this.currentDatas = this.dataTotal.filter(item=>!current.includes(item.key))
          this.pageleft.total=this.pageleft.total+move.length
          this.pageRight.total=current.length
        }else{
          this.currentDatas=this.currentDatas.filter(item=>!move.includes(item.key))
          this.pageleft.total=this.pageleft.total-move.length
          this.pageRight.total=current.length
        }
      }, */
      // 自定义搜索条件
      filterMethod(query){
        this.currentDatas=this.dataTotal.filter(item=>(item.label.includes(query)))
        let num1=(this.pageRight.pageNo-1)*this.pageleft.pageSize
        let num2=this.pageRight.pageNo*this.pageleft.pageSize
        let arr=this.dataTotal.filter(item=>this.value.includes(item.key)) 
        this.dataList=this.currentDatas(num1,num2).concat(arr)
      }, 
    async  transferChange(current, direction, move){
      await enterpriseSaveAPI({
        orgChainId:this.$route.query.id|| getPathId()|| null, //机构产业链关系id
        chainNodeId: this.nodeId.id, //产业链节点id
        enterpriseIds:current
      })
      this.$message.success("添加企业成功")
      },
      cancel(){
        this.$emit('update:dialogFormVisible', false)
      },
      reset(){
        this.form ={
        pageNum: 1, //	int	是	页面编号，默认是1
        pageSize: 10, //	int	是	页面大小，默认是20
        province: "",
        city: "",
        area: "",
      }
      }
      
    }
  };
</script>
<style lang="scss" scoped>
#app .el-pagination{
    bottom:-4px !important
}
::v-deep{
  .el-input--suffix .el-input__inner{
  width: 150px !important;
}
.el-transfer-panel{
  width: 270px;
}
}

</style>