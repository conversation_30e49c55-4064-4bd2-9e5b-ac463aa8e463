<!-- Created by JiangHM on 2022/12/19. 入驻机构管理 -->
<template>
  <div
    ref="msMain"
    class="vue-ms-main vue-ms-main-sel"
  >
    <table-layout :tab-name-list="['机构产业链配置']">
      <!-- 查询 -->
      <template slot="selBtn">
        <!--  <el-button
          type="primary"
          plain
          size="small"
          :loading="listLoading"
          icon="el-icon-search"
          @click="search"
        >
          查询
        </el-button>
        <el-button
          size="small"
          @click="reset"
        >
          重置
        </el-button> -->
        <el-button
          class="btn"
          @click="dialogFormVisible = true"
        >
          添加
        </el-button>
      </template>
      <!-- 查询内容 -->
      <el-form
        slot="elForm"
        ref="params"
        label-width="82px"
        class="demo-form-inline"
        :model="params"
      >
        <el-form-item label="机构名称">
          <!-- <el-autocomplete
            v-model="params.orgName"
            class="inline-input"
            :fetch-suggestions="querySearch"
            :trigger-on-focus="false"
            placeholder="请输入内容"
          /> -->
          <el-select
            v-model="params.orgId"
            filterable
          >
            <el-option
              v-for="item in organizationList"
              :key="item.id"
              :label="item.orgName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产业链">
          <el-autocomplete
            v-model="params.chainName"
            class="inline-input"
            :fetch-suggestions="querySearch1"
            :trigger-on-focus="false"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="params.notes"
            placeholder="备注"
          />
        </el-form-item>
        <el-form-item label="启禁用">
          <el-select
            v-model="params.status"
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="width: 330px">
          <el-button
            v-loading="listLoading"
            class="btn"
            @click="search"
          >
            查询
          </el-button>
          <el-button
            class="btn"
            @click="reset"
          >
            重置
          </el-button>
          <el-button
            class="btn"
            @click="dialogFormVisible = true"
          >
            添加
          </el-button>
        </el-form-item>
      </el-form>
      <div slot="selTable">
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          :row-class-name="tableAddClass"
        >
          <el-table-column
            prop="orgName"
            label="机构名称"
            width="250"
            align="center"
          />
          <el-table-column
            prop="chainName"
            label="产业链名称"
            width="220"
            align="center"
          />
          <el-table-column
            prop="notes"
            label="备注"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            width="100"
            align="center"
            label="状态"
          >
            <template slot-scope="{ row }">
              <el-switch
                :value="row.status"
                active-color="#13ce66"
                inactive-color="#ff4949"
                @change="changeswitch(row)"
              />
            </template>
          </el-table-column>
          <el-table-column
            prop="gmtCreate"
            align="center"
            label="创建时间"
            width="180"
          />
          <el-table-column
            align="center"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="toPage('/MapEcharts', scope.row)"
              >
                地图
              </el-button>
              <el-divider direction="vertical" />
              <el-button
                type="text"
                @click="toPage('/IndustryGraph', scope.row)"
              >
                图谱
              </el-button>
              <el-divider direction="vertical" />
              <el-button
                type="text"
                @click="delindustry(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </table-layout>
    <div class="ye">
      <el-pagination
        :current-page.sync="params.pageNum"
        :page-sizes="[5, 10, 20, 50]"
        :page-size.sync="params.pageSize"
        :total="+total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="SettledList"
        @current-change="SettledList"
      />
    </div>
    <addOrganization
      v-if="dialogFormVisible"
      :dialog-form-visible.sync="dialogFormVisible"
      @SettledList="SettledList()"
    />
  </div>
</template>

<script>
import { autoSearchAPI } from "@/api/Settled";
import { industryChainTagAPI } from "@/api/industryChain";
import {
  configurationListAPI,
  deleteInAPI,
  updateStatusAPI,
  getAllinstitutionAPI,
} from "@/api/industryConfiguration";
import addOrganization from "./components/addOrga.vue";
import TableLayout from "@/common/components/table-layout";
import { getPathId } from "@/utils/utils";

export default {
  name: "ParkingRefund",
  components: {
    TableLayout,
    addOrganization,
  },
  beforeRouteLeave(to, from, next) {
    to.meta.keepAlive = false;
    next();
  },
  data() {
    return {
      loading: false,
      total: "",
      listLoading: false,
      dialogFormVisible: false,
      // 联系人
      options: [
        {
          value: "true",
          label: "启用",
        },
        {
          value: "false",
          label: "禁用",
        },
      ],
      tableData: [],
      params: {
        pageNum: 1, //	int	是	页面编号，默认是1
        pageSize: 10, //	int	是	页面大小，默认是20
        orgName: "", //	String	否	机构名称 枚举见备注
        chainName: "", //	date	否	产业链名称
        notes: "", //	date	否	备注
        status: "",
        orgId: "",
      },
      organizationList: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getorganizationList();
    this.SettledList();
  },

  methods: {
    async getorganizationList() {
      const res = await getAllinstitutionAPI();
      this.organizationList = res.result;
    },
    tableAddClass({ row }) {
      if (row.status == false) {
        return "tr-red";
      }
      return "";
    },
    changeswitch(row) {
      //console.log(row);
      if (!row.status) {
        this.$confirm("确定启用该机构产业链？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          await updateStatusAPI({
            relationId: row.id,
            status: 1,
          });
          // 刷新
          this.SettledList();
          this.$message.success("操作成功!");
        });
      } else {
        this.$confirm("确定禁用该机构产业链？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          await updateStatusAPI({
            relationId: row.id,
            status: 0,
          });
          // 刷新
          this.SettledList();
          this.$message.success("操作成功!");
        });
      }
    },
    delindustry(row) {
      //console.log(row);
      this.$confirm("确定删除该机构产业链？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await deleteInAPI({
          relationId: row.id,
        });
        // 刷新
        this.SettledList();
        this.$message.success("删除成功!");
      });
    },
    toPage(path, row) {
      if (path === "/IndustryGraph") {
        this.$store.dispatch("visView/changeViewData", null);
      }
      if (window.__POWERED_BY_WUJIE__) {
        // const baseURL = process.env.VUE_APP_PORT_URL
        // let newPath = `${baseURL}#/pangu${path}?id=${row.id}`;
        // window.open(newPath);
        // window.$wujie?.bus.$emit('newRouter', {path:`/pangu${path}`});
      } else {
        this.$router.push({ path: path });
      }
      localStorage.setItem("routerQuery", row.id);
    },
    async SettledList() {
      try {
        this.loading = true;
        const res = await configurationListAPI(this.params);
        // console.log(res);
        this.tableData = res.result.records;
        this.total = res.result.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    async querySearch(queryString, cb) {
      if (this.params.orgName !== "") {
        const res = await autoSearchAPI({
          orgName: this.params.orgName,
        });
        var results = res.result.searchHits;
        let dataList = [];
        for (let i = 0; i <= results.length - 1; i++) {
          dataList[i] = {
            value: results[i].content.orgName,
          };
        }
        cb(dataList);
      }
    },
    async querySearch1(queryString, cb) {
      const res = await industryChainTagAPI({
        chainName: this.params.chainName,
      });
      var results = res.result;
      let dataList = [];
      for (let i = 0; i <= results.length - 1; i++) {
        dataList[i] = {
          value: results[i].content.chainName,
        };
      }
      cb(dataList);
    },
    reset() {
      (this.params = {
        pageNum: 1, //	int	是	页面编号，默认是1
        pageSize: 10, //	int	是	页面大小，默认是20
        orgName: "", //	String	否	机构名称 枚举见备注
        chainName: "", //	date	否	产业链名称
        notes: "", //	date	否	备注
        status: "",
      }),
        this.SettledList();
    },
    async search() {
      try {
        this.params.pageNum = 1;
        this.listLoading = true;
        await this.SettledList();
      } catch (error) {
        console.log(error);
      } finally {
        this.listLoading = false;
      }
    },
  },
};
</script>
<style lang="scss">
.el-tooltip__popper {
  max-width: 900px;
}
</style>
<style scoped lang="scss">
::v-deep {
  .el-table .tr-red {
    color: #3333 !important;

    .el-button--text {
      color: #3333 !important;
    }
  }
}

.ye {
  margin-bottom: 50px;
}

.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #3370ff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}

.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}

.btn:focus {
  background: #3370ff;
  color: rgba(255, 255, 255, 0.85);
}
</style>
