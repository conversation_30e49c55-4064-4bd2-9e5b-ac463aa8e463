<!-- Created by JiangHM on 2022/12/19.  测试git 入驻机构管理 -->
<template>
  <div
    ref="msMain"
    class="vue-ms-main vue-ms-main-sel"
  >
    <table-layout
      :tab-name-list="['入驻机构管理']"
    >
      <!-- 查询 -->
      <template slot="selBtn">
        <!--         <el-button
          type="primary"
          plain
          size="small"
          :loading="listLoading"
          icon="el-icon-search"
          @click="search"
        >
          查询
        </el-button>
        <el-button
          size="small"
          @click="reset"
        >
          重置
        </el-button> -->
        <el-button
          class="btn"
          @click="Addorganization"
        >
          添加
        </el-button>
      </template>
      <!-- 查询内容 -->
      <el-form
        slot="elForm"
        ref="params"
        label-width="82px"
        class="demo-form-inline"
        :model="params"
      >
        <el-form-item label="机构名称">
          <el-autocomplete
            v-model="params.orgName"
            class="inline-input"
            :fetch-suggestions="querySearch"
            :trigger-on-focus="false"
            placeholder="请输入机构名称"
          />
        </el-form-item>
        <el-form-item label="主联系人">
          <el-autocomplete
            v-model="params.primaryContactName"
            class="inline-input"
            :fetch-suggestions="querySearch1"
            :trigger-on-focus="false"
            placeholder="请输入主联系人姓名"
          />
        </el-form-item>
        <el-form-item label="账户类型">
          <el-select
            v-model="params.accountType"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!--  <el-form-item label="启禁用">
          <el-select
            v-model="params.status"
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item style="width: 330px;">
          <el-button
            v-loading="listLoading"
            class="btn"
            @click="search"
          >
            查询
          </el-button>
          <el-button
            class="btn"
            @click="reset"
          >
            重置
          </el-button>
          <el-button
            class="btn"
            @click="Addorganization"
          >
            添加
          </el-button>
        </el-form-item>
      </el-form>
      <div slot="selTable">
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column
            prop="orgCode"
            label="机构代码"
            width="130"
            align="center"
          />
          <el-table-column
            prop="orgName"
            label="机构名称"
            width="180"
            align="center"
          />
          <el-table-column
            width="220"
            label="所在省市"
            align="center"
          >
            <template slot-scope="{ row }">
              {{ row.province }}{{ row.city }}{{ row.area }}
            </template>
          </el-table-column>
          <el-table-column
            prop="primaryContactName"
            width="160"
            label="主联系人"
            align="center"
          />
          <el-table-column
            prop="primaryAccount"
            label="主账号"
            align="center"
          />
          <el-table-column
            prop="accountType"
            label="账户类型"
            align="center"
          >
            <template slot-scope="{row}">
              {{ row.accountType | judgeType }}
            </template>
          </el-table-column>
          <!-- <el-table-column
            width="100"
            label="状态"
            align="center"
          >
            <template slot-scope="{ row }">
              <el-switch
                v-model="row.status"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </template>
          </el-table-column> -->
          <el-table-column
            prop="gmtCreate"
            align="center"
            label="创建时间"
            width="180"
          />
          <el-table-column
            align="center"
            label="操作"
          >
            <template slot-scope="{row}">
              <el-button
                type="text"
                @click="compile(row)"
              >
                编辑
              </el-button>
              <el-divider direction="vertical" />
              <el-button
                type="text"
                @click="deleteorganization(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </table-layout>
    <div class="ye">
      <el-pagination
        :current-page.sync="params.pageNum"
        :page-sizes="[5, 10, 20, 50]"
        :page-size.sync="params.pageSize"
        :total="+total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="SettledList"
        @current-change="SettledList"
      />
    </div>
    <addOrganization
      v-if="dialogFormVisible"
      :dialog-form-visible.sync="dialogFormVisible"
      :compile-echo="compileEcho"
      @SettledList="SettledList()"
    />
  </div>
</template>

<script>
import { SettledListAPI, autoSearchAPI ,detailAPI,removeAPI} from "@/api/Settled";
import addOrganization from "./components/addOrganization.vue";
import TableLayout from "@/common/components/table-layout";

export default {
  name: "ParkingRefund",
  components: {
    TableLayout,
    addOrganization,
  },
  filters:{
        //处理函数
        judgeType(value){
          if(value=='01'){
            return '正式用户'
          } else if (value=='02'){
            return '试用账户'
          } else{
            return '其他账户'
          }
        }
    },
  data() {
    return {
      //remark
      loading: false,
      total: "",
      listLoading: false,
      dialogFormVisible: false,
      compileEcho:{},
      // 联系人
      options: [
        {
          label: "全部",
          value:""
        },
        {
          label: "正式账户",
          value:"01"
        },
        {
          label: "试用账户",
          value:"02"
        },
        {
          label: "其他账户",
          value:"03"
        }
      ],
      tableData: [],
      params: {
        pageNum: 1, //	int	是	页面编号，默认是1
        pageSize: 10, //	int	是	页面大小，默认是20
        orgName: "", //	String	否	机构名称 枚举见备注
        primaryContactName: "", //	date	否	主联系人名称
        primaryAccountName: "", //	date	否	主账号姓名
        status: "",
        accountType:""
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    this.SettledList();
  },
  methods: {
    // 编辑
   async compile(row){
   const res = await detailAPI({
     id :row.id
   })
   this.compileEcho =res.result
   this.dialogFormVisible = true
  },
  async deleteorganization(row){
    this.$confirm("确定删除该机构吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        // 删除请求
        await removeAPI({
          id: row.id,
        });
        // 刷新产业链
        this.SettledList();
        this.$message({
          type: "success",
          message: "删除机构成功!",
        });
      });
  },
    Addorganization(){
      this.dialogFormVisible = true
      this.compileEcho = {}
    },
    async SettledList() {
      try {
        this.loading = true;
        const res = await SettledListAPI(this.params);
        this.tableData = res.result.records;
        this.total = res.result.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    reset() {
      this.params={
        pageNum: 1, //	int	是	页面编号，默认是1
        pageSize: 10, //	int	是	页面大小，默认是20
        orgName: "", //	String	否	机构名称 枚举见备注
        primaryContactName: "", //	date	否	主联系人名称
        primaryAccountName: "", //	date	否	主账号姓名
        status: "",
        accountType:""
      },
      this.SettledList()
    },
    async querySearch(queryString, cb) {
      const res = await autoSearchAPI({
        orgName: this.params.orgName,
      });
      var results = res.result.searchHits;
      let dataList = [];
      for (let i = 0; i <= results.length - 1; i++) {
        dataList[i] = {
          value: results[i].content.orgName,
        };
      }
      cb(dataList);
    },
    async querySearch1(queryString, cb) {
      const res = await autoSearchAPI({
        primaryContactName: this.params.primaryContactName,
      });
      var results = res.result.searchHits;
      let dataList = [];
      for (let i = 0; i <= results.length - 1; i++) {
        dataList[i] = {
          value: results[i].content.primaryContactName,
        };
      }
      cb(dataList);
    },
    async querySearch2(queryString, cb) {
      const res = await autoSearchAPI({
        primaryAccountName: this.params.primaryAccountName,
      });
      var results = res.result.searchHits;
      let dataList = [];
      for (let i = 0; i <= results.length - 1; i++) {
        dataList[i] = {
          value: results[i].content.primaryAccountName,
        };
      }
      cb(dataList);
    },
    async search() {
    try {
      this.params.pageNum = 1;
      this.listLoading=true
      await this.SettledList();
    } catch (error) {
      console.log(error);
    }finally{
      this.listLoading=false
    }
    }
  },
};
</script>
<style lang="scss">
.el-tooltip__popper {
 max-width: 900px;
}
</style>
<style scoped lang="scss">
.ye {
  margin-bottom: 50px;
}
.btn{
  color: rgba(255,255,255,0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover{
 background: #1765AD;;
 color: rgba(255,255,255,0.85);
}
.btn:active{
 background: #52ABFF;
 color: rgba(255,255,255,0.85);
}
.btn:focus{
 background: #3370FF;
 color: rgba(255,255,255,0.85);
}
</style>
