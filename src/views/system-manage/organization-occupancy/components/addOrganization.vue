<template>
  <div>
    <el-dialog
      width="50%"
      :title="compileEcho.id ? '编辑机构' : '新增机构'"
      :visible="dialogFormVisible"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="form"
        label-position="left"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item
          prop="accountType"
          label="账户类型"
        >
          <el-radio-group v-model="form.accountType">
            <el-radio label="01">
              正式账户
            </el-radio>
            <el-radio label="02">
              试用账户
            </el-radio>
            <el-radio label="03">
              其他账户
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          prop="orgName"
          label="机构名称"
        >
          <el-input
            v-model="form.orgName"
            placeholder="请输入机构名称"
          />
        </el-form-item>
        <el-form-item
          v-if="compileEcho.id"
          label="机构代码"
        >
          <el-input
            v-model="orgCode"
            disabled
          />
        </el-form-item>
        <el-form-item
          prop="province"
          label="辖区范围"
        >
          <el-select
            v-model="form.province"
            placeholder="请选择"
            clearable 
            @clear="empty"
            @change="cityList2"
          >
            <el-option
              v-for="item in city"
              :key="item.value"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
          <el-select
            v-model="form.city"
            placeholder="请选择"
            clearable 
            :no-data-text="'请先选择上一级'"
            @clear="empty2"
            @change="cityList3"
          >
            <el-option
              v-for="item in city1"
              :key="item.value"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
          <el-select
            v-model="form.area"
            :no-data-text="'请先选择上一级'"
            placeholder="请选择"
            clearable 
          >
            <el-option
              v-for="item in city2"
              :key="item.value"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="primaryContactName"
          label="主联系人"
        >
          <el-input
            v-model="form.primaryContactName"
            placeholder="请输入主联系人名称"
            class="miniInput"
          />
        </el-form-item>
        <el-form-item
          prop="primaryContactPhone"
          label="手机号码"
        >
          <el-input
            v-model="form.primaryContactPhone"
            placeholder="请输入联系人手机号码"
            class="miniInput"
          />
        </el-form-item>
        <el-form-item
          label="邮箱"
          prop="email"
        >
          <el-input
            v-model="form.email"
            placeholder="请输入邮箱"
          />
        </el-form-item>
        <!--         <div class="linkmanphone">
          <el-form-item
            prop="primaryAccountName"
            label="主账号"
          >
            姓名：<el-input
              v-model="form.primaryAccountName"
              placeholder="请输入主联系人姓名"
              class="miniInput"
            />
          </el-form-item>
          <el-form-item
            class="phone"
            prop="primaryAccount"
          >
            账号（电话）：<el-input
              v-model="form.primaryAccount"
              placeholder="请输入主联系人电话"
              class="miniInput"
            />
          </el-form-item>
        </div> -->
        <el-form-item
          prop="roleIds"
          label="主账号角色"
        >
          <el-select
            v-model="form.roleIds"
            multiple
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in cityListCommand"
              :key="index"
              command="item"
              :label="item.roleName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            placeholder="请输入备注"
            type="textarea"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="cancel">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="preserve"
        >
          确 认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addSettledAPI, roleAPI ,institutionupdateAPI} from "@/api/Settled";
import { cityAPI } from "@/api/city";
export default {
  name:"AddOrganization",
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
    compileEcho:{
      type:Object,
      default:()=>{}
    }
  },
  data() {
    return {
      form: {
        orgName: "",
        accountType: "",
        province: "",
        city: "",
        area: "",
        orgCode:"",
        primaryContactName: "",
        primaryContactPhone: "",
        remark: "",
        email:"",
        roleIds: [],
      },
      cityid: "",
      areaid: "",
      cityListCommand: [],
      city: [],
      city1: [],
      city2: [],
      rules: {
        accountType: [
          { required: true, message: "账户类型必填", trigger: "blur" },
        ],
        orgName: [
          { required: true, message: "机构名称不能为空", trigger: "blur" },
          { min: 4, message: "机构名称应为四位以上", trigger: "blur" },
          { max: 14, message: "机构名称最多为十四位", trigger: "blur" },
        ],
        province: [
          { required: true, message: "辖区范围不能为空", trigger: "blur" },
        ],
        primaryContactName: [
          { required: true, message: "主联系人姓名不能为空", trigger: "blur" },
          { max: 10, message: "主联系人姓名不能超过十位", trigger: "blur" },
        ],
        email: [
        { pattern:/^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,message: "邮箱格式不符合规则", trigger: "blur"}
        ],
        primaryContactPhone: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          { pattern:/^(?:(?:\+|00)86)?1[3-9]\d{9}$/,message: "手机号不符合规则", trigger: "blur"}
        ],
/*         primaryAccount: [
          { required: true, message: "主账号电话不能为空", trigger: "blur" },
          { pattern:/^(?:(?:\+|00)86)?1[3-9]\d{9}$/,message: "手机号不符合规则", trigger: "blur"}
        ], */
        roleIds: [
          { required: true, message: "请选择主账号角色", trigger: "blur" },
        ],
      },
    };
  },
 async created() {
    if(this.compileEcho.id){
    this.form.orgName = this.compileEcho.orgName
    this.form.accountType = this.compileEcho.accountType
    this.form.province = this.compileEcho.province
    this.form.area = this.compileEcho.area
    this.form.city = this.compileEcho.city
    this.form.primaryContactName = this.compileEcho.primaryContactName
    this.form.primaryContactPhone = this.compileEcho.primaryContactPhone
    this.form.remark = this.compileEcho.remark
    this.form.email = this.compileEcho.email
    this.form.roleIds = this.compileEcho.roles.map(item => item.id)
    this.form.id = this.compileEcho.id
    this.orgCode = this?.compileEcho?.orgCode
    }
    this.role();
    await this.cityList();
    await this.cityList2s(this.form.province)
    this.cityList3s(this.form.city)
  },
  methods: {
    empty(){
    this.form.area = ''
    this.form.city = ''
    this.city1= [],
    this.city2=[]
    },
    empty2(){
    this.form.area = ''
    this.form.city = ''
    this.city2=[]
    },
    cancel() {
      this.$emit("update:dialogFormVisible", false);
    },
    async preserve() {
      await this.$refs.form.validate();
       this.compileEcho.id ? await institutionupdateAPI(this.form)  : await addSettledAPI(this.form);
        this.cancel();
        this.compileEcho.id ?  this.$message.success("编辑机构成功") : this.$message.success("新增机构成功");
        this.$emit("SettledList");
    },
    async role() {
      const res = await roleAPI();
      this.cityListCommand = res.result;
    },
    async cityList() {
      const res = await cityAPI({
        type: 1,
      });
      this.city = res.result;
    },
    async cityList2(value) {
      const data = this.city.filter((item) => {
        if (item.name == value) {
          return item;
        }
      });
      this.cityid = data[0].id;
      const res = await cityAPI({
        type: 2,
        parentId: this.cityid,
      });
      this.form.city = "";
      this.form.area = "";
      this.city2 = []
      this.city1 = res.result;
    },
    async cityList3(value) {
      const data = this.city1.filter((item) => {
        if (item.name == value) {
          return item;
        }
      });
      this.areaid = data[0].id;
      const res = await cityAPI({
        type: 3,
        parentId: this.areaid,
      });
      this.form.area = "";
      this.city2 = res.result;
    },
    async cityList2s(value) {
      const data = this.city.filter((item) => {
        if (item.name == value) {
          return item;
        }
      });
      this.cityid = data[0].id;
      const res = await cityAPI({
        type: 2,
        parentId: this.cityid,
      });
      this.city2 = []
      this.city1 = res.result;
    },
    async cityList3s(value) {
      const data = this.city1.filter((item) => {
        if (item.name == value) {
          return item;
        }
      });
      this.areaid = data[0].id;
      const res = await cityAPI({
        type: 3,
        parentId: this.areaid,
      });
      this.city2 = res.result;
    },
  },
};
</script>

<style scoped lang="scss">
</style>