<template>
  <div>
    <el-dialog
      width="50%"
      title="导入失败原因"
      :visible="toleadfail"
      :close-on-click-modal="false"
      center
      @close="cancel"
    >
      <p>{{ excelEesult.msg }}</p>
      <h2 v-if="excelEesult.result !=null">
        错误数据:
      </h2>
      <div
        v-if="excelEesult.result !=null"
        class="ovfl"
      >
        <p
          v-for="(item,index) in excelEesult.result"
          :key="index"
        >
          {{ item }}
        </p>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="cancel">
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
    <script>
export default {
  name: "ExcelError",
  props: {
    toleadfail: {
      type: Boolean,
      default: false,
    },
    excelEesult: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {

    };
    
  },
  methods:{
    cancel() {
        this.$emit("update:toleadfail",false)
      },
  }
};
</script>
    
<style scoped lang="scss">
.ovfl{
  overflow: auto;
  height: 100%;
  overflow-x: hidden;
  height: 300px;
}
</style>