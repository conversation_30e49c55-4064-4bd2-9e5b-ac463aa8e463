<template>
  <div>
    <el-dialog
      width="50%"
      title="请先选择标签"
      :visible="choosetag"
      :close-on-click-modal="false"
      center
      @close="cancel"
    >
      <el-select
        v-model="value"
        filterable
        placeholder="请选择标签类型"
        @change="enterpriseTagList"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.typeName"
          :value="item.id"
        />
      </el-select>
      <br>
      <el-select
        v-model="value1"
        class="select"
        filterable
        placeholder="请选择标签"
        :no-data-text="'请先选择上一级'"
      >
        <el-option
          v-for="item in tagList"
          :key="item.value"
          :label="item.labelName"
          :value="item.id"
        />
      </el-select>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-upload
          v-if="value1!==''"
          class="upload"
          action="#"
          :show-file-list="false"
          :on-change="Binding"
          accept="'.xlsx','.xls'"
          :auto-upload="false"
        >
          <el-button
            v-loading="affirm"
            :disabled="affirm"
          >
            选择文件
          </el-button>
        </el-upload>
      </div>
    </el-dialog>
  </div>
</template>
      <script>
import { enterpriseTagListAPI ,tagClassifyAPI ,BindingAPI} from "@/api/enterpriseData";
export default {
  name: "ExcelError",
  props: {
    choosetag: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
        options:[],
        value:'',
        tagList:[],
        value1:"",
        affirm:false,
        Eesult:[],
         actionUrl:"",
    };
  },
  created() {
    this.actionUrl=localStorage.getItem('originPath')
    this.tagClassify()
  },
  methods: {
    cancel() {
      this.$emit("update:choosetag", false);
    },
    async tagClassify() {
      const res = await tagClassifyAPI();
      this.options=res.result
    },
    async enterpriseTagList(){
        this.value1=''
        const res =await enterpriseTagListAPI({
            labelTypeId:this.value
        })
        this.tagList=res.result
    },
   async Binding(file){
    var name=file.name.substring(file.name.lastIndexOf(".")+1);
      try {
      this.affirm =true
      var date = new Date(); 
      var year = date.getFullYear(); 
      var month = date.getMonth() + 1;
      var day = date.getDate();
      var hour = date.getHours(); // 获取当前小时数(0-23)
      var minute = date.getMinutes(); // 获取当前分钟数(0-59)
      var s =  date.getSeconds(); // 获取当前秒数(0-59)
      month = (month > 9) ? month : ("0" + month);
      day = (day < 10) ? ("0" + day) : day;
      hour = (hour < 10) ? ("0" + hour) : hour;
      minute = (minute < 10) ? ("0" + minute) : minute;
      s = (s < 10) ? ("0" + s) : s;
      var today = year  + month  + day + hour + minute +s
      //console.log(year,"-",month,"-",day,"-",hour,"-",minute,'-',s);
      if(name !== 'xlsx' && name !== 'xls'){  
         return this.$message.error("文件格式有误");
      }
      //console.log(today);
      const res = await BindingAPI({
         file:file.raw,
         batchNumber:today,
         labelTypeId:this.value,
         labelId:this.value1
      })
      this.Eesult=res
      if(res.code =="SUCCESS"){
        this.$message.success("导入成功");      
      }else{
        //this.toleadfail=true
        this.$emit('Ee',this.Eesult)
        this.$message.error("导入失败");
      }
      } catch (error) {
        console.log(error);
      } finally{
        this.cancel()
        this.affirm =false
      }
   }
  },
};
</script>
<style scoped lang="scss">
.select{
    margin-top: 1rem;
}
</style>