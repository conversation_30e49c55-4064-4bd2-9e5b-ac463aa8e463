<!-- Created by JiangHM on 2022/12/19. 入驻机构管理 -->
<template>
  <div
    ref="msMain"
    class="vue-ms-main vue-ms-main-sel"
  >
    <table-layout :tab-name-list="['企业数据管理']">
      <!-- 查询 -->
      <template slot="selBtn">
        <!--  <el-button
          type="primary"
          plain
          size="small"
          :loading="listLoading"
          icon="el-icon-search"
          @click="search"
        >
          查询
        </el-button>
        <el-button
          size="small"
          @click="reset"
        >
          重置
        </el-button> -->
        <!--  <el-button> 导入专利 </el-button> -->
        <!--<el-button @click="$router.push('/business/toLead')">
          导入企业
        </el-button> -->
      </template>
      <!-- 查询内容 -->
      <el-form
        slot="elForm"
        ref="form"
        label-width="82px"
        class="demo-form-inline"
        :model="form"
      >
        <el-form-item
          label="企业名称"
        >
          <div style="display: flex; flex-direction: column;position: relative;">
            <el-autocomplete
              v-model="form.enterpriseName"
              class="inline-input"
              :fetch-suggestions="querySearch"
              :trigger-on-focus="false"
              placeholder="请输入关键词"
            />
          </div>
        </el-form-item>
        <el-form-item label="辖区范围">
          <div class="item">
            <el-select
              v-model="form.province"
              placeholder="省"
              @change="cityList2"
            >
              <el-option
                v-for="item in city"
                :key="item.value"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="form.city"
            placeholder="市"
            :no-data-text="'请先选择上一级'"
            @change="cityList3"
          >
            <el-option
              v-for="item in city1"
              :key="item.value"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="form.area"
            placeholder="区"
            :no-data-text="'请先选择上一级'"
          >
            <el-option
              v-for="item in city2"
              :key="item.value"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <!--     <el-form-item label="经营范围">
          <el-input
            v-model="form.businessScope"
            placeholder="请输入内容"
          />
        </el-form-item> -->
        <el-form-item label="企业标签">
          <el-select
            v-model="form.enterpriseLabelId"
            filterable
            remote
            clearable
            no-data-text="没有找到该企业标签"
            reserve-keyword
            placeholder="请输入关键词"
            :remote-method="remoteMethod"
          >
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.content.labelName"
              :value="item.content.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="行业分类">
          <!-- :append-to-body="false"  class="appendToBodyFalse classifier" -->
          <el-cascader 
            v-model="classifier"
            style="width: 100%;"
            :options="industry"
            collapse-tags
            :props="{ checkStrictly: true ,children:'childNode' ,label:'name',value:'id'}"
            clearable

          />
        </el-form-item>
        <el-form-item label="产业链标签">
          <el-select
            v-model="form.industryLabelId"
            filterable
            remote
            clearable
            no-data-text="没有找到该产业链标签"
            reserve-keyword
            placeholder="请输入关键词"
            :remote-method="remoteMethod1"
          >
            <el-option
              v-for="item in options1"
              :key="item.id"
              :label="item.content.labelName"
              :value="item.content.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            v-loading="listLoading"
            class="btn"
            @click="search"
          >
            查询
          </el-button>
          <el-button
            class="btn"
            @click="reset"
          >
            重置
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button
            class="btn"
            @click="open"
          >
            企业数据导入
          </el-button>
        </el-form-item>
        <el-dialog
          title=" 企业数据导入"
          :visible.sync="opendig"
          width="30%"
          :before-close="opendig"
        >
          <el-upload
            class="upload"
            action="#"
            :show-file-list="false"
            :on-change="mainCompanyto"
            accept="'.xlsx','.xls'"
            :auto-upload="false"
          >
            <el-button
              v-loading="mainCompany"
              class="btn"
            >
              上市公司营收导入
            </el-button>
          </el-upload>
          <el-button
            class="btn"
            @click="choosetag = true"
          >
            导入企业和企业标签关联关系
          </el-button>
          <el-upload
            class="upload"
            action="#"
            :show-file-list="false"
            :on-change="affirmstate"
            accept="'.xlsx','.xls'"
            :auto-upload="false"
          >
            <el-button
              v-loading="affirm"
              class="btn"
            >
              导入更新状态
            </el-button>
          </el-upload>
          <el-upload
            class="upload"
            action="#"
            :show-file-list="false"
            :on-change="BindingRelation"
            accept="'.xlsx','.xls'"
            :auto-upload="false"
          >
            <el-button
              v-loading="Relation"
              class="btn"
            >
              导入企业和产业链标签的关系
            </el-button>
          </el-upload>
          <el-upload
            class="upload"
            action="#"
            :show-file-list="false"
            :on-change="toleadtag"
            accept="'.xlsx','.xls'"
            :auto-upload="false"
          >
            <el-button
              v-loading="totag"
              class="btn"
            >
              导入产业链标签
            </el-button>
          </el-upload>
          <el-upload
            class="upload"
            action="#"
            :show-file-list="false"
            :on-change="handleExcel"
            accept="'.xlsx','.xls'"
            :auto-upload="false"
          >
            <el-button
              v-loading="tolead"
              class="btn"
            >
              导入企业基本信息
            </el-button>
          </el-upload>
          <span
            slot="footer"
            class="dialog-footer"
          >
            <el-button @click="opendig = false">关 闭</el-button>
          </span>
        </el-dialog>
      </el-form>
      <div slot="selTable">
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column
            align="center"
            fixed="left"
            label="企业名称/省市区"
            width="330"
          >
            <template slot-scope="{ row }">
              {{ row.enterpriseName }}
              <br>
              {{ row.province }}<span v-if="row.province != row.city">{{ row.city }}</span>{{ row.area }}
            </template>
          </el-table-column>
          <el-table-column
            label="实缴资本/注册资本(万元)"
            align="center"
            width="180"
          >
            <template slot-scope="{ row }">
              {{ row.paidUpCapital }}/{{ row.registeredCapital }}
            </template>
          </el-table-column>
          <el-table-column
            width="100"
            align="center"
            label="参保人数"
          >
            <template slot-scope="{ row }">
              {{ row.insuredPersonsNumber }}
            </template>
          </el-table-column>
          <el-table-column
            width="220"
            align="center"
            label="行业分类"
          >
            <template slot-scope="{ row }">
              {{ row.nationalStandardIndustry
              }}{{ row.nationalStandardIndustryBig
              }}{{ row.nationalStandardIndustryMiddle }}
            </template>
          </el-table-column>
          <el-table-column
            label="经营范围"
            align="center"
            show-overflow-tooltip
            width="300"
          >
            <template slot-scope="{ row }">
              {{ row.businessScope }}
            </template>
          </el-table-column>
          <!--   <el-table-column
            label="上市信息:股票代码/上市类型"
            align="center"
            width="300"
          >
            <template slot-scope="{ row }">
              {{ row.stockCode }}/{{ row.typeOfListing }}
            </template>
          </el-table-column> -->
          <el-table-column
            label="企业标签"
            show-overflow-tooltip
            align="center"
            width="300"
          >
            <template slot-scope="{ row }">
              {{ row.enterpriseLabels }}
            </template>
          </el-table-column>
          <el-table-column
            label="专利个数"
            align="center"
            width="100"
          >
            <template slot-scope="{ row }">
              {{ row.patentNumber }}
            </template>
          </el-table-column>
          <!--    <el-table-column
            label="企业评分"
            align="center"
            width="100"
          >
            <template slot-scope="{ row }">
              {{ row.scoreOfEnterprise }}
            </template>
          </el-table-column> -->
          <el-table-column
            label="产业链标签"
            show-overflow-tooltip
            align="center"
            width="300"
          >
            <template slot-scope="{ row }">
              {{ row.industryLabels }}
            </template>
          </el-table-column>
          <!-- <el-table-column
            width="100"
            align="center"
            label="状态"
          >
            <template slot-scope="{ row }">
              <el-switch
                v-model="row.status"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </template>
          </el-table-column> -->
          <el-table-column
            prop="gmtModify"
            label="最近更新"
            align="center"
            width="200"
          />
          <el-table-column
            fixed="right"
            align="center"
            width="130"
            label="操作"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                @click="particulars(row.id)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </table-layout>
    <div class="ye">
      <span
        style="text-align: center;margin-top: 20px;padding-bottom: 20px;font-size: 14px;"
      >共{{ total }}条</span>
      <el-pagination
        :current-page.sync="form.pageNum"
        :page-sizes="[5, 10, 20, 50]"
        :page-size.sync="form.pageSize"
        :total="+totals"
        layout="sizes, prev, pager, next, jumper"
        @size-change="enterpriseList"
        @current-change="enterpriseList"
      />
      <div
        v-if="total>=10000"
        style="text-align: center;margin-top: 20px;padding-bottom: 20px;font-size: 14px;"
      >
        最多查看10000条数据
      </div>
    </div>
    <excer
      v-if="toleadfail"
      :toleadfail.sync="toleadfail"
      :excel-eesult="excelEesult"
    />
    <tagSelect
      v-if="choosetag"
      :choosetag.sync="choosetag"
      @Ee="Eesult"
    />
  </div>
</template>

<script>
import excer from "./excer.vue";
import tagSelect from "./tagSelect.vue";
import { enterpriseListAPI, tagAutoSearchAPI,classificationTreeAPI} from "@/api/enterpriseData";
import { industrylabelAPIqiyong } from "@/api/industryChain";
import listRmdPageMixin from "@/common/mixins/listRmdPageMixin";
import TableLayout from "@/common/components/table-layout";
import { cityAPI } from "@/api/city";
//import {filterData} from '@/utils/utils'
import {nameAssociateAPI} from '@/views/system-manage/attract-investment/apiUrl'
import {
  enterpriseExeclAPI,
  enterpriseTagExeclAPI,
  BindingRelationAPI,
  affirmstateAPI,
  mainCompanyAPI
} from "@/api/enterpriseData";
export default {
  name: "EnterpriseData",
  components: {
    TableLayout,
    excer,
    tagSelect,
  },
  mixins: [listRmdPageMixin],
  data() {
    return {
       actionUrl:"",
      mainCompany:false,
      toleadfail: false,
      choosetag: false,
      excelEesult: {},
      affirm: false,
      Relation: false,
      tolead: false,
      totag: false,
      loading: false,
      dialogFormVisible: false,
      opendig:false,
      options: [],
      options1: [],
      city: [],
      city1: [],
      city2: [],
      cityid: "",
      areaid: "",
      //总数
      total: 0,
      totals:0,
      // 列表
      tableData: [],
      form: {
        pageNum: 1, //	int	是	页面编号，默认是1
        pageSize: 10, //	int	是	页面大小，默认是20
        province: "",
        city: "",
        area: "",
        businessScope: "", // 经营范围
        enterpriseLabelId: "", // 企业标签
        patentName: "", //专利名称
        enterpriseName: "", //企业名称
        industryLabelId: "", //产业链标签id
        nationalStandardIndustryId:"",//门类
        nationalStandardIndustryBigId:"",//大类
        nationalStandardIndustryMiddleId:"",//中类
        nationalStandardIndustrySmallId:"",//小类
      },
      classifier:[],//行业分类
      associationalword:[],//筛选项list
      time:null,
      showlx:false,
      industry:[{label:'1',value:'2',children:[{label:'1',value:'2'}]}],
    };
  },
  watch: {},
  created() {
    this.actionUrl=localStorage.getItem('originPath')
    this.enterpriseList();
    this.cityList();
    this.gettree()
  },
  methods: {
    async querySearch(queryString, cb) {
      if (this.form.enterpriseName !== "") {
        const res = await nameAssociateAPI({
          keyword: this.form.enterpriseName,
          unRestrict:true
        });
        var results = res.result;
        let dataList = [];
        for (let i = 0; i <= results.length - 1; i++) {
          dataList[i] = {
            value: results[i].enterpriseName,
          };
        }
        cb(dataList);
      }
    },
   async gettree(){
     const res = await classificationTreeAPI()
     this.industry=res.result
     //this.$forceUpdate();
    },
    Eesult(es) {
      this.excelEesult = es;
      this.toleadfail = true;
    },
    open(){
      this.opendig=true
    },
    async remoteMethod(query) {
      if (query !== "") {
        const res = await tagAutoSearchAPI({
          labelName: query,
        });
        this.options = res.result.searchHits;
      } else {
        this.options = [];
      }
    },
    async remoteMethod1(query) {
      if (query !== "") {
        const res = await industrylabelAPIqiyong({
          labelName: query,
        });
        this.options1 = res.result.searchHits;
      } else {
        this.options1 = [];
      }
    },
    async mainCompanyto(file){
      var name = file.name.substring(file.name.lastIndexOf(".") + 1);
      try {
        this.mainCompany = true;
        if (name !== "xlsx" && name !== "xls") {
          return this.$message.error("文件格式有误");
        }
        const res = await mainCompanyAPI(file.raw);
        this.excelEesult = res;
        if (res.code == "SUCCESS") {
          this.$message.success("导入成功");
          this.enterpriseList();
        } else {
          this.toleadfail = true;
          this.$message.error("导入失败");
        }
      } catch (error) {
        //this.$message.error(error.Message)
      } finally {
        this.mainCompany = false;
      }
    },
    // 导入企业
    async handleExcel(file) {
      var name = file.name.substring(file.name.lastIndexOf(".") + 1);
      try {
        this.tolead = true;
        if (name !== "xlsx" && name !== "xls") {
          return this.$message.error("文件格式有误");
        }
        const res = await enterpriseExeclAPI(file.raw);
        this.excelEesult = res;
        if (res.code == "SUCCESS") {
          this.$message.success("导入成功");
          this.enterpriseList();
        } else {
          this.toleadfail = true;
          this.$message.error("导入失败");
        }
      } catch (error) {
        //this.$message.error(error.Message)
      } finally {
        this.tolead = false;
      }
    },
    // 导入产业链标签
    async toleadtag(file) {
      var name = file.name.substring(file.name.lastIndexOf(".") + 1);
      try {
        this.totag = true;
        if (name !== "xlsx" && name !== "xls") {
          return this.$message.error("文件格式有误");
        }
        const res = await enterpriseTagExeclAPI(file.raw);
        if (res.code == "SUCCESS") {
          this.$message.success("导入成功");
        } else {
          this.$message.error("导入失败");
        }
      } finally {
        this.totag = false;
      }
    },
    // 企业与产业链标签之间的关系
    async BindingRelation(file) {
      var name = file.name.substring(file.name.lastIndexOf(".") + 1);
      try {
        this.Relation = true;
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var day = date.getDate();
        var hour = date.getHours(); // 获取当前小时数(0-23)
        var minute = date.getMinutes(); // 获取当前分钟数(0-59)
        var s = date.getSeconds(); // 获取当前秒数(0-59)
        month = month > 9 ? month : "0" + month;
        day = day < 10 ? "0" + day : day;
        hour = hour < 10 ? "0" + hour : hour;
        minute = minute < 10 ? "0" + minute : minute;
        s = s < 10 ? "0" + s : s;
        var today = year + month + day + hour + minute + s;
        if (name !== "xlsx" && name !== "xls") {
          return this.$message.error("文件格式有误");
        }
        const res = await BindingRelationAPI({
          file: file.raw,
          batchNumber: today,
        });
        this.excelEesult = res;
        if (res.code == "SUCCESS") {
          this.$message.success("导入成功");
        } else {
          this.toleadfail = true;
          this.$message.error("导入失败");
        }
      }finally {
        this.Relation = false;
      }
    },
    // 导入更新状态
    async affirmstate(file) {
      var name = file.name.substring(file.name.lastIndexOf(".") + 1);
      try {
        this.affirm = true;
        if (name !== "xlsx" && name !== "xls") {
          return this.$message.error("文件格式有误");
        }
        const res = await affirmstateAPI(file.raw);
        if (res.code == "SUCCESS") {
          this.$message.success("导入成功");
        } else {
          this.$message.error("导入失败");
        }
      } finally {
        this.affirm = false;
      }
    },
    // 省
    async cityList() {
      const res = await cityAPI({
        type: 1,
      });
      this.city = res.result;
    },
    // 市
    async cityList2(value) {
      const data = this.city.filter((item) => {
        if (item.name == value) {
          return item;
        }
      });
      this.cityid = data[0].id;
      const res = await cityAPI({
        type: 2,
        parentId: this.cityid,
      });
      this.form.city = "";
      this.form.area = "";
      this.city2 = [];
      this.city1 = res.result;
    },
    // 区
    async cityList3(value) {
      const data = this.city1.filter((item) => {
        if (item.name == value) {
          return item;
        }
      });
      this.areaid = data[0].id;
      const res = await cityAPI({
        type: 3,
        parentId: this.areaid,
      });
      this.form.area = "";
      this.city2 = res.result;
    },
    // 列表
    async enterpriseList() {
      try {
        this.loading = true;
        if(this.classifier.length!==0){
          if(this.classifier.length===4){
          this.form.nationalStandardIndustryId=this.classifier[0],//门类
          this.form.nationalStandardIndustryBigId=this.classifier[1]//大类
          this.form.nationalStandardIndustryMiddleId=this.classifier[2],//中类
          this.form.nationalStandardIndustrySmallId=this.classifier[3]//小类
        } else if(this.classifier.length==3){
          this.form.nationalStandardIndustryId=this.classifier[0],//门类
          this.form.nationalStandardIndustryBigId=this.classifier[1]//大类
          this.form.nationalStandardIndustryMiddleId=this.classifier[2]//中类
        }else if(this.classifier.length==2){
          this.form.nationalStandardIndustryId=this.classifier[0],//门类
          this.form.nationalStandardIndustryBigId=this.classifier[1]//大类
        }else{
          this.form.nationalStandardIndustryId=this.classifier[0]//门类
        }
        }else{
          this.form.nationalStandardIndustryId=''
          this.form.nationalStandardIndustryBigId=''
          this.form.nationalStandardIndustryMiddleId=''
          this.form.nationalStandardIndustrySmallId=''
        }
        //filterData(this.form)
        const res = await enterpriseListAPI(this.form);
        this.tableData = res.result?.records;
        this.total = res.result.total;
        this.totals = res.result.total;
        if(res.result.total>10000){
          this.totals = 10000
        }
      } finally {
        this.loading = false;
      }
    },
    // 重置
    reset() {
      (this.form = {
        pageNum: 1, //	int	是	页面编号，默认是1
        pageSize: 10, //	int	是	页面大小，默认是20
        province: "",
        city: "",
        area: "",
        businessScope: "", // 经营范围
        enterpriseLabelId: "", // 企业标签
        patentName: "", //专利名称
        enterpriseName: "", //企业名称
        industryLabelId: "", //产业链标签id
        nationalStandardIndustryId:"",//门类
        nationalStandardIndustryBigId:"",//大类
        nationalStandardIndustryMiddleId:"",//中类
        nationalStandardIndustrySmallId:"",//小类
      }),
      this.classifier=[]
      this.city1 = [],
      this.city2 = []
        this.enterpriseList();
    },
    // 详情
    particulars(id) {
      this.$router.push({
        path: "/business/particulars",
        query: {
          id,
        },
      });
    },
    async search() {
      try {
        this.form.pageNum = 1;
        this.listLoading = true;
        await this.enterpriseList();
      } finally {
        this.listLoading = false;
      }
    },
  },
};
</script>
<style lang="scss">
.el-tooltip__popper {
 max-width: 900px;
}
</style>
<style scoped lang="scss">
::v-deep{
  .el-pagination{
    width: auto !important;
  }
}
.asso {
  margin-top: 1px;
  width: 482px;
  position: absolute;
  top: 128px;
  z-index: 99;
  border-radius: 4px 0px 0px 4px;
  border: 1px solid #dddddd;
  background-color: #fff;
  .alone {
    cursor: pointer;
    height: 30px;
    display: flex;
    font-size: 14px;
    align-items: center;
    padding-left: 10px;
  }
  .alone:hover {
    background-color: #dee0e3;
    font-size: 14px;
  }
}
.ye {
  display: flex;
  align-items: center;
  margin-bottom: 50px;
  justify-content: center;
  float: right;
  padding-right: 30px;
  position: relative;
  //position: absolute;
  left: 0;
  bottom: 1rem;
  right: 0;
  top: 0.5rem;
}
.item {
  display: flex;
  .el-option {
    width: 200px;
  }
}
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #3370FF;
  color: rgba(255, 255, 255, 0.85);
}
</style>
