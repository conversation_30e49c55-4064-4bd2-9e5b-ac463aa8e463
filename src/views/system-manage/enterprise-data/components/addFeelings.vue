<template>
  <div>
    <el-dialog
      width="60%"
      :title="rows.id ? '编辑舆情' : '添加舆情'"
      :visible="addfeelings"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="form"
        label-position="top"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item
          prop="newsSummary"
          label="舆情摘要"
        >
          <el-input
            v-model="form.newsSummary"
            placeholder="请输入舆情摘要"
            type="textarea"
            maxlength="150"
            show-word-limit
            :autosize="{ minRows: 2, maxRows: 3 }"
          />
        </el-form-item>
        <el-form-item
          prop="newsUrl"
          label="舆情链接"
        >
          <el-input
            v-model="form.newsUrl"
            placeholder="请输入舆情链接"
          />
        </el-form-item>
        <el-form-item
          prop="publicDate"
          label="发生日期"
          class="appendToBodyFalse"
        >
          <div class="block">
            <el-date-picker
            :append-to-body="false"
              v-model="form.publicDate"
              value-format="timestamp"
              type="date"
              placeholder="选择日期"
              :picker-options="pickerOptions"
            />
          </div>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="cancel">
          取 消
        </el-button>
        <el-button
          v-loading="loading"
          type="primary"
          @click="preserve"
        >
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
  <script>
import { addFeelingsAPI , updateAPI } from "@/api/Feelings";
export default {
  name: "CompileTag",
  props: {
    addfeelings: {
      type: Boolean,
      default: false,
    },
    id: {
      type: Number,
      default: 1,
    },
    rows: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
            return time.getTime() > Date.now();
          },
      },
      form: {},
      rules: {
        newsUrl: [
          { required: true, message: "舆情链接不能为空", trigger: "blur" },          
        ],
        publicDate: [
          { required: true, message: "发生日期不能为空", trigger: "blur" },
        ],
        newsSummary: [
          { required: true, message: "舆情摘要不能为空", trigger: "blur" },
        ],
      },
    };
  },
    created() {
    this.echo()
  },
  methods: {
    cancel() {
      this.$emit("update:addfeelings", false);
    },
    async preserve() {
        await this.$refs.form.validate();
        this.form.enterpriseId = this.id;
        this.form.source=1
        const arr = {
            id: this.form.id,
            newsSummary:this.form.newsSummary,
            newsUrl: this.form.newsUrl,
            publicDate:this.form.publicDate
      }
      try {
        this.rows.id ? await updateAPI(arr) : await addFeelingsAPI(this.form);
        this.$message.success(this.rows.id ? "编辑企业舆情成功" : "新增企业舆情成功");
        this.cancel();
        this.$emit("opinion");
      } catch (error) {
        console.log(error);
      }
      },
      echo() {
          if (this.rows.id) {
          this.form = this.rows
          const a = new Date(this.rows.publicDate)
          const time1 = a.getTime();
          this.form.publicDate = time1
        }
    },
  },
};
</script>
  
  <style>
</style>