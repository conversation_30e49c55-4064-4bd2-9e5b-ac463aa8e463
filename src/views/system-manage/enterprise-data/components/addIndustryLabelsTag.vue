<template>
  <div>
    <el-dialog
      width="30%"
      title="添加产业链标签"
      :visible="dialogFormVisible1"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-select
        v-model="labelRequests"
        placeholder="请选择"
        filterable
        @change="addTag"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.labelName"
          :value="item.labelName"
        />
      </el-select>
      <br>
      <el-tag
        v-for="tag in dynamicTags"
        :key="tag"
        closable
        :disable-transitions="false"
        @close="handleClose(tag)"
      >
        {{ tag }}
      </el-tag>
      <el-input
        v-if="inputVisible"
        ref="saveTagInput"
        v-model="inputValue"
        maxlength="10"
        placeholder="输入完成后点击回车添加"
        class="input-new-tag"
        size="small"
        @keyup.enter.native="handleInputConfirm"
      />
      <el-button
        v-else
        class="button-new-tag"
        size="small"
        @click="showInput"
      >
        + 添加标签
      </el-button>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="cancel">
          取 消
        </el-button>
        <el-button
          v-loading="loading"
          type="primary"
          @click="preserve"
        >
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
  import { industrylabelAPI ,addTagAPI ,bindTagAPI} from "@/api/industryChain";
  export default {
    name: "AddIndustryLabelsTag",
    props: {
      dialogFormVisible1: {
        type: Boolean,
        default: false,
      },
      id: {
        type: Number,
        default:1
      }
    },
    data() {
      return {
        // 产业链列表
        options: [],
        // 标签
        dynamicTags: [],
        inputVisible: false,
        inputValue: "",
        // 用于选中下拉
        labelRequests: '',
        // 选中的标签列表
        labelIds: [],
      };
    },
    created() {
      this.enterpriseTagList()
    },
    methods: {
     async preserve() {
        await bindTagAPI({
          labelIds: this.labelIds,
          enterpriseId:this.id
        })
        this.$message.success("企业标签绑定成功");
        this.cancel()
        this.$emit('Particulars')
      },
      // 点击的时候可以添加
      showInput() {
        this.inputVisible = true;
        this.$nextTick(() => {
          this.$refs.saveTagInput.$refs.input.focus();
        });
      },   
      // 新增产业链标签
      async handleInputConfirm() {
        let inputValue = this.inputValue;
        if (inputValue) {
          /* this.dynamicTags.push(inputValue); */
          // 新增产业链标签
          await addTagAPI({
            labelName: this.inputValue,
          });
          this.enterpriseTagList()
          this.$message.success("新增产业链标签成功");
        }
        this.inputVisible = false;
        this.inputValue = "";
      },
      cancel() {
        this.$emit("update:dialogFormVisible1", false);
      },
      handleClose(tag) {
        //可以在这里通过标签名找到对应的id然后删除
        //console.log(tag);
       const res =  this.options.filter(item => {
          if (item.labelName === tag) {
            return item.id
          }
       })
        
        /* this.labelIds.forEach(item => {
          if (res === item) {
            return 
          }
        })  */
        this.labelIds.splice(this.labelIds.findIndex(item => item === res)) 
       //console.log(this.labelIds);
        this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
        },
      // 标签列表
      async enterpriseTagList() {
        const res = await industrylabelAPI()
        this.options=res.result
      },
      addTag(value) {
        this.labelRequests=""
        this.dynamicTags.push(value);
        this.options.filter(item => {
          if (item.labelName == value) {
          return this.labelIds.push(item.id)
         }
        })
        this.dynamicTags = Array.from(new Set(this.dynamicTags))
        this.labelIds = Array.from(new Set(this.labelIds))
        //console.log(this.labelIds);
      },
    }
  };
  </script>
  
  <style>
  </style>