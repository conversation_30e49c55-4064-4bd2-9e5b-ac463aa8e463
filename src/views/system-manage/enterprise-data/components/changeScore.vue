<template>
  <div>
    <el-dialog
      width="25%"
      title="更改企业评分"
      :visible="dialogFormVisible2"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        class="demo-ruleForm"
      >
        <el-form-item
          label="企业评分"
          prop="scoreOfEnterprise"
        >
          <el-input v-model="ruleForm.scoreOfEnterprise" />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="cancel">
          取 消
        </el-button>
        <el-button
          v-loading="loading"
          type="primary"
          @click="submit"      
        >
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
    <script>
    import {
  updategradeAPI,
} from "@/api/enterpriseData";
    export default {
      name: "ChangeScore",
      props: {
        dialogFormVisible2: {
          type: Boolean,
          default: false,
        },
        id: {
          type: Number,
          default:1
        },
        scoreOfEnterprise:{
            type: String,
            default:"0"
        }
      },
      data() {
        return {
        ruleForm: {
          scoreOfEnterprise: '',
        },
        rules: {
        scoreOfEnterprise: [
            { required: true, message: '请输入企业评分', trigger: 'blur' },
            {
            pattern: /^100$|^(\d|[1-9]\d)$/,
            message: "最大显示个数需为正整数并且在0到100之间",
            trigger: "blur",
          },
        ]
        }
        };
      },
      created() {
        this.show()
      },
      methods: {
        show(){
            this.ruleForm.scoreOfEnterprise =  this.scoreOfEnterprise
        },
        cancel() {
          this.$emit("update:dialogFormVisible2", false);
        },
       async submit(){
        await await this.$refs.ruleForm.validate();
        try {
            await updategradeAPI({
            enterpriseId:this.id,
            scoreOfEnterprise:this.ruleForm.scoreOfEnterprise
         })
         this.cancel()
         this.$message.success("企业评分更改成功");
         this.$emit("Particulars")
        } catch (error) {
            console.log(error);
        }
        }
      }
    };
    </script>
    
    <style>
    </style>