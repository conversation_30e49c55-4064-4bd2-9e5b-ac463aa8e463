<template>
  <div>
    <el-dialog
      width="40%"
      title="添加企业标签"
      :visible="dialogFormVisible"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-select
        ref="labelRequests"
        v-model="labelRequests"
        class="select"
        multiple
        filterable
        placeholder="请选择"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.labelName"
          :value="item.id"
        />
      </el-select>
      <p>添加标签至标签库：</p>
      <div class="addtag">
        <el-select
          v-model="value"
          filterable
          placeholder="请选择标签类型"
          @change.stop="tagType"
        >
          <el-option
            v-for="item in options1"
            :key="item.value"
            :label="item.typeName"
            :value="item.id"
          />
        </el-select>
        <span>
          <el-input
            v-model="input"
            placeholder="请输入标签名称"
          />
        </span>
        <span><el-button
          class="btn"
          @click="addtag"
        >添加</el-button></span>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="cancel">
          取 消
        </el-button>
        <el-button
          v-loading="loading"
          type="primary"
          @click="preserve"
        >
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  enterpriseTagListAPI,
  addTagAPI,
  bindingTagAPI,
  tagClassifyAPI,
} from "@/api/enterpriseData";
export default {
  name: "CompileTag",
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: Number,
      default: 1,
    },
    enterpriseLabelDTOS:{
      type:Array,
      default:()=>[]
    }
  },
  data() {
    return {
      // 选中的标签列表
      labelRequests: [],
      input: "",
      value:"",
      options:[],
      options1:[],
      loading:false
    };
  },
  created() {
    this.enterpriseTagList();
    this.tagClassify()
/*     this.enterpriseLabelDTOS.filter((item)=>{
       return this.labelRequests.push(item.id)
    }) */
  },
  methods: {
    tagType(){
     //this.$refs.labelRequests.isSilentBlur=true
    },
    // 保存
    async preserve() {
      await bindingTagAPI({
        labelIds: this.labelRequests,
        enterpriseId: this.id,
      });
      this.$message.success("企业标签绑定成功");
      this.cancel();
      this.$emit("Particulars");
    },
    // 关闭弹层
    cancel() {
      this.$emit("update:dialogFormVisible", false);
    },
    // 企业标签列表
    async enterpriseTagList() {
      const res = await enterpriseTagListAPI();
      this.options = res.result;
    },
    // 标签分类
    async tagClassify() {
      const res = await tagClassifyAPI();
      this.options1=res.result
    },
    async addtag() {
      let input = this.input;
      let value = this.value;
      if (input && value) {
        await addTagAPI({
          labelName: this.input,
          labelTypeId:this.value
        });
        this.input = "";
        this.value=""
        this.$message.success("新增企业标签成功");
        this.enterpriseTagList()
      } else {
        if(input==''){
          this.$message.error("标签名称不能为空");
        }else{
          this.$message.error("标签类型不能为空")
        }

      }
    },
  },
};
</script>

<style scoped lang="scss">
p{
  font-weight:900
}
.addtag{
  display: flex;
  width: 500px;
  justify-content:space-between;
  .btn{
    margin-right: 5px;
  }
}
.select{
  width: 500px;
}
::v-deep{
.el-select-dropdown__list{
 width: 400px;
}
}
</style>