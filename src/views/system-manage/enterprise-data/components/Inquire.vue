<template>
  <div
    ref="msMain"
    class="vue-ms-main vue-ms-main-sel"
  >
    <table-layout
      :tab-name-list="['公众号搜索']"
    >
      <!-- 查询 -->
      <template slot="selBtn">
        <!--         <el-button
          class="btn"
          @click="dialogFormVisible = true"
        >
          添加
        </el-button> -->
      </template>
      <!-- 查询内容 -->
      <el-form
        slot="elForm"
        ref="params"
        label-width="62px"
        class="demo-form-inline"
        :model="form"
      >
        <el-form-item label="关键词">
          <el-input
            v-model="form.keywords"
            placeholder="请输入关键词"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            v-loading="loading"
            class="btn"
            @click="Official"
          >
            查询
          </el-button>
          <el-button
            class="btn"
            @click="reset"
          >
            重置
          </el-button>
        </el-form-item> 
      </el-form>
      <div slot="selTable">
        <el-table
          v-loading="loading"
          :data="list"
          style="width: 100%"
        >
          <el-table-column
            prop="title"
            label="标题"
            width="250"
            align="center"
          />
          <el-table-column
            prop="publisher"
            label="发布者"
            width="200"
            align="center"
          />
          <el-table-column
            prop="publishTime"
            label="发布时间"
            align="center"
            width="160"
          />
          <el-table-column
            prop="url"
            align="center"
            show-overflow-tooltip="true"
            label="文章地址"
            width="250"
          >
            <template slot-scope="{row}">
              <el-link
                target="_blank"
                :href="row.url"
                type="primary"
                :underline="false"
              >
                {{ row.url }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column
            prop="content"
            align="center"
            show-overflow-tooltip="true"
            label="内容"
          />
          <!--           <el-table-column
            align="center"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="toPage('/MapEcharts',scope.row)"
              >
                地图
              </el-button>
              <el-divider direction="vertical" />
              <el-button
                type="text"
                @click="toPage('/IndustryGraph',scope.row)"
              >
                图谱
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>
      </div>
    </table-layout>
    <div class="ye">
      <el-pagination
        :current-page.sync="form.pageNum"
        :page-sizes="[5, 10, 20, 50]"
        :page-size.sync="form.pageSize"   
        :total="+total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="Official"
        @current-change="Official"
      />
    </div>
  </div>
</template>

<script>
import TableLayout from "@/common/components/table-layout";
import { OfficialAccountsAPI } from "@/api/OfficialAccounts";
export default {
  name: "InquireCs",
  components: {
    TableLayout,
  },
  data() {
    return {
      form:{
        pageSize: 10,
        pageNum: 1,
      },
      loading:false,
      total:"",
      list:[]   
    };
  },
  computed: {},
  methods: {
    async Official() {
      try {
      this.loading = true;
      const res = await OfficialAccountsAPI(this.form);
     // console.log(res);
      this.list =res.result.records
      this.total=res.result.total
      } catch (error) {
        console.log(error);
      } finally {
      this.loading = false;
      }

    },
    reset(){
      this.form={
        pageSize: 10,
        pageNum: 1,
      }
    }
  },
};
</script>

<style scoped lang="scss">
.ye {
  margin-bottom: 50px;
}
.item {
  display: flex;
  .el-option {
    width: 200px;
  }
}
.btn{
  color: rgba(255,255,255,0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover{
 background: #1765AD;;
 color: rgba(255,255,255,0.85);
}
.btn:active{
 background: #52ABFF;
 color: rgba(255,255,255,0.85);
}
.btn:focus{
 background: #3370FF;
 color: rgba(255,255,255,0.85);
}
</style>

