<template>
  <div v-loading="loading">
    <el-button
      icon="el-icon-arrow-left"
      type="text"
      @click="$router.back()"
    >
      返回
    </el-button>

    <div>
      <el-card>
        <div class="logoImg">
          <img src="https://static.idicc.cn/cdn/pangu/logo.png">
        </div>
        <div class="name">
          <h2>{{ enterprise.enterpriseName }}</h2>
        </div>
      </el-card>
    </div>
    <el-card>
      <el-descriptions
        class="margin-top"
        title="系统信息"
        :column="3"
        border
      >
        <el-descriptions-item label="创建时间">
          {{ enterprise.gmtCreate }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ enterprise.gmtModify }}
        </el-descriptions-item>
        <el-descriptions-item label="企业评分">
          <div>
            {{ enterprise.scoreOfEnterprise }}
            <el-button
              class="text-input"
              type="text"
              @click="grade"
            >
              编辑
            </el-button>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <div class="longList">
        <div class="industryTag">
          <div class="addtag">
            <p>企业标签</p>
          </div>
          <el-tag
            v-for="tag in enterpriseLabels"
            :key="tag"
            closable
            class="tags"
            :disable-transitions="false"
            @close="handleClose(tag)"
          >
            #{{ tag }}
          </el-tag>
          <el-button
            class="btn"
            size="small"
            @click="dialogFormVisible = true"
          >
            添加
          </el-button>
        </div>
        <div class="industryTag">
          <p>产业链标签</p>
          <el-tag
            v-for="tag in industryLabels"
            :key="tag"
            closable
            class="tags"
            :disable-transitions="false"
            @close="handleClose1(tag)"
          >
            #{{ tag }}
          </el-tag>
          <el-button
            size="small"
            class="btn"
            @click="dialogFormVisible1 = true"
          >
            添加
          </el-button>
        </div>
        <div class="feelings">
          <p>新闻舆情</p>
          <el-button
            class="btn"
            size="small"
            @click="addfeeling"
          >
            添加
          </el-button>
        </div>
        <el-table
          :data="result"
          style="width: 100%"
        >
          <el-table-column
            width="120"
            align="center"
            type="index"
            label="序号"
          />
          <el-table-column
            prop="newsSummary"
            label="舆情摘要"
            show-overflow-tooltip="true"
            align="center"
            width="400"
          />
          <el-table-column
            label="链接"
            align="center"
            width="350"
          >
            <template slot-scope="{ row }">
              <a
                class="a"
                :href="row.newsUrl"
                target="_blank"
              >{{ row.newsUrl }}</a>
            </template>
          </el-table-column>
          <el-table-column
            prop="publicDate"
            align="center"
            label="发生时间"
          />
          <el-table-column
            label="操作"
            align="center"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                class="compile"
                @click="detail(row)"
              >
                编辑
              </el-button>
              <el-divider direction="vertical" />
              <el-button
                class="remove"
                type="text"
                @click="delete1(row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="ye">
          <el-pagination
            :current-page.sync="form.pageNum"
            :page-sizes="[5, 10, 20, 50]"
            :page-size.sync="form.pageSize"
            :total="+total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="opinion"
            @current-change="opinion"
          />
        </div>
        <el-descriptions
          class="margin-top"
          title="工商注册"
          :column="3"
          label-style="word-break:keep-all"
          content-style="max-width:300px;word-break:keep-all"
          border
        >
          <el-descriptions-item label="企业名称">
            {{ enterprise.enterpriseName }}
          </el-descriptions-item>
          <el-descriptions-item label="成立日期">
            {{ enterprise.registerDate }}
          </el-descriptions-item>
          <el-descriptions-item label="纳税人识别号">
            {{ enterprise.taxpayerIdentificationNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="行政区储">
            {{ enterprise.province }}{{ enterprise.city
            }}{{ enterprise.area }}
          </el-descriptions-item>
          <el-descriptions-item label="所属行业">
            {{ enterprise.industrySector }}
          </el-descriptions-item>
          <el-descriptions-item label="组织机构代码">
            {{ enterprise.organizeCode }}
          </el-descriptions-item>
          <el-descriptions-item label="实缴资本">
            {{ enterprise.paidUpCapital }}
          </el-descriptions-item>
          <el-descriptions-item label="注册资本">
            {{ enterprise.registeredCapital }}
          </el-descriptions-item>
          <el-descriptions-item label="登记状态">
            {{ enterprise.registerStatus }}
          </el-descriptions-item>
          <el-descriptions-item label="参保人数">
            {{ enterprise.insuredPersonsNumber }}
          </el-descriptions-item>
          <!--  <el-descriptions-item label="人员规模">
            {{ enterprise.personnelSize }}
          </el-descriptions-item> -->
          <el-descriptions-item label="企业类型">
            {{ enterprise.enterpriseType }}
          </el-descriptions-item>
          <el-descriptions-item label="股票代码">
            {{ enterprise.stockCode }}
          </el-descriptions-item>
          <el-descriptions-item label="上市类型">
            {{ enterprise.typeOfListing }}
          </el-descriptions-item>
          <el-descriptions-item label="法人">
            {{ enterprise.legalPerson }}
          </el-descriptions-item>
          <el-descriptions-item
            :span="2"
            label="曾用名"
          >
            {{ enterprise.formerName }}
          </el-descriptions-item>
          <el-descriptions-item label="图标(logo)">
            <img
              v-if="enterprise.logo"
              :src="enterprise.logo"
            >
            <div
              v-else
              class="default"
            >
              <span>{{ enterprise.enterpriseName.slice(0, 4) }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item
            :span="3"
            label="经营范围"
          >
            {{ enterprise.businessScope }}
          </el-descriptions-item>
        </el-descriptions>
  
        <!-- <p>股东信息</p>
        <el-table
          :data="shareholders"
          border
          style="width: 100%"
        >
          <el-table-column
            width="120"
            align="center"
            type="index"
            label="序号"
          />
          <el-table-column
            prop="shareholder"
            label="股东名称"
            align="center"
            width="300"
          />
          <el-table-column
            prop="shareholdingRatio"
            label="持股比例"
            align="center"
          />
          <el-table-column
            prop="subscribedCapitalContribution"
            align="center"
            label="认缴出资额"
          />
          <el-table-column
            prop="subscribedDate"
            align="center"
            label="认缴出资日期"
          />
        </el-table>
        <p>对外投资企业</p>
        <el-table
          :data="outwardInvestments"
          border
          style="width: 100%"
        >
          <el-table-column
            width="120"
            align="center"
            type="index"
            label="序号"
          />
          <el-table-column
            prop="nameOfEnterprise"
            label="被投资企业"
            align="center"
            width="300"
          />
          <el-table-column
            prop="investmentProportion"
            label="投资占比"
            align="center"
          />
          <el-table-column
            prop="subscribedCapitalContribution"
            align="center"
            label="认缴金额"
          />
          <el-table-column
            prop="registerDate"
            align="center"
            label="成立日期"
          />
          <el-table-column
            prop="managementForms"
            align="center"
            label="状态"
          />
        </el-table>
        <p>总公司</p>
        <el-table
          :data="headOffices"
          border
          style="width: 100%"
        >
          <el-table-column
            width="120"
            align="center"
            type="index"
            label="序号"
          />
          <el-table-column
            prop="name"
            label="总企业名称"
            align="center"
            width="300"
          />
          <el-table-column
            prop="legalPerson"
            label="总公司法定代表人"
            align="center"
          />
          <el-table-column
            prop="registeredCapital"
            align="center"
            label="注册资本"
          />
          <el-table-column
            prop="registerDate"
            align="center"
            label="成立日期"
          />
          <el-table-column
            prop="managementForms"
            align="center"
            label="状态"
          />
        </el-table>
        <p>分支机构</p>
        <el-table
          :data="branchOffices"
          border
          style="width: 100%"
        >
          <el-table-column
            width="120"
            align="center"
            type="index"
            label="序号"
          />
          <el-table-column
            prop="enterpriseName"
            label="企业名称"
            align="center"
            width="300"
          />
          <el-table-column
            prop="principal"
            label="负责人"
            align="center"
          />
          <el-table-column
            prop="registerDate"
            align="center"
            label="成立日期"
          />
          <el-table-column
            prop="managementForms"
            align="center"
            label="状态"
          />
        </el-table> -->
        <!--  <p>专利</p>
        <el-table
          :data="patents"
          border
          style="width: 100%"
        >
          <el-table-column
            width="120"
            align="center"
            type="index"
            label="序号"
          />
          <el-table-column
            prop="patentName"
            label="专利名称"
            align="center"
            width="300"
          />
          <el-table-column
            prop="publicationNumber"
            label="公告/公布号"
            align="center"
          />
          <el-table-column
            prop="patentType"
            align="center"
            label="专利类型"
          />
          <el-table-column
            prop="publicationDate"
            align="center"
            label="公布/公告日期"
          />
        </el-table> -->
      </div>
    </el-card>
    <compileTag
      v-if="dialogFormVisible"
      :id="id"
      :dialog-form-visible.sync="dialogFormVisible"
      :enterprise-label-d-t-o-s="enterpriseLabelDTOS"
      @Particulars="Particulars()"
    />
    <addFeelings
      v-if="addfeelings"
      :id="id"
      :addfeelings.sync="addfeelings"
      :rows="rows"
      @opinion="opinion()"
    />
    <addIndustryLabelsTag
      v-if="dialogFormVisible1"
      :id="id"
      :dialog-form-visible1.sync="dialogFormVisible1"
      @Particulars="Particulars()"
    />
    <changeScore
      v-if="dialogFormVisible2"
      :id="id"
      :score-of-enterprise="scoreOfEnterprise"
      :dialog-form-visible2.sync="dialogFormVisible2"
      @Particulars="Particulars()"
    />
  </div>
</template>

<script>
// 舆情
import addFeelings from "./addFeelings.vue";
// 添加企业标签
import compileTag from "./compileTag.vue";
// 更改企业评分
import changeScore from "./changeScore.vue"
// 添加产业链标签
import addIndustryLabelsTag from "./addIndustryLabelsTag.vue";
import { getPathId } from '@/utils/utils'

import {
  ParticularsAPI,
  unbindTagAPI,
} from "@/api/enterpriseData";
import { opinionAPI, deleteAPI } from "@/api/Feelings";
import { estateunBindTagAPI } from "@/api/industryChain";
export default {
  name: "IndustrialChainConfiguration",
  components: {
    compileTag,
    addFeelings,
    addIndustryLabelsTag,
    changeScore
  },
  data() {
    return {
      // 舆情总数
      total:0,
      form:{
        pageNum:1,
        pageSize:5
      },
      loading: false,
      // 控制编辑企业标签的弹层
      dialogFormVisible: false,
      // 编辑产业链标签
      dialogFormVisible1: false,
      // 编辑企业评分的弹层
      dialogFormVisible2: false,
      // 控制添加舆情的弹层
      addfeelings: false,
      // 舆情详情
      rows: {},
      // 企业数据
      enterprise: {},
      // 舆论详情
      result: {},
      // 股东信息
      shareholders: [],
      // 对外投资企业
      outwardInvestments: [],
      // 总公司
      headOffices: [],
      // 分支机构
      branchOffices: [],
      // 专利信息
      patents: [],
      // 企业标签
      enterpriseLabels: [],
      id: "",
      // 企业标签
      enterpriseLabelDTOS: [],
      // 产业链标签
      industryLabelDTOS: [],
      industryLabels: [],
      // 企业评分
      scoreOfEnterprise: "",
    };
  },
  created() {
    this.Particulars();
    this.opinion();
  },
  methods: {
    // 企业评分更改
    grade() {
    this.dialogFormVisible2 = true
    },
    // 标签解绑
    async handleClose(tag) {
      let id = "";
      this.enterpriseLabelDTOS.filter((item) => {
        if (item.labelName == tag) {
          return (id = item.id);
        }
      });
      await unbindTagAPI({
        enterpriseId: this.id,
        enterpriseLabelId: id,
      });
      this.$message.success("企业标签解绑成功");
      this.Particulars();
    },
    async handleClose1(tag) {
      let id = "";
      this.industryLabelDTOS.filter((item) => {
        if (item.labelName == tag) {
          return (id = item.id);
        }
      });
      await estateunBindTagAPI({
        enterpriseId: this.id,
        industryLabelId: id,
      });
      this.$message.success("产业链标签解绑成功");
      this.Particulars();
    },
    // 企业详情
    async Particulars() {
      try {
        this.loading = true;
        this.id =this.$route.query.id|| getPathId()|| null;
        const res = await ParticularsAPI({
          enterpriseId: this.id,
          listedSectorIds:[],
          technologyCertificationIds:[]
        });
        this.enterprise = res.result.enterprise;
        if (this.enterprise.enterpriseLabels) {
          this.enterpriseLabels = this.enterprise.enterpriseLabels.split(",");
        } else {
          this.enterpriseLabels = this.enterprise.enterpriseLabels;
        }
        if (this.enterprise.industryLabels) {
          this.industryLabels = this.enterprise.industryLabels.split(",");
        } else {
          this.industryLabels = this.enterprise.industryLabels;
        }
        // 专利
        this.patents = res.result.patents;
        // 股东信息
        this.shareholders = res.result.result;
        // 对外投资
        this.outwardInvestments = res.result.outwardInvestments;
        // 总公司
        this.headOffices = res.result.headOffices;
        // 分支机构
        this.branchOffices = res.result.branchOffices;
        // 标签id+名称
        //console.log( "??",res.result.enterpriseLabelDTOS);
        this.enterpriseLabelDTOS = res.result.enterprise.enterpriseLabelDTOS;
        this.industryLabelDTOS = res.result.enterprise.industryLabelDTOS;
        // 企业评分
        this.scoreOfEnterprise = this.enterprise.scoreOfEnterprise;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    // 舆情
    async opinion() {
      const res = await opinionAPI({
        enterpriseId:this.$route.query.id|| getPathId()|| null,
        pageNum:this.form.pageNum,
        pageSize:this.form.pageSize
      });
      //console.log(res.result);
      this.result = res.result.records;
      this.total=res.result.total;
    },
    // 删除企业舆情
    async delete1(id) {
      try {
        this.$confirm("确定删除该舆情信息吗", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          await deleteAPI({
            id,
          });
          this.opinion();
          this.$message({
            type: "success",
            message: "企业舆情删除成功!",
          });
        });
      } catch (error) {
        console.log(error);
      }
    },
    // 编辑
    async detail(row) {
      //console.log(row, "详情");
      var objString = JSON.stringify(row);
      var obj2 = JSON.parse(objString);
      this.rows = obj2;
      this.addfeelings = true;
      /* const res = await detailAPI({
        id,
      });
      //console.log(res.result);
      this.particulars = res.result */
    },
    addfeeling() {
      this.addfeelings = true;
      this.rows = {};
    },
  },
};
</script>
<style scoped lang="scss">
.ye {
  position: relative;
}
.btn{
  color: rgba(255,255,255,0.85);
  background: #3370FF;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover{
 background: #1765AD;;
 color: rgba(255,255,255,0.85);
}
.btn:active{
 background: #52ABFF;
 color: rgba(255,255,255,0.85);
}
.btn:focus{
 background: #3370FF;
 color: rgba(255,255,255,0.85);
}
.addtag{
  display: flex;
}
.text-input{
   color: #3370FF;
}
.tags{
   margin-right: 6px;
   background-color: #e9f4ff;
   color: #3370FF;
}
.longList{
  p{
    font-size: 16px;
    font-weight: bold;
  }
}
.industryTag{
  margin-top: 20px;
}
.pf {
  width: 100px;
}
.feelings {
  display: flex;
  justify-content: space-between;
  button{
    margin-top: 10px;
    text-align: center;
    justify-content: space-between;
    height: 40px;
    width: 70px;
  }
}
.el-card {
  margin-top: 20px;
  width: 95%;
  // margin-left: 2.5%;
}

.logo {
  display: flex;
  flex-direction: row;
}

.logoImg {
  float: left;

  img {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
  }
}
.scoreOfEnterprise{
  float: left;
}

.name {
  display: flex;

  h2 {
    margin-left: 20px;
  }
}

.margin-top {
  margin-top: 16px;
  img {
    margin-left: 5px;
    width: 50px;
    height: 50px;
  }
}
.default {
  margin-left: 5px;
  width: 50px;
  height: 50px;
  background-color: #3370FF;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.default span {
  font-size: 17px;
  color: #ffff;
  display: block;
  width: 100%;
  margin: auto;
  text-align: center;
}
.compile{
  color:#3370FF; 

}
.a{
  color:#3370FF;
}
.remove{
  color: red;
}
</style>