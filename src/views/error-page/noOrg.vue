<template>
  <div class="noData">
    <div class="fixed-header">
      <Navbar />
    </div>
    <div class="noDataBg" />
    <img src="https://static.idicc.cn/cdn/SSO/zhaoshangnodata.png">
    <span class="p1">您的账号权限不足，请联系客服</span>
  </div>
</template>

<script>
import Navbar from "@/layout/components/Navbar.vue";
export default {
  name: "PageOrg",
  components: {
    Navbar,
  },
  data() {
    return {};
  },
  methods: {
    back() {},
  },
};
</script>

<style lang="scss" scoped>
.noData {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: 100%;
}
.p1 {
  margin-top: 38px;
  font-size: 14px;
  font-family: Source <PERSON> Sans CN-Medium, Source <PERSON> Sans CN;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}
</style>
