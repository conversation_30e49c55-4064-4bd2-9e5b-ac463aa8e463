<template>
  <div>
    <el-card class="cardBody">
      <div
        slot="header"
        class="clearfix"
      >
        <span>{{ cardTitle }}</span>
      </div>
      <el-form
        ref="formRef"
        name="basic"
        label-width="120px"
        layout="vertical"
        :model="formData"
        :rules="rules"
      >
        <!-- 身份选择 -->
        <el-form-item
          label="您的身份"
          prop="userIdentity"
        >
          <el-select
            v-model="formData.userIdentity"
            placeholder="请选择身份"
            @change="changeIdentity"
          >
            <el-option
              v-for="item in identityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 关注产业 -->
        <el-form-item
          label="您的关注的产业"
          prop="focusIndustryIds"
        >
          <el-select
            v-model="formData.focusIndustryIds"
            :multiple-limit="3"
            placeholder="请选择您关注的产业"
            :options="purchaseChain"
            :multiple="true"
          >
            <el-option
              v-for="item in purchaseChain"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 招商属地 -->
        <el-form-item
          label="招商属地"
          prop="code"
          :help="locationHelp"
        >
          <el-cascader
            v-model="formData.code"
            :props="citys"
            :options="areaList"
            change-on-select
            placeholder="请选择招商属地"
          />
          <el-tooltip
            class="item"
            effect="dark"
            :content="locationHelp"
            placement="right"
          >
            <el-button
              icon="el-icon-warning"
              class="smallBtn"
            />
          </el-tooltip>
        </el-form-item>

        <el-form-item
          label="您的姓名"
          prop="realName"
        >
          <el-input
            ref="realName"
            v-model="formData.realName"
            placeholder="请输入"
            autocomplete="on"
          />
        </el-form-item>
        <el-form-item
          label="联系电话"
          prop="phone"
        >
          <el-input
            ref="phone"
            v-model="formData.phone"
            placeholder="请输入"
            autocomplete="on"
            :disabled="true"
          />
        </el-form-item>
        <el-form-item
          label="所在单位"
          prop="company"
        >
          <el-input
            ref="company"
            v-model="formData.company"
            placeholder="请输入"
            autocomplete="on"
          />
        </el-form-item>
        <el-form-item
          label="所在单位职务"
          prop="jobTitle"
        >
          <el-input
            ref="jobTitle"
            v-model="formData.jobTitle"
            placeholder="请输入所在单位职务"
            autocomplete="on"
          />
        </el-form-item>

        <el-form-item
          v-if="formData.userIdentity === '5'"
          label="所在城市"
          prop="cityCode"
        >
          <el-cascader
            v-model="formData.cityCode"
            :props="citys2"
            placeholder="请选择所在城市"
          />
        </el-form-item>
        <el-form-item
          v-if="formData.userIdentity === '5'"
          label="优势资源城市"
          prop="advantageCity"
        >
          <el-input
            ref="advantageCity"
            v-model="formData.advantageCity"
            placeholder="请输入优势资源城市"
          />
        </el-form-item>
        <el-form-item
          v-if="formData.userIdentity === '5'"
          label="优势资源简述"
          prop="resume"
        >
          <el-input
            v-model="formData.resume"
            type="textarea"
            placeholder="请输入优势资源简述"
          />
        </el-form-item>

        <el-form-item
          label="邮箱"
          prop="email"
        >
          <el-input
            ref="email"
            v-model="formData.email"
            placeholder="请输入邮箱"
            autocomplete="on"
          />
        </el-form-item>
        <el-form-item
          v-if="formData.userIdentity === '5'"
          label="加入团队"
          prop="parentWorker"
        >
          <el-input
            ref="parentWorker"
            v-model="formData.parentWorker"
            placeholder="请输入邀请码"
            autocomplete="on"
          />
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="onFinish"
          >
            确定
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getCityListAPI, listAllPurchaseChainAPI } from "@/api/userCenter";
import { completeUserInfoAPI, getUserInfoAPI } from "@/api/login";
import { getAllAddress } from "@/api/user";
export default {
  name: "CompleteInfo",
  props: {
    userInfo: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      listLoading: false,
      areaList: [],
      citys2: {
        lazy: true,
        lazyLoad(node, resolve) {
          let type = node.level;
          if (!node.level) {
            type = 1;
          } else {
            if (node.level === 2) {
              resolve();
            } else {
              type = node.level + 1;
            }
          }
          getCityListAPI({
            type,
            parentId: node?.data?.uid || 0,
          }).then((res) => {
            const nodes = res.result.map((e) => {
              return {
                value: e.code,
                uid: e.id,
                label: e.name,
                leaf: node.level === 1,
                children: type < 2 ? [] : null,
              };
            });
            resolve(nodes);
          });
        },
      },
      citys: {
        checkStrictly: true,
      },
      formData: {
        userIdentity: undefined,
        code: [],
        focusIndustryIds: [],
        realName: "",
        phone: "",
        company: "",
        jobTitle: "",
        email: "",
        parentWorker: "",
        advantageCity: "",
        resume: "",
        cityCode: "",
      },
      rules: {
        userIdentity: [
          { required: true, message: "您的身份不能为空", trigger: "change" },
        ],
        code: [
          { required: true, message: "招商属地不能为空", trigger: "change" },
        ],
        focusIndustryIds: [
          {
            required: true,
            message: "您的关注的产业不能为空",
            trigger: "change",
          },
        ],
        realName: [
          { required: true, message: "您的姓名不能为空", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "您的手机号不能为空", trigger: "blur" },
        ],
        jobTitle: [
          { required: true, message: "您的职位不能为空", trigger: "blur" },
        ],
        company: [
          { required: true, message: "所在单位不能为空", trigger: "blur" },
        ],
      },
      identityOptions: [
        { value: "1", label: "产业运营方" },
        // { value: "2", label: "产业研究机构" },
        { value: "5", label: "产业顾问方" },
      ],
      options: [],
      purchaseChain: [],
      locationHelp:
        "哒达招商将优先推荐招商属地相关产业资讯，后期无法更改，请谨慎填写",
      cardTitle: "信息完善",
    };
  },
  mounted() {
    this.formData.phone = this.userInfo.username;
    this.getListAll();
    this.getAllAddressList();
  },
  methods: {
    getAllAddressList() {
      this.listLoading = true;
      getAllAddress({ token: this.userInfo?.token }).then((res) => {
        let addressList = [];
        let areaList = [];
        res?.result?.map((e) => {
          if (e.parentId == 0) {
            if (e.id != "3645" && e.id != "3646" && e.id != "3647")
              e.disabled = true; //台湾、澳门、香港只有一级
            e.label = e.province;
            e.value = e.code;
            addressList.push(e);
          } else {
            e.label = e.province;
            e.value = e.code;
            areaList.push(e);
          }
        });
        // 市
        addressList.map((el) => {
          res.result.map((e) => {
            if (el.id == e.parentId) {
              e.label = e.city;
              e.value = e.code;
              if (el.children) {
                el.children.push(e);
              } else {
                el.children = [];
                el.children.push(e);
              }
            }
          });
          return el;
        });
        // 区
        addressList.map((el) => {
          if (el.children && el.children.length > 0) {
            for (let i = 0; i < el.children.length; i++) {
              let el1 = el.children[i];
              areaList.map((e) => {
                if (e.parentId == el1.id) {
                  e.label = e.area;
                  e.value = e.code;
                  if (el1.children) {
                    el1.children.push(e);
                  } else {
                    el1.children = [];
                    el1.children.push(e);
                  }
                }
              });
            }
          }
          return el;
        });
        this.areaList = addressList;
        this.listLoading = false;
      });
    },
    changeIdentity(e) {
      if (e === "5") {
        this.rules.advantageCity = [
          {
            required: true,
            message: "您的优势资源城市不能为空",
            trigger: "blur",
          },
        ];
        this.rules.resume = [
          {
            required: true,
            message: "您的优势资源简述不能为空",
            trigger: "blur",
          },
        ];
        this.rules.cityCode = [
          { required: true, message: "所在城市不能为空", trigger: "blur" },
        ];
      } else {
        this.rules.advantageCity = [];
        this.rules.resume = [];
        this.rules.cityCode = [];
      }
    },
    // 获取采购链与城市数据
    getListAll() {
      listAllPurchaseChainAPI({ token: this.userInfo?.token }).then((res) => {
        this.purchaseChain = res.result.map((e) => ({
          value: e.id,
          label: e.name,
        }));
      });
    },

    // 表单提交处理
    onFinish() {
      this.$refs["formRef"].validate((valid) => {
        if (valid) {
          const data = {
            ...this.formData,
            code: this.formData.code[this.formData.code.length - 1],
            userId: this.userInfo?.userId,
            token: this.userInfo?.token,
            cityCode:
              this.formData.userIdentity === "5"
                ? this.formData.cityCode[this.formData.cityCode.length - 1]
                : "",
          };
          completeUserInfoAPI(data).then((res) => {
            if (res?.code === "SUCCESS") {
              localStorage.setItem("Admin-Token-DaDa", this.userInfo.token);
              localStorage.setItem("userInfo", JSON.stringify(this.userInfo));
              localStorage.setItem(
                "MAINUSERINFO",
                JSON.stringify(this.userInfo)
              );
              this.$store.dispatch("user/login", this.userInfo);
              this.getAiInfo();
            } else {
              console.error(res?.msg || "服务器繁忙");
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },

    // 获取 AI 用户信息
    getAiInfo() {
      getUserInfoAPI()
        .then((obj) => {
          localStorage.setItem("AiUserInfo", JSON.stringify(obj.result));
          let newUser = this.userInfo;
          newUser.realName = obj.result.realName;
          localStorage.setItem("userInfo", JSON.stringify(newUser));
          localStorage.setItem("MAINUSERINFO", JSON.stringify(newUser));
          this.$router.push("/idicc/view");
        })
        .catch((error) => {
          console.error("获取 AI 信息失败:", error);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.cardBody {
  width: 600px;
  height: auto;
  margin: 0px 1.25rem;
  padding: 20px 16px;
  box-sizing: border-box;
  margin-top: 0.4375rem;
  background: #ffffff;
  box-shadow: 0px 4px 12px 0px #eef1f8 !important;
  border-radius: 10px 10px 10px 10px;
  border: 0px;

  .cardTitle {
    text-align: center;
    font-weight: 600;
    font-size: 24px;
  }

  .label {
    margin-right: 5px;
  }

  :global(.ant-card-head) {
    border-bottom: 0px solid #e8e8e8;
    min-height: auto !important;
    height: 25px;
  }

  :global(.ant-card-head) {
    min-height: 48px;
    padding: 0 0;
  }
}

.btn {
  width: 80%;
  height: 34px;
  box-shadow: 0px 6px 30px 0px rgba(174, 174, 174, 0.2);
  background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #eaeaea;
  font-weight: 600;
  font-size: 20px;
  color: #ffffff;
  margin-top: 40px;

  > span {
    position: relative;
  }

  &::before {
    content: "";
    background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
    position: absolute;
    inset: -1px;
    opacity: 1;
    transition: all 0.3s;
    border-radius: inherit;
  }
}
.btnbox {
  margin-top: 20px;
}
.smallBtn {
  background: white;
  border: none !important;
  // 鼠标悬停样式
  &:hover {
    background: white !important;
  }
}
</style>