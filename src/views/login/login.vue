<template>
  <div class="loginBox">
    <div class="login">
      <div
        v-if="loginState !== 3"
        class="codeBox"
      >
        <!-- Tab栏 -->
        <div class="tab-bar">
          <span
            :class="['tab-item', { active: tabIndex === 0 }]"
            @click="switchLoginState(0)"
          >
            微信登录
          </span>
          <span
            :class="['tab-item', { active: tabIndex === 1 }]"
            @click="switchLoginState(1)"
          >
            账户登录
          </span>
          <div
            v-if="tabIndex === 0"
            class="line0"
          />
          <div
            v-if="tabIndex === 1"
            class="line1"
          />
        </div>
        <div class="title">
          欢迎登录哒达招商管理平台
        </div>
        <div
          v-if="tabIndex === 0"
          class="leftBox"
        >
          <div v-if="loginState === 1">
            <div v-loading="isLoading">
              <div class="QRcodes">
                <img
                  :src="codeData.qrOssPath || ''"
                  class="codeImg"
                >
                <div
                  v-if="showRefresh"
                  class="maskBox"
                >
                  {{ mistakeText }}，
                  <span
                    class="cx"
                    @click="getCode"
                  >
                    刷新
                    <img
                      :src="refresh"
                      class="refreshIcon"
                    >
                  </span>
                </div>
              </div>
            </div>
            <div class="scanQRcodes">
              请使用微信扫码登录
            </div>
          </div>

          <div
            v-if="loginState === 2"
            class="type2"
          >
            <img
              :src="codeScanning"
              class="codeScanningImg"
            >
            <div class="codep1">
              扫描成功
            </div>
            <div class="codep2">
              在微信中授权即可登录
            </div>
          </div>
        </div>

        <div
          v-if="tabIndex === 1"
          class="leftBox1"
        >
          <div class="form">
            <div class="login-form">
              <el-form
                ref="loginForm"
                style="margin-top: 31px"
                :model="loginForm"
                :rules="loginRules"
                autocomplete="on"
                label-position="top"
                :hide-required-asterisk="true"
              >
                <el-form-item
                  style="margin-top: 17px"
                  label=""
                  prop="username"
                >
                  <el-input
                    ref="username"
                    v-model="loginForm.username"
                    placeholder="请输入账号"
                    name="username"
                    type="text"
                    tabindex="1"
                    autocomplete="on"
                  />
                </el-form-item>
                <el-tooltip
                  v-model="capsTooltip"
                  content="Caps lock is On"
                  placement="right"
                  manual
                >
                  <el-form-item
                    style="margin-top: 17px"
                    prop="password"
                    label=""
                  >
                    <el-input
                      :key="passwordType"
                      ref="password"
                      v-model="loginForm.password"
                      :type="passwordType"
                      placeholder="请输入密码"
                      name="password"
                      tabindex="2"
                      autocomplete="on"
                      @keyup.native="checkCapslock"
                      @blur="capsTooltip = false"
                      @keyup.enter.native="handleLogin"
                    />
                    <span
                      class="show-pwd"
                      @click="showPwd"
                    >
                      <svg-icon
                        class="svgicon"
                        :icon-class="
                          passwordType === 'password' ? 'eye' : 'eye-open'
                        "
                      />
                    </span>
                  </el-form-item>
                </el-tooltip>
                <div
                  class="text"
                  @click="$router.push('/retrievePassword')"
                >
                  忘记密码?
                </div>
                <el-button
                  :loading="loading"
                  type="primary"
                  class="login-btn"
                  @click.native.prevent="handleLogin"
                >
                  登 录
                </el-button>
                <!--  <p>为保证您的正常使用，若手机号变更，请联系管理员及时调整</p> -->
              </el-form>
              <!--  <span class="kj">快捷登录</span> -->
              <div class="agreement">
                <el-checkbox
                  v-model="checked"
                  style="margin-right: 5px"
                />
                阅读并同意<span
                  class="goagreement"
                  @click="shows(false)"
                >《用户使用协议》</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="loginState === 3"
        class="codeBox2"
      >
        <CompleteInfo :user-info="userInfo" />
      </div>

      <changePassDig
        v-if="changePass"
        :change-pass.sync="changePass"
        :pass-token="passToken"
        @changesu="changesu"
      />
      <div
        v-show="!show"
        style="width: 100%; height: 100%;z-index: 111; overflow: scroll; background-color: #fff;"
      >
        <agreement @show="shows" />
      </div>
    </div>
  </div>
</template>

<script>
import {
  wechatGenerateAPI,
  wechatgetstatusAPI,
  isPopUserInfoCompletionAPI,
  getUserInfoAPI,
  codeBeforeCheckNumberAPI,
} from "@/api/login";
import CompleteInfo from "./component/completeInfo.vue";
import agreement from "./component/agreement.vue"; 
import changePassDig from "./component/changePass.vue";
import {
  encryptionAPI,
  login,
  getOwnList,
  queryResourceByTypeBtn,
} from "@/api/user";
import { getToken, removeToken } from "@/utils/auth";
import { JSEncrypt } from "jsencrypt";
import getters from "@/store/getters";

export default {
  name: "Login",
  components: {
    CompleteInfo,
    agreement,
    changePassDig,
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      var reg_tel =
        ///^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/; //11位手机号码正则
        /^1[0-9]{10}$/;
      if (!reg_tel.test(value)) {
        callback(new Error("请输入手机号"));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入密码"));
      } else {
        callback();
      }
    };
    return {
      tabIndex: 0,
      loginState: 1,
      showRefresh: false,
      mistakeText: "二维码已过期",
      codeData: {},
      isLoading: false,
      userInfo: {},
      codeScanning:'https://static.idicc.cn/cdn/pangu/codeScanning.png',
      refresh: 'https://static.idicc.cn/cdn/pangu/refresh.png',

      result: "",
      resultstandby:
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC27tAIp7i+qhyunO+O5xvk5ilVR9npOkrfeJB69OtafL1i3ZjXNct0gxZF09WRCzWCOkLzG0rcKzWaFieWRscci5pAHqSzfld5Qob6e3BVgI+BtJcge4NOGtMN8ASEMXBUdzhuo1ud0VQUqVjDcBMqQMKtsyIpBh+onZH8jqXz/wIDAQAB",
      // 控制滑块验证码
      SliderVerificationCode: false,
      // 滑块验证是否成功
      verification: true,
      show: true,
      checked: true,
      loginForm: {
        username: "", //开发
        password: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", validator: validateUsername },
        ],
        password: [
          { required: true, trigger: "blur", validator: validatePassword },
        ],
      },
      passwordType: "password",
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      changePass: false,
      passToken: "",
      otherQuery: {},
      source: "",
    };
  },
  mounted() {
    if (getToken()) {
      removeToken();
    }
    this.getCode();
    this.checked = sessionStorage.getItem("checked") === "0";
  },
  beforeUnmount() {
    if (this.intervalRef) clearInterval(this.intervalRef);
  },
  methods: {
    changesu() {
      this.changePass = false;
      this.loginForm.password = "";
    },
    shows(i) {
      this.show = i;
    },
    checkCapslock(e) {
      const { key } = e;
      this.capsTooltip = key && key.length === 1 && key >= "A" && key <= "Z";
    },
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "";
      } else {
        this.passwordType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    async encryption() {
      const res = await encryptionAPI();
      if (res.result) {
        this.resultstandby = res.result;
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          if (!this.checked) {
            return this.$message.warning("请先勾选用户协议");
          }
          this.loading = true;
          await this.encryption();
          let encrypt = new JSEncrypt();
          encrypt.setPublicKey(this.resultstandby);
          const password = encrypt.encrypt(this.loginForm.password);
          const code = await codeBeforeCheckNumberAPI({
            phoneNumber: this.loginForm.username,
          });
          if (code.result == true) {
            login({
              username: this.loginForm.username,
              password,
            })
              .then((res) => {
                this.loading = false;
                localStorage.setItem("accountType", res?.result?.accountType);
                // 登录成功后清除routerQuery，防止获取错误routerQuery。
                localStorage.removeItem("routerQuery");
                if (res.result?.isChangedPassword == "0") {
                  sessionStorage.setItem("checked", "0");
                  this.passToken = res.result.token;
                  this.changePass = true;
                } else {
                  this.handleAuthorized(res.result);
                }
              })
              .catch(() => {
                this.loading = false;
              })
              .finally(() => {
                this.loading = false;
              });
          } else {
            this.$message.error("该手机号未注册");
            this.loading = false;
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },

    switchLoginState(state) {
      this.tabIndex = state;
      if (state === 1) {
        // 切换到微信登录时，重新获取二维码
        this.loginState = 1;
        this.getCode();
      }
    },
    async getCode() {
      this.isLoading = true;
      try {
        const res = await wechatGenerateAPI();
        this.showRefresh = false;
        this.codeData = res.result || {};
        this.startPolling(this.codeData.uniqueKey);
      } catch (error) {
        console.error("API Error:", error);
      } finally {
        this.isLoading = false;
      }
    },
    startPolling(uniqueKey) {
      if (this.intervalRef) clearInterval(this.intervalRef);

      this.intervalRef = setInterval(async () => {
        try {
          const res = await wechatgetstatusAPI({ uniqueKey });
          if (res.result) {
            const status = res.result.status;

            switch (status) {
              case "INIT":
                break;
              case "SCANNED":
                this.loginState = 2;
                break;
              case "SCAN_EXPIRE":
              case "AUTHORIZE_EXPIRE":
              case "AUTHORIZE_EXIT":
              case "TOKEN_COMPLETE":
                this.handleExpired();
                break;
              case "AUTHORIZED":
                this.handleAuthorized(res.result.userLoginInfo);
                break;
              default:
                break;
            }
          }
        } catch (error) {
          console.error("Polling Error:", error);
        }
      }, 1000);
    },
    handleExpired() {
      this.loginState = 1;
      this.mistakeText = "二维码已过期";
      this.showRefresh = true;
      clearInterval(this.intervalRef);
    },
    async handleAuthorized(userInfo) {
      clearInterval(this.intervalRef);
      try {
        const res2 = await isPopUserInfoCompletionAPI({
          token: userInfo.token,
        });
        if (res2.result === true) {
          this.userInfo = userInfo;
          this.loginState = 3;
        } else {
          localStorage.setItem("Admin-Token-DaDa", userInfo.token);
          localStorage.setItem("userInfo", JSON.stringify(userInfo));
          localStorage.setItem("MAINUSERINFO", JSON.stringify(userInfo));
          this.$store.dispatch("user/login", userInfo);
          this.getPermission();
          this.getAiInfo();
        }
      } catch (error) {
        console.error("User Info Completion Error:", error);
      }
    },
    getAiInfo() {
      getUserInfoAPI()
        .then((obj) => {
          getOwnList().then((list) => {
            const r = list?.result?.selected || "";
            console.log(r, "r");
            if (r && !r.isDefault && r?.orgCode) {
              localStorage.setItem("orgCode", r?.orgCode);
              localStorage.setItem("orgName", r?.orgName);
            } else {
              localStorage.setItem("orgCode", "");
              localStorage.setItem("orgName", "");
            }
            localStorage.setItem("AiUserInfo", JSON.stringify(obj.result));
            this.$router.push("/");
          });
        })
        .catch((error) => {
          console.error("Get User Info Error:", error);
        });
    },
    // 获取权限
    getPermission() {
      queryResourceByTypeBtn().then((res) => {
        localStorage.setItem(
          "resourceCode",
          JSON.stringify(
            res.result.map((item) => {
              return item.resourceCode;
            })
          )
        );
        this.$router.push({ path: `/` });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.loginBox {
  width: 100%;
  height: 100%;

  .refreshIcon {
    width: 16px;
    height: 16px;
    margin-left: 4px;
    margin-top: 1px;
    min-width: 16px;
    min-height: 16px;
    cursor: pointer;
  }

  .login {
    display: flex;
    min-width: 1024px;
    min-height: 700px;
    margin: 0;
    padding: 0;
    font-family: sans-serif;
    background: url("https://static.idicc.cn/cdn/SSO/zhaoshangBgc.png")
      no-repeat center 0px;
    background-size: cover;
    background-position: center 0;
    background-repeat: no-repeat;
    background-attachment: fixed;
    -webkit-background-size: cover;
    -o-background-size: cover;
    -moz-background-size: cover;
    -ms-background-size: cover;
    background-repeat: no-repeat;
    width: 100%;
    overflow: hidden;
    height: 100vh;

    .codeBox2 {
      position: absolute;
      top: 48%;
      left: 80%;
      transform: translate(-50%, -50%);
    }

    .codeBox {
      position: absolute;
      top: 48%;
      left: 80%;
      transform: translate(-50%, -50%);
      width: 468px;
      height: 495px;
      border-radius: 16px;
      background-color: #fff;
      box-shadow: 0px 4px 53px 0px rgba(37, 81, 147, 0.25);
      .tab-bar {
        width: 100%;
        display: flex;
        margin-top: 20px;
        margin-bottom: 20px;
        padding-left: 12px;
        position: relative;

        .tab-item {
          padding: 10px 20px;
          cursor: pointer;
          font-size: 24px;
          color: #666;

          &.active {
            color: #000;
            font-weight: bold;
            font-size: 28px;
          }

          &:not(:last-child) {
            margin-right: 20px;
          }
        }
      }

      .introduceImg {
        width: 444px;
        height: 495px;
      }

      .leftBox {
        width: 468px;
        height: 388px;
        display: flex;
        justify-content: center;

        .scanQRcodes {
          margin-top: 24px;
          font-size: 18px;
          font-weight: 400;
          line-height: 32px;
          color: rgba(0, 0, 0, 0.88);
          width: auto;
          display: flex;
          justify-content: center;
          position: relative;
        }

        .QRcodes,
        .QRcodes2 {
          width: 227px;
          height: 227px;
          border-radius: 8px;
          margin-top: 24px;
          background: #ffffff;
          box-sizing: border-box;
          border: 1px solid rgba(0, 0, 0, 0.06);
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          .codeImg {
            width: 194px;
            height: 194px;
          }
        }

        .QRcodes2 {
          filter: blur(2px);
        }

        .maskBox {
          position: absolute;
          width: 200px;
          height: 200px;
          background: rgba(255, 255, 255, 0.9);
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 500;
          font-size: 16px;
          color: #1d2129;

          .cx {
            color: #5a8bff;
            cursor: pointer;
            display: flex;
            align-items: center;
          }
        }

        .codeScanningImg {
          margin-top: 50px;
          width: 160px;
          height: 160px;
          margin-bottom: 32px;
        }

        .codep1 {
          font-size: 24px;
          font-weight: 600;
          line-height: 32px;
          color: rgba(0, 0, 0, 0.88);
          display: flex;
          justify-content: center;
        }

        .codep2 {
          font-size: 14px;
          font-weight: normal;
          line-height: 22px;
          color: rgba(0, 0, 0, 0.88);
          display: flex;
          justify-content: center;
          margin-top: 8px;
        }
      }
    }
  }
}

.form {
  width: 100%;
  // position: absolute;
  // top: 48%;
  // left: 50%;
  // transform: translate(-50%, -50%);
}

.login-form {
  margin: 0 auto;

  p {
    text-align: center;
    margin-top: 80px;
    font-size: 15px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.25);
  }
}

.tips {
  font-size: 14px;
  color: #fff;
  margin-bottom: 10px;

  span {
    &:first-of-type {
      margin-right: 16px;
    }
  }
}

.svg-container {
  padding: 6px 5px 6px 15px;
  color: $dark_gray;
  vertical-align: middle;
  width: 30px;
  display: inline-block;
}

.title-container {
  position: relative;

  .title {
    font-size: 30px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 600;
    color: #000000;
    margin: 5px auto 10px auto;
    //text-align: center;
    //font-weight: bold;
  }

  .t2 {
    font-size: 20px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #000000;
    font-weight: 400;
    color: #000000;
  }

  /*  img{
      margin-left: 165px;
      width: 100px;
      height: 100px;
    } */
}

.show-pwd {
  position: absolute;
  right: 10px;
  top: 2px;
  font-size: 16px;
  color: $dark_gray;
  cursor: pointer;
  user-select: none;
}

.text {
  float: left;
  cursor: pointer;
  font-size: 14px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #333333;
}

footer {
  margin-top: 25px;
  display: flex;
  width: 100%;
  bottom: 63px;
  font-size: 12px;
  color: #ffffff66;

  span {
    max-width: 1200px;
    margin: auto;
    display: block;
    width: 100%;
    text-align: center;
  }

  img {
    width: 20px;
    display: inline-block;
    height: 20px;
    margin-right: 12px;
  }

  .imgs {
    height: 20px;
    margin-right: 3px;
  }
}

.login-btn {
  margin-left: 0px !important;
  width: 100%;
  height: 54px;
  margin-top: 33px;
  background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
  box-shadow: 0px 10px 40px 0px rgba(174, 174, 174, 0.2);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
  border: 1px solid #eaeaea;
  font-size: 16px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}

.login-btn:hover {
  margin-left: 0px !important;
  width: 100%;
  height: 54px;
  margin-top: 33px;
  background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
  box-shadow: 0px 10px 40px 0px rgba(174, 174, 174, 0.2);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
  border: 1px solid #eaeaea;
  font-size: 16px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}

.login-btn:active {
  margin-left: 0px !important;
  width: 100%;
  height: 54px;
  margin-top: 33px;
  background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
  box-shadow: 0px 10px 40px 0px rgba(174, 174, 174, 0.2);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
  border: 1px solid #eaeaea;
  font-size: 16px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}

.login-btn:focus {
  margin-left: 0px !important;
  width: 100%;
  height: 54px;
  margin-top: 33px;
  background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
  box-shadow: 0px 10px 40px 0px rgba(174, 174, 174, 0.2);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
  border: 1px solid #eaeaea;
  font-size: 16px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}
.agreement {
  display: flex;
  margin-top: 20px;
  height: 30px;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #333333;
  line-height: 16px;

  .goagreement {
    cursor: pointer;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #1a5bff;
    line-height: 16px;
  }
}
.title {
  font-size: 20px;
  color: #000000;
  margin-left: 32px;
}

.leftBox1 {
  width: calc(100% - 64px);
  height: 380px;
  display: flex;
  justify-content: center;
  margin-left: 32px;
}
.line0 {
  position: absolute;
  left: 72px;
  top: 48px;
  width: 35px;
  height: 4px;
  background: #3370ff;
  border-radius: 2px;
}
.line1 {
  position: absolute;
  left: 224px;
  top: 48px;
  width: 35px;
  height: 4px;
  background: #3370ff;
  border-radius: 2px;
}
</style>