<template>
  <div />
</template> 

<script>
import { encryptionAPI, listUserBindAcl2 } from "@/api/user";
import { JSEncrypt } from "jsencrypt";
import { getToken } from "@/utils/auth"; // get token from cookie
// import SocialSign from './components/SocialSignin'

export default {
  name: "UserLogin",
  // eslint-disable-next-line vue/no-unused-components
  // components: { SocialSign },
  data() {
    const validateUsername = (rule, value, callback) => {
      var reg_tel =
        /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/; //11位手机号码正则
      if (!reg_tel.test(value)) {
        callback(new Error("请输入手机号"));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入密码"));
      } else {
        callback();
      }
    };
    return {
      result: "",
      resultstandby:
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC27tAIp7i+qhyunO+O5xvk5ilVR9npOkrfeJB69OtafL1i3ZjXNct0gxZF09WRCzWCOkLzG0rcKzWaFieWRscci5pAHqSzfld5Qob6e3BVgI+BtJcge4NOGtMN8ASEMXBUdzhuo1ud0VQUqVjDcBMqQMKtsyIpBh+onZH8jqXz/wIDAQAB",
      // 控制滑块验证码
      SliderVerificationCode: false,
      // 滑块验证是否成功
      verification: true,
      loginForm: {
        //username: '13381801205',
        //password: '123'
        //username: '13958032060',//测试
        //username: '17834343434',//开发
        //password: 'abc123'
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", validator: validateUsername },
        ],
        password: [
          { required: true, trigger: "blur", validator: validatePassword },
        ],
      },
      passwordType: "password",
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query;
        if (query) {
          this.redirect = query.redirect;
          this.otherQuery = this.getOtherQuery(query);
        }
      },
      immediate: true,
    },
  },
  beforeCreate() {
    //this.aa()
    /* const baseURL = process.env.VUE_APP_SSO_URL
    window.open(`${baseURL}`); */
  },
  created() {
    // this.aa()
    // window.addEventListener('storage', this.afterQRScan)
    //this.encryption()
  },
  mounted() {
    localStorage.removeItem("MAINUSERINFO");

    if (this.loginForm.username === "") {
      this.$refs.username.focus();
    } else if (this.loginForm.password === "") {
      this.$refs.password.focus();
    }
  },
  destroyed() {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    checkCapslock(e) {
      const { key } = e;
      this.capsTooltip = key && key.length === 1 && key >= "A" && key <= "Z";
    },
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "";
      } else {
        this.passwordType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    async encryption() {
      const res = await encryptionAPI();
      if (res.result) {
        this.resultstandby = res.result;
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate(async (valid) => {
        /* if (this.verification==false) { 
          return  this.$message('请先进行滑块验证');
        } */
        if (valid) {
          this.loading = true;
          await this.encryption();
          let encrypt = new JSEncrypt();
          encrypt.setPublicKey(this.resultstandby);
          const password = encrypt.encrypt(this.loginForm.password);
          this.$store
            .dispatch("user/login", {
              username: this.loginForm.username,
              password,
            })
            // this.$store.dispatch('user/login', this.loginForm)
            .then(async () => {
              //if(this.$store.getters.user)
              //console.log(this.$store.getters.user)
              //this.$router.push({ path: this.redirect || '/', query: this.otherQuery })
              this.loading = false;
              if (this.$store.getters.user.accountType == "1") {
                //console.log('1运营侧2用户侧');
                // this.$router.push('/system/industryConfiguration')
                this.$router.push("/");
              } else {
                this.$router.push("/home-page");
                // const res = await listUserBindAcl2();
                // this.$store.commit("user/SET_DESIGNATE", false);
                // res.result.forEach((item) => {
                //   if (item.aclName == "线索指派") {
                //     this.$store.commit("user/SET_DESIGNATE", true);
                //   }
                // });
              }
            })
            .catch(() => {
              this.loading = false;
              // 密码输入错误出现滑块验证码
              /*               this.SliderVerificationCode = true
              // 需要验证滑块
              this.verification=false */
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          // console.log("error submit!!");
          return false;
        }
      });
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== "redirect") {
          acc[cur] = query[cur];
        }
        return acc;
      }, {});
    },
    // afterQRScan() {
    //   if (e.key === 'x-admin-oauth-code') {
    //     const code = getQueryObject(e.newValue)
    //     const codeMap = {
    //       wechat: 'code',
    //       tencent: 'code'
    //     }
    //     const type = codeMap[this.auth_type]
    //     const codeName = code[type]
    //     if (codeName) {
    //       this.$store.dispatch('LoginByThirdparty', codeName).then(() => {
    //         this.$router.push({ path: this.redirect || '/' })
    //       })
    //     } else {
    //       alert('第三方登录失败')
    //     }
    //   }
    // }
  },
};
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #283443;
$light_gray: #fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 100%;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    margin-bottom: 5px;
  }
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  display: flex;
  min-width: 1024px;
  min-height: 700px;
  margin: 0;
  padding: 0;
  font-family: sans-serif;
  background: url("~@/assets/login/Group.jpg") no-repeat center 0px;
  background-size: cover;
  background-position: center 0;
  background-repeat: no-repeat;
  background-attachment: fixed;
  -webkit-background-size: cover;
  -o-background-size: cover;
  -moz-background-size: cover;
  -ms-background-size: cover;
  background-repeat: no-repeat;
  width: 100%;
  overflow: hidden;
  height: 100%;
  .form {
    margin-top: 50px;
    margin: auto;
  }
  .login-form {
    border-radius: 20px 20px 20px 20px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    background: rgba(0, 23, 58, 0.6);
    position: relative;
    width: 500px;
    height: 600px;
    padding: 50px 35px 0;
    margin: 0 auto;
    p {
      text-align: center;
      margin-top: 80px;
      font-size: 15px;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.25);
    }
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;
    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 5px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
    img {
      margin-left: 165px;
      width: 100px;
      height: 100px;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }
}
.text {
  float: right;
  color: #3370ff;
}
footer {
  margin-top: 25px;
  display: flex;
  width: 100%;
  bottom: 63px;
  font-size: 12px;
  color: #ffffff66;
  span {
    max-width: 1200px;
    margin: auto;
    display: block;
    width: 100%;
    text-align: center;
  }
  img {
    width: 20px;
    display: inline-block;
    height: 20px;
    margin-right: 12px;
  }
  .imgs {
    height: 20px;
    margin-right: 3px;
  }
}
</style>
