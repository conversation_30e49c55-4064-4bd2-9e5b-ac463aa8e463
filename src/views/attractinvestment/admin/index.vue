<template>
  <div style="overflow: hidden;">
    <title1 :ishow="ishow">
      <span
        slot="chainName"
        class="name-info"
      >
        {{ chainName || '产业金脑' }}<span style="font-size: 0.9rem; font-weight: 400; padding-left: 0.5rem;">[{{ orgArea ||
          '产业链' }}]</span></span>

      <div
        slot="tabs"
        class="tabsbutton"
      >
        <div
          v-for="(item, index) in showList"
          :key="index"
          class="btn"
          @click="radarFn(item.id)"
        >
          <img 
            v-if="ishow == item.id"
            src="https://static.idicc.cn/cdn/pangu/tab_height.webp"
            class="leftImg"
          >
          <img
            v-if="ishow !== item.id"
            src="https://static.idicc.cn/cdn/pangu/tab_normal.webp"
            class="leftImg"
          >
          <div class="titles">
            <!-- <i :class="ishow == item.id ? 'nocircle' : 'circle'" /> -->
            <span>
              {{ item.name }}
            </span>
          </div>
        </div>
      </div>
      <div slot="content">
        <administration
          v-if="ishow == 1"
          ref="administration"
          @assign="assign"
          @entrust="entrust"
        />
        <intelligence
          v-if="ishow == 2"
          :chain-id="chainId"
          @skipishow="skipishow"
        />
      </div>

      <div slot="radar">
        <div
          v-if="$store.getters.user.accountType == '2' && ishow == 3 && showattac"
          class="reportcs"
          @click="report"
        >
          <div class="count">
            <img
              src="https://static.idicc.cn/cdn/pangu/att.png"
              class="message"
            ><span>数智招商</span>
          </div>
        </div>
        <radar
          v-if="ishow == 3"
          :node-name="nodeName"
          @intelli="intelli"
          @showatt="showatt"
          @skipishow="skipishow"
        />
      </div>
    </title1>
    <designate
      v-if="assignball"
      :assignball="assignball"
      :clue-id="clueId"
      :enterprise-name="enterpriseName"
      :follow-up-person="followUpPerson"
      @closeassignball="closeassignball"
      @getList="getList"
    />
    <entrust
      v-if="entrustball"
      :entrustball="entrustball"
      :clue-id="clueId"
      :enterprise-name="enterpriseName"
      @closeentrust="closeentrust"
      @getList="getList"
    />
  </div>
</template>

<script>

import title1 from "./title.vue"; // 大屏 - 框
import administration from "./components/administration.vue"; //招商管理
import intelligence from "./components/intelligence.vue"; //招商情报
import radar from "./components/radar-1.vue"; //招商雷达
import designate from '@/views/system-manage/attract-investment/manage/components/operation/designate.vue'
import entrust from '@/views/system-manage/attract-investment/manage/components/operation/entrust.vue'
import {
  getIndustryChainIdByIdAPI,
  loadByOrgIdAPI, getMinAreaByTokenAPI
} from "@/api/CattractInvestment";
import { getPathId } from '@/utils/utils'

export default {
  name: "RelevanceEnterpriseNumber",
  components: { title1, administration, intelligence, radar, designate, entrust },
  data () {
    return {
      tabPosition: "left",
      ishow: 3,
      xyList: {},
      chainName: '',
      orgArea: '',
      showattac: true,
      chainId: "",
      nodeName: '',
      assignball: false,//线索指派
      clueId: '',//线索id
      enterpriseName: '',//企业名称
      entrustball: false,//委托招商
      followUpPerson: "",//跟进人
      showList: [
        { name: "招商雷达", id: 3 },
        { name: "招商情报", id: 2 },
        // { name: "招商管理", id: 1 }
      ],
    };
  },
  created () {
    let resourceCode = JSON.parse(localStorage.getItem('resourceCode'))
    if (resourceCode && resourceCode.includes('components/administration')){
      this.showList.push({ name: "招商管理", id: 1 },)
    }
    this.getChainIdById();
    this.loadByOrgId()
    this.getMinAreaByToken()
    if (this.$route.query.show)
    {
      this.ishow =this.$route.query.show
    }
  },
  methods: {
    // 关闭线索指派弹层
    closeassignball () {
      this.assignball = false;
      this.followUpPerson = null
    },
    closeentrust () {
      this.entrustball = false
    },
    assign (name, id, followUpPerson) {
      this.followUpPerson = followUpPerson
      this.enterpriseName = name
      this.clueId = id
      this.assignball = true
    },
    entrust (name, id) {
      this.enterpriseName = name
      this.clueId = id
      this.entrustball = true
    },
    //更新列表
    getList () {
      this.$refs.administration.getList()
    },
    intelli (val) {
      this.ishow = val
    },
    showatt (it) {
      this.showattac = it
    },
    skipishow () {
      // console.log(11)
      this.ishow = 1
    },
    async getMinAreaByToken () {
      let queryId = this.$route.query.id || getPathId() || null;

      const res = await getMinAreaByTokenAPI({
        relationId: queryId
      })
      this.chainName = res.result.chainName
      this.orgArea = res.result.minArea
    },
    async getChainIdById () {
      let queryId = this.$route.query.id || getPathId() || null;

      const res = await getIndustryChainIdByIdAPI({
        id: queryId,
      });
      this.chainId = res.result;
    },
    async loadByOrgId () {
      let queryId = this.$route.query.id || getPathId() || null;

      const res = await loadByOrgIdAPI({
        id: queryId,
      });
      this.nodeName = res.result.nodeName
      this.orgAreaName = res.result.orgAreaName
    },
    radarFn (id) {
      this.ishow = id;
    },
    intelligenceFn () {
      this.ishow = 2;
    },
    administrationFn () {
      this.ishow = 1;
    },
    report () {
      this.$router.push('/attract/intelligentRecommendation')
      
      // window.$wujie?.bus.$emit('newRouter', { path: '/pangu/attract/intelligentRecommendation' });
    }
  },
};
</script>

<style lang="scss" scoped>
.reportcs {
  z-index: 11111;
  position: absolute;
  background: url('~@/assets/screenCount/derive-button.png') no-repeat;
  background-size: 130px 42px;
  top: 6rem;
  right: 50px;
  border-radius: 8px;
  color: #c0c4cc;
  cursor: pointer;
  width: 130px;
  height: 37px;
  margin-left: 10px;
  display: flex;
  font-weight: 700;

  .count {
    justify-content: center;
    align-items: center;
    text-align: center;
    display: flex;

    .message {
      width: 20px;
      height: 20px;
      margin-left: 22px;
      margin-top: 3.8px;
    }

    span {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      margin-left: 5px;
      margin-top: 6px;
      font-weight: 800;
      color: #2FD5FF;
      background: linear-gradient(0deg, rgba(39, 171, 245, 1) 0%, rgba(255, 255, 255, 1) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}

.tabsbutton {
  position: absolute;
  z-index: 9999;
  // margin-top: -3rem;


  .circle {
    width: 0.5rem;
    height: 0.5rem;
    background-color: #021733;
    border: 1px solid #1f324c;
    border-radius: 50%;
    display: inline-block;
    // margin-right: 2rem;
    // margin-top: 2.5rem;
  }

  .nocircle {
    width: 0.5rem;
    height: 0.5rem;
    background-color: #fff;
    border-radius: 50%;
    display: inline-block;
    // margin-right: 2rem;
    // margin-top: 2.5rem;
  }

  .btn {
    width: 10rem;
    height: 4rem;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;

    span {
      text-align: center;
    }
  }

  .leftImg {
    width: 130px;
    height: 36px;
    position: absolute;
    left: 0px;
  }

  .titles {
    position: absolute;
    z-index: 22;
    padding: 3px 1.25rem 0 1.2rem;
    height: 1.5rem;
    color: white;
    font-family: YouSheBiaoTiHei;
    text-shadow: 0px 0px 16px rgba(60, 152, 255, 0.27);

    span {
      padding-left: 0.9rem;
      font-size: 16px;
    }
  }
}
</style>