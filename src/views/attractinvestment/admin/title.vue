<template>
  <div class="screen-main">
    <!-- 产业招商 -->
    <div class="screen-info">
      <Headers>
        <div slot="view-container">
          <div class="tabs">
            <slot name="tabs" />
          </div>
          <div class="content">
            <slot name="content" />
          </div>
          <div class="radar">
            <slot name="radar" />
          </div>
        </div>
      </Headers>
    </div>
  </div>
</template>
    
<script>
import Headers from "@/components/header.vue";
import { getPathId } from "@/utils/utils";

export default {
  name: "ScreenMain",
  components: { Headers },
  props: {
    /*       className: {
            type: String,
            default: "chart",
          }, */
    ishow: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      pageTypeName: "产业地图",
      name: "",
      xy: {},
    };
  },
  computed: {},
  mounted() {
    this.coordinate();
  },
  beforeDestroy() {},
  methods: {
    coordinate() {
      this.xy = document.getElementById("screenBGC");
      // console.log(this.xy);
      this.$emit("xy", this.xy);
    },
    // skip(value) {
    //   if (value == "/nodata") {
    //     return this.$message("开发中~敬请期待");
    //   }

    //   const id =  this.$route.query.id|| getPathId()|| null;

    //   this.$router.push({ path: value, query: { id } });
    // },
    async logout() {
      this.$confirm("确定退出登录吗?", "退出登录", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        center: true,
      }).then(async () => {
        await this.$store.dispatch("user/logout");
        this.$router.push("/login");
        // 清空token
        this.$message({
          type: "success",
          message: "退出登录成功",
          customClass: "admin-tips-message",
          duration: 3000,
          showClose: true,
          center: false, // 是否居中
        });
      });
    },

    // 查询
    OnSearch() {},

    // 查看舆情
    OnCheckOpinion() {},

    // 查看统计
    OnViewStatistics() {},
  },
};
</script>
<style lang="scss" scoped>
.content {
  position: absolute;
  margin-left: 8%;
  width: 92%;
  height: calc(100vh - 60px);
}

.tabs {
  position: absolute;
  margin-top: 2.7%;
}

.navigation {
  .navigation-info {
    margin-left: 8rem;
    display: flex;

    .el-button {
      background: rgba(136, 200, 255, 0.2);
      height: 2.6rem;
      width: 8rem;
      border: 1px;
      font-size: 1rem;
      margin-left: 4rem;
      color: #fff;
      border-radius: 8px;
      position: relative;

      span {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .highlight {
      background: url(https://static.idicc.cn/cdn/pangu/assets/screen/tabBtn.jpg);
      background-size: cover;
    }

    :hover.el-button {
      background: url(https://static.idicc.cn/cdn/pangu/assets/screen/tabBtn.jpg);
      background-size: cover;
    }
  }
}

// 标题
.left-headline {
  .node-name {
    z-index: 99;
    font-weight: 600;
    margin-left: 7vw;
    top: 0px;
    left: 0px;

    .name-info {
      font-size: 1.6rem;
      color: #fff;
      line-height: 2.5rem;
      float: left;
      min-width: 10rem;
      margin-right: 2rem;

      .suffix {
        position: relative;
        font-size: 0.9rem;
        font-style: normal;
        margin-left: 1rem;
      }

      .suffix::after {
        position: absolute;
        content: "";
        background: #fff;
        height: 0.2rem;
        width: 0.2rem;
        top: 50%;
        left: -10px;
      }
    }

    .navigation {
      float: left;
      height: 2.4rem;
      line-height: 2.4rem;
    }
  }
}

.screen-main {
  background-color: #070d61;
  width: 100vw;
  height: 100vh;
  position: relative;
  min-width: 1080px;

  .btn {
    position: relative;
    cursor: pointer;
    float: left;
    color: #fff;
    font-size: 1rem;
    height: 2.6rem;
    text-align: center;
    width: 6rem;

    img {
      position: absolute;
      z-index: 9;
      left: 0;
      top: -2px;
      width: 100%;
      height: 100%;
    }

    span {
      position: absolute;
      z-index: 99;
      text-align: center;
      line-height: 2.4rem;
      text-align: center;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
    }
  }

  .screen-bg,
  .screen-info {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
  }

  .screen-bg {
    position: absolute;
    z-index: 99;
    background-image: linear-gradient(
      to bottom,
      #e4e4e421,
      #ffffff14 3%,
      #00800000 20%
    );
    z-index: 9;
  }

  .screen-info {
    z-index: 99;
    background: transparent;
    position: fixed;
    z-index: 999;
    position: relative;
  }

  .screen-title {
    position: absolute;
    // height: 80px;
    z-index: 99;
    width: 100%;
    position: relative;

    .title-bg-img {
      position: absolute;
      z-index: 9;
      pointer-events: none;
      width: 70%;
      height: 10rem;
    }

    .title-top-info {
      position: absolute;
      width: 100%;
      z-index: 99;
      display: flex;
      justify-content: space-between;
      padding: 0.6rem 0.8rem;
    }

    .el-dropdown-link {
      color: #fff;
      float: right;
      padding: 10px 0;
      margin-left: 1rem;
    }

    // 右侧查询
    .right-search {
      display: flex;
      padding-top: 0.2rem;

      .search-main {
        display: flex;

        .input-icon {
          height: 1.2rem;
          width: auto;
          float: left;
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          margin: auto;
          margin-left: 1.8rem;
          overflow: hidden;
          margin-right: 0rem;
        }

        .input-main {
          position: relative;
          // min-width: 33rem;
          height: 3rem;
        }

        .input-bg {
          width: auto;
          height: 2.4rem;
        }

        .input-info {
          position: absolute;
          z-index: 99;
          width: 100%;
          height: 100%;
          display: flex;
          height: 2.6rem;
          border-radius: 8px;
          line-height: 2.6rem;
          width: 100%;
          top: -0.1rem;

          .input-icon {
          }

          .value-info {
            float: left;
          }
        }

        .btn {
          width: 4.8rem;
          height: 3rem;

          img {
            top: 0.01rem;
            height: 2.4rem;
          }

          span {
            position: absolute;
            z-index: 99;
            text-align: center;
            line-height: 2.5rem;
            text-align: center;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            font-size: 0.8rem;
          }
        }
      }

      .check-other {
        display: flex;
        margin-left: 2rem;
      }

      .btn {
        float: left;
      }
    }
  }
}
</style>
    