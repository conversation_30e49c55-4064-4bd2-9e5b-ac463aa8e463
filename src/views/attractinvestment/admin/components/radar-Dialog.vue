<template>
  <!-- 招商雷达 -->
  <!-- v-if="dialogData.show" -->
  <div>
    <div class="dialog">
      <!-- <div class="line" /> -->
      <img
        src="https://static.idicc.cn/cdn/pangu/close.png"
        class="close"
        alt=""
        srcset=""
        @click="closeEvent"
      >
      <div
        class="title"
        :title="dialogData.enterpriseName"
        @click="particulars(dialogData)"
      >
        {{ dialogData.enterpriseName || '' }}
      </div>
      <div class="btn-box">
        <div
          v-if="!dialogData?.isClue"
          class="btn-list nr"
          @click="checkEvent(dialogData)"
        >
          纳入意向
        </div>
        <div
          v-else
          class="btn-list nr"
        >
          已纳入意向
        </div>
      </div>
      <!-- <div v-else-if="dialogData.allocationState == 1" class="btn-box">
        <div class="btn-list no">跟进中</div>
      </div>
      <div v-else-if="dialogData.allocationState == 2" class="btn-box">
        <div class="btn-list sx">已失效</div>
      </div> -->
      <div class="txt">
        <div class="p p3">
          所在地区：{{ dialogData.province
          }}<span v-if="dialogData.province != dialogData.city">- {{ dialogData.city }}</span>
        </div>
        <div
          class="p p3"
          :title="'所在环节：' + dialogData.linkInPlace"
        >
          所在环节：{{ dialogData.linkInPlace }}
        </div>
        <div class="p">
          <span> 推荐日期：{{ dialogData.recommendedDate }} </span>
          <!-- <span v-else> 关注日期：{{ dialogData.attentionDate }} </span> -->
        </div>
        <div class="p p3 line1">
          推荐招商模式: {{ dialogData.modelTypeStr }}
        </div>
        <div
          class="p p3"
          :title="'联系方式: ' + dialogData.enterpriseContact"
        >
          联系方式: {{ dialogData.enterpriseContact }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { updatainclusionIntention, leaveAside } from '@/api/CattractInvestment';

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'RadarDialog',
  props: {
    dialogData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      dialogDataObj: {},
    };
  },
  methods: {
    // 是否纳入意向
    async checkEvent(item) {
      let res = await updatainclusionIntention({
         clueSource: 1,
      uniCode:item.unifiedSocialCreditCode,
        });

        if (res.code === 'SUCCESS') {
          this.closeEvent();
          this.$emit('getData');
          this.message();
        }
    },
    message() {
      const h = this.$createElement;
      this.$message({
        message: h('div', null, [
          h('span', { style: 'color:white' }, '纳入意向成功，该企业已进入'),
          h(
            'span',
            {
              style: 'color:#11CE66;cursor:pointer',
              on: {
                click: () => {
                  this.skip(); 
                },
              },
            },
            '产业招商-招商管理'
          ),
          h('span', { style: 'color:white' }, '中'),
        ]),
        type: 'success',
        duration: 5000,
        customClass: 'admin-tips-message',
      });
    },
    skip() {
      this.$message.closeAll(); //关闭message弹窗
      this.$emit('skipShow');
    },
    closeEvent() {
      this.$emit('closeEvent');
    },
    // 获取企业详情
    particulars(dialogData) {
      this.$emit('getCompanyInfo', dialogData);
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog {
  position: absolute;
  width: 250px;
  min-height: 157px;
  background: url('https://static.idicc.cn/cdn/pangu/assets/img/img05.png') no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 23px 17px;

  .line {
    width: 36px;
    background: #3695ff;
    height: 1px;
    position: absolute;
    left: -60px;
    top: 90px;
  }

  .close {
    width: 23px;
    height: 23px;
    position: absolute;
    right: 5px;
    top: 5px;
    cursor: pointer;
  }

  .title {
    width: 210px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 25px;
    cursor: pointer;
  }

  .btn-box {
    display: flex;
    padding-top: 5px;
    padding-bottom: 13px;

    .btn-list.nr {
      width: 80px;
      height: 30px;
      background: #0c2753;
      border: 1px solid #3695ff;
      opacity: 0.7;
      border-radius: 4px;
      text-align: center;

      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      line-height: 30px;
    }

    .btn-list {
      width: 80px;
      height: 30px;
      background: #0b1d3f;
      opacity: 0.7;
      border-radius: 4px;
      margin-right: 8px;
      text-align: center;
      cursor: pointer;

      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      line-height: 30px;

      &.no {
        cursor: no-drop;
        background: linear-gradient(
          0deg,
          rgba(0, 255, 240, 0.8),
          rgba(0, 102, 255, 0.8)
        );
        border-radius: 4px;
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        opacity: 1;
        margin-left: 0;
      }

      &.sx {
        cursor: no-drop;
        border-radius: 4px;
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        opacity: 1;
        margin-left: 0;
      }
    }
  }

  .txt {
    overflow: auto;
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 24px;
    opacity: 0.6;

    .p3 {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    // .p3.line1 {
    //   -webkit-line-clamp: 1;

    // }
  }
}
</style>
