#app {
  .el-dialog__wrapper{
    z-index: 9999999999 !important;
  }
  .dialog_tip {
    padding: 9px !important;
    background: transparent !important;
    &::before {
      content: "";
      width: 100%;
      height: 5px;
      position: absolute;
      top: 9px;
      left: 10px;
      background: left/cover url("~@/assets/screen/new/dialogBg.webp") no-repeat;
      background-size: 30% 100%;
      z-index: 1;
    }
    .el-dialog__header {
      border-radius: 3px 3px 0 0 !important;
      border: 1px solid #2cadff !important;
      border-bottom: 0 !important;
      margin-top: 5px;
      padding: 2px !important;
      background: #080e2b !important;
    }
    .el-dialog__body {
      border: 1px solid #2cadff !important;
      border-top: 0 !important;
      border-radius: 0 0 3px 3px !important;
      background: #080e2b !important;
    }
    .el-dialog__headerbtn {
      width: 30px;
      height: 30px;
      background: center/cover url("~@/assets/screen/new/close.webp") no-repeat;
      position: absolute;
      right: 0px;
      top: 0px;
      z-index: 222;
    }
  }
}
.dialog-header,  .popover_tip_header{
  width: 100%;
  display: inline-block;
  height: 26px;
  background: linear-gradient(90deg, #2cadff 0%, rgba(0, 13, 59, 0.188) 105%);
  font-family: YouSheBiaoTiHei;
  color: #ffffff;
  font-size: 18px;
  line-height: 26px;
  padding-left: 10px;
}
.popover_tip {
  padding: 5px !important;
  background: transparent !important;
  border: 0 !important;
  padding: 2px;
  .popover_tip_content {
    background: #080e2b !important;
    border: 1px solid #2cadff !important;
    border-radius: 3px !important;
    padding: 2px;
    height: 280px;
  }
  &::before {
    content: "";
    width: 100%;
    height: 5px;
    position: absolute;
    top: 0;
    left: 7px;
    background: left/cover url("~@/assets/screen/new/dialogBg.webp") no-repeat;
    background-size: 30% 100%;
    z-index: 1;
  }
  .el-form{
    padding: 16px  ;
  }
 .el-form-item{
  width: 100%;
  display: flex;
  .el-form-item__label{
    width: 100px !important;
  }
  .el-form-item__content{
flex: 1;
margin: 0 !important;
.el-select,.el-textarea{
  width: 100%;
  input,textarea{
    color: #fff;
    width: 100%;
    background: rgba(72, 82, 115, 0.5);
    border: 0px;
  }
}
  }


 }
}

.popover_tip {
  overflow-y: auto !important;
  color: #fff;
  border-radius: 3px;

  // 文字颜色
  .el-radio__label {
    color: #fff;
  }

  // 标题颜色
  .el-popover__title {
    color: #fff;
    font-size: 1rem;
    text-align: center;
  }

  // 选中框
  .el-radio__inner {
    background-color: #0c244d;
    border: 1px solid #1d5293;
  }

  .el-textarea__inner {
    background-color: transparent;
    color: #fff;
  }

  .el-textarea .el-input__count {
    background-color: transparent;
  }

  // .el-radio__input.is-checked .el-radio__inner::after {
  //   background-color: #1C91FF;
  // }

  // 输入框
  // .el-input__inner {
  //   background-color: transparent;
  //   color: #fff;
  // }

  .el-form-item__content ,.el-input--prefix .el-input__inner , .el-form-item__label {
    color: #fff;
  }

 

  p {
    padding: 0;
  }

  .el-button {
    border: 1px solid #1b589d;
    margin-left: 45%;
  }
  :hover.el-button {
    background: #1272CD;
  }

  .el-radio {
    margin: 5px;
  }
  // .el-dialog__header {
  //   background: transparent !important;
  //   .el-dialog__title {
  //     color: rgba(0, 255, 240, 1) !important;
  //     padding-left: 44%;
  //   }
  // }
}

.popover_tip::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px;
  /* 对应横向滚动条的宽度 */
  height: 0px;
}

/* 滚动条上的滚动滑块 */
.popover_tip::-webkit-scrollbar-thumb {
  background-color: #5470c6;
  border-radius: 32px;
}

/* 滚动条轨道 */
.popover_tip::-webkit-scrollbar-track {
  display: none;
}
// 背景颜色
.select-hand {
  //background-color: red !important;
  background-color: #00112d !important;
border: 1px solid #008CFF !important;

  // 鼠标经过时的颜色
  .el-select-dropdown__item.hover {
    background: #03152D;
  }

  // 未选中文字颜色
  .el-select-dropdown__item {
    color: #fff;
    font-weight: 600 !important;
  }

  // 选中的文字颜色
  .el-select-dropdown__item.selected {
    color: #fff;
    font-weight: 600 !important;
  }

  // 下拉框移入的颜色
  .el-select-dropdown__item:hover {
    background-image: linear-gradient(270deg, rgba(0, 140, 255, 0) 0%, #008CFF 100%);
    color: #fff;
  }

  // 移出不要丢失颜色
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
        background-image: linear-gradient(270deg, rgba(0, 140, 255, 0) 0%, #008CFF 100%);
    color: #fff;
  }
  .popper__arrow{
    border-bottom-color: #008CFF !important;
    &::after{
      border-bottom-color: #008CFF !important;
    }
  }
}