<template>
  <div>
    <div class="title">
      <img
        src="https://static.idicc.cn/cdn/pangu/firmParticularsdel.png"
        class="title-del"
        @click="close"
      >
      <img
        :src="getIconByType(showLabelType)"
        class="iconImg"
      >
      <el-popover
        placement="top-start"
        width="200"
        trigger="hover"
        :disabled="firmParticulars.orgEnterpriseDTO.enterpriseName.length<15"
        :content="firmParticulars.orgEnterpriseDTO.enterpriseName"
        :open-delay="500"
        popper-class="tag-popover"
      >
        <span
          slot="reference"
          style="white-space: nowrap;
          display: block;
          overflow: hidden;
          max-width: 19rem;
          text-overflow: ellipsis;"
          class="enterpriseName"
        >       
          {{ firmParticulars.orgEnterpriseDTO.enterpriseName }}</span>
      </el-popover>
      <!--       <span
        style="white-space: nowrap;
        display: block;
        overflow: hidden;
        max-width: 24rem;
        text-overflow: ellipsis;"
        class="enterpriseName"
      >
        {{ firmParticulars.orgEnterpriseDTO.enterpriseName }}
      </span> -->
      <div
        v-if="!firmParticulars.isInvestClue"
        class="attention"
        @click="attention()"
      >
        <span style="font-weight: 700;display: flex;margin-top: 1px;">
          纳入意向
        </span>
      </div>
      <div
        v-else
        class="attention2"
      >
        <span style="display: flex;margin-top: 1px;">已纳入意向</span>
      </div>
      <div
        v-if="firmParticulars.orgEnterpriseDTO.enterpriseLabelNames"
        class="tag"
      >
        <span
          v-for="(
            tag, idx
          ) in tagShow"
          :key="idx"
        >{{ tag }}</span>
        <el-popover
          v-if="isMore.length > 0"
          placement="bottom-start"
          width="200"
          class="more"
          visible-arrow="false"
          trigger="hover"
          popper-class="tag-popover"
        >
          <div 
            slot="reference"
            style="z-index: 999; position: absolute;"
          >
            更多标签
          </div>
          <span>
            <span
              v-for="(
                tag, index
              ) in isMore"
              :key="index"
            >#{{ tag }}
            </span>
          </span>
        </el-popover>
      </div>
    </div>
    <div class="content">
      <div class="tabs">
        <div
          v-for="(item, index) in activeName"
          :key="index"
          :class="istabs==item.id ? 'opttab' : 'tab'"
          @click="cutTabs(item)"
        >
          <span>
            {{ item.name }}
          </span>
        </div>
      </div>
      <!-- 基本信息 -->
      <div
        v-if="istabs == 1"
        class="detail-page"
      >
        <div style="margin-top: 8px;">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="name">
                <span>统一社会信用代码</span>
                <span class="span2">{{
                  firmParticulars.businessInfoDTO.unifiedSocialCreditCode
                }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="name">
                <span>成立日期</span>
                <span class="span2">{{
                  firmParticulars.businessInfoDTO.registerDate
                }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="name">
                <span>法定代表人</span>
                <span class="span2">{{
                  firmParticulars.businessInfoDTO.legalPerson
                }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="name">
                <span>注册资本</span>
                <span class="span2">{{
                  firmParticulars.businessInfoDTO.registeredCapital
                }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="name">
                <span>企业类型</span>
                <el-popover
                  placement="top-start"
                  width="200"
                  trigger="hover"
                  :disabled="firmParticulars.businessInfoDTO.enterpriseType.length<10"
                  :content="firmParticulars.businessInfoDTO.enterpriseType"
                  :open-delay="500"
                  popper-class="tag-popover"
                >
                  <span
                    slot="reference"
                    class="span2"
                    style="margin-left: 0"
                  >{{
                    firmParticulars.businessInfoDTO.enterpriseType
                  }}</span>
                </el-popover>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="name">
                <span>登记状态</span>
                <span class="span2">{{
                  firmParticulars.businessInfoDTO.registerStatus
                }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="name">
                <span>所属地区</span>
                <el-popover
                  placement="top-start"
                  width="200"
                  trigger="hover"
                  :disabled="region.length<11"
                  :content="region"
                  :open-delay="500"
                  popper-class="tag-popover"
                >
                  <span
                    slot="reference"
                    class="span2"
                    style="margin-left: 0;"
                  >{{ region }}</span>
                </el-popover>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="name">
                <span>所属行业</span>
                <el-popover
                  placement="top-start"
                  width="200"
                  trigger="hover"
                  :disabled="involved.length<10"
                  :content="involved"
                  :open-delay="500"
                  popper-class="tag-popover"
                >
                  <span
                    slot="reference"
                    style="margin-left: 0;"
                    class="span2"
                  >{{
                    involved
                  }}</span>
                </el-popover>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="name">
                <span>参保人数</span>
                <span class="span2">{{
                  firmParticulars.businessInfoDTO.insuredPersonsNumber
                }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="name">
                <span>专利个数</span>
                <span class="span2">{{
                  firmParticulars.businessInfoDTO.patentNumber
                }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="names">
                <span>经营范围</span>
                <el-popover
                  placement="top-start"
                  width="600"
                  trigger="hover"
                  :disabled="firmParticulars.businessInfoDTO.businessScope.length<130"
                  :content="firmParticulars.businessInfoDTO.businessScope"
                  :open-delay="500"
                  popper-class="tag-popover"
                >
                  <span
                    slot="reference"
                    style="margin-left: 0;"
                    class="span2"
                  >{{
                    firmParticulars.businessInfoDTO.businessScope
                  }}</span>
                </el-popover>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div
        v-if="istabs == 2"
        class="detail-page"
      >
        <div v-if="publicList.length>0">
          <div
            v-for="(item,index) in publicList"
            :key="index"
          >
            <a
              style="margin-left: 2rem;margin-top: 2rem; display: flex; justify-content: space-between;"
              :href="item.newsUrl"
              target="_blank"
            > <span style="width: 22rem;">{{ item.newsSummary }} </span><span style="margin-right: 3rem;">{{ item.publicDate }}</span></a>
          </div>
        </div>
        <div v-else>
          <p style="text-align: center; margin-top: 3rem;font-size: 1.5rem;font-size: 16px;">
            暂无舆情
          </p>
        </div>

        <div class="ye">
          <el-pagination
            small
            layout="total,prev, pager, next"
            :total="+publicTotal"
            :current-page.sync="public1.pageNum"
            :page-size.sync="public1.pageSize"
            @size-change="detail"
            @current-change="detail"
          />
        </div>
      </div>
      <div
        v-if="istabs == 3"
        class="detail-page"
      >
        <div class="addfollow">
          <span>
            共{{ total }}条走访记录
          </span>

          <el-popover
            placement="bottom"
            width="350"
            title="新增记录"
            trigger="click"
            popper-class="el_popover_class"
          >
            <el-form
              ref="form"
              label-position="left"
              label-width="80px"
              :model="form"
              hide-required-asterisk
              :rules="rules"
            >
              <el-form-item
                prop="followUpDate"
                label="跟进日期:"
                class="appendToBodyFalse"
              >
                <div class="block">
                  <el-date-picker
                    v-model="form.followUpDate"
                    :append-to-body="false"
                    value-format="timestamp"
                    type="date"
                    placeholder="选择日期"
                    :picker-options="pickerOptions"
                  />
                </div>
              </el-form-item>
              <el-form-item
                prop="followUpPersonId"
                label="跟进人："
              >
                {{ $store.getters.user.realName }}
              </el-form-item>
              <el-form-item
                prop="havaInvestmentIntention"
                label="投资意向:"
              >
                <el-radio
                  v-model="form.havaInvestmentIntention"
                  label="true"
                >
                  有投资意向
                </el-radio>
                <el-radio
                  v-model="form.havaInvestmentIntention"
                  label="false"
                >
                  无投资意向
                </el-radio>
              </el-form-item>
              <el-form-item
                prop="overview"
                label="跟进概述:"
              >
                <el-input
                  v-model="form.overview"
                  type="textarea"
                  maxlength="200"
                  show-word-limit
                  :autosize="{ minRows: 2, maxRows: 3 }"
                />
              </el-form-item>
            </el-form>
            <el-button
              type="primary"
              size="mini"
              @click="preserve(row)"
            >
              保存
            </el-button>
            <el-button
              slot="reference"
              size="small"
              class="smallBtn"
            >
              新增记录
            </el-button>
          </el-popover>
        </div>
        <div class="followList">
          <div v-if="followList.length >0">
            <div
              v-for="(item,index) in followList"
              :key="index"
              class="followList-el"
            >
              <span style="margin-left: 2rem; line-height: 2.4rem;">
                跟进日期：{{ item.followUpDate.split(' ')[0] }}
                <!--     跟进日期：{{ item.followUpDate.split(' ')[0] }} -->
                <span style="margin-left: 4rem;">
                  跟进人：
                </span>
                <span style="color:#086872">
                  {{ item.fillInPerson }}
                </span>
              </span>
              <br>
              <span style="margin-left: 2rem;">
                投资意向：<i :class="item.havaInvestmentIntention==true ? 'circle' :'nocircle'" />{{ item.havaInvestmentIntention==true ? '有投资意向' : '无投资意向' }}
              </span>
              <p style="margin-left: 2rem;word-break: break-all;">
                跟进概述：{{ item.overview }}
              </p>
            </div>
          </div>
          <div v-else>
            <p style="text-align: center; margin-top: 3rem;font-size: 16px;">
              暂无走访记录
            </p>
          </div>
        </div>
        <div class="ye">
          <el-pagination
            small
            layout="prev, pager, next"
            :total="+total"
            :current-page.sync="pageNum"
            :page-size.sync="pageSize"
            @size-change="followUpRecordPage"
            @current-change="followUpRecordPage"
          />
        </div>
      </div>
      <div
        v-if="istabs == 4"
        class="detail-page"
      >
        <div style="padding: 22px;">
          <span
            v-if="firmParticulars.data360"
            class="texte"
            v-html="firmParticulars.data360"
          />
          <span
            v-else
            style="display: flex;justify-content: center;align-items: center;width: 100%;margin-top: 4rem;font-size: 16px;"
          >
            暂无数据
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
  import { getEnterpriseIconByType } from '@/utils/utils'
import { followUpRecordPageAPI ,addfollowUpRecordAPI,enterpriseDetailAPI } from "@/api/CattractInvestment";
import {opinionAPI} from '@/api/Feelings'
import {updatainclusionIntention} from "@/api/CattractInvestment";
import { getPathId } from '@/utils/utils'

export default {
  name: "FirmParticulars",
  props: {
    firmParticulars: {
      type: Object,
      default: () => {},
    },
    rowID: {
      type: String,
      default: "0",
    },
    enterpriseID: {
      type: String,
      default: "0",
    },
    showLabelType:{
      type:String,
      default:null
    }
    
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
            return time.getTime() > Date.now();
          },
      },
      // tabs切换
      activeName: [
        { name: "基本信息", id: 1 },
        { name: "企业动态", id: 2 },
        { name: "360洞察", id: 4 }, 
        //{ name: "跟进记录", id: 3 },
      ],
      radio:'',//投资意向
      istabs: 1,//tab切换
      total:'',//总数
      form:{
      },
      new:[],
      tagShow:[],
      isMore:[],
      followList:{},
      pageNum:1,
      pageSize:2,
      public1:{
        pageSize:10,
        pageNum:1
      },
      publicTotal:'',
      publicList:{},
      rules: {
        followUpDate: [
          { required: true, message: "跟进日期不能为空", trigger: "blur" },          
        ],
        havaInvestmentIntention: [
          { required: true, message: "投资意向不能为空", trigger: "blur" },
        ],
        overview: [
          { required: true, message: "跟进概述不能为空", trigger: "blur" },
        ],
      },
      region:'',
      involved:'',
    };
  },
  watch:{
    'rowID'(){
      this.pageNum=1,
      this.pageSize=2,
      this.public1={
        pageSize:10,
        pageNum:1
      }
      //this.followUpRecordPage();
      this.detail() 
    },
    'enterpriseID'(){
      this.public1={
        pageSize:10,
        pageNum:1
      }
      this.detail()
      this.tagdispose()
    },
  },
  created() {
    //this.followUpRecordPage();
    this.detail()
    this.tagdispose()
    // console.log(this.firmParticulars);
  },
  methods: {
    getIconByType (type) {
      return getEnterpriseIconByType(type, 'webp');
    },

    async attention(){
    const res =   await updatainclusionIntention({
      clueSource: 1,
      uniCode: this.firmParticulars.businessInfoDTO.unifiedSocialCreditCode,
    })
      if(res.code==='SUCCESS'){
        this.$message({
          message: '纳入意向成功',
          type: 'success',
          customClass: 'admin-tips-message',
          duration: 3000,
          showClose: true,
          center: false, // 是否居中
        })
        const res = await enterpriseDetailAPI({
        enterpriseId:this.enterpriseID,
        secondQueryParam: "",
        orgIndustryChainRelationId:this.$route.query?.id|| getPathId()|| null,
      });
      // this.$emit('particularsSource',res.result.isInvestClue)
      }
    },
    tagdispose(){
      if(this.firmParticulars.orgEnterpriseDTO){
         // 省市区合集
         if(this.firmParticulars.businessInfoDTO?.province !=this.firmParticulars.businessInfoDTO?.city){
          this.region  = 
              this.firmParticulars.businessInfoDTO?.province +
              this.firmParticulars.businessInfoDTO?.city +
              this.firmParticulars.businessInfoDTO?.area;
         }else{
          this.region  = 
              this.firmParticulars.businessInfoDTO?.province + this.firmParticulars.businessInfoDTO?.area;
         }
         // 所属行业合集
            this.involved =
              this.firmParticulars.businessInfoDTO?.nationalStandardIndustry +
              this.firmParticulars.businessInfoDTO?.nationalStandardIndustryBig +
              this.firmParticulars.businessInfoDTO?.nationalStandardIndustryMiddle;
              // console.log(this.region,this.involved);
      }
      if(this.firmParticulars.orgEnterpriseDTO.enterpriseLabelNames!==null){
        this.new  = this.firmParticulars.orgEnterpriseDTO.enterpriseLabelNames.split(',')
      this.tagShow = this.new.slice(0, 2);
      if (this.tagShow.join('').length > 8) {
        this.tagShow = [this.new[0]];
      }
      this.isMore = this.new.filter(item => !this.tagShow.includes(item));
      }else {
      this.new=[]
      this.tagShow=[]
      this.isMore=[]
      }
    },
    // 获取企业舆情
   async detail(){
     const res = await opinionAPI({
      enterpriseId:this.enterpriseID,
      pageNum:this.public1.pageNum,
      pageSize:this.public1.pageSize
     })
     this.publicTotal =res.result.total
     this.publicList =res.result.records
    },
    // 新增跟进记录
   async preserve(){
    await this.$refs.form.validate();
       await addfollowUpRecordAPI({
        clueId:this.rowID,
        followUpDate:this.form.followUpDate,//跟进日期
        havaInvestmentIntention:this.form.havaInvestmentIntention,//投资意向
        overview:this.form.overview,//跟进概述
       })
       document.body.click();
       this.$message({
        message: '走访记录新增成功！',
        type: 'success',
        customClass: 'admin-tips-message',
        duration: 3000,
        showClose: true,
        center: false, // 是否居中
      });
       this.form={}
       this.pageNum =1
       this.followUpRecordPage()
       this.$emit("pageList")
    },
    // 获取跟进记录列表
    async followUpRecordPage() {
      const res = await followUpRecordPageAPI({
        clueId:this.rowID,
        pageNum:this.pageNum,
        pageSize:this.pageSize, 
      });
      this.followList=res.result.records
      this.total =res.result.total
    },
    // tab切换
    cutTabs(item) {
      this.istabs = item.id;
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>
  
<style scoped lang="scss">
.texte{
  line-height: 27px;
  font-size: 14px;
}
::v-deep {
  .el-pagination--small .btn-prev,
  .el-pagination--small .btn-next,
  .el-pagination--small .el-pager li,
  .el-pagination--small .el-pager li.btn-quicknext,
  .el-pagination--small .el-pager li.btn-quickprev,
  .el-pagination--small .el-pager li:last-child {
    background-color:transparent
  }
  .el-pagination .btn-prev .el-icon,
  .el-pagination .btn-next .el-icon {
    font-size: 12px;
    color: #fff;
  }
  .el-pager li.active {
    color: #fff !important;
  }
  .el-pagination {
    background-color:transparent !important;
    position: unset !important;
    text-align: center !important;
  }
  .el-pagination__total {
    color: #fff;
  }
  .input-info .el-input .el-input__inner {
    margin-top: 0.4rem !important;
  }
}
</style>
  <style scoped lang="scss">
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px;
  /* 对应横向滚动条的宽度 */
  height: 0px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #324156;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  display: none;
}
.ye {
  position: relative;
  /* 和body这个盒子的底部距离为0px */
  bottom: 0px;
}
.el-row {
  display: flex;
  width: 33.9rem;
  margin-bottom: 5px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  display: flex;
  height: 5rem;
  padding-right: 0px !important;
  padding-left: 15px !important;
}
.title {
  color: #fff;
  width: 100%;
  height: 6rem;
  //background: rgba(32, 154, 219, 0.1);
  box-sizing: border-box;
  border: 1px solid transparent;
  background-image: linear-gradient(to right, #04234c , #02162e),
        linear-gradient(to bottom,
            rgba(5, 84, 138, 0.1),
            rgba(5, 84, 138, 1));
  background-origin: border-box;
  background-clip: padding-box, border-box;
  border-radius: 10px;
  // 企业名称
  .enterpriseName {
    font-size: 16px;
    margin-left: 5rem;
    line-height: 4.4rem;
  }
  // 叉号按钮
  .title-del {
    width: 15px;
    height: 15px;
    float: right;
    margin-right: 0.3rem;
    margin-top: 0.2rem;
    cursor: pointer;
  }
  // 企业标签
  .tag {
    margin-left: 5rem;
    margin-top: -1.2rem;
    height: 6rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    .more {
      background-color: transparent;
    }
    span {
      float: left;
      height: 20px;
      font-size: 12px;
      background: #52abfb3d;
      padding: 5px 9px;
      margin: 2px;
      margin-right: 4px;
      border-radius: 2px;
      color: #ffffffd4;
      line-height: 10px;
    }
  }
  .attention{
          //@debugbackground: url('~@/assets/attract/inventory-button.png')no-repeat;
          width: 80px;
          height: 30px;
          border: 1px solid #3695ff;
          border-radius: 4px;
          opacity: 0.8;
          background-size: cover;
          margin-left: 25.5rem;
          position: absolute;
          margin-top: -2rem;
          color: #fff;
          font-size: 16px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
        }
        .attention2{
          //@debugbackground: url('~@/assets/attract/inventory-button.png')no-repeat;
          width: 80px;
          height: 30px;
          //border: 1px solid #3695ff;
          background-color: #0a234a;
          border-radius: 4px;
          opacity: 0.8;
          background-size: cover;
          margin-left: 25.5rem;
          position: absolute;
          margin-top: -2rem;
          color: #fff;
          font-size: 16px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
        }
}
.content {
  width: 100%;
  margin-top: 0.6rem;
  height: 42rem;
  color: #fff;
 // box-sizing: border-box;
 //   border: 1px solid transparent;
 //   background-image: linear-gradient(rgba(2, 24, 51),rgba(2, 24, 51)),
 //       linear-gradient(to bottom,
 //           rgba(5, 84, 138, 0.1),
 //           rgba(5, 84, 138, 1));
 //   background-origin: border-box;
 //   background-clip: padding-box, border-box;
 //   border-radius: 10px;
    border-radius: 10px;
  background: url('~@/assets/img/bg04.png') no-repeat center bottom;
  background-size: cover;
  //background: rgba(28, 145, 255, 0.1);
  //background: rgba(2, 37, 80, 0.1);
  //background:  rgba(6, 112, 180, 0.1);
 // background-image: linear-gradient(to right, #04234c , #02162e);
  //border-radius: 10px;
  .tabs {
  width: 100%;
  display: flex;
  border-radius: 10px 10px 10px 10px;
  //background: rgba(2, 37, 80, 0.1);
  cursor: pointer;
  .tab {
    width: 100%;
    text-align: center;
    border-radius: 10px 10px 0px 0px;
    font-size: 14px;
    background-color: #061F3D;
    position: relative;
    &::before{
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 1px;
      background: url('~@/assets/img/bg01.png') no-repeat;
      background-size: contain;
    }
    span {
      line-height: 3.4rem;
    }
  }
  .opttab {
    width: 100%;
    text-align: center;
    font-size: 14px;
    background: url('~@/assets/img/icon06.png') no-repeat center bottom #062347;
    border-radius: 10px 10px 0px 0px;
    position: relative;
    &::before{
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 1px;
      background: url('~@/assets/img/bg01.png') no-repeat;
      background-size: contain;
    }
    span {
      line-height: 3.4rem;
    }
  }
}
  .detail-page {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    max-height: 38rem; // 设置容器最大高度
    .name {
    width: 100%;
    min-height: 30px;
    background: rgba(28, 145, 255, 0.1);
    display: flex;
    flex-direction: column;
    span {
      color: #fff;
     // opacity: 0.7;
      margin-left: 1.5rem;
      margin-right: 0.2rem;
      margin-top: 0.8rem;
      max-width: 11.5rem; // 设置容器最大宽度
      white-space: nowrap; // 设置段落文本不换行(不换行才有可能行溢出)；
      overflow: hidden; // 关闭滚动条，超出部分隐藏；
      text-overflow: ellipsis; // 超出部分添加省略号
    }
    .span2 {
      color: #fff;
     // opacity: 1;
      margin-left: 1.5rem;
      margin-right: 0.2rem;
      margin-top: 0.8rem;
      max-width: 11.5rem; // 设置容器最大宽度
      white-space: nowrap; // 设置段落文本不换行(不换行才有可能行溢出)；
      overflow: hidden; // 关闭滚动条，超出部分隐藏；
      text-overflow: ellipsis; // 超出部分添加省略号
    }
  }
  .names {
    width: 100%;
    min-height: 9rem;
    background: rgba(28, 145, 255, 0.1);
    display: flex;
    flex-direction: column;
    span {
      color: #fff;
      //opacity: 0.7;
      margin-left: 1.5rem;
      margin-top: 0.5rem;
      margin-right: 0.2rem;
      max-width: 30rem; // 设置容器最大宽度
      overflow: hidden; //多出的隐藏
      text-overflow: ellipsis; //多出部分用...代替
      display: -webkit-box; //定义为盒子模型显示
      -webkit-line-clamp: 5; //用来限制在一个块元素显示的文本的行数
      -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
    }
    .span2 {
      color: #fff;
      opacity: 1;
    }
  }
    .addfollow{
      span{
       margin-left: 2rem;
       margin-top: 1rem;
      }
      // 新增记录按钮
      .smallBtn{
        background-color: #082851;
        color: #fff;
        border: 1px solid #0b3c79;
        margin-top: 1rem;
        margin-left: 16rem;
      }
    }
    .followList{
      width: 90%;
      margin-left: 5%;
      .followList-el{
        margin-top: 1rem;
        // background-image: linear-gradient(to bottom right, #0066FF, #04234C );
        //  opacity: 0.2;
        background: rgba(0, 0, 0,0.1);
        //background-color: red;
        height: 14rem;
        span{
          opacity: 1;
        }
        .circle{
            width: 10px;
            height: 10px;
            background-color: #0df0d6;
            border-radius: 50%; 
            display: inline-block;
            margin-right: 5px;
        }
        .nocircle{
            width: 10px;
            height: 10px;
            background-color: #495a6f;
            border-radius: 50%; 
            display: inline-block;
            margin-right: 5px;
        }
      }
    }
    .subdivide {
      white-space: nowrap; // 设置段落文本不换行(不换行才有可能行溢出)；
      overflow: hidden; // 关闭滚动条，超出部分隐藏；
      text-overflow: ellipsis; // 超出部分添加省略号；
      width: 90%;
      height: 2.5rem;
      border: 10px;
      margin-left: 5%;
      margin-top: 1rem;
      font-size: 14px;
      //background: rgba(2, 26, 55, 0.1);
      background: rgba(0,0,0, 0.3);
      border-radius: 2px;
      line-height: 2.4rem;
      .name {
        margin-left: 2rem;
        margin-right: 2rem;
      }
    }
  }
}
.iconImg {
  position: relative;
  width: 24px;
  height: 24px;
  margin-right: 18px;
  margin-left: 28px;
}
</style>