.screen {
  display: flex;
  z-index: 11111;
  position: absolute;
  left: 30px;
  bottom: 32px;
  height: 50px;
  border-bottom: 1px solid #182231;

  .it1 {
    margin-top: 10px;
  }

  .btn-text {
    display: inline-block;
    transform: skewX(20deg);
    transform-style: preserve-3d;
  }

  .xz {
    display: inline-block;
    height: 36px;
    /* 将高度更改为50px */
    position: relative;
    top: -4px;
    padding: 10px 20px;
    cursor: pointer;
    background: linear-gradient(-2deg, #1c92ff, rgba(28, 146, 255, 0.06));
    color: #fff;
    font-size: 13px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    margin-left: 3px;
    border: none;
    outline: none;
    transform: skewX(-20deg);
  }

  .on {
    display: inline-block;
    height: 32px;
    padding: 10px 20px;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.2);
    color: #c8e4ff;
    font-size: 13px;
    opacity: 0.6;
    font-family: Source Han Sans CN;
    font-weight: 400;
    margin-left: 3px;
    border: none;
    outline: none;
    transform: skewX(-20deg);
  }
.qiyvbtn{
  margin-left: 105px !important;
}
  .xzs {
    height: 36px;
    /* 将高度更改为50px */
    margin-top: -4px;
    /* 向上移动9px以保持位置不变 */
    clip-path: polygon(0 0, 100% 0, 88% 100%, 0% 100%, 0 80%);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    padding-left: 30px;
    padding-right: 30px;
    background: linear-gradient(-2deg, #1c92ff, rgba(28, 146, 255, 0.06));
    border-radius: 2px;
    font-size: 13px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
  }

  .ons {
    height: 32px;
    clip-path: polygon(0 0, 100% 0, 88% 100%, 0% 100%, 0 80%);
    background: rgba(255, 255, 255, 0.2);
    cursor: pointer;
    position: absolute;
    opacity: 0.6;
    font-size: 13px;
    padding-left: 30px;
    padding-right: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #c8e4ff;
    border-radius: 2px;
  }
}
.industry {
  height: calc(100vh - 3.75rem);
  overflow: hidden;

}

.radarBottomBtn {
  z-index: 11111;
  display: flex;
  flex-wrap: nowrap;
  position: absolute;
  align-items: flex-end;
  justify-content: flex-start;
  left: 30px;
  bottom: 32px;
  height: 50px;
  border-bottom: 1px solid #182231;
  padding-bottom: 6px;

  .bottomActive {
    height: 36px;
    -webkit-clip-path: polygon(0 0, 100% 0, 89% 100%, 0% 100%);
    clip-path: polygon(0 0, 100% 0, 89% 100%, 0% 100%);
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding-left: 30px;
    padding-right: 30px;
    background: linear-gradient(-2deg, #1c92ff, rgba(28, 146, 255, 0.06));
    border-radius: 2px;
    font-size: 16px;
    color: #ffffff;

  }

  .left {
    height: 32px;
    -webkit-clip-path: polygon(0 0, 100% 0, 89% 100%, 0% 100%, 0 80%);
    clip-path: polygon(0 0, 100% 0, 89% 100%, 0% 100%, 0 80%);
    background: rgba(255, 255, 255, 0.2);
    cursor: pointer;
    opacity: 0.6;
    font-size: 13px;
    padding-left: 30px;
    padding-right: 30px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #C8E4FF;
    border-radius: 2px;
  }

  .right {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;

    .bottomRightActive {
      display: inline-block;
      height: 36px;
      position: relative;
      top: -4px;
      padding: 10px 26px;
      cursor: pointer;
      background: linear-gradient(-2deg, #1c92ff, rgba(28, 146, 255, 0.06));
      color: #fff;
      font-size: 13px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      border: none;
      outline: none;
      clip-path: polygon(0% 100%, 12% 0%, 100% 0%, 88% 100%);
      -webkit-clip-path: polygon(0% 100%, 12% 0%, 100% 0%, 88% 100%);
    }

    .rightDom {
      height: 32px;
      padding: 10px 20px;
      cursor: pointer;
      background: rgba(255, 255, 255, 0.2);
      color: #C8E4FF;
      font-size: 13px;
      opacity: 0.6;
      font-family: Source Han Sans CN;
      font-weight: 400;
      margin-left: -4px;
      border: none;
      outline: none;
      clip-path: polygon(0% 100%, 12% 0%, 100% 0%, 88% 100%);
      -webkit-clip-path: polygon(0% 100%, 12% 0%, 100% 0%, 88% 100%);


    }
  }
}

.radarRightBtn {
  width: 130px;
  height: calc(100% - 200px);

  .list {
    .listItem {
      width: 100%;
    }

    .active {
      background: top 0 left 0 / contain no-repeat url('../../../../assets/img/radar/bt-bg2.png'),
        top 0 right 0 / contain no-repeat url('../../../../assets/img/radar/bt-bg3.png'),
        bottom 0 left 0 / contain no-repeat url('../../../../assets/img/radar/bt-bg1.png'),
        bottom 0 right 0 / contain no-repeat url('../../../../assets/img/radar/bt-bg4.png');
      background-size: 10px 10px;

      .activeLine {
        border: 1px solid rgba(47, 213, 255, 0.5);
        z-index: -1;
        background-clip: text;
        color: transparent;
        -webkit-text-fill-color: transparent;
        background-image: linear-gradient(#FFFFFF, rgba(47, 213, 255));
      }
    }


  }

  .list {

    background: rgba(2, 18, 38, 0.5);

    color: #619ACF;
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-bottom: 20px;
    text-align: center;

    .activeLine {
      width: 100%;
      height: 100%;
      padding: 7px 10px;
      border: 1px solid rgba(0, 63, 157, 0.5);
    }

    // &:hover {
    //   div {
    //     border: 1px solid rgba(0, 63, 157, 0.5);
    //     background-clip: text;
    //     color: transparent;
    //     -webkit-text-fill-color: transparent;
    //     background-image: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 15.72265625%, #98B8FF 43.9208984375%, rgba(249, 251, 255, 0.38) 89.7216796875%);
    //   }
    // }
  }


}

.industryCard {
  width: 100%;
  height: 100%;
  background: bottom /contain no-repeat url('../../../../assets/img/radar/bg-2.png');
  background-size: 100% 100%;
  position: relative;


  .card {
    width: 100%;
    height: 100%;
    background: top 45% left 48% /contain no-repeat url('../../../../assets/img/radar/bg-1.png');
    background-size: 43% 47%;
    position: absolute;
    animation: dong1 8s infinite;
  }

  .radarImg2 {
    z-index: 2222;
    cursor: pointer;
    width: 14%;
    height: 16%;
    top: 36%;
    left: 42%;
    background-image: url('https://static.idicc.cn/cdn/pangu/raadarTop.png');
    background-size: 100% 100%;
    position: absolute;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2.25rem;
    font-family: YouSheBiaoTiHei !important;

  }

  .container {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .radarImg_bg {
    width: 100%;
    height: 100%;
    background: top 45% left 48% /contain no-repeat url('../../../../assets/img/radar/bg-3.png');
    background-size: 100% 100%;
    position: absolute;
    z-index: 1111;
  }

  .guides {
    width: 0;
    height: 42%;
    top: 42%;
    left: 49%;
    position: absolute;
  }

  .guides_point {
    z-index: 2222;
  }

  .guides_line {
    z-index: 111;
  }

  .point {
    position: absolute;
    width: 1rem;
    height: 1rem;
    background: top left /contain no-repeat url('../../../../assets/img/radar/icon2.png');

  }

  .isClue.point.active {
    background: top left /contain no-repeat url('../../../../assets/img/radar/iconGreenRound.png');

  }

  .isClue.point {
    background: top left /contain no-repeat url('../../../../assets/img/radar/iconGreenRound.png');
  }
  .point.active {
    width: 1.5rem;
    height: 1.5rem;
    background: top left /contain no-repeat url('../../../../assets/img/radar/icon4.png');

    .hight {
      width: 0rem;
      height: 0rem;
    }

  }

  .isClue.point .hight {
    background: top left /contain no-repeat url('../../../../assets/img/radar/iconGreenBg.png');
  }
  .point .hight {
    width: 1rem;
    height: 1rem;
    background: center /contain no-repeat url('../../../../assets/img/radar/icon3.png');
    -webkit-animation: dot_pulse 8s infinite linear;
    animation: dot_pulse 8s infinite linear;
    opacity: 0;
  }

  .point_dialog {

    display: none;

    &.active {
      position: absolute;
      display: block;
    }
  }



  .line {

    position: absolute;
    width: 53.2rem;
    height: 53.2rem;
    top: -53.2rem;
    left: -53.2rem;
    z-index: 9999;
    -webkit-transform-origin: 100% 100%;
    transform-origin: 100% 100%;
    -webkit-border-radius: 43.8rem 0 0 0;
    border-radius: 43.8rem 0 0 0;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    background: center /contain no-repeat url('../../../../assets/img/radar/line2.png');
    /* linear-gradient(80deg, #009FFF, #0096FF, #0084FF); */
    /* background-image: -webkit-linear-gradient(45deg, #009FFF 0%, #0096FF 50%, #0084FF 100%);
  background-image: linear-gradient(45deg, #009FFF 0%, #0096FF 50%, #0084FF 100%); */
    /* background-image: -webkit-linear-gradient(45deg, rgba(139, 168, 146, 0.01) 0%, rgba(139, 168, 146, 0.01) 50%, #8ba892 100%);
		background-image: linear-gradient(45deg, rgba(139, 168, 146, 0.01) 0%, rgba(139, 168, 146, 0.01) 50%, #8ba892 100%); */

    -webkit-animation: radar 8s infinite linear;
    animation: radar 8s infinite linear;
  }
}

.compInfo {
  position: absolute;
  width: 29%;
  height: 100%;
  margin-left: 1.5%;
  background: rgba(3, 37, 80, 0.1);
  box-sizing: border-box;
  border: 1px solid transparent;
  background-origin: border-box;
  background-clip: padding-box, border-box;
  border-radius: 10px;
  top: 60px;
  right: 10px;
  z-index: 11111;
}

.radarRight{
  width: 18.75rem;
  height: 74vh;
  position: absolute;
  top: 9.25rem;;
  right: 3.125rem;
  z-index: 1111;
  display: flex;
  align-items: end;
  justify-content: space-between;
  flex-direction: column;
}
.tips{
font-family: puhuiti;
font-size: 14px;
line-height: 21.06px;
text-align: right;
color: rgba(217, 217, 217, 0.8);
	
}
@-webkit-keyframes radar {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }



  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }


}

@keyframes radar {

  0% {

    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    /* transform-origin: bottom right 0px; */
  }



  100% {

    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
    /* transform-origin: bottom right 0px; */
  }

}

@-moz-keyframes dot_pulse {
  0% {
    /* background-image: url('../../../../assets/img/radar/icon1.png'); */
    opacity: 1;
  }

  70% {
    /* background-image: url('../../../../assets/img/radar/icon1.png'); */
    opacity: 0.5;
  }

  100% {
    opacity: 1;
    /* background-image: url('../../../../assets/img/radar/icon.png'); */

  }
}

@keyframes dong1 {
  0% {
    opacity: 1;
  }

  70% {
    opacity: 0.4;
  }

  100% {
    opacity: 1;
  }
}

@-webkit-keyframes dot_pulse {
  0% {
    opacity: 1;
  }

  70% {
    opacity: 0.1;
  }

  100% {
    opacity: 0.1;
  }
}