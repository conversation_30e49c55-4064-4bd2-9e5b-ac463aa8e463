<template>
  <div class="industry">
    <div class="industryCard">
      <div class="card" />
      <div class="radarImg2">
        <el-tooltip
          class="item"
          effect="dark"
          :content="middleName"
          placement="bottom-start"
        >
          <div>
            {{
              middleName.length > 6
                ? `${middleName.slice(0, 6)}...`
                : middleName
            }}
          </div>
        </el-tooltip>
      </div>
      <div class="container">
        <!-- <div id="radar">   -->
        <div class="radarImg_bg" />
        <div class="guides guides_line">
          <div
            v-if="style"
            class="line"
          />
        </div>

        <div class="guides guides_point">
          <div
            v-for="list in listData"
            :key="list.id"
            class="point"
            :class="{ showHover, active: list.id === activeId,isClue: list.isClue }"
            :style="{ left: list.left + 'rem', top: list.top + 'rem' }"
            @click="select(list)"
          >
            <div
              class="hight"
              :style="{
                '-webkit-animation-delay': list.delay + 's',
                'animation-delay': list.delay + 's',
              }"
            >
              <el-tooltip
                class="item"
                effect="dark"
                :content="list.enterpriseName"
                placement="right"
              >
                <!--  :disabled="disabled" -->
                <span class="pointSpan" />
              </el-tooltip>
            </div>
          </div>
          <div
            :class="{ active: listItem.id === activeId }"
            :style="{
              left: listItem.left + 2 + 'rem',
              top:
                listItem.top >= 9
                  ? listItem.top - 13 + 'rem'
                  : listItem.top < 9 && listItem.top > 3.7
                    ? listItem.top - 9 + 'rem'
                    : listItem.top + 1 + 'rem',
            }"
            class="point_dialog"
          >
            <RadarDialog
              :dialog-data="listItem"
              @getCompanyInfo="getCompanyInfo"
              @closeEvent="closeEvent"
              @getData="getData"
              @skipShow="skipShow"
            />
          </div>
        </div>
      </div>
      <!-- </div> -->
    </div>
    <!-- 右侧导航 -->
    <div class="radarRight">
      <div class="radarRightBtn">
        <div
          v-for="(nodeList, index) in nodeData"
          :key="index"
          class="list"
          @click="choseCompanyType(nodeList)"
        >
          <el-tooltip
            class="item"
            effect="dark"
            :content="nodeList.nodeName + '·' + nodeList.count"
            placement="left"
          >
            <div
              :class="{ active: nodeList.id === activeNode }"
              class="listItem"
            >
              <div class="activeLine">
                <div>
                  {{
                    nodeList.nodeName.length > 4
                      ? `${nodeList.nodeName.slice(0, 4)}...`
                      : nodeList.nodeName
                  }}·{{ nodeList.count }}
                </div>
              </div>
            </div>
          </el-tooltip>
        </div>
      <!-- <div class="sample">
        <span class="point" />未分配
      </div> -->
      </div>
      <div class="tips">
        * 注：雷达图上只展示最新推荐的前50家企业
      </div>
    </div>


    <!--  <div class="radarBottomBtn">
      <div :class="modelType === '1' ? 'bottomActive' : 'left'" @click="chooseModelType(&quot;1&quot;)">
        亲商招商
      </div>
      <div class="right">
        <div v-for="(item, index) in radarBottomBtnData" :key="index"
          :class="modelType === item.id ? 'bottomRightActive' : 'rightDom'" @click="chooseModelType(item.id)">
          {{ item.name }}
        </div>
      </div>
    </div> -->
    <div class="screen">
      <div class="it1">
        <div
          :class="modelType == 1 ? 'xzs' : 'ons'"
          @click="chooseModelType(1)"
        >
          亲缘招商
        </div>
        <div class="qiyvbtn">
          <div
            v-for="(item, index) in radarBottomBtnData"
            :key="index"
            :class="modelType == item.id ? 'xz' : 'on'"
            @click="chooseModelType(item.id)"
          >
            <span class="btn-text">{{ item.name }}</span>
          </div>
        </div>
      </div>

      <!--         <div
          :class="atpresent == '其他' ? 'xz' : 'on'"
          @click="cut('其他')"
        >
          其他
        </div> -->
    </div>
    <div
      v-if="showCompanyInfo"
      class="compInfo"
    >
      <!-- <div v-if="true" class="compInfo"> -->
      <!--       <FirmParticulars
        :firm-particulars="firmParticulars"
        :show-label-type="showLabelType"
        :enterprise-i-d="enterpriseID"
        @close="close"
        @skipShow="skipShow"
      /> -->
      <AtlasDetailCompany
        :child-id="enterpriseID"
        :model-type="modelTypes"
        :recommend-region-code="recommendRegionCode"
        @closeDetail="close"
        @formationList="formationList"
      />
      <!-- <AtlasDetailCompany child-id="316404" @closeDetail="close" @formationList="formationList" /> -->
    </div>
  </div>
</template>

<script>
import RadarDialog from './radar-Dialog.vue';
import { getCountByNodes } from '@/api/attractInvestment';
import AtlasDetailCompany from '@/views/echarts/large-screen/companyDetail.vue';
import { getPathId } from '@/utils/utils';
import mock from './mock.js';
export default {
  components: {
    RadarDialog,
    //FirmParticulars,
    AtlasDetailCompany,
  },
  props: {
    nodeName: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      listData: [],
      style: false,
      showHover: true,
      disabled: false,
      nodeData: [],
      enterprisesData: [],
      activeId: '',
      listItem: {},
      showCompanyInfo: false,
      firmParticulars: {}, //企业详情
      showLabelType: null, // 企业状态
      enterpriseID: '',
      radarBottomBtnData: [],
      activeNode: null,
      modelType: 1,
      middleName: '',
      modelTypes: '',
      recommendRegionCode:'',
    };
  },
  mounted() {
    this.radarBottomBtnData = mock.radarBottomBtn;
    this.getData();
  },
  methods: {
    formationList() {
      this.showCompanyInfo = false;
      this.activeId = '';
      this.getData();
    },
    choseCompanyType(type) {
      this.style = false;
      this.activeNode = type.id;
      this.closeEvent();
      this.close();
      this.getData();
    },
    chooseModelType(id) {
      if (this.modelType == id) {
        this.modelType = '';
      } else {
        this.modelType = id;
      }
      this.closeEvent();
      this.close();
      this.getData();
    },
    //getData
    // 获取所有展示数据
    async getData() {
      this.style = false;
      let queryId = this.$route.query.id || getPathId() || null;

      let parm = {
        orgIndustryChainRelationId: queryId, //机构产业关系id，不能为空
        nodeId:this.activeNode, //节点id，可以为空
        modelType: this.modelType, //招商模式 1亲商招商 2资源招商 3链主招商 4政策招商 5舆情招商 6AI+招商 可以为空
      };
      const res = await getCountByNodes(parm);
      let { enterprises, nodeCountDTOS, chainNodeName } = res?.result;
      let data = nodeCountDTOS || [];
      // let data =
      //   mock.listData
      this.nodeData = data;
      this.middleName = chainNodeName.split('·').reverse()[0];
      this.changeType(enterprises);
      // ,
    },
    drawEllipse() {
      let enterprise = this.enterprisesData;
      let centerX = 180,
        centerY = 100;
      const min = -300;
      const max = 200;

      // crX = 800,
      // x半径在-400到400范围之间
      // y半径在-150到150范围之间
      let datas = enterprise?.map((item, index) => {
        let X =
          mock.mockX[
            enterprise.length > 40 ? index : Math.floor(Math.random() * 50)
          ];
        let Y = null;
        // 获取-300到200之间的随机数
        const randomNumber = Math.floor(Math.random() * (max - min) + min);
        let RangeY = this.getRange(centerY, randomNumber);
        Y =
          (X < centerX && X > 0) || (X > -centerX && X < 0)
            ? RangeY
            : randomNumber;

        return {
          distanceX: X,
          distanceY: Y,
          //  mock.mockY[index],
          ...item,
        };
      });
      this.radar(datas);
    },
    getRange(range, num) {
      let number = num;
      // -100到0，-100
      if (num < 0 && num > -range) {
        number = num - range;
      }
      // 0到100+100
      if (num > 0 && num < range) {
        number = num + range;
      }
      return number;
    },

    radar(people) {
      let peoples = people;
      let time = 9;
      // let data = []
      let data = peoples.map((item) => {
        // 求夹角度数（度数大于0计算时从Y轴正轴顺时针计算，反着就是负数）
        var angleRadians = Math.atan2(item.distanceX, item.distanceY);
        var angleDegrees = angleRadians * (180 / Math.PI);
        var delay = 0;

        if (angleDegrees < 0) {
          delay = time / 2 + (time / 360) * (180 + angleDegrees);
        } else {
          delay = (time / 360) * angleDegrees;
        }
        return {
          id: item.id,
          left: (item.distanceX - 8) / 16, //-50适应圆点位置
          top: (-item.distanceY - 8) / 16, //-70适应圆点位置
          delay,
          ...item,
        };
      });

      this.listData = data;
      this.style = true;
    },
    select(e) {
      // this.disabled = !this.disabled
      this.showHover = false;
      this.listItem = e;
      this.activeId = this.activeId === e.id ? '' : e.id;
      this.close();
      // setTimeout(() => {
      //   this.disabled = !this.disabled
      // }, 1000)
    },
    changeType(enterprises) {
      //  设置企业节点信息
      let data = enterprises;
      this.enterprisesData = data;
      // 画出企业节点位置
      this.drawEllipse();
    },
    async getCompanyInfo(dialogData) {
      this.modelTypes = dialogData.modelTypes[0];
      this.recommendRegionCode = dialogData.recommendRegionCode || '';
      this.showCompanyInfo = true;
      this.enterpriseID = dialogData.enterpriseId;
    },
    close() {
      this.showCompanyInfo = false;
    },
    closeEvent() {
      this.activeId = '';
    },

    skipShow() {
      this.$emit('skipishow');
    },
  },
};
</script>

<style scoped lang="scss">
@import './radar.scss';



</style>
