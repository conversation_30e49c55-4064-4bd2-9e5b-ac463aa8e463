
// 1111
.dialog_tip_content {
  color: #fff;
  font-size: 14px;
  line-height: 1.5rem;
  // height: 106px;
  padding: 42px 34px;
  box-sizing: border-box;
}
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px;
  /* 对应横向滚动条的宽度 */
  height: 0px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #324156;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  display: none;
}
::v-deep {
  // 选择器文字颜色
  .el-form-item__label {
    color: #fff;
  }

  // 选中文字的颜色
  .el-input--suffix .el-input__inner {
    color: #fff;
  }

  .el-input--suffix .el-input__inner {
    background-color: #031c3e;
    width: 11.5rem;
    height: 2rem;
    border: 1px #07366d;
    font-weight: 400;
  }

  .el-form-item__label {
    margin-left: 2rem;
    margin-top: 0.1rem;
  }
}

::v-deep {
  .el-pagination--small .btn-prev,
  .el-pagination--small .btn-next,
  .el-pagination--small .el-pager li,
  .el-pagination--small .el-pager li.btn-quicknext,
  .el-pagination--small .el-pager li.btn-quickprev,
  .el-pagination--small .el-pager li:last-child {
    background-color: transparent;
  }

  .el-pagination .btn-prev .el-icon,
  .el-pagination .btn-next .el-icon {
    font-size: 12px;
    color: #fff;
  }

  .el-pager li.active {
    color: #fff !important;
  }

  .el-pagination {
    background-color: transparent !important;
    position: unset !important;
    text-align: center !important;
  }

  .el-pagination__total {
    color: #fff;
  }

  .input-info .el-input .el-input__inner {
    margin-top: 0.4rem !important;
  }
}

.demo-form-inline {
 position: relative;
  width: 100%;
  height: 70px;
  z-index: 9999;
  display: flex;
      align-items: center;
      justify-content: space-between;
  background: center/contain no-repeat url("~@/assets/screen/new/searchBg.webp") !important;
  background-size: 100% 100% !important;
  padding: 0 !important;
  -webkit-box-shadow: 0px !important;
  box-shadow: 0 0 0 !important;
  border-radius: 0px !important;
      .el-form-item:last-child {
        width: 10%;
      }
      .el-form-item {
        margin-bottom: 0 !important; 
        width: 30%;
        display: flex;
        justify-content: left;
        ::v-deep {
          .el-form-item__content{
            flex: 1;
            .el-select{
              width: 100%;
              input{
              width: 100%;
              background-color: rgba(72, 82, 115, 0.5);
              }
            }
          }
        }
   
      }
      .el-form-item:last-child {
        width: 15% !important; 
      }
}

.table-tableData {
  height: 13.5vh;
  cursor: pointer !important;
  &:hover{
    opacity: 0.8; 
    background-image: linear-gradient(to right, #023c72 5%, rgba(1, 140, 254, 0) 100%),
    radial-gradient(
      circle at 0 50%,
      rgba(0, 165, 254, 0.54),
      rgba(0, 165, 254, 0.52) 20%,
      rgba(0, 165, 254, 0.3) 45%,
      rgba(0, 165, 254, 0.1) 75%,
      rgba(113, 161, 194, 0.04) 85%,
      rgba(113, 161, 194, 0.04) 100%
    ) !important;
   } 
  .conpanyTitle {
    padding-top: 5px;
    .conpanyTitleLeft {
      width: 60%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .companytText{
        font-weight: 600;
      }
    
    }
    .conpanyTitleRight {
      width: 24%;
      display: flex;
      justify-content: space-around;
      align-items: center;
    }
  }
}

.ye {
  position: relative;
  //position: absolute;
  left: 0;
  bottom: 1rem;
  right: 0;
  top: 0.5rem;
}
.enterpriseName {
  cursor: pointer;
}

 

.list-echarts {
  display: flex;
  height: 90vh;
  margin-top: 15px;
  justify-content: space-between;

  .echarts {
    width: 39rem;
    height: 100%;
    background: rgba(0, 24, 53, 0.8);
    padding: 32px;
  }
    .echarts2 {
    width: 39rem;
    height: 100%;
    background: rgba(0, 24, 53, 0.8);
  }
 
  .detail-main{
    position: relative !important;
    top: 0 !important;
    margin: 0;
  }
}

.dialogBg {
}

// 111
.demo-table-expand {
  font-size: 0;
}

.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
.dialogBg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 11111;
}
.rightContent {
  height: 34%;
}
.rightContentLast {
  height: 33%;
}
#distribution {
  height: 15rem;
  width: 100%;
  padding: 10px 100px 10px 50px;
}

.circle,
.nocircle {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
  margin-bottom: 2px;
}
.nocircleText,
.circleText {
  font-size: 12px;
  background: rgba(216, 216, 216, 0.1);
  padding: 2px 10px;
  border-radius: 15px;
  margin-left: 10px;
}
.circle {
  background-color: #24ffba;
}

.nocircle {
  background-color: #b9c4c0;
}
.nocircleText {
  color: #b9c4c0;
}
.circleText {
  color: #24ffba;
}

.colorText{
  color: #1C91FF;
}
.listNoData{
  width: 100%;
    height: 100%;
}
.iconImg {
  background-size: 100%;
    background-position: center;
    background-repeat: no-repeat;
}

.enterpriseTag{
padding: 2px 8px ;
font-size: 12px;
font-weight: 500;
margin-left: 10px;
border-radius: 2px;
}
.success{
color: #4CC169;
background: rgba(76, 193, 105, 0.2);
}
.ing{
  background: rgba(51, 112, 255, 0.2);
  color: #3370FF;
}
.fail{
  background: rgba(224, 72, 72, 0.2);
  color: #E04848;
}
.errorView{
  background: rgba(216, 216, 216, 0.1);
padding: 0px 10px 1px;
    margin-left: 10px;
    border-radius: 15px;

  .txt{
font-size: 12px;
 margin-left: 5px;
color: #B9C4C0;
  }
}
.el-icon-warning-outline{
  color: #e04848;
  position: relative;
    width: 15px;
   height: 12px;
    &::before{
      position: absolute;
      transform: rotate(180deg);
    }
}
.two-B{
    span{
 width: 95%;
    }
}
.two-C,.two-B{
  span{
    white-space: nowrap;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
   
  }
}