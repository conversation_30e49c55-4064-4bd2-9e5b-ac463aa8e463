<template>
  <!-- 招商雷达 -->
  <div
    id="radar"
    class="radar"
  >
    <div
      style="cursor: pointer"
      @click="intelli"
    >
      <img
        style="
            position: relative;
            margin-top: 5rem;
            z-index: 99;
            margin-left: 106rem;
          "
        src="https://static.idicc.cn/cdn/pangu/information-bgc.png"
      >
      <img
        style="
            position: absolute;
            z-index: 99;
            top: 5rem;
            left:107rem;
          "
        src="https://static.idicc.cn/cdn/pangu/information-2.png"
      >
      <span
        style="
            position: absolute;
            z-index: 99;
            top: 5.6rem;
            left: 110rem;
            font-weight: 700;
            color: #3370FF;
          "
      >
        招商情报
      </span>
    </div>
  
    <div
      class="radar-main"
      data="test"
    >
      <div class="radar-con">
        <canvas
          id="myCanvas"
          canvas-id="canvasId"
          :width="cvsData.cvsW"
          :height="cvsData.cvsH"
        />
        <div class="list-main">
          <!-- <div :style="'position: absolute;left:'+domData.centerX+'px;top:'+domData.centerY+'px;color:red;'">
                0
              </div>
              <div style="position: absolute;left:570px;top:240px">
                {{ cvsData.num }}
              </div> -->
          <div class="img-q1" />
          <div class="img-q2" />
          <div
            v-for="(item, i) in radarList"
            :key="i"
            class="list"
            :class="'chainNodeId-' + item.chainNodeId"
          >
            <div
              v-for="(list, k) in item.chainNodes"
              :key="k"
              class="item"
            >
              <template v-for="(dian, j) in list.enterprises">
                <div
                  v-if="point['id-' + dian.id]"
                  :key="j"
                  class="item"
                  @click="dialogClick(dian)"
                >
                  <el-tooltip
                    effect="dark"
                    :content="dian.enterpriseName"
                    placement="right-end"
                  >
                    <div
                      v-if="dian.allocationState == 0"
                      :class="'img id-' + dian.id"
                      :style="
                        'left:' +
                          point['id-' + dian.id].x +
                          'px;top:' +
                          point['id-' + dian.id].y +
                          'px;'
                      "
                    />
                    <img
                      v-else-if="dian.allocationState == 1"
                      :class="'id-' + dian.id"
                      src="https://static.idicc.cn/cdn/pangu/green.png"
                      width="50"
                      :style="
                        'left:' +
                          point['id-' + dian.id].x +
                          'px;top:' +
                          point['id-' + dian.id].y +
                          'px;'
                      "
                      alt=""
                      :data-txt="list.chainNodeName"
                      :data-gs="dian.enterpriseName"
                      srcset=""
                    >
                    <img
                      v-else-if="dian.allocationState == 2"
                      :class="'id-' + dian.id"
                      src="https://static.idicc.cn/cdn/pangu/grey.png"
                      width="50"
                      :style="
                        'left:' +
                          point['id-' + dian.id].x +
                          'px;top:' +
                          point['id-' + dian.id].y +
                          'px;'
                      "
                      alt=""
                      :data-txt="list.chainNodeName"
                      :data-gs="dian.enterpriseName"
                      srcset=""
                    >
                  </el-tooltip>
                </div>
              </template>
            </div>
          </div>
  
          <div
            v-if="dialogData.show"
            class="dialog"
            :style="
              'left:' +
                (dialogData.x + 90) +
                'px;top:' +
                (dialogData.y - 70) +
                'px'
            "
          >
            <img
              src="https://static.idicc.cn/cdn/pangu/icon01.png"
              class="line"
              alt=""
              srcset=""
            >
            <img
              src="https://static.idicc.cn/cdn/pangu/close.png"
              class="close"
              alt=""
              srcset=""
              @click="closeEvent"
            >
            <div
              class="title"
              :title="dialogData.enterpriseName"
              @click="particulars(dialogData)"
            >
              {{ dialogData.enterpriseName }}
            </div>
            <div
              v-if="dialogData.allocationState == 0"
              class="btn-box"
            >
              <div
                class="btn-list"
                :class="isCheck === 1 ? 'on' : ''"
                @click="checkEvent(1)"
              >
                纳入意向
              </div>
              <!--               <div
                class="btn-list"
                :class="isCheck === 2 ? 'on' : ''"
                @click="checkEvent(2)"
              >
                暂不处理
              </div> -->
            </div>
            <div
              v-else-if="dialogData.allocationState == 1"
              class="btn-box"
            >
              <div class="btn-list no">
                跟进中
              </div>
            </div>
            <div
              v-else-if="dialogData.allocationState == 2"
              class="btn-box"
            >
              <div class="btn-list no">
                已失效
              </div>
            </div>
            <div class="txt">
              <div class="p">
                所在地区：{{ dialogData.province }}- {{ dialogData.city }}
              </div>
              <div class="p">
                所在环节：{{ dialogData.linkInPlace }}
              </div>
              <div class="p">
                推荐日期：{{
                  dialogData.recommendedDate.replace(" 00:00:00", "")
                }}
              </div>
              <div class="p">
                推荐理由; {{ dialogData.recommendationReason }}
              </div>
            </div>
          </div>
  
          <div
            v-if="isecharts == true"
            class="echarts"
          >
            <div
              id="main"
              style="width: 100%; height: 16rem"
            />
            <div
              id="distribution"
              style="width: 100%; height: 18rem"
            />
            <div
              id="Statistics"
              style="width: 100%; height: 16rem"
            />
          </div>
          <div
            v-else
            class="compInfo"
          >
            <firmParticulars
              :firm-particulars="firmParticulars"
              class="echarts"
              :show-label-type="showLabelType"
              @close="close"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
    
  <script>
    import { attractInvestmentRadarListAPI, updateAttractInvestmentStatusAPI, enterpriseDetailAPI } from "@/api/CattractInvestment";
    import firmParticulars from "./firmParticulars.vue";
    import { getPathId } from '@/utils/utils'

    export default {
      // eslint-disable-next-line vue/multi-word-component-names
      name: 'Radar',
      components: {
        firmParticulars
      },
      props: {
        nodeName: {
          type: String,
          default: '',
        }
      },
      data() {
        // 圆心
        let centerX = 0;
        let centerY = 0;
        return {
          firmParticulars: {}, //企业详情
          isecharts: true, //展示详情还是图谱
          showLabelType: null, // 企业状态
          isCheck: '',
          dialogData:{
            show: false
          },
          point:{
    
          },
          domData:{
            centerX: 750, // left: 450px
            centerY: 464 - 62, // top: 209px
  
            // centerX: 490, // left: 450px
            // centerY: 262, // top: 209px
          },
          radarList: [],
          cvsData: {
            cvsW: 1920,
            cvsH: 1080,
            centerX: 960, // 1920/2,
            centerY: 540, // 1080/2,
  
            // centerX: 700, // 1920/2,
            // centerY: 400, // 1080/2,
            num: 0, // 分成多少份
            round_1:{
              centerX, // 圆心x
              centerY: centerY, // // 圆心y
              x_r: 252/2, // x轴半径
              y_r: 169/2, // y轴半径
            },
            round_2:{
              centerX: centerX, 
              centerY: centerY,
              x_r: 521/2,
              y_r: 346/2,
            },
            round_3:{
              centerX: centerX, 
              centerY: centerY,
              // centerX: centerX+10, 
              // centerY: centerY+45,
              x_r: 890/2,
              y_r: 610/2,
            }
          },
          txtPoint: [],
          listAngle: [],
          listPoint: [],
        };
      },
      mounted() {
    let queryId= this.$route.query.id|| getPathId()|| null;

        this.chainId =queryId;
        this.stmentRadarList(this.chainId);
        document.getElementById('radar').width
      },
      methods: {
        intelli(){
           this.$emit('intelli',2)
        },
        // 关闭详情
        async close() {
          this.isecharts = true;
          await this.drawChart();
          this.linkDistribution();
          this.followupStatistics();
        },
        // 获取企业详情
        async particulars(row) {
          // console.log('row', row)
          this.showLabelType = row.showLabelType; // 企业状态
          const res = await enterpriseDetailAPI({
            enterpriseId: row.enterpriseId,
          });
          this.firmParticulars = res.result;
          this.isecharts = false;
          this.$set(this, 'dialogData', {});
        },
        checkEvent(e){
          this.isCheck=e;
          // this.dialogData.show= false;
          let data = {
            id: this.dialogData.id,
            status: e
          }
          updateAttractInvestmentStatusAPI(data).then(res=>{
           // //console.log(res)
            location.reload()
            this.$set(this, 'dialogData', {});
          })
        },
        async stmentRadarList(chainId) {
          const res = await attractInvestmentRadarListAPI({
            orgIndustryChainRelationId: chainId,
          });
          let radarList = res.result;
          radarList.map(e=>{
            let len = e.chainNodes.length;
            this.cvsData.num = this.cvsData.num + len;
            let _len = 0, _lens=0;
            e.chainNodes?.map(e_1=>{
                _len = _len + e_1.enterprises?.length
                _len && e_1.enterprises?.map(()=>{
                  _lens++
                })
            });
            e._len = _lens;
            e.len = len;
            return e;
          })
          this.radarList = radarList;
          this.draw()
        },
        async drawBg(obj) {
          let that = this;
          let { context, canvas } = obj;
          let image = await this.loadingImg({url:require('./../../../../assets/img/zs_bg.jpeg')})
          let {width, height} = canvas
          context.drawImage(image, 0, 0-60, width, height, -width/2, -height/2, width, height);
          
          // let img1 = await this.loadingImg({url:require('./../../../../assets/img/zs_bg_1.png')})
          let img1 = await this.loadingImg({url:require('./../../../../assets/img/zs_bg_7.png')})
          context.drawImage(img1, 0, 0, 1248, 829, -1248/2, -829/2, 1248, 829);
  
  
          context.beginPath();
          context.strokeStyle = '#089cff'
          let angle = 360/that.cvsData.num;
          for(let i=0; i<that.cvsData.num; i++) {
            // 划线
            context.moveTo(0, 0);
  
            let lineX = Math.cos((i * angle) * Math.PI / 180) * 430,
            lineY = Math.sin((i * angle) * Math.PI / 180) * 430
  
            // 创建一个渐变
            var gradient=context.createLinearGradient(0,0,lineX,lineY);
            gradient.addColorStop("1", "#089cff");
            gradient.addColorStop(0, "#13437f");
            context.strokeStyle = gradient;
            
            context.lineTo(
              lineX,
              lineY
            );
  
          }
          context.stroke();
  
          let img2 = await this.loadingImg({url:require('./../../../../assets/img/zs_bg_2.png')});
          context.drawImage(img2, 0-1, 0-5, 521, 346, -521/2, -346/2, 521, 346);
  
          that.drawRound(obj)
          
        },
        draw() {
          let canvas = document.getElementById('myCanvas');
          let context = canvas.getContext('2d');
    
          context.save();
        //   context.translate(canvas.width / 2, canvas.height / 2);
          context.translate(this.cvsData.centerX, this.cvsData.centerY);
        //   context.translate(700, 450);
    
          this.drawBg({canvas,context})
  
          canvas.onclick = function(e){
            var rect = this.getBoundingClientRect();
            var gravityPoint = {
                  x: e.clientX - rect.left,
                  y: e.clientY - rect.top
                };
                //console.log("gravityPoint", gravityPoint);
          };
    
        },
        drawRound(obj){
          let { context } = obj;
    
          this.getPoint(obj)
    
          let that = this;
    
          this.listAngle.map(e=>{
            context.beginPath();
            context.strokeStyle = '#022346'
            for(let k=0; k<16; k++){
              context.moveTo(0, 0);
              context.lineTo(
                Math.cos((e.count + k*0.15) * Math.PI / 180) * 430, 
                Math.sin((e.count + k*0.15) * Math.PI / 180) * 430
              );
            }
            context.stroke();
          })
              
          let img4 = new Image();
          img4.src=require('./../../../../assets/img/zs_bg_4.png');
          img4.onload = function() {
            context.drawImage(img4, 0, 0, 553, 373, -553/2, -373/2, 553, 373);
          
            // 写字
            that.txtPoint.map(e=>{
              context.moveTo(0, 0);
              context.fillStyle="#fff";
              context.font="15px Arial";
              context.textAlign="center";
              context.fillText(e.name, e.x+10, e.y);
              // context.fillText('0', e.x+10, e.y);
            })
            context.moveTo(0, 0);
            context.fillStyle="#fff";
            context.font="22px regular";
            context.textAlign="center";
            context.fillText(that.nodeName, 0, 0)
    
          }
        },
        getPoint(){
    
          let cvsData = this.cvsData;
          let { round_1, round_2 } = cvsData
    
    
          let count=0; // 起始夹角
          let angle = 360/this.cvsData.num; // 夹角
          this.radarList.map((e)=>{
            let len = 0, _len=0, _lens=0;
            if(e.chainNodes.length){
              len = len + e.chainNodes.length
              e.chainNodes?.map(e_1=>{
                _len = _len + e_1.enterprises?.length
                _len && e_1.enterprises?.map(async (e_2)=>{
                  // let suiji = fun(count, angle*len)
  
                  let { round_2, round_3 } = cvsData;
                  // round_3.y_r, // 大圆小半径
                  // round_2.x_r; // 小圆大半径
                  // console.log('_len', e._len)
                  // console.log(_lens, 'e.len', e.len, '111-count', count, 'count + angle*_len', count + angle*_lens)
                  let r = (round_3.y_r - round_2.x_r) / 2 + round_2.x_r;
                  let jiao = count + (e.len / e._len) * angle * _lens;
  
                  // _lens 当前夹角内第几个节点
  
                  if (_lens / 3 < 1) {
                    // jiao = e.len * angle/(e.len*2) * (i+1)
                    jiao = count + ((e.len * angle) / 4) * (_lens + 1);
                  } else if (_lens / 7 < 1) {
                    jiao = count + ((e.len * angle) / 5) * (_lens + 1 - 3);
                    r = r + 50;
                  } else if (_lens / 12 < 1) {
                    jiao = count + ((e.len * angle) / 8) * (_lens + 1 - 7);
                    r = r + 100;
                  } else if (_lens / 17 < 1) {
                    jiao = count + ((e.len * angle) / 6) * (_lens + 1 - 12);
                    r = r + 150;
                  } else if (_lens / 25 < 1) {
                    jiao = count + ((e.len * angle) / 9) * (_lens + 1 - 17);
                    r = r + 200;
                  } else {
                    r = r + 250;
                  }
                  // console.log("jiao", jiao, i, _lens, r, count);
  
                  let _jiao = jiao;
                  let dx = Math.cos((_jiao * Math.PI) / 180) * r;
                  let dy = Math.sin((_jiao * Math.PI) / 180) * r; // + _lens/2*35;
  
                  this.listPoint.push({ x: dx, y: dy, data: e_2 });
  
                  // 点的dom坐标
                  let px = dx + this.domData.centerX + 180;
                  let py = dy + this.domData.centerY + 35;
                  this.$set(this.point, "id-" + e_2.id, {
                    x: px,
                    y: py,
                    data: e_2,
                  });
                  _lens++;
                });
            });
  
            if (!len) {
              return;
            }
  
            let thisAngle = len * angle; // 当前文字占的夹角
  
            let r_a = round_1.x_r + (round_2.x_r - round_1.x_r) / 2;
            let r_b = round_2.y_r + (round_2.y_r - round_1.y_r) / 2 - 75;
            let dx = Math.cos(((count + thisAngle / 2) * Math.PI) / 180) * r_a;
            let dy = Math.sin(((count + thisAngle / 2) * Math.PI) / 180) * r_b;
  
            // 存划线数据
            this.listAngle.push({ count });
  
            // 存文本及坐标
            this.txtPoint.push({
              name: e.chainNodeName,
              x: dx,
              y: dy,
            });
  
            count = count + thisAngle;
          }
        });
  
        //console.log("this.point", this.point);
      },
      dialogClick(e) {
        e.show = true;
        //console.log(e);
        let point = this.point["id-" + e.id];
        //console.log("point", point);
        e.x = point.x;
        e.y = point.y;
        this.dialogData = e;
        this.$set(this, "dialogData", e);
      },
      closeEvent() {
        this.$set(this, "dialogData", {});
        //console.log("this.dialogData", this.dialogData);
      },
      //   加载图片
      async loadingImg({ url }) {
        return await new Promise((resolve, reject) => {
          let img = new Image();
          img.src = url;
          img.onload = function () {
            resolve(img);
          };
          img.onerror = reject;
        });
      },
      windowToCanvas(canvas, x, y) {
        let bbox = canvas.getBoundingClientRect();
        return {
          x: x - bbox.left * (canvas.width / bbox.width),
          y: y - bbox.top * (canvas.height / bbox.height),
        };
      },
    },
  };
  </script>
    
    <style lang="scss" scoped>
  .radar {
    width: 100%;
    height: 100%;
    min-height: 900px;
    .radar-main {
      width: 100%;
      height: 100%;
      min-height: 900px;
      position: absolute;
      left: 0;
      top: 0;
      .img-q1{
        width: 732px;
        height: 489px;
        background: url('./../../../../assets/img/nei.png') no-repeat;
        background-size: 100%;
        position: absolute;
        left: 600px;
        top: 262px;
        animation: dong1 3s infinite;
      }
      @keyframes dong1 {
        0% {
          top: 262px;
        }
        50% {
          top: 282px;
        }
        100% {
          top: 262px;
        }
      } 
      .img-q2{
        width: 923px;
        height: 615px;
        background: url('./../../../../assets/img/wai.png') no-repeat;
        background-size: 100%;
        position: absolute;
        left: 506px;
        top: 219px;
        animation: dong2 5s infinite;
      }
      @keyframes dong2 {
        0% {
          top: 219px;
        }
        50% {
          top: 250px;
        }
        100% {
          top: 219px;
        }
      }
      .radar-con {
        width: 100%;
        height: 100%;
        min-width: 1500px;
        /* background: url('./../../../../assets/img/zs_bg.jpeg') no-repeat;
          background-size: 100%; */
        .list {
          &-main {
            /* width: 727px;
              height: 515px;
              left: 390px;
              top: 230px; */
  
            width: 100vw;
            height: 100vh;
            left: 0;
            top: 0;
            position: absolute;
            .list {
              img {
                position: absolute;
                cursor: pointer;
              }
              .img {
                position: absolute;
                cursor: pointer;
                width: 50px;
                height: 56.5px;
                animation: shan 1s infinite;
              }
              @keyframes shan {
                from {
                  background: url("./../../../../assets/img/red.png") no-repeat;
                  background-size: cover;
                }
                to {
                  background: url("./../../../../assets/img/red_no.png") no-repeat;
                  background-size: cover;
                }
              }
              .item {
                display: inline-block;
              }
            }
            .dialog {
              position: absolute;
              width: 366px;
              height: 377px;
              background: url("./../../../../assets/img/img02.png") no-repeat;
              background-size: cover;
              box-sizing: border-box;
              padding-left: 83px;
              padding-top: 84px;
              .line {
                width: 137px;
                height: 69px;
                position: absolute;
                left: -60px;
                top: 90px;
              }
              .close {
                width: 23px;
                height: 23px;
                position: absolute;
                right: 70px;
                top: 68px;
                cursor: pointer;
              }
              .title {
                width: 200px;
                font-size: 15px;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #ffffff;
                line-height: 24px;
                cursor: pointer;
              }
              .btn-box {
                display: flex;
                padding-top: 5px;
                padding-bottom: 13px;
                .btn-list.on {
                  width: 80px;
                  height: 30px;
                  background: #0c2753;
                  border: 1px solid #3695ff;
                  opacity: 0.7;
                  border-radius: 4px;
                  text-align: center;
  
                  font-size: 14px;
                  font-family: Source Han Sans CN;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 30px;
                }
                .btn-list {
                  width: 80px;
                  height: 30px;
                  background: #0b1d3f;
                  opacity: 0.7;
                  border-radius: 4px;
                  margin-left: 8px;
                  text-align: center;
                  cursor: pointer;
  
                  font-size: 14px;
                  font-family: Source Han Sans CN;
                  font-weight: 400;
                  color: #ffffff;
                  line-height: 30px;
                  &.no {
                    cursor: no-drop;
                  }
                }
              }
              .txt {
                width: 200px;
                height: 119px;
                overflow: auto;
                font-size: 14px;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #ffffff;
                line-height: 24px;
                opacity: 0.6;
              }
            }
          }
        }
      }
      #myCanvas {
        /* width: 100%;
          height: 100%;
          width: 1500px;
          height: 900px;
          width: 1920px;
          height: 1080px; */
      
          position: absolute;
          left: 0;
          top: -62px;
  
          /* width: 923px;
          height: 615px;
          position: absolute;
          left: 50%;
          top: 50%;
          margin-left: -461.5px;
          margin-top: -307.5px; */
      }
      .compInfo {
        position: absolute;
        width: 32%;
        height: 96%;
        margin-left: 1.5%;
        background: rgba(3, 37, 80, 0.1);
        padding: 0.5rem;
        padding-top: 20px;
        top: 50px;
        right: 30px;
        background: rgb(1 20 41);
      }
    }
  }
  </style>