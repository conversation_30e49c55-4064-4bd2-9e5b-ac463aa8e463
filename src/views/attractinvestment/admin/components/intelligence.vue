<template>
  <!-- 招商情报 -->
  <div class="list-echarts">
    <div class="form">
      <div class="title">
        <div class="tab">
          <div
            v-for="(item, index) in tabList"
            :key="item.id"
            :class="['tab-button', isList == item.id ? 'pitch-on' : '']"
            @click="cutTab(item.id)"
          >
            <span :class="['tab-button' + index]">
              {{ item.name }}
            </span>
          </div>
        </div>
        <div class="forms">
          <el-form
            :inline="true"
            :model="formInline"
            class="demo-form-inline"
          >
            <div
              v-show="isList == 1"
              class="demo-form-company"
            >
              <el-form-item
                class="formsearch"
                label="推荐日期"
              >
                <el-select
                  v-model="formInline.recommendSource"
                  clearable
                  popper-class="select-hand"
                  @change="screenformationList"
                >
                  <el-option
                    label="近一周"
                    value="2"
                  />
                  <el-option
                    label="近一月"
                    value="3"
                  />
                  <el-option
                    label="近半年"
                    value="4"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="招商模式">
                <el-select
                  v-model="formInline.industrialLinkId"
                  clearable
                  popper-class="select-hand"
                  @change="screenformationList"
                >
                  <el-option
                    v-for="(item, index) in linkList"
                    :key="index"
                    :label="item.nodeName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <!-- <el-form-item>
                <div
                  slot="label"
                  style="cursor: pointer;"
                >
                  <el-dropdown trigger="click">
                    <span class="el-dropdown-link">
                      {{ link }}<i class="el-icon-arrow-down el-icon--right" />
                    </span>
                    <el-dropdown-menu
                      slot="dropdown"
                      class="menu"
                    >
                      <el-dropdown-item @click.native="Selection('强链环节')">
                        强链环节
                      </el-dropdown-item>
                      <el-dropdown-item @click.native="Selection('补链环节')">
                        补链环节
                      </el-dropdown-item>
                      <el-dropdown-item @click.native="Selection('延链环节')">
                        延链环节
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
                <el-select
                  v-model="formInline.nodeid[0]"
                  clearable
                  popper-class="select-hand"
                  no-data-text="暂无相应环节"
                  @change="screenformationList"
                >
                  <el-option
                    v-for="(item) in Chainnode"
                    :key="item.nodeId"
                    :label="item.nodeName"
                    :value="item.nodeId"
                  />
                </el-select>
              </el-form-item> -->
              <el-form-item>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="默认导出前5000条数据"
                  placement="bottom"
                >
                  <el-button
                    :class="[Number(total) === 0 ? 'exportBtn0' : 'exportBtn']"
                    :disabled="Number(total) === 0"
                    @click="exportFile"
                  >
                    <div class="span">
                      <img
                        v-if="Number(total) === 0"
                        src="https://static.idicc.cn/cdn/pangu/icon_ex_0.svg"
                        alt=""
                        class="exImg"
                      >
                      <img
                        v-else
                        src="https://static.idicc.cn/cdn/pangu/icon_ex_1.svg"
                        alt=""
                        class="exImg"
                      >
                      <span class="exTxt">导出</span>
                    </div>
                  </el-button>
                </el-tooltip>
              </el-form-item>
            </div>
            <div
              v-show="isList == 2"
              class="demo-form-news"
            >
              <el-form-item label="发布时间">
                <el-select
                  v-model="information.recommendDateType"
                  popper-class="select-hand"
                  clearable
                  @change="pageListToUser"
                >
                  <el-option
                    label="近一周"
                    value="1"
                  />
                  <el-option
                    label="近一月"
                    value="2"
                  />
                  <el-option
                    label="近半年"
                    value="3"
                  />
                </el-select>
              </el-form-item>
              <!-- <el-form-item label="主题词">
                <el-select
                  v-model="information.newsThemeIds"
                  clearable
                  multiple
                  collapse-tags
                  class="select-newsThemeIds"
                  popper-class="select-hand"
                  @change="pageListToUser"
                >
                  <el-option
                    v-for="(item, index) in subjectList"
                    :key="index"
                    :label="item.name"
                    :value="item.name"
                  />
                </el-select>
              </el-form-item> -->
            </div>
          </el-form>
        </div>
      </div>

      <!-- 列表 企业 -->
      <div
        v-show="isList == 1"
        v-if="tableData?.length > 0"
        class="tables"
      >
        <div
          v-loading="listloading"
          element-loading-background="rgba(2, 28, 60, 0.8)"
        >
          <div
            v-for="(item, index) in tableData"
            :key="index"
            class="table-tableData"
            :class="{ tableActive: item.id == rowID }"
            @click="particulars(item)"
          >
            <div class="conpanyTitle">
              <div class="companyName">
                <img
                  class="iconImg"
                  :src="getIconByType(item.showLabelType)"
                >
                <span class="companytText">
                  {{ item.enterpriseName }}
                </span>
                <div
                  v-if="item.enterpriseLabelNameList"
                  class="tag"
                >
                  <span
                    v-for="(tag, idx) in item.enterpriseLabelNameList"
                    v-show="idx < 3"
                    :key="idx"
                  >{{ tag }}</span>
                </div>
              </div>
              <div>
                <!-- <el-button
                  v-if="item.allocationState == 2"
                  class="colorBtn1"
                  size="small"
                >
                  已失效
                </el-button> -->
                <div v-if="!item.isInvestClue">
                  <el-button
                    size="small"
                    :class="[
                      item.isInvestClue ? 'grayness' : '',
                      'colorBtn',
                    ]"
                    :disabled="item.isInvestClue ? true : false"
                    @click.stop="intention(item)"
                  >
                    纳入意向
                  </el-button>
                  <!--                   <el-button
                    size="small"
                    style="
                      background-color: #092550;
                      color: #fff;
                      border: 1px solid #3695ff;
                      opacity: 0.7;
                    "
                    :class="item.allocationState == 2 ? 'grayness' : ''"
                    :disabled="item.allocationState == 2 ? true : false"
                    @click="noProcessing(item.id)"
                  >
                    暂不处理
                  </el-button> -->
                </div>
                <div v-else> 
                  <el-button
                  
                    class="colorBtn1"
                    size="small"
                  >
                    已纳入意向
                  </el-button>
                </div>
              </div>
            </div>
            <div>
              <div class="companyContent-one">
                <div class="one-A">
                  <span>成立日期：{{ item.registerDate }}</span>
                </div>
                <div class="one-B">
                  <el-tooltip
                    effect="dark"
                    :visible-arrow="false"
                    placement="top"
                    :content="item.registeredCapital"
                    popper-class="tag-popover"
                  >
                    <span class="value">注册资本：{{ item.registeredCapital }}</span>
                  </el-tooltip>
                  <!--                   <div v-if="!item.havaAttention">
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="item.recommendationReasonDetail"
                      placement="top-start"
                      popper-class="too-recommendationReason"
                    >
                      <span> 推荐理由：{{ item.recommendationReasonDetail }} </span>
                    </el-tooltip>
                  </div> -->
                </div>
                <div class="one-C">
                  <span>所在地区：</span>
                  <el-tooltip
                    effect="dark"
                    :visible-arrow="false"
                    placement="top"
                    popper-class="tag-popover"
                    :content="
                      item.city == item.province
                        ? item.province + item.area
                        : item.province + item.city + item.area
                    "
                  >
                    <span class="value">{{ item.province
                    }}<span v-if="item.city !== item.province">{{
                      item.city
                    }}</span>{{ item.area }}</span>
                  </el-tooltip>
                </div>
              </div>
              <div class="companyContent-two">
                <div class="two-A">
                  <span class="key">所在环节：</span>
                  <el-tooltip
                    effect="dark"
                    :visible-arrow="false"
                    placement="top-start"
                    :content="item.linkPlace"
                    popper-class="tag-popover"
                  >
                    <span class="value">{{ item.linkPlace }}</span>
                  </el-tooltip>
                </div>
                <div class="two-B">
                  <el-tooltip
                    effect="dark"
                    :visible-arrow="false"
                    placement="top"
                    :content="item.modelTypeStr"
                    popper-class="tag-popover"
                  >
                    <span>推荐招商模式：{{ item.modelTypeStr }}</span>
                  </el-tooltip>
                </div>
                <div class="two-C">
                  <span>推荐日期：{{ item.recommendedDate }}</span>
                </div>
              </div>
              <div class="companyContent-three">
                <div class="telephone">
                  <el-tooltip
                    effect="dark"
                    :visible-arrow="false"
                    placement="top"
                    :content="item.enterpriseContact"
                    popper-class="tag-popover"
                  >
                    <div class="wite">
                      联系方式：{{ item.enterpriseContact }}
                    </div>
                  </el-tooltip>
                </div>
                <div class="two-C city">
                  <!--  -->
                  <span>推送地区：{{ item.recommendRegionName }}</span>
                </div>
                <!-- <div
                  :style="{ 'width': item.modelTypeStr.includes('舆情招商') ? '30%' : '80%' }"
                  class="three-A"
                >
                  <span>推荐理由：</span>
                  <el-tooltip
                    effect="dark"
                    :visible-arrow="false"
                    placement="top-start"
                    :content="item.recommendationReasonDetail"
                    popper-class="tag-popover"
                  >
                    <span class="value">{{ item.recommendationReasonDetail }}</span>
                  </el-tooltip>
                </div>
                <div
                  v-if="item.modelTypeStr.includes('舆情招商')"
                  class="three-B"
                >
                  <span>相关资讯：<a
                    v-if="item.information"
                    :href="item.information.url"
                    class="value"
                    target="_blank"
                  >{{
                    item.information.title }}</a></span>
                </div> -->
              </div>
            </div>
          </div>
        </div>

        <div class="ye company">
          <el-pagination
            small
            :current-page.sync="formInline.pageNum"
            :page-sizes="[5, 10, 20, 50]"
            :page-size.sync="formInline.pageSize"
            :total="+total"
            layout="total,prev, pager, next"
            @size-change="formationList"
            @current-change="formationList"
          />
        </div>
      </div>
      <div
        v-else
        class="listNoData"
      >
        <NoData />
      </div>
      <!-- 资讯 -->
      <div
        v-show="isList == 2"
        v-if="informationList?.length > 0"
        class="tables"
      >
        <div
          v-loading="listloading2"
          element-loading-background="rgba(2, 28, 60, 0.8)"
        >
          <div
            v-for="(item, index) in informationList"
            :key="index"
            class="table-tableData2"
          >
            <div>
              <div>
                <el-popover
                  placement="top"
                  width="200"
                  trigger="hover"
                  :content="item.eventTitle"
                  :open-delay="500"
                  popper-class="tag-popover"
                >
                  <span
                    slot="reference"
                    class="title-a"
                    style="margin-left: 0.8rem; margin-top: 0.8rem"
                  >
                    <!-- <a
                      :href="item.newsUrl || 'https://www.baidu.com'"
                      target="_blank"
                    > -->
                    {{ item.eventTitle }}
                    <!-- </a> -->
                  </span>
                </el-popover>
              </div>
            </div>
            <div>
              <span
                class="content-a"
                style="margin-left: 0.8rem"
              >{{ item.eventContent }}
              </span>
            </div>
            <div
              class="companyContent-one"
              style="margin-bottom: 0.8rem"
            >
              <div class="one-D">
                发布日期：{{ item.publishDate }}
              </div>
              <div class="one-E">
                <!-- <el-popover
                  placement="top-end"
                  trigger="hover"
                  :open-delay="500"
                  style="color: #3370ff"
                  popper-class="tag-popover"
                > slot="reference"-->
                <div>
                  <span style="color: #fff; cursor: pointer">相关企业：
                    <span
                      style="color: #3370ff"
                      @click="nameparticulars(item)"
                    >{{ item.enterpriseName }}
                    </span>
                  </span>
                </div>
                <!-- </el-popover> -->
              </div>
            </div>
          </div>
        </div>

        <div class="ye views">
          <span
            style="
              text-align: center;
              font-size: 13px;
              margin-bottom: 8px;
              color: #fff;
            "
          >共{{ ' ' + informationtotals + ' ' }}条</span>
          <el-pagination
            small
            :current-page.sync="information.pageNum"
            :page-sizes="[5, 10, 20, 50]"
            :page-size.sync="information.pageSize"
            :total="+informationtotal"
            layout="prev, pager, next"
            @size-change="pageListToUser"
            @current-change="pageListToUser"
          />
          <div
            v-if="informationtotal >= 10000"
            style="
              text-align: center;
              margin-bottom: 8px;
              font-size: 13px;
              color: #fff;
            "
          >
            最多查看10000条数据
          </div>
        </div>
      </div>
      <div
        v-else
        class="listNoData"
      >
        <NoData />
      </div>
    </div>

    <div
      v-if="isecharts == true"
      class="echarts"
    >
      <RightComponentsTop3 :show-data="dataList.dateStatistics" />

      <!-- <RightComponentsTop3/> -->
      <!-- <div
        id="main"
        style="width: 100%; height: 18rem"
      /> -->
      <div class="rightContentLast">
        <Contents title="推荐企业产业环节分布">
          <div
            v-if="dataList"
            slot="main"
          >
            <RightComponentsMiddlePie
              :show-data="dataList.industrialLinkDistributionStatistics"
            />
          </div>

          <!-- <div
        id="distribution"
        style="width: 100%; height: 16rem; margin-top: -8%"
      /> -->
        </Contents>
      </div>
      <RightComponentsMiddle3
        :show-data="dataList.followUpStatistics"
        :total="followUpTotal"
        title="推荐企业跟进统计"
      />
      <!-- <div
        id="Statistics"
        style="width: 100%; height: 20rem"
      /> -->
    </div>
    <!--     <firmParticulars
      v-else
      :firm-particulars="firmParticulars"
      class="echartsfirm"
      :show-label-type="showLabelType"
      :row-i-d="rowID"
      :enterprise-i-d="enterpriseId"
      @close="close"
      @particularsSource="particularsSource"
    /> -->
    <AtlasDetailCompany
      v-else
      :child-id="enterpriseID"
      :cyl-id="chainId"
      :recommend-region-code="recommendRegionCode"
      :model-type="modelType"
      @closeDetail="close"
      @formationList="formationList"
    />
  </div>
</template>

<script>
import { getEnterpriseIconByType } from '@/utils/utils';
import RightComponentsTop3 from './rightComponents/top3/index.vue';
import RightComponentsMiddle3 from './rightComponents/middle3/index.vue';
import Contents from '@/views/echarts/large-screen/enterpriseCharts/components/index.vue';

import RightComponentsMiddlePie from './rightComponents/middlePie/index.vue';
import AtlasDetailCompany from '@/views/echarts/large-screen/companyDetail.vue';
import { filterData } from '@/utils/utils';
import * as echarts from 'echarts';
import { getPathId, formatDate } from '@/utils/utils';
import { investment_enterprisedownloadAPI } from '@/api/export';
import NoData from '@/views/overview/components/component/noData';

import {
  attractInvestmentInformationStatisticsAPI,
  listByTypeAPI,
  getByNameAPI,
  updatainclusionIntention,
  leaveAside,
  searchListAPI,
  getTypeNodeLinkPlaceAPI,
  informationListEventAPI,
  searchListAPI_V2,
} from '@/api/CattractInvestment';
export default {
  name: 'IntelligenceA',
  components: {
    //firmParticulars,
    AtlasDetailCompany,
    RightComponentsTop3,
    RightComponentsMiddle3,
    RightComponentsMiddlePie,
    Contents,
    NoData,
  },
  filters: {
    source(value) {
      if (value == true) {
        return '系统推荐';
      } else {
        return '关注企业';
      }
    },
  },
  props: {
    chainId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      modelType: '',
      tabList: [
        {
          name: '企业',
          id: 1,
        },
        {
          name: '资讯',
          id: 2,
        },
      ],
      link: '选择环节',
      isList: 1,
      information: {
        recommendDateType: '',
        newsThemeIds: [],
        pageNum: 1,
        pageSize: 5,
      },
      // 资讯列表
      informationList: [],
      // 资讯总数
      informationtotal: 0,
      informationtotals: 0,
      spread: [],
      showLabelType: null, // 企业状态
      optionsList: [], // 产业链数据
      searchCity: { 0: '-1', filesUrl: ['-1'], path: '-1' },
      optionProps: {
        lazy: true,
        checkStrictly: true, // 任意一级选中
        lazyLoad: this.lazyLoad,
      },
      formInline: {
        pageNum: 1,
        pageSize: 4,
        province: '',
        city: '',
        area: '',
        industrialLinkId: '',
        recommendSource: '',
        nodeid: [],
      },
      total: 0,
      listloading: false,
      listloading2: false,
      linkList: [
        {
          nodeName: '亲缘招商',
          id: 1,
        },
        {
          nodeName: '链式招商',
          id: 3,
        },
        {
          nodeName: '舆情招商',
          id: 5,
        },
        {
          nodeName: '人才招商',
          id: 7,
        },
        {
          nodeName: '资本招商',
          id: 8,
        },
        // {
        //   nodeName: "AI+招商",
        //   id: 6,
        // },
      ], //环节列表=>招商模式
      subjectList: [], //新闻主题列表
      tableData: {}, //列表
      dataList: {}, //图表数据
      firmParticulars: {}, //企业详情
      isecharts: true, //展示详情还是图谱
      rowID: '',
      chain: {},
      Chainnode: [],
      enterpriseID: '', //企业id
      tagsum: 1,
      followUpTotal: 0,
      recommendRegionCode: '',
    };
  },
  watch: {
    chainId: {
      handler() {
        this.getAllChainId();
      },
      immediate: true,
    },
  },
  async mounted() {
    //this.getscreenWidth()
    this.formationList();
    //this.getAllChainId();
    await this.drawChart();
    this.linkDistribution();
    this.followupStatistics();
    this.pageListToUser();
  },

  methods: {
    getIconByType(type) {
      return getEnterpriseIconByType(type, 'webp');
    },
    async exportFile() {
      if (Number(this.total) === 0) {
        return;
      }
      let data = {
        chainId: this.chainId, //产业链id
        // nodeIds: this.formInline.nodeid[0] == '' ? '' : this.formInline.nodeid,
        type:
          this.formInline.industrialLinkId == ''
            ? -1
            : this.formInline.industrialLinkId, //招商模式
        recommendDateType:
          this.formInline.recommendSource === ''
            ? null
            : this.formInline.recommendSource, //推荐时间
        pageSize: 5000,
        pageNum: 1,
      };
      try {
        this.xzloading = true;
        const res = await investment_enterprisedownloadAPI(data);
        if (res.msg) {
          return this.$message({
            message: res.msg,
            type: 'error',
            customClass: 'admin-tips-message',
            duration: 3000,
            showClose: true,
            center: false, // 是否居中
          });
        }
        let blob = new Blob([res], {
          type: 'text/csv,charset=UTF-8',
        });
        let objectUrl = URL.createObjectURL(blob);
        const fileName = `招商情报企业推荐列表.xlsx`;
        const downloadLink = document.createElement('a');
        downloadLink.href = objectUrl;
        downloadLink.download = fileName;
        downloadLink.click();
      } finally {
        this.xzloading = false;
      }
      // TODO
    },
    getscreenWidth() {
      var screenWidth = window.innerWidth;
      // console.log(screenWidth,'屏幕宽度');
      if (screenWidth >= 1920) {
        this.tagsum = 4;
        // console.log(4);
      } else if (screenWidth <= 1440) {
        this.tagsum = 1;
        // console.log(1);
      } else {
        this.tagsum = 2;
        // console.log(2);
      }
    },
    // tab
    cutTab(id) {
      this.isList = id;
    },
    Selection(name) {
      if (this.link == name) {
        //console.log('重复');
        (this.link = '选择环节'), (this.Chainnode = []);
        this.formInline.nodeid[0] = '';
        return;
      }
      //console.log(name,'name');
      this.link = name;
      if (name == '强链环节') {
        this.Chainnode = this.chain.strongChainList;
      } else if (name == '延链环节') {
        this.Chainnode = this.chain.extendedChainList;
      } else if (name == '补链环节') {
        this.Chainnode = this.chain.supplementaryChainList;
      }
      this.formInline.nodeid[0] = '';
    },
    screenformationList() {
      this.formInline.pageNum = 1;
      this.formationList();
    },
    // 关闭详情
    async close() {
      this.isecharts = true;
      await this.drawChart();
      this.linkDistribution();
      this.followupStatistics();
    },
    async nameparticulars(it) {
      const res = await getByNameAPI({
        enterpriseName: it.enterpriseName,
      });
      this.particulars(res.result, 1);
    },
    // 获取企业详情
    async particulars(row, type) {
      this.modelType = row.modelType;
      this.recommendRegionCode = row.recommendRegionCode || '';
      this.rowID = row.id; //线索id
      this.enterpriseID = row.enterpriseId;
      this.showLabelType = row.showLabelType; // 企业状态

      if (type == 1) {
        this.enterpriseID = row.id;
      } else {
        this.enterpriseID = row.enterpriseId;
      }
      //this.firmParticulars = res.result;
      this.isecharts = false;
    },
    /*     lazyLoad(node, resolve) {
          const { level, data = {} } = node;
          const { id = "" } = data;
          let params = { type: level + 1, parentId: id };
          let nodes = level === 0 ? [{ value: "-1", label: "全国" }] : [];
          if (level <= 2) {
            getCity(params)
              .then((res) => {
                res.forEach((city) => {
                  const { id, name } = city;
                  nodes.push({
                    value: name,
                    label: name,
                    id: id,
                    leaf: level >= 2,
                  });
                });
                resolve(nodes);
              })
              .catch(() => {});
          } else {
            resolve([]);
          }
        }, */
    // 资讯列表
    async pageListToUser() {
      // console.log('资讯列表--');

      let pageSize = 7;
      // if (this.information.pageNum == 1667) {
      //   pageSize = 4;
      // } else {
      //   pageSize = 6;
      // }
      var screenWidth = window.innerWidth;
      //console.log(screenWidth,'屏幕宽度');
      if (screenWidth >= 1920) {
        this.tagsum = 4;
        //console.log(4);
      } else if (screenWidth <= 1440) {
        this.tagsum = 1;
        //console.log(1);
      } else {
        this.tagsum = 2;
        //console.log(2);
      }
      try {
        this.listloading2 = true;
        let industryIdsList = [];
        industryIdsList[0] = this.chainId; //产业链id
        const res = await informationListEventAPI({
          publishingDateType: this.information.recommendDateType, //日期
          chainIds: industryIdsList,
          pageNum: this.information.pageNum,
          pageSize: this.information.pageSize,
        });
        this.informationList = res.result.records;
        this.informationtotals = res.result.total;
        this.informationtotal = res.result.total;
        if (res.result.total > 10000) {
          this.informationtotal = 10000;
        }
        this.informationList.forEach((item) => {
          if (item.publishDate) {
            item.publishDate = formatDate(
              'yyyy-MM-dd',
              new Date(+item.publishDate)
            );
          }
        });
      } catch (error) {
        //console.log(error);
      } finally {
        this.listloading2 = false;
      }
    },
    // 企业列表
    async formationList() {
      /*       // 解决：el-cascader当设置了checkStrictly:true属性时，可以选择任意一级的菜单。但是同时设置动态加载的时候。点击前面的radio按钮会出现一个暂无数据的面板
      const panelRefs = this.$refs.refHandle.$refs.panel;
      if (panelRefs.activePath.length !== 0) {
        panelRefs.activePath.forEach((item) => {
          if (item.children.length === 0) {
            panelRefs.lazyLoad(panelRefs.getCheckedNodes()[0]);
          }
        });
      }
      let province = panelRefs.row;
      let city = panelRefs.row;
      let area = panelRefs.row;
      if (this.searchCity.filesUrl[0] != -1) {
        if (this.searchCity.filesUrl.length == 1) {
          province = this.searchCity.filesUrl[0];
        } else if (this.searchCity.filesUrl.length == 2) {
          province = this.searchCity.filesUrl[0];
          city = this.searchCity.filesUrl[1];
        } else {
          province = this.searchCity.filesUrl[0];
          city = this.searchCity.filesUrl[1];
          area = this.searchCity.filesUrl[2];
        }
      } */
      try {
        this.listloading = true;
        this.spread = [];
        let data = {
          chainId: this.chainId, //产业链id
          // nodeIds:
          //   this.formInline.nodeid[0] == '' ? '' : this.formInline.nodeid,
          type:
            this.formInline.industrialLinkId == ''
              ? -1
              : this.formInline.industrialLinkId, //招商模式
          recommendDateType: this.formInline.recommendSource, //推荐时间
          //recommendSource: this.formInline.recommendSource, //企业来源
          //industrialLinkId: this.formInline.industrialLinkId, //产业环节id
          pageSize: this.formInline.pageSize,
          pageNum: this.formInline.pageNum,
        };
        filterData(data);
        const res = await searchListAPI_V2(data);
        this.tableData = res.result.records;
        this.tableData.map((item) => {
          if (item.modelTypeStr == null) {
            item.modelTypeStr = '';
          }
        });
        this.total = res.result.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.listloading = false;
      }
    },
    // 产业环节列表
    async getAllChainId() {
      if (!this.chainId) {
        return;
      }
      const res = await getTypeNodeLinkPlaceAPI({
        chainId: this.chainId,
      });
      const res2 = await listByTypeAPI({
        keywordType: 1,
      });
      this.chain = res.result;
      this.Selection('强链环节');
      // 产业环节列表
      //this.linkList = res.result;
      // 新闻主题词列表
      this.subjectList = res2.result;
    },
    // 纳入意向
    async intention(item) {
      this.isecharts = true;
      const res = await updatainclusionIntention({
       clueSource: 1,
      uniCode:item.unifiedSocialCreditCode,
      });
      if (res.code === 'SUCCESS') {
        const h = this.$createElement;
        this.$message({
          message: h('div', null, [
            h('span', { style: 'color:white' }, '纳入意向成功，该企业已进入'),
            h(
              'span',
              {
                style: 'color:#11CE66;cursor:pointer',
                on: {
                  click: () => {
                    this.skip();
                  },
                },
              },
              '产业招商-招商管理'
            ),
            h('span', { style: 'color:white' }, '中'),
          ]),
          type: 'success',
          duration: 5000,
          customClass: 'admin-tips-message',
        });
        this.formationList();
        await this.drawChart();
        this.linkDistribution();
        this.followupStatistics();
      }
    },
    skip() {
      this.$message.closeAll(); //关闭message弹窗
      this.$emit('skipishow');
    },
    // 暂不处理
    async noProcessing(id) {
      const res = await leaveAside({
        id: id,
      });
      if (res.code === 'SUCCESS') {
        this.$message({
          type: 'success',
          message: '处理成功',
          customClass: 'admin-tips-message',
          duration: 3000,
          showClose: true,
          center: false, // 是否居中
        });
        this.formationList();
        await this.drawChart();
        this.linkDistribution();
        this.followupStatistics();
      }
    },
    //推荐企业统计
    async drawChart() {
      let queryId = this.$route.query.id || getPathId() || null;

      const res = await attractInvestmentInformationStatisticsAPI({
        orgIndustryChainRelationId: queryId,
      });
      this.dataList = res.result;
      // console.log(this.dataList, 'this.dataList.dateStatistics');
      // // const dataY = Object.keys(this.dataList.dateStatistics);
      // const dataSeries = Object.values(this.dataList.dateStatistics);

      // //console.log(dataY,dataSeries);
      // // 基于准备好的dom，初始化echarts实例  这个和上面的main对应
      // let myChart = echarts.init(document.getElementById('main'));
      // // 指定图表的配置项和数据
      // let option = {
      //   tooltip: {
      //     trigger: 'item',
      //     backgroundColor: '#00112dbf', // 提示框浮层的背景颜色。
      //     borderColor: '#0066FF', // 提示框浮层的边框颜色。
      //     borderWidth: 1, // 提示框浮层的边框宽。

      //     axisPointer: {
      //       type: 'none', // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
      //       axis: 'auto', // 指示器的坐标轴。
      //       snap: true, // 坐标轴指示器是否自动吸附到点上
      //       label: {
      //         color: '#fff',
      //         show: false,
      //         backgroundColor: '#00112D',
      //       },
      //     },
      //     textStyle: {
      //       // 提示框浮层的文本样式。
      //       color: '#fff',
      //       fontStyle: 'normal',
      //       fontWeight: 'normal',
      //       fontFamily: 'sans-serif',
      //       fontSize: 14,
      //     },
      //   },
      //   title: {
      //     text: '推荐企业统计',
      //     left: '5%',
      //     top: '10%',
      //     textStyle: {
      //       color: '#ffffff',
      //       fontSize: 15,
      //     },
      //   },
      //   grid: {
      //     left: '20%',
      //     top: '30%', // grid布局设置适当调整避免X轴文字只能部分显示
      //     button: '20%',
      //   },
      //   xAxis: {
      //     type: 'value',
      //     show: true,
      //     // 不显示轴线
      //     axisLine: {
      //       show: false,
      //     },

      //     // 不显示刻度线
      //     axisTick: {
      //       show: false,
      //     },
      //     axisLabel: {
      //       show: false,
      //     },
      //     splitLine: {
      //       // 网格线为虚线
      //       show: false,
      //     },
      //     // axisLabel:{
      //     //     formatter:'{value}%'
      //     // }
      //   },
      //   yAxis: {
      //     type: 'category',
      //     inverse: true,
      //     splitLine: {
      //       show: false,
      //     },
      //     axisTick: {
      //       show: false,
      //     },
      //     axisLine: {
      //       show: false,
      //     },
      //     axisLabel: {
      //       interval: 0,
      //       color: 'rgba(255, 255, 255, 1)',
      //       fontSize: 11,
      //     },
      //     data: dataY,
      //   },
      //   series: [
      //     {
      //       type: 'bar',
      //       barWidth: 4,
      //       //zlevel: 2,
      //       z: 3,
      //       barGap: '-150%',
      //       offset: [8, 1],
      //       label: {
      //         show: true, //开启显示
      //         position: 'right', //在上方显示
      //         offset: [6, 1],
      //         textStyle: {
      //           //数值样式
      //           color: '#fff',
      //           fontSize: 10,
      //         },
      //       },
      //       itemStyle: {
      //         normal: {
      //           borderRadius: [0, 0, 4, 0],
      //           color: function (params) {
      //             let colorList = [
      //               new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      //                 { offset: 0, color: 'rgba(15, 55, 102, 0)' }, //柱图渐变色
      //                 { offset: 1, color: 'rgba(53, 125, 207, 1)' }, //柱图渐变色
      //               ]),
      //               new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      //                 { offset: 0, color: 'rgba(44, 61, 76, 0)' }, //柱图渐变色
      //                 { offset: 1, color: 'rgba(177, 156, 115, 1)' }, //柱图渐变色
      //               ]),
      //               new echarts.graphic.LinearGradient(0, 0, 1, 1, [
      //                 { offset: 0, color: 'rgba(34, 50, 90, 0)' }, //柱图渐变色
      //                 { offset: 1, color: 'rgba(136, 122, 202, 1)' }, //柱图渐变色
      //               ]),
      //             ];
      //             return colorList[params.dataIndex];
      //           },
      //         },
      //       },
      //       data: dataSeries,
      //     },
      //     {
      //       type: 'pictorialBar',
      //       data: dataSeries,
      //       left: 90,
      //       z: 4,
      //       symbol: 'rect',
      //       symbolRotate: 65,
      //       symbolSize: [8, 3],
      //       symbolPosition: 'end',
      //       symbolOffset: [4, 0],
      //       itemStyle: {
      //         color: function (params) {
      //           // 根据当前数据项的索引返回不同的颜色
      //           return params.data > 0
      //             ? 'rgba(255, 255, 255, 0.8)'
      //             : 'transparent';
      //         },
      //       },
      //     },
      //     {
      //       type: 'pictorialBar',
      //       data: dataSeries,
      //       z: 6,
      //       symbol: 'rect',
      //       symbolRotate: 80,
      //       symbolSize: [9, 6],
      //       symbolPosition: 'end',
      //       symbolOffset: [0, 0],
      //       itemStyle: {
      //         color: function (params) {
      //           // 根据当前数据项的索引返回不同的颜色
      //           var colors = ['#2f75b9', '#cdae8d', '#9995da'];
      //           return params.data > 0
      //             ? colors[params.dataIndex]
      //             : 'transparent';
      //         },
      //         opacity: 0.4,
      //       },
      //     },
      //   ],
      // };
      // // 使用刚指定的配置项和数据显示图表。
      // myChart.setOption(option);
    },
    // 推荐企业产业环节分布
    linkDistribution() {
      // const result = Object.entries(
      //   this.dataList.industrialLinkDistributionStatistics
      // ).map(([key, value]) => ({
      //   value,
      //   name: key,
      // }));
      // let myChart = echarts.init(document.getElementById('distribution'));
      // const option = {
      //   title: {
      //     text: '推荐企业产业环节分布',
      //     left: '5%',
      //     textStyle: {
      //       color: '#ffffff',
      //       fontSize: 15,
      //     },
      //   },
      //   tooltip: {
      //     trigger: 'item',
      //     backgroundColor: '#00112dbf', // 提示框浮层的背景颜色。
      //     borderColor: '#0066FF', // 提示框浮层的边框颜色。
      //     borderWidth: 1, // 提示框浮层的边框宽。
      //     axisPointer: {
      //       type: 'none', // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
      //       axis: 'auto', // 指示器的坐标轴。
      //       snap: true, // 坐标轴指示器是否自动吸附到点上
      //       label: {
      //         color: '#fff',
      //         show: false,
      //         backgroundColor: '#00112D',
      //       },
      //     },
      //     textStyle: {
      //       // 提示框浮层的文本样式。
      //       color: '#fff',
      //       fontStyle: 'normal',
      //       fontWeight: 'normal',
      //       fontFamily: 'sans-serif',
      //       fontSize: 14,
      //     },
      //   },
      //   legend: {
      //     // orient: "vertical",
      //     bottom: '10px',
      //     left: '40px',
      //     type: 'scroll',
      //     orient: 'horizontal',
      //     itemWidth: 10,
      //     itemHeight: 5,
      //     textStyle: {
      //       color: '#fff',
      //       opacity: 0.6,
      //     },
      //     pageTextStyle: {
      //       color: '#fff',
      //     },
      //   },
      //   series: [
      //     {
      //       // name: '产业环节分布',
      //       type: 'pie',
      //       radius: [0, 50],
      //       center: ['50%', '50%'],
      //       roseType: 'area',
      //       itemStyle: {
      //         borderRadius: 8,
      //       },
      //       label: {
      //         show: true,
      //         color: '#fff',
      //         formatter: function (params) {
      //           if (params.name.length > 6) {
      //             return params.name.slice(0, 6) + '...'; // 超过6个字符时，添加省略号
      //           }
      //           return params.name;
      //         },
      //       },
      //       data: result,
      //       /* [
      //   { value: 40, name: 'rose 1' },
      //   { value: 38, name: 'rose 2' },
      //   { value: 32, name: 'rose 3' },
      //   { value: 30, name: 'rose 4' },
      //   { value: 28, name: 'rose 5' },
      //   { value: 26, name: 'rose 6' },
      //   { value: 22, name: 'rose 7' },
      //   { value: 18, name: 'rose 8' }
      // ] */
      //     },
      //   ],
      // };
      // myChart.setOption(option);
    },
    // 推荐企业跟进统计
    followupStatistics() {
      // let myChart = echarts.init(document.getElementById('Statistics'));
      let total = 0;
      Object.entries(this.dataList.followUpStatistics).map(([key, value]) => {
        total = total + Number(value);
      });
      this.followUpTotal = total;
      // const option = {
      //   tooltip: {
      //     trigger: 'item',
      //     backgroundColor: '#00112dbf', // 提示框浮层的背景颜色。
      //     borderColor: '#0066FF', // 提示框浮层的边框颜色。
      //     borderWidth: 1, // 提示框浮层的边框宽。

      //     axisPointer: {
      //       type: 'none', // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
      //       axis: 'auto', // 指示器的坐标轴。
      //       snap: true, // 坐标轴指示器是否自动吸附到点上
      //       label: {
      //         color: '#fff',
      //         show: false,
      //         backgroundColor: '#00112D',
      //       },
      //     },
      //     textStyle: {
      //       // 提示框浮层的文本样式。
      //       color: '#fff',
      //       fontStyle: 'normal',
      //       fontWeight: 'normal',
      //       fontFamily: 'sans-serif',
      //       fontSize: 14,
      //     },
      //   },
      //   title: {
      //     text: '推荐企业跟进统计',
      //     left: '5%',
      //     textStyle: {
      //       color: '#ffffff',
      //       fontSize: 15,
      //     },
      //   },
      //   legend: {
      //     orient: 'vertical',
      //     itemWidth: 10,
      //     itemHeight: 5,
      //     top: '20%',
      //     right: '5%',
      //     textStyle: {
      //       color: '#fff',
      //       opacity: 0.6,
      //     },
      //   },
      //   series: [
      //     {
      //       type: 'pie',
      //       center: ['50%', '50%'],
      //       radius: '50%',
      //       avoidLabelOverlap: false,
      //       emphasis: {
      //         itemStyle: {
      //           shadowBlur: 10,
      //           shadowOffsetX: 0,
      //           shadowColor: 'rgba(0, 0, 0, 0.5)',
      //         },
      //       },
      //       label: {
      //         color: '#fff',
      //       },
      //       data: result,
      //     },
      //   ],
      // };
      // myChart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
@import './public.scss';
@import './intelligence.scss';

</style>
