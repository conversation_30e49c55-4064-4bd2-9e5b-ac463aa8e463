.menu.el-dropdown-menu {
  background-color: #00112d !important;
  border: #468ae79e 1px solid !important;

  .el-dropdown-menu__item {
    color: #fff;
    font-weight: 600 !important;

    &:hover {
      background-image: linear-gradient(to bottom right, #2484ef, #09217b);
      color: #fff;
    }
  }
}

.el-dropdown-link {
  color: #fff;
}

.mores {
  border: 1px solid #3370ff;
  color: #3370ff !important;
}

.bq {
  float: left;
  height: 20px;
  font-size: 12px;
  //opacity: 0.6;
  padding: 4px 9px;
  margin: 2px;
  margin-right: 4px;
  border-radius: 2px;
  color: #ffffffd4;
  line-height: 10px;
}

.title {
  height: 120px;
  width: 100%;
  margin-top: -5px;
  background: center/contain no-repeat url("~@/assets/screen/new/searchBg.webp") !important;
  background-size: 100% 100% !important;
  padding: 15px 20px;
  .tab {
    display: flex;
    width: 100%;
    border-bottom: 1px solid rgba(216, 216, 216, 0.17);

    .tab-button {
      width: 90px;
      height: 32px;
      color: rgba(255, 255, 255, 0.8);
      position: relative;
      cursor: pointer;
      padding: 0 10px;

      span {
        padding: 0 10px 0 25px;

        line-height: 32px;
        &::before {
          content: "";
          position: absolute;
          width: 16px;
          height: 16px;
          top: 8px;
          left: 10px;
          background-size: cover;
        }
      }
      .tab-button0 {
        &::before {
          background: url("~@/assets/screen/new/companyDefault.svg") center/contain no-repeat;
        }
      }
      .tab-button1 {
        &::before {
          background: url("~@/assets/screen/new/news.svg") center/contain no-repeat;
        }
      }
    }

    .pitch-on {
      color: #19cdff;
      &::before {
        content: "";
        position: absolute;
        width: 100%;
        height: 2px;
        background: linear-gradient(270deg, #16d9ff 0%, #0075ff 100%);
        bottom: 0;
        left: 0;
      }
      .tab-button0 {
        &::before {
          background: url("~@/assets/screen/new/company.svg") center/contain no-repeat;
        }
      }
      .tab-button1 {
        &::before {
          background: url("~@/assets/screen/new/viewsHeight.svg") center/contain no-repeat;
        }
      }
    }
  }
  .demo-form-inline,
  .demo-form-news {
    width: 100%;
    background: transparent !important;
    padding: 0 !important;
    box-shadow: 0 0 0 !important;
    border-radius: 0px !important;
  }
  .demo-form-news,
  .demo-form-company {
    width: 100%;
    height: 70px;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .el-form-item {
      margin-bottom: 0 !important;
      width: 30%;
      display: flex;
      justify-content: left;
      ::v-deep {
        .el-form-item__content {
          flex: 1;
          .el-select {
            width: 100%;
            input {
              width: 100%;
              background-color: rgba(72, 82, 115, 0.5);
            }
          }
        }
      }
    }
  }
  .demo-form-company {
    .el-form-item:last-child {
      width: 15% !important;
    }
  }
}
.tables {
  height: calc(100% - 105px) !important;
  .table-tableData {
    margin-top: 20px !important;
    cursor: pointer !important;
    &:hover {
      opacity: 0.8;
      background-image: linear-gradient(to right, #023c72 5%, rgba(1, 140, 254, 0) 100%),
        radial-gradient(
          circle at 0 50%,
          rgba(0, 165, 254, 0.54),
          rgba(0, 165, 254, 0.52) 20%,
          rgba(0, 165, 254, 0.3) 45%,
          rgba(0, 165, 254, 0.1) 75%,
          rgba(113, 161, 194, 0.04) 85%,
          rgba(113, 161, 194, 0.04) 100%
        ) !important;
    }
  }
}
::v-deep {
  .select-newsThemeIds {
    width: 14rem;

    .el-input--suffix .el-input__inner {
      width: 14rem;
    }
  }

  // 多选栏标签位置
  .el-tag.el-tag--info {
    margin-bottom: 0.5rem;
  }

  .el-input--suffix .el-input__inner {
    background-color: #031c3e;
    width: 11rem;
    height: 2rem;
    border: 1px #07366d;
    font-weight: 400;
  }

  .el-form-item__label {
    margin-left: 1rem;
    margin-top: 0.1rem;
  }

  // 选择器文字颜色
  .el-form-item__label {
    color: #fff;
  }

  // 选中文字的颜色
  .el-input--suffix .el-input__inner {
    color: #fff;
  }

  .el-input--suffix .el-input__inner {
    background-color: #031c3e;
    width: 9rem;
    height: 2rem;
    border: 1px #07366d;
    font-weight: 400;
  }

  .el-form-item__label {
    margin-left: 2rem;
    margin-top: 0.1rem;
  }
}

// 分页
::v-deep {
  .el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
    background-image: linear-gradient(to bottom right, #2484ef, #09217b) !important;
    color: #fff !important;
  }

  .el-tag.el-tag--info {
    border-color: #2d6fba;
    color: #fff;
    background: rgba(46, 113, 188, 0.2);
  }

  .el-pagination {
    background: #fff;
    width: auto !important;
    text-align: right;
    height: 50px;
    padding-top: 10px;
  }

  .el-pagination--small .btn-prev,
  .el-pagination--small .btn-next,
  .el-pagination--small .el-pager li,
  .el-pagination--small .el-pager li.btn-quicknext,
  .el-pagination--small .el-pager li.btn-quickprev,
  .el-pagination--small .el-pager li:last-child {
    background-color: transparent !important;
  }

  .el-pagination .btn-prev .el-icon,
  .el-pagination .btn-next .el-icon {
    font-size: 12px;
    color: #fff;
  }

  .el-pager li.active {
    color: #fff !important;
  }

  .el-pagination {
    background-color: transparent !important;
    position: unset !important;
    text-align: center !important;
  }

  .el-pagination__total {
    color: #fff;
  }

  .input-info .el-input .el-input__inner {
    margin-top: 0.4rem !important;
  }
}

::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px;
  /* 对应横向滚动条的宽度 */
  height: 0px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #324156;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  display: none;
}

.table-tableData {
  height: 120px;
  padding: 5px 16px;
  padding-left: 0;
}
.table-tableData {
  margin-top: 0.5rem;
  .grayness {
    background-color: #1c2634 !important;
    border: 0px solid #1b589d !important;
  }

  .tag {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 16px;
    .more {
      background-color: transparent;
    }

    span {
      font-size: 12px;
      background: rgba(71, 175, 255, 0.2);
      padding: 5px 9px;
      color: #47afff !important;
      margin-right: 4px;
      border-radius: 2px;
      color: #ffffffd4;
    }
  }

  .tag2 {
    overflow: hidden;
    margin-top: 0.5rem;
    margin-left: 1rem;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;

    .more {
      background-color: transparent;
    }

    span {
      float: left;
      height: 20px;
      font-size: 12px;
      background: #52abfb3d;
      opacity: 0.6;
      padding: 5px 9px;
      margin: 2px;
      margin-right: 4px;
      border-radius: 2px;
      color: #ffffffd4;
      line-height: 10px;
    }
  }

  // 第一行内容
}

.table-tableData2 {
  width: 100%;
  .title-a {
    font-size: 16px;
    display: block;
    font-weight: 500;
    white-space: nowrap;
    max-width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: auto 0;
    // cursor: pointer;
    line-height: 20px;
    padding: 0 16px 10px;
  }
  .content-a {
    font-size: 14px;
    display: block;
    white-space: wrap;
    max-width: 80%;
    margin: auto 0;
    line-height: 20px;
    padding: 0 16px 10px;
  }

  .grayness {
    background-color: #1c2634 !important;
    border: 0px solid #1b589d !important;
  }

  .tag2 {
    overflow: hidden;
    //margin-top: 0.2rem;
    margin-left: 1rem;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;

    .more {
      background-color: transparent;
    }

    span {
      float: left;
      height: 20px;
      font-size: 12px;
      //opacity: 0.6;
      padding: 5px 9px;
      margin: 2px;
      margin-right: 4px;
      border-radius: 2px;
      color: #ffffffd4;
      line-height: 10px;
    }
  }

  // 第一行内容
  .companyContent-one {
    padding: 0 32px 0px !important;
  }
}
.company.ye {
  margin-top: 1rem;
}
.ye {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  //position: absolute;
  left: 0;
  bottom: 1rem;
  right: 0;
  top: 0.5rem;
}

.enterpriseName {
  cursor: pointer;
}

.list-echarts {
  display: flex;
  height: 90vh;
  margin-top: 15px;
  justify-content: space-between;

  .echarts {
    width: 39rem;
    height: 100%;
    background: rgba(0, 24, 53, 0.8);
    padding: 16px;
  }
}
.wite {
  width: 90%;
    word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

.el-tooltip__popper {
  max-width: 400px;
  z-index: 199999 !important;
}

.too-recommendationReason {
  background: rgba(5, 53, 115, 0.9) !important;
  max-width: 28rem;
}

// 提示弹窗
.prompt-message {
  background: rgba(0, 102, 255, 0.1) !important;
  border: 1px solid #0051c9 !important;

  .el-message-box__title {
    color: #fff;
    background: url("~@/assets/attract/title-c.png") !important;
  }

  .el-message-box__status + .el-message-box__message {
    color: #fff;
  }

  .el-message-box__message p {
    color: #fff;
  }

  .el-button--small {
    background: rgba(0, 8, 28, 0.2);
    border: 1px solid #305f9a;
    color: #fff;
  }

  .el-button--small:hover {
    background: rgba(0, 8, 28, 0.2);
    border: 1px solid #305f9a;
    color: #fff;
  }

  .el-button--small:active {
    background: rgba(0, 8, 28, 0.2);
    border: 1px solid #305f9a;
    color: #fff;
  }

  .el-button--small:focus {
    background: rgba(0, 8, 28, 0.2);
    border: 1px solid #305f9a;
    color: #fff;
  }
}

.cascader-hand {
  // 背景颜色
  .el-cascader-menu__list {
    //background-color: #091e76 !important;
    // rgba(0, 17, 45, 1)
    // border: #468ae79e 1px solid !important;
  }

  .el-cascader-node__label {
    // color: #fff;
  }

  .cascader-hand .el-cascader-node__label.hover {
    // background-color: red;
  }
}

.demo-table-expand {
  font-size: 0;
}

.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
.rightContent {
  height: 33%;
}
.rightContentLast {
  height: 34%;
}
#middlePie {
  height: 15rem;
  width: 100%;
}

.listNoData {
  width: 100%;
  height: 100%;
}
