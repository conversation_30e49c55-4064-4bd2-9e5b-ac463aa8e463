

.form {
width: calc(100% - 39rem);
  padding: 0 35px 1rem 0.5rem;
  position: relative;

  .tables {
    height: calc(100% - 63px);
    overflow-x: hidden;
    padding: 0rem 1rem 0rem 1rem;
    background: rgba(0, 24, 53, 0.8);
    
  }

}
.table-tableData,.table-tableData2 {
  width: 97%;
  color: #fff;
  margin-top: 1rem;

  border-radius: 3px;
  border: 1px solid transparent;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, #02274e, #001530),
    radial-gradient(
      circle at 0 50%,
      rgba(0, 165, 254, 0.54),
      rgba(0, 165, 254, 0.52) 20%,
      rgba(0, 165, 254, 0.3) 45%,
      rgba(0, 165, 254, 0.1) 75%,
      rgba(113, 161, 194, 0.04) 85%,
      rgba(113, 161, 194, 0.04) 100%
    ) !important;
  box-sizing: content-box;
  // border-radius: 8px;
  cursor: default;


}

.ye {
  position: relative;
  //position: absolute;
  left: 0;
  bottom: 1rem;
  right: 0;
  top: 0.5rem;
}

.colorBtn,.colorBtn1,.colorBtn5,.colorBtn4{
  width: 98px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  cursor: pointer;
  // margin-left: 20px;
  background-color: transparent !important;
  // border-radius: 0.1875rem;
  background-size: cover;
  font-size: 14px;
  font-weight: normal;
  border: none !important;
  box-sizing: border-box !important;
  font-family: YouSheBiaoTiHei;
  // letter-spacing: 0em;
  // background-clip: padding-box, border-box;
  //   background-origin: padding-box, border-box;
    // box-sizing: content-box;
    // border: 1px solid transparent !important;
    color: white;
    span{
      color: #ffffff;
      line-height: 24px !important;
     }
}
.colorBtn{
  height: 24px !important;
  line-height: 24px !important;
  background-size: cover !important;
  background-image: url('https://static.idicc.cn/cdn/pangu/assets/attract/btn_bg1.png') !important;
    // background-image: linear-gradient(to right, rgba(0, 82, 248, 0.6) 70%, rgba(0, 181, 253, 0.8)), radial-gradient(circle at 0 50%, #007efa, rgba(0, 126, 250, 0.8) 20%, rgba(0, 126, 250, 0.5) 45%, white 100%) !important;
 &:hover{
   color: white !important;
 } 
}
.colorBtn4{
  height: 24px;
  line-height: 24px;
  border: none !important;
  background-size: cover !important;
  background-image: url('https://static.idicc.cn/cdn/pangu/assets/attract/btn_bg1.png') !important;
    // background-image: linear-gradient(to right, rgba(0, 126, 250, 1) 70%, rgba(0, 181, 253, 0.8)), radial-gradient(circle at 0 50%, #007efa, rgba(0, 126, 250, 0.8) 20%, rgba(0, 126, 250, 0.5) 45%, white 100%) !important;
    &:hover{
     color: white !important;
     } 
  
}
 
.colorBtn1{
  height: 24px;
  line-height: 24px;
  background-size: cover !important;
  border: none !important;
  background-image: url('https://static.idicc.cn/cdn/pangu/assets/attract/btn_bg0.png') !important;
  // background-image: linear-gradient(to right, #014779 30%,  #042450), radial-gradient(circle at 0 50%, #007efa, rgba(0, 126, 250, 0.8) 20%, rgba(0, 126, 250, 0.5) 45%, rgba(4, 36, 80, 0.3) 100%) !important;
  &:hover{
    color: white !important;
  } 
}
.conpanyTitle{
 display: flex;
  justify-content: space-between;
  align-items: center; 
  padding: 0 16px 0 0;
  height: 40px;
}
.companyName{
  font-size: 1rem;
  display: flex;
  cursor: pointer;
  align-items: center;
  .companytText{
    font-weight: 600;
  }
}
.companyContent-one,  .companyContent-two, .companyContent-three{
  width: 100%;
  padding-left:  70px;
  padding-top: 0.5rem;
  display: flex;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  .one-A {
  color: rgba(255, 255, 255, 0.8) !important;
  }
  .one-A,  .one-B ,  .one-C , .two-A,  .two-B , .two-C, .three-B,.three-A {
    white-space: nowrap;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
}
}

.companyContent-one {
  .one-A {
    width: 30%;
  }
  .one-B {
    width: 30%;
  }

  .one-C {
    width: 30%;
  }
  .one-D{
    width: 200px;
  }
  .one-E {
 flex:1
  }
 
}

.companyContent-two {

  .two-A {
    width: 30%;
  }
  .two-B {
    width: 30%;
  }
  .two-C {
    flex: 1;
 
  }
}

.companyContent-three{
  .telephone{
    width: 60%;
  }
  .city{
     width: 35%;
  }
  .three-A {
    width: 30%;
  }
  .three-B {
    width: 60%;

    .value {
      font-family: Abel-Regular, Abel;
      cursor: pointer;
      font-weight: 400;
      color: #3370ff;
    }
  }
}
.demo-form-inline {
  
 
}

.exportBtn {
  background: #1C91FF;
  color: rgb(255, 255, 255);
  border: 0px solid rgb(54, 149, 255);
  width: 95px;
  height: 32px;
  display: flex;
  align-items: center;
  margin: 0 auto;
  justify-content: center;
  .span {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
:hover.exportBtn{
  color: rgb(255, 255, 255);
  background: #1272CD !important;
}
:hover.exportBtn0{
   cursor: not-allowed;
}
.exportBtn0 {
  background: #6B6D71 !important;
  color: #C4C5C6 !important;
  border: none !important;
  width: 95px;
  height: 32px;
  display: flex;
  align-items: center;
  margin: 0 auto;
  justify-content: center;
  .span {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}



.table-tableData.tableActive,
.table-tableData2.tableActive{
  background-image: linear-gradient(to right, #023c72 5%, rgba(1, 140, 254, 0) 100%),
    radial-gradient(
      circle at 0 50%,
      rgba(0, 165, 254, 0.54),
      rgba(0, 165, 254, 0.52) 20%,
      rgba(0, 165, 254, 0.3) 45%,
      rgba(0, 165, 254, 0.1) 75%,
      rgba(113, 161, 194, 0.04) 85%,
      rgba(113, 161, 194, 0.04) 100%
    ) !important;
}
::v-deep {
  .el-pagination{
    ul.el-pager {
      li.active{
        background: linear-gradient(180deg, #1CCEFF 0%, rgba(34, 69, 243, 0.88) 100%) !important;
        border-radius: 50% !important;
       }
    }
  }
}

.iconImg {
  position: relative;
  width: 24px;
  height: 24px;
  margin-right: 18px;
  margin-left: 28px;
}
.exImg {
  display: block;
  width: 14px;
  height: 15px;
  margin-right: 8px;
}
.exTxt {
  line-height: 24px;
}