<template>
  <!-- 招商管理 -->
  <div class="list-echarts">
    <div class="form">
      <el-form
        :inline="true"
        :model="formInline"
        class="demo-form-inline"
      >
        <el-form-item label="企业来源">
          <el-select
            v-model="formInline.clueSource"
            popper-class="select-hand"
            clearable
            @change="screenPageList"
          >
            <el-option
              v-for="(item, index) in managerList[0]?.paramValueList"
              :key="index"
              :label="item.key"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="当前进度">
          <el-select
            v-model="formInline.clueDealState"
            clearable
            popper-class="select-hand"
            @change="screenPageList"
          >
            <el-option
              v-for="(item, index) in managerList[2]?.paramValueList"
              :key="index"
              :label="item.key"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="跟进人">
          <el-select
            v-model="formInline.followers"
            clearable
            popper-class="select-hand"
            @change="screenPageList"
          >
            <el-option
              v-for="(item, index) in managerList[1]?.paramValueList"
              :key="index"
              :label="item.key"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-tooltip
            class="item"
            effect="dark"
            content="默认导出前5000条数据"
            placement="bottom"
          >
            <el-button
              :class="[Number(total) === 0 ? 'exportBtn0' : 'exportBtn']"
              :disabled="Number(total) === 0"
              @click="exportFile"
            >
              <div class="span">
                <img
                  v-if="Number(total) === 0"
                  src="https://static.idicc.cn/cdn/pangu/icon_ex_0.svg"
                  alt=""
                  class="exImg"
                >
                <img
                  v-else
                  src="https://static.idicc.cn/cdn/pangu/icon_ex_1.svg"
                  alt=""
                  class="exImg"
                >
                <span class="exTxt">导出</span>
              </div>
            </el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>

      <div
        v-if="tableData?.length > 0"
        class="tables"
      >
        <div
          v-loading="listloading"
          element-loading-background="rgba(2, 28, 60, 0.8)"
        >
          <div
            v-for="(item, index) in tableData"
            :key="index"
            class="table-tableData"
            :class="{ tableActive: item.id == rowID }"
            @click="particulars(item)"
          >
            <div class="conpanyTitle">
              <div class="conpanyTitleLeft">
                <div
                  class="iconImg"
                  :style="{
                    backgroundImage: `url(${getListIcon(
                      item?.enterpriseLabelNames?.[0] || '其他'
                    )})`,
                  }"
                />

                <span class="companytText">{{ item.enterpriseName }}</span>

                <span class="tagView2">
                  <span
                    v-if="item.clueDealState == 2"
                    class="enterpriseTag fail"
                  >
                    签约失败
                  </span>
                  <span
                    v-if="item.clueDealState == 1"
                    class="enterpriseTag success"
                  >
                    签约成功
                  </span>
                  <span
                    v-if="item.clueDealState != null && item.clueDealState == 0"
                    class="enterpriseTag ing"
                  >
                    跟进中
                  </span>
                  <span
                    v-if="item.isNotFollowMonth"
                    class="errorView"
                  >
                    <i class="el-icon-warning-outline" />
                    <span class="txt">近一个月未跟进</span>
                  </span>
                </span>
              </div>
              <div class="conpanyTitleRight">
                <el-popover
                  placement="bottom"
                  width="450"
                  trigger="click"
                  popper-class="popover_tip dialog_tip"
                >
                  <div class="popover_tip_content">
                    <div class="popover_tip_header">
                      指派线索
                    </div>
                    <el-form
                      ref="forms"
                      label-position="top"
                      label-width="100px"
                      :model="Interview"
                      hide-required-asterisk
                    >
                      <el-form-item label="招商经理:">
                        <!--  prop="radio"
                        :rules="[
                          {
                            required: true,
                            message: '招商经理不能为空',
                            trigger: 'blur',
                          },
                        ]" -->
                        <el-select
                          v-model="radio"
                          clearable
                          popper-class="select-hand"
                        >
                          <el-option
                            v-for="(ite, ind) in managerList2"
                            :key="ind"
                            :label="ite.key"
                            :value="ite.value"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="线索流转备注:">
                        <!--  
                       prop="remark"
                      :rules="[
                          {
                            required: true,
                            message: '线索流转备注不能为空',
                            trigger: 'blur',
                          },
                        ]" -->
                        <el-input
                          v-model="remark"
                          type="textarea"
                          maxlength="200"
                          show-word-limit
                          :autosize="{ minRows: 3, maxRows: 8 }"
                        />
                      </el-form-item>
                    </el-form>
                    <el-button
                      type="primary"
                      size="mini"
                      @click="preserve(item)"
                    >
                      保存
                    </el-button>
                  </div>
                  <el-button
                    slot="reference"
                    size="small"
                    :disabled="item.clueDealState !== '0'"
                    :class="[
                      item.clueDealState !== '0' ? 'colorBtn1' : 'colorBtn',
                    ]"
                    @click.stop="intention()"
                  >
                    <span>线索指派 </span>
                  </el-button>
                </el-popover>
                <el-button
                  slot="reference"
                  size="small"
                  :disabled="item.entrustOrNot"
                  :class="[item.entrustOrNot ? 'colorBtn1' : 'colorBtn']"
                  @click.stop="empty(item)"
                >
                  <span>
                    {{ !item?.entrustOrNot ? "委托招商" : "已委托" }}</span>
                </el-button>
              </div>
            </div>
            <div>
              <div class="companyContent-one">
                <div class="one-A">
                  <span style="color: #fff">跟进人： {{ item?.beAssignPerson || "--" }}
                  </span>
                </div>
                <div class="one-B">
                  <span>纳入意向日期：{{ item?.intentionDate || "--" }}</span>
                  <!--   <span v-else>推荐日期：{{ item.recommendedDate }}</span> -->
                </div>
                <div class="one-C">
                  <span>纳入意向人：{{ item?.intentionPerson || "--" }}</span>
                </div>
              </div>
              <div class="companyContent-two">
                <div class="two-A">
                  <span>企业来源：
                    {{
                      item.clueSource === "2" ? "自行添加" : "系统推荐"
                    }}</span>
                </div>
                <div class="two-B">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="item.regionName || '--'"
                    placement="top-start"
                    popper-class="tag-popover"
                  >
                    <span>所在地区：{{ item.regionName || "--" }} </span>
                  </el-tooltip>
                </div>
                <div class="two-C">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="item.chainNames || '--'"
                    placement="top-start"
                    popper-class="tag-popover"
                  >
                    <span> 所属产业链：{{ item.chainNames || "--" }}</span>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="ye">
          <el-pagination
            small
            :current-page.sync="formInline.pageNum"
            :page-sizes="[5, 10, 20, 50]"
            :page-size.sync="formInline.pageSize"
            :total="+total"
            layout="total,prev, pager, next"
            @size-change="pageList"
            @current-change="pageList"
          />
        </div>
      </div>
      <div
        v-else
        class="listNoData"
      >
        <NoData />
      </div>
      <!--       <div class="ye">
        <el-pagination
          small
          :current-page.sync="formInline.pageNum"
          :page-sizes="[5, 10, 20, 50]"
          :page-size.sync="formInline.pageSize"
          :total="+total"
          layout="total,prev, pager, next"
          @size-change="pageList"
          @current-change="pageList"
        />
      </div> -->
    </div>

    <div
      v-if="isecharts == true"
      class="echarts"
    >
      <div class="rightContentLast">
        <Contents title="意向企业产业链分布">
          <div slot="main">
            <RightComponentsMiddlePie :show-data="dataList.chainGroupCount" />
          </div>
        </Contents>
      </div>
      <RightComponentsMiddle4
        :show-data="dataList.signStatus"
        title="自行跟进状态统计"
      />

      <div class="rightContentLast">
        <Contents title="跟进人员排行榜">
          <!-- <div  slot="main"> -->
          <div
            v-if="!dataList?.personCount?.length > 0"
            slot="main"
            class="noDataSelf"
          >
            <NoData />
          </div>
          <div
            v-else
            id="distribution"
            slot="main"
          />
          <!-- </div> -->
        </Contents>
      </div>
    </div>
    <div
      v-else
      class="echarts2"
    >
      <AtlasDetailCompany
        v-if="
          selectedRow &&
            (selectedRow.clueSource === '1' ||
              (selectedRow.enterpriseId && selectedRow.enterpriseId !== ''))
        "
        :child-id="enterpriseID"
        :recommend-region-code="recommendRegionCode"
        :row-i-d="rowID"
        :isinterview="true"
        :selected-row="selectedRow"
        @closeDetail="close"
      />

      <personalAdd
        v-else
        :selected-row="selectedRow"
        @closeDetail="close"
      />
    </div>

    <div
      v-if="showDialog"
      class="dialogBg"
    >
      <el-dialog
        :visible="showDialog"
        width="400px"
        top="20%"
        :modal-append-to-body="false"
        custom-class=" dialog_tip"
        @close="showDialog = false"
      >
        <span
          slot="title"
          class="dialog-header"
        >提示 </span>
        <div class="dialog_tip_content">
          web端不支持委托招商，请前往“哒达招商”APP或小程序哒达助招模块委托
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getListIcon } from "@/utils/utils";
import RightComponentsMiddlePie from "./rightComponents/middlePie1/index.vue";
import RightComponentsMiddle4 from "./rightComponents/middle4/index.vue";
import Contents from "@/views/echarts/large-screen/enterpriseCharts/components/index.vue";
import NoData from "@/views/overview/components/component/noData";
import personalAdd from "./personalAdd/index.vue";
import AtlasDetailCompany from "@/views/echarts/large-screen/companyDetail.vue";
import * as echarts from "echarts";
import {
  newListOrgUserListAPI, //招商经理
  newPageListAPI, //列表
  newListAllDeptAPI, //查询
  newAssignmentClueAPI, //指派
} from "@/api/CattractInvestment";
import { getDataReport, pageListExport } from "@/api/weeklyReport";

import { getPathId } from "@/utils/utils";
// import Bottom from '@/views/echarts/large-screen/enterpriseCharts/bottom.vue';

export default {
  name: "IntelligenceB",
  components: {
    AtlasDetailCompany,
    personalAdd,
    RightComponentsMiddle4,
    Contents,
    RightComponentsMiddlePie,
    NoData,
  },
  data() {
    return {
      graphic: [
        {
          type: "rect",
          left: "center",
          top: "middle",
          shape: {
            width: 160,
            height: 70,
            radius: 20,
          },
          style: {
            fill: "#00112c",
            stroke: "#003485",
          },
          z: 10,
        },
        {
          type: "text",
          left: "center",
          top: "middle",
          style: {
            text: "暂无数据",
            textAlign: "center",
            textVerticalAlign: "middle",
            fill: "#fff",
            fontSize: 18,
          },
          z: 10,
        },
      ],
      spread: [],
      showLabelType: null, // 企业状态
      Interview: {
        name: "",
        tip: "",
      },
      formInline: {
        clueDealState: "",
        followers: "",
        recommendDateType: "",
        clueSource: "",
        pageNum: 1,
        pageSize: 5,
      },
      managerList: {},
      managerList2: [],
      listloading: false,
      visible: false,
      // 委托成功弹层
      centerDialogVisible: false,
      // 选择指派线索人
      radio: "",
      remark: "",
      tableData: {},
      total: 0,
      dataList: {},
      firmParticulars: {}, //企业详情
      isecharts: true, //展示详情还是图谱
      rowID: "", //招商线索id
      enterpriseID: "", //企业id
      zsLoading: false,
      wtLoading: false,
      showDialog: false,
      assignmentStateTotal: 0,
      showChaerts: false,
      recommendRegionCode: "",
      followUpTotal: "",
      getListIcon: getListIcon || null, // 将导入的 getListIcon 添加到 data 中
      selectedClueSource: "1", // 添加默认值来表示当前选择企业的来源
      selectedRow: null, // 添加用于保存当前选择行的完整数据
    };
  },
  async mounted() {
    this.pageList();
    this.listAllChildDeptUsersAPI();
    this.screenlistAllChildDeptUsersAPI();
    await this.drawChart();
    // 确保在有数据后再执行图表渲染
    if (this.dataList && Object.keys(this.dataList).length > 0) {
      this.linkDistribution();
      this.followupStatistics();
    }
  },
  created() {},
  methods: {
    async exportFile() {
      if (Number(this.total) === 0) {
        return;
      }
      // let queryId = this.$route.query.id || getPathId() || null;
      // let data = {
      //   orgIndustryChainRelationId: queryId,
      //   //recommendDateType: this.formInline.recommendDateType,
      //   // clueSource: this.formInline.clueSource,
      //   // clueDealState: this.formInline.clueDealState,
      //   // followers: this.formInline.followers,
      //   pageSize: 5000,
      //   pageNum: 1,
      // };
      try {
        this.xzloading = true;
        const res = await pageListExport();
        if (res.code === "SUCCESS") {
          let objectUrl = res?.result;
          window.open(objectUrl);
        }
      } finally {
        this.xzloading = false;
      }
    },
    // 保存指派人
    async preserve(row) {
      if (this.wtLoading == true) {
        return;
      }

      if (this.radio == "") {
        return this.$message({
          message: "招商经理为必填",
          type: "error",
          customClass: "admin-tips-message",
          duration: 3000,
          showClose: true,
          center: false, // 是否居中
        });

        // .error('招商经理为必填');
      } else if (this.remark == "") {
        return this.$message({
          message: "线索流转备注必填",
          type: "error",
          customClass: "admin-tips-message",
          duration: 3000,
          showClose: true,
          center: false, // 是否居中
        });
      }
      let assignUserName = this.managerList2.find(
        (e) => e.value === this.radio
      )?.key;
      try {
        this.wtLoading = true;
        let data = {
          assignUserId: this.radio,
          assignUserName: assignUserName,
          clueId: row.id,
          remark: this.remark,
        };
        await newAssignmentClueAPI(data);
        // row.clueState == 1
        //   ? await assignOrReassignAPI(data)
        //   : await assignmentClueAPI(data);
        this.$message({
          message: "线索指派成功",
          type: "success",
          customClass: "admin-tips-message",
          duration: 3000,
          showClose: true,
          center: false, // 是否居中
        });
        document.body.click();
        this.pageList();
        await this.drawChart();
        this.linkDistribution();
        this.followupStatistics();
      } finally {
        setTimeout(() => {
          this.wtLoading = false;
        }, 1000);
      }
    },

    // 点击指派线索
    intention() {
      this.isecharts = true;
      this.radio = "";
      this.remark = "";
    },

    empty(e) {
      localStorage.setItem("investmentCompany", JSON.stringify(e));
      // window.$wujie?.bus.$emit('newRouter', { path: '/pangu/attract/entrust' });
      const baseURL = process.env.VUE_APP_PORT_URL;

      let newPath = `${baseURL}#/attract/entrust`;
      window.open(newPath);
      // tiaozhuan
      // if (!e.entrustOrNot) {
      //   this.showDialog = true;
      // } else {
      //   this.Interview.record = '';
      // }
    },
    screenPageList() {
      this.formInline.pageNum = 1;
      this.pageList();
    },
    async close() {
      this.isecharts = true;
      await this.drawChart();
      // 确保在有数据后再执行图表渲染
      if (this.dataList && Object.keys(this.dataList).length > 0) {
        this.linkDistribution();
        this.followupStatistics();
      }
    },
    // 获取企业详情
    async particulars(row) {
      this.selectedRow = row; // 保存当前选中的完整行数据
      this.selectedClueSource = row.clueSource; // 记录当前选中企业的来源
      if (
        row.clueSource === "1" ||
        (row.enterpriseId && row.enterpriseId !== "")
      ) {
        this.recommendRegionCode = row.recommendRegionCode || "";
        this.rowID = row.id; //线索id
        this.enterpriseID = row.enterpriseId;
        this.isecharts = false;
      } else {
        // 自行添加的企业
        this.selectedRow.isInvestClue = true;
        this.rowID = row.id; // 记录当前行ID
        this.isecharts = false;
      }
    },
    //  获取筛选列表字段
    async listAllChildDeptUsersAPI() {
      const res = await newListAllDeptAPI();
      this.managerList = res.result;
    },
    async screenlistAllChildDeptUsersAPI() {
      //const res = await listAllChildDeptUsersAPI();
      const res = await newListOrgUserListAPI();
      this.managerList2 = res.result; //跟进人
    },
    // 获取列表
    async pageList() {
      try {
        this.spread = [];
        this.listloading = true;
        let queryId = this.$route.query.id || getPathId() || null;

        const res = await newPageListAPI({
          clueDealState:
            (this.formInline.clueDealState !== "" && [
              this.formInline.clueDealState,
            ]) ||
            [],
          clueSource:
            (this.formInline.clueSource !== "" && [
              this.formInline.clueSource,
            ]) ||
            [],
          entrustOrNot: [],
          followers:
            (this.formInline.followers !== "" && [this.formInline.followers]) ||
            [],
          intentionDate: [],
          pageSize: this.formInline.pageSize,
          pageNum: this.formInline.pageNum,
        });
        this.tableData = res.result.records;
        /*         this.tableData.forEach((item)=>{
          item.clueSource=1
        }) */
        this.total = res.result.total;
      } finally {
        this.listloading = false;
      }
    },
    /*     intention(row){
      this.$emit('assign',row.enterpriseName,row.id,row.followUpPerson)
    }, */
    getList() {
      this.pageList();
    },
    // 委托招商
    async noProcessing(row) {
      this.$emit("entrust", row.enterpriseName, row.id);
    },

    //推荐线索统计
    async drawChart() {
      // let queryId = this.$route.query.id || getPathId() || null;

      try {
        const res = await getDataReport({});
        this.dataList = res.result || {}; // 确保dataList至少是一个空对象
        // console.log(this.dataList, "this.dataList");
        // 确保数据存在后再处理
        if (res.result && res.result.briefing) {
          const briefing = res.result.briefing;
          const stateSign = Number(briefing.stateSign) || 0;
          const stateFollow = Number(briefing.stateFollow) || 0;
          const stateFail = Number(briefing.stateFail) || 0;

          this.dataList.signStatus = {
            total: stateSign + stateFollow + stateFail,
            state: {
              签约成功: stateSign,
              跟进中: stateFollow,
              签约失败: stateFail,
            },
          };
        } else {
          this.dataList.signStatus = {
            total: 0,
            state: {
              签约成功: 0,
              跟进中: 0,
              签约失败: 0,
            },
          };
        }
      } catch (error) {
        // console.error('获取数据报告失败:', error);
        this.dataList = {
          signStatus: {
            total: 0,
            state: {
              签约成功: 0,
              跟进中: 0,
              签约失败: 0,
            },
          },
        };
      }
    },
    // 跟进人员排行榜
    linkDistribution() {
      // 确保数据存在
      if (
        !this.dataList ||
        !this.dataList.personCount ||
        !this.dataList.personCount.length
      ) {
        // 如果没有数据，不执行图表渲染
        return;
      }

      let barChartData = this.dataList.personCount.reverse();
      // 准备数据
      const names = barChartData.map((item) => item.name || "未知");
      const successData = barChartData.map((item) => item.stateSign || 0);
      const processingData = barChartData.map((item) => item.stateFollow || 0);
      const failedData = barChartData.map((item) => item.stateFail || 0);

      // 配置项
      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          // 添加echarts图例并设置在右上角
          data: ["签约成功", "跟进中", "签约失败"],
          right: 10,
          top: 0,
          itemWidth: 12,
          itemHeight: 12,
          textStyle: {
            color: "#C9D2DD",
            fontSize: 12,
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "40px", // 为顶部图例留出空间
          containLabel: true,
        },
        xAxis: [
          {
            type: "value",

            axisTick: {
              show: false,
            },
            splitLine: {
              show: true, // 显示网格线
              lineStyle: {
                color: "rgba(255, 255, 255, 0.1)", // 网格线颜色
                width: 1,
                type: "dashed",
              },
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#C9D2DD",
                opacity: 0.2,
              },
            },
            axisLabel: {
              interval: 0,
              rotate: 30,
              textStyle: {
                color: "#C9D2DD",
                fontSize: "12",
                itemSize: "",
                marginLeft: "2px",
              },
            },
          },
        ],
        yAxis: [
          {
            type: "category",
            data: names,
            axisTick: {
              show: false,
            },
            axisLine: {
              lineStyle: {
                color: "#2f3750",
                fontSize: "20px",
              },
            },
            axisLabel: {
              interval: 0,
              rotate: 0,
              textStyle: {
                color: "#C9D2DD",
                fontSize: "12",
                itemSize: "",
                marginLeft: "2px",
              },
            },
          },
        ],
        series: [
          {
            name: "签约成功",
            type: "bar",
            stack: "Total",
            barWidth: 8,
            barGap: "0%",
            barCategoryGap: "50%",
            emphasis: {
              focus: "series",
            },
            itemStyle: {
              color: "#6CCE26",
              // 设置为直角
              borderRadius: [0, 0, 0, 0],
            },
            data: successData,
          },
          {
            name: "跟进中",
            type: "bar",
            stack: "Total",
            barWidth: 8,
            emphasis: {
              focus: "series",
            },
            itemStyle: {
              color: "#3370FF",
              // 设置为直角
              borderRadius: [0, 0, 0, 0],
            },
            data: processingData,
          },
          {
            name: "签约失败",
            type: "bar",
            stack: "Total",
            barWidth: 8,
            emphasis: {
              focus: "series",
            },
            itemStyle: {
              color: "#E04848",
              // 为红色柱子设置右侧圆角
              borderRadius: [0, 0, 0, 0],
            },
            data: failedData,
          },
        ],
      };

      // 确保DOM元素存在后再初始化图表
      const chartElement = document.getElementById("distribution");
      if (chartElement) {
        let myChart = echarts.init(chartElement);
        myChart.clear();
        myChart.setOption(option);
      }
    },
    // 线索状态统计
    followupStatistics() {
      // 确保数据存在
      if (!this.dataList || !this.dataList.assignmentStateStatistics) {
        this.assignmentStateTotal = 0;
        return;
      }

      let total = 0;
      Object.entries(this.dataList.assignmentStateStatistics).forEach(
        ([key, value]) => {
          total = total + (Number(value) || 0);
        }
      );
      this.assignmentStateTotal = total;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./administration.scss";
@import "./public.scss";
</style>
<style lang="scss">
@import "./dialog_tip.scss";

.noDataSelf {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;

  > div {
    display: none;
  }

  .noData {
    margin-top: 10vh;
  }
}
</style>
