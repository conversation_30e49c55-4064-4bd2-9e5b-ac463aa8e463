<template>
  <div
    v-if="showCharts"
    id="middlePie"
  />
  <div
    v-else
    class="noDataSelf"
  >
    <NoData />
  </div>
</template>

<script>
import * as echarts from 'echarts';
import NoData from '@/views/overview/components/component/noData'

export default {
  name: 'ModuleMiddlePie',
  components: {
    NoData
  },
  props: {
    showData: {
      type: Object,
      default:  ()=>{},
    },
  },
  data() {
    return {
      animate: true,
      list: [],
      showCharts: false,
      chart: null // 添加一个变量来存储 echarts 实例
    };
  },
  computed: {},
  watch: {
    showData(newValue) {
      // console.log(newValue,'newValue')
      if (newValue) {
        this.industrySector(newValue)
      }
    }
  },
  beforeCreate() {},
  mounted() {
    //this.industrySector();
  },
  created() {},
  beforeDestroy() {
    // 在组件销毁前销毁图表实例，避免内存泄漏
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.resizeHandler)
  },

  methods: {
    // 处理窗口调整大小
    resizeHandler() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    
    industrySector(newValue) {
      if (!newValue || !newValue.nodeCount) {
        this.showCharts = false
        return
      }
       
      let colors = [
        ['#1637FF', '#0482FF'],
        ['#FCC413', '#F7E049'],
        ['#3FC444', '#29CC79'],
        ['#FC4907', '#F19C0B'],
        ['#771DFF', '#A946FF'],
        ['#60BFCA', '#1DFFF7'],
        ['#FFAC46', '#FFCE6C'],
        ['#1637AF', '#0482AF'],
        ['#FCC4A3', '#F7E0A9'],
        ['#3FC4A4', '#29CCC9'],
        ['#FC49F7', '#F19CAB'],
        ['#771DAF', '#A946AF'],
        ['#60BFAA', '#1DFFA7'],
        ['#FFACC6', '#FFCEBC'],
      ];
      const result = Object.entries(
        newValue?.nodeCount || {}
      ).map(([key, value]) => ({
        value,
        name: key,
      }));

      // 先设置 showCharts 状态，确保 DOM 会被渲染
      this.showCharts = result.length > 0;

      // 如果没有数据，直接返回，不需要初始化图表
      if (!this.showCharts) {
        return;
      }

      // 使用 nextTick 确保 DOM 已更新
      this.$nextTick(() => {
        try {
          // 如果已有实例，先销毁
          if (this.chart) {
            this.chart.dispose();
          }
          
          const dom = document.getElementById('middlePie');
          if (!dom) {
            // console.error('Failed to find DOM element with id "middlePie"');
            return;
          }
          
          this.chart = echarts.init(dom);
          let totalQuantity = newValue.all || 0;
          
          let newArr = result.map((item, index) => {
            // totalQuantity=totalQuantity+Number(item.value)
            return {
              name: item.name,
              value: item.value,
              proportion: '', // 这里应该计算占比
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: colors[index % colors.length][0] },
                  { offset: 1, color: colors[index % colors.length][1] },
                ]),
              },
            };
          });
          
          // 计算占比
          newArr.forEach(item => {
            if (totalQuantity > 0) {
              item.proportion = ((item.value / totalQuantity) * 100).toFixed(2) + '%';
            } else {
              item.proportion = '0%';
            }
          });

          let grid = {
            // 离容器左侧的距离
            left: '5%',
            top: '0%',
            right: '5%',
            bottom: '10%',
          };
          let tooltip = {
            trigger: 'item',
            backgroundColor: '#00112dbf', // 提示框浮层的背景颜色。
            borderColor: '#0066FF', // 提示框浮层的边框颜色。
            borderWidth: 1, // 提示框浮层的边框宽。
            textStyle: {
              // 提示框浮层的文本样式。
              color: '#fff',
              fontStyle: 'normal',
              fontWeight: 'normal',
              fontFamily: 'sans-serif',
              fontSize: 12,
            },
            //实例
            formatter: function (params) {
              let res =
                params.data.name +
                '： ' +
                '<span style="color: #00FFF0;font-size: 12px;">' +
                params.data.value +
                '</span> 家' +
                '<br/>' +
                '占比：' +
                '<span style="color: #00FFF0;font-size: 12px;">' +
                params.data.proportion +
                '</span>';
              return res;
            },
          };
          let title = [
            {
              text: `${totalQuantity}`,
              top: '43%',
              textAlign: 'center',
              left: '24%',
              textStyle: {
                color: '#FFFFFF',
                fontSize: 20,
                fontWeight: '400',
                fontFamily: "YouSheBiaoTiHei",
              },
            },
            {
              text: `家`,
              top: '52%',
              textAlign: 'center',
              left: '24%',
              textStyle: {
                color: '#AEB9C0',
                fontSize: 16,
                fontWeight: '400',
                fontFamily: "YouSheBiaoTiHei",
              },
            },
          ];
          let series = [
            {
              type: 'pie',
              zlevel: 2,
              radius: ['45%', '55%'],
              animationDuration: 1500,
              animationDurationUpdate: 1500,
              itemStyle: {
                // borderRadius: 10,
              },
              left: '-50%', //2top10%
              top: '0%',
              emphasis: {},
              label: {
                show: false, // 不显示名称
              },
              data: newArr,

              //minAngle: 5 // 设置最小角度为5度
            },
            {
              type: 'gauge',
              zlevel: 1,
              // z: 198,
              center: ['25%', '50%'],
              radius: '40%',
              // left: '-25%', //2top10%
              // top: '10%',
              startAngle: 90,
              endAngle: -270,
              axisLine: {
                show: false,
              },
              axisTick: {
                distance: -6,
                length: 6,
                lineStyle: {
                  color: '#585e67',
                  width: 2,
                },
              },
              axisLabel: {
                show: false,
              },
              splitNumber: 6,
              splitLine: {
                show: true,
                distance: -6,
                length: 6,
                lineStyle: {
                  color: 'rgba(255, 255, 255, 0.18)',
                  width: 2,
                },
              },
              data: [],
              detail: {},
              pointer: {
                show: false,
              },
              // detail: {
              //   show: 0,
              // },
            },
          ];
          let lineHeight = 14;
          let fontSize = 12;
          let top = '20%';
          let left = '50%';

          let option = {
            grid,
            tooltip,
            // 圆心文字
            title,
            // 图例样式
            legend: {
              type: 'scroll',
              textStyle: {
                color: '#fff',
                fontSize,
                opacity: 0.8,
                lineHeight, // 设置文字之间的上下间距
              },
              //scrollDataIndex:2,//默认第几页
              top, //调整图例位置
              left, //整体盒子距离
              /*  pageIcons:{
                vertical:[
                  "image://https://img1.baidu.com/it/u=1890390320,3399874998&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto?sec=1703091600&t=c155259c9b8ac8844e997f0fc9e182fe"
                ]
              }, */
              pageIconColor: '#fff',
              pageIconInactiveColor: '#fff',
              pageIconSize: '12', //翻页按钮大小，可以是数组[]
              pageTextStyle: {
                color: '#FFF',
              },
              orient: 'vertical', //竖着展示
              //bottom: 'middle',
              //itemGap: 16, // 设置图例项之间的间距
              itemWidth: 10, //图例大小
              itemHeight: 10, //修改icon图形大小
              // icon: 'circle', //图例前面的图标形状
              formatter: function (name) {
                //const item = newArr.find(item => item.name === name); // 查找当前图例对应的数据项
                return `${name}`;
              },
            },
            series,
          };
          // 设置配置并绑定 resize 事件
          this.chart.setOption(option);
          window.addEventListener('resize', this.resizeHandler);
        } catch (error) {
          // console.error('Error initializing chart:', error);
          this.showCharts = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
#main {
  width: 100%;
  height: 30vh;
}
.noDataSelf{
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
