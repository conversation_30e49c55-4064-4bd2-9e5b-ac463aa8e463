<template>
  <div class="rightContent">
    <Contents :title="title">
      <div
        slot="main"
        class="contain"
      >
        <div class="total">
          <div class="number">
            <div class="init">
              <span class="text">总数</span>
              <span class="num"> {{ showData?.total || 0 }}</span>
              <span class="int"> 家</span>
            </div>
          </div>
        </div>
        <div class="cardContent">
          <ShowCard :show-data="showData?.state" />
        </div>
      </div>
    </Contents>
  </div>
</template>
<script>
import Contents from '@/views/echarts/large-screen/enterpriseCharts/components/index.vue';
import ShowCard from './card/index.vue';
export default {
  components: {
    Contents,
    ShowCard,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    showData: {
      type: Object,
      default: null,
    },
  },
};
</script>
<style scoped lang="scss">
@import '@/styles/public.scss';
@import '@/styles/screen/card.scss';

.number {
  margin-top: 16px !important;
  .text {
    font-family: YouSheBiaoTiHei;
    font-size: 20px;
    color: #ffffff;

    text-shadow: 0px 0px 6.44px rgba(30, 198, 255, 0.8);
  }
}

.contain {
  width: 100%;
  height: 25vh;
  display: flex;
  flex-wrap: wrap;
  .total {
    width: 75%;
  }
  .cardContent {
    height: calc(100% - 80px);
    width: 100%;
  }
}
</style>
