<template>
  <div class="rightContent">
    <Contents :title="title">
      <div
        slot="main"
        class="contain"
      >
        <div class="total">
          <div class="number">
            <div class="init">
              <span class="text">总数</span>
              <span class="num"> {{ total }}</span>
              <span class="int"> 家</span>
            </div>
          </div>
        </div>
        <div class="cardContent">
          <ShowCard :show-data="showData" />
        </div>
      </div>
    </Contents>
  </div>
</template>
<script>
import Contents from '@/views/echarts/large-screen/enterpriseCharts/components/index.vue';
import ShowCard from './card.vue';
export default {
  components: {
    Contents,
    ShowCard,
  },
  props: {
    total: {
      type: Number,
      default: 0,
    },
    showData: {
      type: Object,
      default: null,
    },
    title: {
      type: String,
      default: null, 
    }
  },
};
</script>
<style scoped lang="scss">
@import '@/styles/public.scss';
@import '@/styles/screen/card.scss';
 

.contain {
  width: 100%;
  height: 25vh;
  display: flex;
  flex-wrap: wrap;
  padding: 20px 0px 20px;
  .total{    width: 75%;}
  .cardContent{
    height: calc(100% - 80px);
    width: 100%;
  
  }
 
}
</style>
