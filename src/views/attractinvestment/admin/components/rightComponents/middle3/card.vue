<template>
  <div
    v-if="showData"
    class="dataList"
  >
    <div
      v-for="([key, value], index) in Object.entries(showData)"
      :key="key"
      class="itemList"
    >
      <div :class="['iconBg', 'icon' + index]" />
      <div class="content">
        <div :class="['text']">
          {{ key }}
        </div>
        <div class="num">
          <span class="number">{{ value }}</span>
          <span class="int">个</span>
        </div>
      </div>
    </div>
    <div />
  </div>
</template>
<script>
export default {
  props: {
    showData: {
      type: Object,
      default: null,
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.dataList {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  padding: 16px;
  height: 100%;
  /* border-bottom: 1px solid #eee; */
}

.itemList {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  width: 50%;
  /* border-bottom: 1px solid #eee; */
}

.iconBg {
  width: 77px;
  height: 77px;
}
.icon0 {
  background: url('~@/assets/screen/new/rightComponents4.webp') center/contain
    no-repeat;
}

.icon1 {
  background: url('~@/assets/screen/new/rightComponents5.webp') center/contain
    no-repeat;
}

.content {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  width: calc(100% - 40px);
  height: 55px;
  padding-left: 10px;

  .text {
    width: 100%;
    font-size: 0.875rem;
    color: #b8cee5;
    position: relative;
  }

  .num {
    font-size: 1.38rem;
    font-family: DINPro;
    color: #ffffff;
    display: flex;
    flex-wrap: nowrap;
    align-items: baseline;
    width: 100%;
    justify-content: flex-start;
    .number {
      @include YouSheBiaoTi28(1.38rem, bold);
    }
    .int {
      padding-left: 10px;

      font-size: 0.75rem;
      color: #cad3de;
    }
  }
}

@media screen and (max-width: 1450px) {
  .content {
    padding-left: 5px;
  }
}
</style>
