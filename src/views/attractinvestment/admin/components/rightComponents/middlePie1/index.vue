<template>
  <div
    v-if="showCharts"
    id="middlePie"
    class="chart-container" 
  />
  <div
    v-else
    class="noDataSelf"
  >
    <NoData />
  </div>
</template>

<script>
import * as echarts from 'echarts';
import NoData from '@/views/overview/components/component/noData'

export default {
  name: 'RightComponentsMiddlePie1',
  components: {
    NoData
  },
  props: {
    showData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      animate: true,
      list: [],
      showCharts: false,
      chart: null
    };
  },
  watch: {
    showData: {
      handler(newValue) {
        // console.log('showData changed:', newValue);
        if (newValue && newValue.length > 0) {
          this.$nextTick(() => {
            this.industrySector(newValue);
          });
        } else {
          this.showCharts = false;
        }
      },
      immediate: true, // 确保组件创建时立即执行
      deep: true // 深度监听数组变化
    }
  },
  mounted() {
    // 确保组件挂载后有正确的高度
    window.addEventListener('resize', this.resizeHandler);
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener('resize', this.resizeHandler);
  },
  methods: {
    resizeHandler() {
      if (this.chart) {
        this.chart.resize();
      }
    },
    
    industrySector(newValue) {
      // 移除debugger
      // console.log('初始化图表数据:', newValue);
      
      if (!newValue || newValue.length === 0) {
        this.showCharts = false;
        return;
      }
       
      let colors = [
        ['#F0C92E', '#0482FF'],
        ['#3370FF', '#F7E049'],
        ['#6CCE26', '#29CC79'],
        ['#987EFB', '#29CC79'],
        ['#F0722E', '#F19C0B'],
        ['#6CCBFF', '#A946FF'],
        ['#31A9FF', '#1DFFF7'],
        ['#EE97FD', '#FFCE6C'],
        ['#ACE2DD', '#0482FF'],
        ['#E04848', '#F7E049'],
        ['#5A39D8', '#F7E049'],
        ['#E9F148', '#29CC79'],
        ['#FC4907', '#F19C0B'],
        ['#771DFF', '#A946FF'],
        ['#1DFFF7', '#1DFFF7'],
        ['#FFAC46', '#FFCE6C'],
      ];
      
      // 确保数据有效
      const result = newValue.map(e => ({
        value: Number(e.total) || 0, // 防止NaN
        name: e.name || '未命名',
      }));

      // 计算总量用于占比
      const totalQuantity = result.reduce((sum, item) => sum + item.value, 0);
      
      // 更新显示状态
      this.showCharts = result.length > 0;

      if (!this.showCharts) {
        // console.log('无数据，不显示图表');
        return;
      }

      // 使用nextTick确保DOM已更新
      this.$nextTick(() => {
        try {
          // 如果已有实例，先销毁
          if (this.chart) {
            this.chart.dispose();
          }
          
          const dom = document.getElementById('middlePie');
          if (!dom) {
            // console.error('找不到图表容器DOM元素');
            return;
          }
          
          // 确保DOM元素有宽高
          if (dom.offsetHeight === 0) {
            dom.style.height = '300px'; // 设置一个默认高度
          }
          
          // console.log('图表容器尺寸:', dom.offsetWidth, 'x', dom.offsetHeight);
          
          this.chart = echarts.init(dom);
          
          let newArr = result.map((item, index) => {
            // 计算占比
            const proportion = totalQuantity > 0 ? 
              ((item.value / totalQuantity) * 100).toFixed(2) + '%' : '0%';
              
            return {
              name: item.name,
              value: item.value,
              proportion: proportion,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: colors[index % colors.length][0] },
                  { offset: 1, color: colors[index % colors.length][0] },
                ]),
              },
            };
          });
          
          let option = {
            tooltip: {
              trigger: 'item',
              backgroundColor: '#00112dbf',
              borderColor: '#0066FF',
              borderWidth: 1,
              textStyle: {
                color: '#fff',
                fontSize: 12,
              },
              formatter: function (params) {
                return `${params.data.name}： <span style="color: #00FFF0;">${params.data.value}</span> 家<br/>占比：<span style="color: #00FFF0;">${params.data.proportion}</span>`;
              },
            },
            legend: {
              type: 'scroll',
              textStyle: {
                color: '#fff',
                fontSize: 12,
                opacity: 0.8,
                lineHeight: 14,
              },
              top: '20%',
              left: '50%',
              pageIconColor: '#fff',
              pageIconInactiveColor: '#fff',
              pageIconSize: 12,
              pageTextStyle: {
                color: '#FFF',
              },
              orient: 'vertical',
              itemWidth: 10,
              itemHeight: 10,
              formatter: name => name,
            },
            series: [
              {
                type: 'pie',
                zlevel: 2,
              radius: ['35%', '65%'], // 增加饼图大小
                center: ['25%', '55%'],  // 调整饼图位置
                animationDuration: 1500,
                animationDurationUpdate: 1500,
                label: {
                  show: false,
                },
                data: newArr,
              }
            ]
          };
          
          // 设置配置
          this.chart.setOption(option);
          // console.log('图表初始化完成');
        } catch (error) {
          // console.error('初始化图表时发生错误:', error);
          this.showCharts = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 200px; /* 确保图表容器有足够的高度 */
}
.noDataSelf {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 8%; /* 确保图表容器有足够的高度 */
}
</style>
