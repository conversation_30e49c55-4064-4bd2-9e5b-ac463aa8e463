<template>
  <div class="rightContent">
    <Contents title="推荐企业统计">
      <div slot="main">
        <div
          v-if="!!showData"
          class="contain"
        >
          <div
            v-for="([key, value], index) in Object.entries(showData)"
            :key="key"
            class="charts"
          >
            <!-- <div v-for="(item, index) in showData?.countTop3" :key="index" class="charts"> -->
            <div :class="['icon', 'icon' + index]" />
            <div class="country">
              {{ key }}
            </div>
            <div class="num">
              <span class="number"> {{ value }}</span>
              <span class="int">次</span>
            </div>
          </div>
        </div>
        <div
          v-else
          class="noDataSelf"
        >
          <NoData />
        </div>
      </div>
    </Contents>
  </div>
</template>
<script>
import Contents from '@/views/echarts/large-screen/enterpriseCharts/components/index.vue';
import NoData from '@/views/overview/components/component/noData'
  
  export default {
    components: {
      Contents,
      NoData,
    },
    props: {
      showData: {
        type: Object,
        default: null,
      },
    },
  };
</script>
<style scoped lang="scss">
  @import '@/styles/public.scss';

  .noData {
    font-size: 14px;
    color: rgba(181, 193, 209, 0.7);
    line-height: 15vh;
    width: 100%;
    text-align: center;
  }

  .contain {
    width: 100%;
    height: 25vh;
    // padding: 20px;
    display: flex;
    flex-wrap: nowrap;
    padding: 50px 0px 20px;

    .charts {
      color: white;
      width: 34%;
      height: 80%;
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      text-align: center;

      .icon {
        width: 77px;
        height: 77px;
      }

      .icon0 {
        background: center/contain no-repeat url('https://static.idicc.cn/cdn/pangu/assets/screen/new/rightComponents1.webp');
      }

      .icon1 {
        background: center/contain no-repeat url('https://static.idicc.cn/cdn/pangu/assets/screen/new/rightComponents2.webp');
      }

      .icon2 {
        background: center/contain no-repeat url('https://static.idicc.cn/cdn/pangu/assets/screen/new/rightComponents3.webp');
      }

      .country {
        width: 100%;
        font-size: 16px;
        line-height: 15px;
        padding: 6px 0;
        @include Puhuiti(14px, #AEB9C0, 600)
      }

      .num {
        text-align: center;
        width: 100%;
        padding-left: 15px;

        .number {
          @include YouSheBiaoTi28(24px, 500);
        }

        .int {
          padding-left: 10px;
          font-size: 14px;
          color: rgba(201, 210, 221, 1);
        }
      }
    }
  }
</style>