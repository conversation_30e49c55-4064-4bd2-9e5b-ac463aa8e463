<template>
  <div class="personalAdd">
    <div class="detail-main">
      <div class="detail-sel">
        <div class="detail-list">
          <div class="companyCard">
            <!-- 企业卡片 -->
            <div
              class="close"
              @click="closeDetail"
            >
              <i class="el-icon-close" />
            </div>
            <CompanyCard
              :detail-data="selectedRow"
              :showd-time="false"
              :active-state="activeState"
              :enterprise-show="enterpriseShow"
              :tag-show="tagShow"
              :more-tag="isMore"
              @openDetailSingle="openDetailSingle"
              @attention="attention"
            />
          </div>
          <!-- 下面详细Tab -->
          <div
            v-loading="tab5Loading"
            class="detailpage"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
          >
            <div class="tabs">
              <el-tabs
                v-model="istabs"
                class="custom-tabs"
                :style="{ width: '100%' }"
              >
                <el-tab-pane
                  v-for="item in selectedRow?.entrustOrNot ? activeName : activeName.slice(0,2)"
                  :key="item.id"
                  :label="item.name"
                  :name="item.id.toString()"
                >
                  {{ item.content }}
                </el-tab-pane>
              </el-tabs>
            </div>
            <!-- 基本信息 -->
            <div
              v-if="istabs === '1'"
              class="detail2"
            >
              <div class="describe-info">
                <div class="del">
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <div class="name">
                        <span class="title">通讯电话</span>
                        <span class="span2">{{ selectedRow?.mobile }}</span>
                      </div>
                    </el-col>
                  </el-row>
  
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <div class="name">
                        <span class="title">所属行业</span>
                        <div class="span2">
                          {{ selectedRow?.nationalStandardIndustry }}
                        </div>
                      </div>
                    </el-col>
                  </el-row>
             
          
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <div class="name">
                        <span class="title">统一社会信用代码</span>
                        <div class="span2">
                          {{ selectedRow?.uniCode }}
                        </div>
                      </div>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="24">
                      <div class="name">
                        <span class="title">详细地址</span>
                        <div class="span2">
                          {{ selectedRow?.enterpriseAddress }}
                        </div>
                      </div>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="24">
                      <div
                        class="name"
                        style="min-height: 200px"
                      >
                        <span class="title">经营范围</span>
                        <div class="span2">
                          {{ selectedRow?.businessScope }}
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <div
                        class="name"
                        style="min-height: 200px"
                      >
                        <span class="title">企业简介</span>
                        <div class="span2">
                          {{ selectedRow?.introduction }}
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </div>
            <!-- 自行跟进记录 -->
            <div
              v-if="istabs === '7'"
              class="detail2 detail3"
            >
              <FollowUpRecord
                :clue-id="selectedRow?.id"
                :selected-row="selectedRow"
              />
            </div>
            <!-- 委托跟踪信息 -->
            <div
              v-if="istabs === '8' && selectedRow?.entrustOrNot"
              class="detail2 detail3"
            >
              <TrackeInfo :uni-code="selectedRow?.uniCode" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import CompanyCard from '@/views/echarts/large-screen/companyCard/index.vue'; //  企业 card
import FollowUpRecord from '@/views/echarts/large-screen/companyDetailComponents/FollowUpRecord/index.vue';
import TrackeInfo from '@/views/echarts/large-screen/companyDetailComponents/TrackeInfo/index.vue';
export default {
  components: { CompanyCard, FollowUpRecord, TrackeInfo },
  props: {
    selectedRow: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tab5Loading: false,
      activeName: [
        { name: '基本信息', id: '1' },
        { name: '自行跟进记录', id: '7' },
        { name: '委托跟踪信息', id: '8' },
      ],
      istabs: '1', // 修改为字符串类型
      enterpriseShow: false,
      activeState: '',
      tagShow: null,
      isMore: [],
    };
  },
  mounted() {
    // 在这里添加初始化逻辑
  },
  methods: {
   // 关闭
    closeDetail() {
      this.$emit('closeDetail');
      this.$emit('getEnterpriseList');
    },
    openDetailSingle() {},
    attention() {},
    handleClick(tab, event) {
      this.istabs = tab?.index;
    },
  },
};
</script>

<style scoped lang="scss">
@import '@/views/echarts/large-screen/companyPublic.scss';
@import '@/views/echarts/large-screen/companyDetail.scss';
@import '@/views/attractinvestment/admin/components/dialog_tip.scss';

.personalAdd {
  width: 100%;
  height: 100%;
}

</style>
<style lang="scss">
/* 确保左右切换按钮显示 */
.detailpage .tabs .el-tabs__nav-wrap {
  position: relative;
  overflow: hidden;

  &.is-scrollable {
    padding: 0 32px;
  }

  .el-tabs__nav-prev,
  .el-tabs__nav-next {
    color: #fafafa;
    font-size: 16px;
    position: absolute;
    cursor: pointer;
    line-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 100%;
    z-index: 10;
  }

  .el-tabs__nav-prev{
background:center/contain no-repeat url('~@/assets/bigScreen/left.webp') !important;

  }
  .el-tabs__nav-next {
background:center/contain no-repeat url('~@/assets/bigScreen/right.webp')!important;

  }
  .el-tabs__nav-prev {
    left: 0;
  }

  .el-tabs__nav-next {
    right: 0;
  }
}
</style>
