.box{
    width: 375px;
}
.headTxt {
    height: 25px;
    font-weight: 500;
    font-size: 15px;
    color: #1D2129;
    width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    position: relative;

    .sign {
        width: 4px;
        height: 14px;
        background: linear-gradient(158deg, #F8DBB4 0%, #D8AC6F 100%), ;
        box-shadow: 0px 4px 12px 0px #F8F5EE;
        border-radius: 0px 0px 0px 0px;
    }

    .text {
        margin-left: 8px;
    }
}
   .iconPath {
        width: 22px;
        height: 22px;
    }
.t2 {
    z-index: 80;
    position: relative;
    .onData {
        display: flex;
        width: 100%;
        height: auto;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        position: relative;
        margin-top: 8px;
        .nodataImg {
            background-image: url(../../assets/Member/nodata.png);
            background-size: 100% 100%;
            width: 229px;
            height: 225px;
            margin-top: -40px;
        }
        .nodataText {
            font-weight: 400;
            font-size: 14px;
            color: #3F4A59;
            position: absolute;
            top: 140px;
        }
    }


    .industryListBox {
        width: 345px;
        height: auto;
        background: #FFFFFF;
        //box-shadow: 0px 4px 12px 0px #EEF1F8;
        border-radius: 10px;
        margin-left: 15px;
        //padding: 24px 15px 16px 15px;
        box-sizing: border-box;

        .industryList {
            margin-top: 4px;
            .industryItemSVIPBox {
                background-image: url(../../assets/Member/svipBox2.png);
                background-size: 100% 100%;
                width: 345px;
                height: 82px;
                margin-top: 12px;
                position: relative;
                display: flex;
                .renew {
                    width: 50px;
                    height: 21px;
                    border-radius: 0 10px 0 10px;
                    background: linear-gradient(113deg, #FFEECE 0%, #ECC78F 100%), #3370FF;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: absolute;
                    top: 0;
                    right: 0;
                    font-size: 12px;
                    font-weight: normal;
                    color: #1A1816;
                    cursor: pointer;
                }

                .renewBox {
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 50px;
                    height: 21px;
                    background: linear-gradient(91deg, #FFEECE 0%, #ECC78F 100%), ;
                    border-radius: 0px 10px 0px 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 400;
                    font-size: 12px;
                    color: #1A1816;
                }

                .industryItemalready {
                    padding-left: 12px;

                    .name {
                        display: flex;
                        margin-top: 17px;
                        height: 22px;
                        align-items: center;

                        .nametexte {
                            font-weight: 600;
                            font-size: 14px;
                            color: #efddc1;
                            margin-left: 6px;
                        }
                    }

                    .particulars {
                        display: flex;
                        margin-top: 8px;
                        font-weight: 400;
                        font-size: 10px;
                        color: #F9ECEC;

                        .operator {
                            margin-left: 30px;
                        }
                    }
                }
            }
        }
    }

    .numtext {
        font-weight: 400;
        margin-left: 5px;
        font-size: 15px;
        color: #86909C;
    }


}
.t3 {
    z-index: 80;
    position: relative;
    margin-left: 15px;
    margin-top: 16px;
    width: 345px;
    height: auto;
    background: #FFFFFF;
    box-shadow: 0px 4px 12px 0px #EEF1F8;
    border-radius: 10px 10px 10px 10px;
    padding-bottom: 16px;
    padding-top: 15px;
    padding-left: 15px;
    padding-right: 15px;

    .headText {
        font-weight: 400;
        font-size: 14px;
        color: #32231D;
        text-align: center;
        margin-bottom: 17px;
    }
}
.industryList {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;

    .industryItemBox {
        width: 48%;
        cursor: pointer;
    }

    .iconPath {
        width: 22px;
        height: 22px;
    }

    .industryItem {
        width: 100%;
        height: auto;
        padding-bottom: 9px;
        margin-bottom: 8px;
        background: #FFFFFF;
        position: relative;
        box-shadow: 0px 4px 12px 0px #EEF1F8;
        border-radius: 10px;
        border: 1px solid #FCE2A9;

        .name {
            width: 100%;
            height: 21px;
            display: flex;
            align-items: center;
            padding: 11px;
            margin-top: 11px;
            margin-bottom: 11px;

            .nametexte {
                margin-left: 8px;
                width: 90px;
                font-weight: 500;
                font-size: 14px;
                color: #000000;
            }
        }

        .username {
            font-weight: 400;
            font-size: 11px;
            color: #858585;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-left: 11px;
        }

        .username2 {
            font-weight: 400;
            font-size: 11px;
            color: #858585;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-left: 11px;
            margin-top: 2px;
        }
    }

    .industryItems {
        width: 100%;
        height: auto;
        position: relative;
        //background: linear-gradient(174deg, #AEC6FF 0%, #1F61FF 100%), #FFFFFF;
        //box-shadow: 0rpx 8rpx 24rpx 0rpx #EEF1F8;
        border-radius: 10px;
        margin-bottom: 8px;
        background: linear-gradient(90deg, #FFF4E1 0%, #FFDEA4 100%);
        border: 1px solid #fff;
        padding-bottom: 9px;

        .name {
            width: 100%;
            height: 21px;
            display: flex;
            align-items: center;
            padding: 11px;
            margin-top: 11px;
            margin-bottom: 11px;

            .nametexte {
                margin-left: 8px;
                width: 90px;
                font-weight: 500;
                font-size: 14px;
                color: #000000;
            }
        }

        .username {
            font-weight: 400;
            font-size: 11px;
            color: #858585;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-left: 11px;
        }
    }
}
.Buymore {
    display: flex;
    //justify-content: flex-end;
    justify-content: center;
    align-items: center;
    margin-top: 8px;
    //margin-left: 30rpx;
    //z-index: 92;
    margin-bottom: 4px;
    .Buymoreright {
        //margin-right: 15px;
        cursor: pointer;
        font-weight: 400;
        font-size: 11px;
        color: #86909C;
    }
}
.bgc {
    width: 345px;
    height: 52px;
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
    background-image: url(../../assets/Member/bgc2.png);
    background-size: 100% 100%;
    border-top-left-radius: 10px;
    pointer-events: none;
    border-top-right-radius: 10px;
}
//想获得更多产业链臻享版权限
.AccountUpgrade {
    width: 315px;
    height: auto;
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin:auto

    .textName {
        width: 100%;
        font-weight: 400;
        font-size: 14px;
        color: #3F4A59;
        line-height: 20px;
        margin-bottom: 20px;
        //margin-top: 10px;
    }
    .textName2 {
        width: 100%;
        font-weight: 400;
        font-size: 14px;
        color: #3F4A59;
        line-height: 20px;
        margin-bottom: 10px;
        //margin-top: 10px;
    }
    .codeBox2 {
        margin-top: 12px;
        margin-bottom: 16px;
        width: auto;
        height: auto;
        padding: 0 8px;
        background: #FAEFE0;
        padding-top: 8px;
        padding-bottom: 6px;
        font-weight: 400;
        font-size: 13px;
        color: #32231D;
        display: flex;
        flex-direction: row-reverse;
        align-items: center;
        flex-direction: column;
        border-radius: 6px 6px 6px 6px;

        .serviceCode {
            width: 143px;
            height: 137px;
            margin-bottom: 4px;
        }
    }
    .codeBox {
        margin-top: 24px;
        margin-bottom: 16px;
        width: auto;
        height: auto;
        padding: 0 8px;
        background: #FAEFE0;
        padding-top: 8px;
        padding-bottom: 6px;
        font-weight: 400;
        font-size: 13px;
        color: #32231D;
        display: flex;
        flex-direction: row-reverse;
        align-items: center;
        flex-direction: column;
        border-radius: 6px 6px 6px 6px;

        .serviceCode {
            width: 143px;
            height: 137px;
            margin-bottom: 4px;
        }
    }

    .phone {
        width: 100%;
        font-weight: 500;
        font-size: 15px;
        color: #6B2D00;
    }

    .exclusiveBtn {
        width: 271px;
        height: 44px;
        background: linear-gradient(91deg, #FFEECE 0%, #ECC78F 100%);
        box-shadow: 0px 4px 12px 0px #F8F5EE;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 15px;
        color: #6B2D00;
        margin-bottom: 20px;
    }
}
.interests {
    width: 345px;
    height: auto;
    margin-left: 15px;
    margin-top: 12px;
    margin-bottom: 27px;
    position: relative;
    display: flex;
    justify-content: center;
    .tab1Png {
        width: 345px;
        height: 290.24px;
        pointer-events: none;
    }
}
.tryText{
    font-weight: 400;
    font-size: 14px;
    color: #86909C;
    line-height: 20px;
    width: 345px;
    height: auto;
    margin-left: 15px;
}
.tryButton{
    font-weight: 400;
    font-size: 14px;
    color: #2559DB;
    line-height: 20px;
    margin-left: 3px;
}