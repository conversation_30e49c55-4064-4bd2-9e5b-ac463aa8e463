<template>
  <div class="box">
    <div class="headTxt">
      <div class="sign" />
      <span class="text">哒达招商-已开通产业链-臻享版 ({{ screeningResult.length }})</span>
    </div>

    <div
      v-if="screeningResult.length !== 0"
      class="t2"
    >
      <div class="industryListBox">
        <div class="industryList">
          <div
            v-for="(item, index) in screeningResult"
            :key="index"
            class="industryItemSVIPBox"
            @click="renewFn"
          >
            <div class="industryItemalready">
              <div class="name">
                <img
                  class="iconPath"
                  src="https://static.idicc.cn/cdn/pangu/svipIcon.png"
                >
                <div class="nametexte">
                  {{ item.knowledgeName }}
                </div>
              </div>
              <div class="particulars">
                <div class="ExpiredTime">
                  到期时间：{{ formatDate(item.svipExpiredTime) }}
                </div>
                <div class="operator">
                  运营人：艾瑞数云
                </div>
              </div>
            </div>
            <div class="renew">
              续费
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      v-else
      class="t2"
    >
      <div class="onData">
        <div class="nodataImg" />
        <span class="nodataText">您暂无已开通的臻享版产业链</span>
      </div>
    </div>

    <div v-if="industryList.length !== 0">
      <div
        style="margin-top: 32px"
        class="headTxt"
      >
        <div class="sign" />
        <span class="text">未开通产业链</span>
      </div>
      <div class="t3">
        <div class="bgc" />
        <div class="headText">
          · 请选择想要开通的产业链 ·
        </div>
        <div class="industryListBox">
          <div class="industryList">
            <div
              v-for="(item, index) in industryList1"
              :key="index"
              class="industryItemBox"
              @click="selectItem(item.id)"
            >
              <div
                :class="[
                  knowledgeIds.includes(item.id)
                    ? 'industryItems'
                    : 'industryItem',
                ]"
              >
                <div class="name">
                  <img
                    class="iconPath"
                    :src="item.iconPath"
                  >
                  <div class="nametexte">
                    {{ item.name }}
                  </div>
                </div>
                <div class="username">
                  运营人：艾瑞数云
                </div>
              </div>
            </div>
          </div>

          <div
            v-if="industryList.length > 4"
            class="Buymore"
          >
            <div
              class="Buymoreright"
              @click="toggleShowAll"
            >
              <span style="margin-right: 2px">{{
                showAll ? "收起" : "查看全部"
              }}</span>
              {{ showAll ? "↑" : "↓" }}
            </div>
          </div>
        </div>

        <div
          v-if="knowledgeIds.length !== 0"
          class="AccountUpgrade"
        >
          <div class="textName">
            想获得更多产业链臻享版权限？请拨打商务电话或直接联系专属客服。
          </div>
          <div class="phone">
            商务电话：***********
          </div>
          <div class="codeBox">
            <img
              src="https://static.idicc.cn/cdn/aiChat/applet/newServiceCode2.png"
              class="serviceCode"
            >
            - 扫码添加专属客服 -
          </div>
        </div>
      </div>
    </div>

    <!-- 会员权益对比 -->
    <div
      class="headTxt"
      style="margin-top: 32px"
    >
      <div class="sign" />
      <span class="text">权益对比</span>
    </div>
    <div class="interests">
      <img
        src="https://static.idicc.cn/cdn/pangu/tab1.png"
        class="tab1Png"
      >
    </div>
    <div class="tryText">
      <p>【7天臻享会员 0元体验】🔥</p>
      <!--立即 <span class="tryButton" @click="contactCustomerService">联系客服</span
      >，抢先体验专属特权。 -->
      <p>
        新用户专享！<span
          class="tryButton"
          @click="contactCustomerService"
        >联系客服</span>
        立即领取价值999元的臻享版会员，在试用期内，您将全面解锁固定产业链的所有权益，并畅享全部哒哒招商功能，助您在竞争中占据先机，尽显尊贵身份。
      </p>
    </div>

    <el-dialog
      title="续费"
      :visible.sync="dialogVisible"
      width="25%"
      show-close
      append-to-body
      :before-close="handleClose"
    >
      <div class="AccountUpgrade">
        <div class="textName">
          想获得更多产业链臻享版权限？请拨打商务电话或直接联系专属客服。
        </div>
        <div class="phone">
          商务电话：***********
        </div>
        <div class="codeBox">
          <img
            src="https://static.idicc.cn/cdn/aiChat/applet/newServiceCode2.png"
            class="serviceCode"
          >
          - 扫码添加专属客服 -
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { vipListAPI, listAllPurchaseChainAPI } from "@/api/login";

export default {
  name: "MemberCenter",
  data() {
    return {
      dialogVisible: false,
      screeningResult: [],
      industryList: [], // 可购买产研
      industryList1: [], // 展示的可购买产研
      knowledgeIds: [], // 选中的产研
      showAll: false, // 产研收起还是展开
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    renewFn() {
      this.dialogVisible = true;
    },
    async getList() {
      try {
        const res = await vipListAPI();
        const myattract = Object.entries(res.result.industryStatus).map(
          ([key, value]) => ({
            ...value,
          })
        );
        const mywisdom = Object.entries(res.result.investStatus).map(
          ([key, value]) => ({
            ...value,
          })
        );

        const originalList = {
          myattract,
          mywisdom,
        };

        const screeningResult = {
          vip: {
            myattract: originalList.myattract.filter(
              (it) => it.vipEnum === "VIP" || it.vipEnum === "SVIP_VIP"
            ),
            mywisdom: originalList.mywisdom.filter(
              (it) => it.vipEnum === "VIP" || it.vipEnum === "SVIP_VIP"
            ),
          },
          svip: {
            myattract: originalList.myattract.filter(
              (it) => it.vipEnum === "SVIP" || it.vipEnum === "SVIP_VIP"
            ),
            mywisdom: originalList.mywisdom.filter(
              (it) => it.vipEnum === "SVIP" || it.vipEnum === "SVIP_VIP"
            ),
          },
        };

        this.screeningResult = screeningResult.svip.mywisdom;
        this.getPurchaseList(originalList);
      } catch (error) {
        // console.error("获取我的VIP列表失败", error);
      }
    },
    getPurchaseList(originalList) {
      listAllPurchaseChainAPI().then((res) => {
        let industryResearch = this.screeningResult.map((it) => it.knowId); // 已拥有
        let attract = res.result.filter(
          (it) => !industryResearch.includes(it.id)
        );
        this.industryList = attract;
        this.industryList1 = attract.slice(0, 4);
      });
    },
    selectItem(id) {
      if (this.knowledgeIds.includes(id)) {
        this.knowledgeIds = this.knowledgeIds.filter((item) => item !== id);
      } else {
        this.knowledgeIds.push(id);
      }
    },
    toggleShowAll() {
      this.showAll = !this.showAll;
      this.industryList1 = this.showAll
        ? this.industryList
        : this.industryList.slice(0, 4);
    },
    contactCustomerService() {
      alert("请联系客服：***********");
    },
    formatDate(timestamp) {
      const date = new Date(Number(timestamp));
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>