

const apiDomain2 = 'https://www.fastmock.site/mock/cd339c8f48c9e13af2c490b1481ffe1c/qingteng';
import interfaceRequest from '@/utils/interfaceRequest'
// import requestOther from '@/utils/requestOther'
import axios from 'axios';
import { apiUrl,
  // cityJsonUrl 
} from '@/api/user'
import store from '@/store'
import { getToken } from '@/utils/auth'

// import { download } from '@/utils';
const apiDomain = apiUrl;


 
export const chartApi = {
  chainOverview:apiDomain+'/boardDriver/chainOverview', // 图谱列表
}

// 地图 - 产业链节点数据
export function getChainOverview(data) {
  return interfaceRequest({
      url:chartApi.chainOverview,
      method:'GET',
      params:{...data}   
  })
}
