<template>
  <div class="content">
    <Titles title="产业创新策源" />
    <div class="top">
      <div class="smallTop">
        <div class="topContent">
          <TopView :show-data="showData" />
        </div>
        <div class="middleContent">
          <div class="ranking">
            <div class="textModule">
              <div
                v-if="showData?.avgPatentCountOfEnterprise"
                class="text textDefault"
              >
                <span class="number">
                  {{ showData?.avgPatentCountOfEnterprise || 0 }}</span>
                <span class="int">个</span>
              </div>
              <div
                v-if="showData?.avgPatentCountOfEnterprise"
                class="text textCopy"
              >
                <span class="number">
                  {{ showData?.avgPatentCountOfEnterprise || 0 }}</span>
                <span class="int">个</span>
              </div>
            </div>
            <div class="nameModule">
              本地企业专利平均拥有量
            </div>
          </div>
          <div class="ranking">
            <div class="textModule">
              <div
                v-if="showData?.avgPatentCountOfCountryEnterprise"
                class="text textDefault"
              >
                <span class="number">
                  {{ showData?.avgPatentCountOfCountryEnterprise || 0 }}</span>
                <span class="int">个</span>
              </div>
              <div
                v-if="showData?.avgPatentCountOfCountryEnterprise"
                class="text textCopy"
              >
                <span class="number">
                  {{ showData?.avgPatentCountOfCountryEnterprise || 0 }}</span>
                <span class="int">个</span>
              </div>
            </div>
            <div class="nameModule">
              全国企业专利平均值
            </div>
          </div>
          <div class="ranking">
            <div class="textModule">
              <div
                v-if="showData?.avgPatentRank"
                class="text textDefault"
              >
                <div
                  v-if="showData?.avgPatentRank !== '0'"
                  class="textShow"
                >
                  <span class="no"> NO.</span>
                  <span class="number">{{ showData?.avgPatentRank }}</span>
                </div>
                <span v-else> NO.<span class="number">-</span> </span>
              </div>
              <div
                v-if="showData?.avgPatentRank"
                class="text textCopy"
              >
                <div
                  v-if="showData?.avgPatentRank !== '0'"
                  class="textShow"
                >
                  <span class="no"> NO.</span>
                  <span class="number">{{ showData?.avgPatentRank }}</span>
                </div>
                <span v-else> NO.<span class="number">-</span> </span>
              </div>
            </div>
            <div class="nameModule">
              同级区域排名
            </div>
          </div>
        </div>
      </div>

      <!-- <div class="bottomContent">
        <TopBottomContent :showData="showData" /> 
      </div> -->
    </div>
    <Titles title="产业投资融资" />
    <div class="bottom">
      <div class="bottomTop">
        <Bottom :show-data="chainInvestDTO" />
      </div>
      <div class="bottomMap">
        <!-- <div class="bottomTitle"> -->
        <TitlePoints title="对外投资城市TOP5" />
        <!-- </div> -->

        <div class="investBottomContent">
          <div class="contentLeft">
            <ChinaMap
              ref="chinaMapRef"
              :show-data="topFiveData"
              :active="active"
            />
            <div class="btn">
              <div
                :class="['leftBtn', active === 1 ? 'active' : '']"
                @click="changeDataView(1)"
              >
                <div>对外投资</div>
              </div>
              <div
                :class="['leftBtn', active === 2 ? 'active' : '']"
                @click="changeDataView(2)"
              >
                <div>外来投资</div>
              </div>
            </div>
          </div>

          <div class="contentRight">
            <TopFive
              v-if="topFiveData && topFiveData.length > 0"
              ref="topFiveRef"
              :show-data="topFiveData"
              :active="active"
            />
            <div
              v-else
              class="no-data"
            >
              <NoData />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Titles from "../component/titles.vue";
import TopView from "./component/top.vue";
// import TopBottomContent from './component/topBottomContent.vue';
import Bottom from "./component/bottom.vue";
import TitlePoints from "@/views/overview/components/component/titlePoint.vue";
import TopFive from "./component/topFive.vue";
import ChinaMap from "./component/chinaMap.vue";
import NoData from "@/views/overview/components/component/noData";

export default {
  components: {
    Titles,
    TopView,
    // TopBottomContent,
    Bottom,
    TitlePoints,
    TopFive,
    ChinaMap,
    NoData,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      showData: null,
      topFiveData: null,
      active: 1,
      chainInvestDTO: null,
    };
  },
  watch: {
    data(val) {
      this.init(val);
    },
  },

  mounted() {
    this.init(this.data);
  },
  methods: {
    init(val) {
      // console.log('init1', val);
      let chainInnovateDTO = val?.chainInnovateDTO;
      this.showData = chainInnovateDTO;
      let chainInvestDTO = val?.chainInvestDTO;
      this.chainInvestDTO = chainInvestDTO;
      //对外投资top5,//吸纳投资top5
      let { topOutboundInvestment, topAbsorbInvestment } = chainInvestDTO;
      this.topFiveData = topOutboundInvestment;
    },
    changeDataView(status) {
      let that = this;
      let chainInvestDTO = this.chainInvestDTO;
      //对外投资top5,//吸纳投资top5
      let { topOutboundInvestment, topAbsorbInvestment } = chainInvestDTO;
      that.active = status;
      that.topFiveData =
        status === 1 ? topOutboundInvestment : topAbsorbInvestment;
      that.$refs.chinaMapRef?.init(this.topFiveData);
      that.$refs.topFiveRef?.init(this.topFiveData);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/public.scss";

.content {
  height: 100%;
  padding: 20px 40px 60px 0px;
  background: center/cover no-repeat
    url("https://static.idicc.cn/cdn/pangu/right_bg.png?x-oss-process=image/quality,q_85/format,webp");
  background-size: 95%;
}

.top {
  height: 28%;
  // padding-bottom: 10px;

  .smallTop {
    height: 100%;

    .topContent {
      height: calc(100% - 110px);
      padding: 0 10px;
    }

    .middleContent {
      height: 5.375rem;
      width: calc(100% - 20px);
      margin: 3px auto 10px 20px;
      background: url("https://static.idicc.cn/cdn/pangu/assets/screen/new/tab_bg.png") center/contain
        no-repeat;
      background-size: 100% 100%;
    }
  }

  .bottomContent {
    height: 55%;
    padding: 0.9rem 10px 0.9rem 30px;
  }
}

.bottom {
  height: calc(72% - 110px);
  // background: #f0f8ff6e;

  .bottomTop {
    height: 100px;
    // margin-bottom: 25px;
    // padding-bottom: 30px;
  }

  .bottomMap {
    height: calc(100% - 100px);
    padding: 0px 0px 0 20px;
  }
}

.middleContent {
  display: grid;
  grid-template-columns: 40% 30% 30%;
  align-items: center;

  .ranking {
    margin-bottom: 10px;

    .textModule {
      text-align: center;
      position: relative;

      .text {
        position: absolute;
        width: 100%;
        height: 30px;
        font-size: 1.38rem;
        font-family: DINPro;
        font-weight: 500;
        line-height: 33px;
        background: linear-gradient(180deg, #fff 0%, #068dff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .textShow {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .textCopy {
        .int {
          opacity: 0;
        }

        opacity: 0.3;
        padding-top: 2px;
      }
    }

    .textDefault {
    }

    .int {
      font-size: 0.75rem;
      color: #56a7ec;
      padding-left: 4px;
    }
  }
  .number {
    @include YouSheBiaoTi28(1.38rem);
  }
  .no {
    @include YouSheBiaoTi28(14px);
  }
  .nameModule {
    font-size: 0.8rem;
    color: #f0f3f6;
    text-align: center;
    text-align: center;
    margin-top: 35px;
    font-size: 14px;
    font-family: YouSheBiaoTiHei;
  }
}

// .bottomTitle {
//   display: flex;
//   justify-content: flex-start;
//   padding: 0 10px 0 30px;
//   align-items: baseline;
//   flex-wrap: wrap;

.btn {
  width: 30%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;

  .leftBtn {
    width: 110px;
    height: 35px;
    background: url("https://static.idicc.cn/cdn/pangu/assets/screen/new/btn1.webp") center/contain no-repeat;
    background-size: 100% 100%;
    border-radius: 4px;
    margin-right: 10px;
    margin-bottom: 30px;
    color: rgba(191, 200, 215, 0.8);
    line-height: 25px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.75rem;
    cursor: pointer;
    > div {
      @include YouSheBiaoTi24(14px, normal, #b0ccde, #b0ccde, #b0ccde);
    }
    &.active {
      background: url("https://static.idicc.cn/cdn/pangu/assets/screen/new/btn2.webp") center/contain no-repeat;

      color: #ffffff;
      > div {
        @include YouSheBiaoTi24(14px, normal, #9be5ff, #9be5ff, #fff);
      }
    }
  }
}
// }

.investBottomContent,
.no-data {
  height: calc(100% - 10px);
  width: 100%;
  display: flex;
  // height: calc(100% - 35px);
  flex-direction: column;
  // padding-top: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.contentRight {
  min-height: 170px;
}
.contentLeft,
.contentRight {
  width: 100%;
}
.contentLeft {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@media screen and (max-width: 1650px) {
  .top .bottomContent {
    padding: 0 10px 0.9rem 30px;
  }
}

@media screen and (max-width: 1450px) {
  .content {
    padding: 10px 5px 0 0;
  }

  .top {
    .smallTop {
      height: 50%;
    }
  }

  .bottomContent {
    height: 55%;
    padding: 0 10px 0.9rem 30px;
  }
}
</style>
