<template>
  <div class="dataList">
    <div
      v-for="(item, index) in dataList"
      :key="item.id"
      class="itemList"
    >
      <div :class="['iconBg', 'icon' + index]" />
      <div class="content">
        <div :class="['text']">
          {{ item.name }}
        </div>
        <div class="num">
          <span class="number">{{ item.num }}</span>
          <span class="int">{{ item.int }}</span>
        </div>
      </div>
    </div>
    <div />
  </div>
</template>
<script>
export default {
  props: {
    showData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      dataList: [],
      // 本地 / 全部切换
      selNum: 0,
      // 企业类型
      selTypeList: 4,
      // 本地 / 全部切换
      tab: 4,
      // 全国本地企业切换
      showLocality: false,
      // 全国本地企业切换
      elements: null,
    };
  },
  // watch: {
  //   "$store.getters.allViewData": {
  //     deep: true,
  //     immediate: true,
  //     handler (val) {
  //       val && this.init(val)
  //     }
  //   },
  // },
  watch: {
    showData(newValue) {
      this.init(newValue);
    },
  },
  mounted() {
    this.init(this.showData);
  },
  // mounted() {
  //   // this.init()
  // },
  methods: {
    init(val) {
      if (val) {
        let { outBoundCount, absorbCount } = val;
        //       outBoundCount//对外投资总数
        // absorbCount//吸纳投资总数

        this.dataList = [
          {
            id: '1',
            name: '对外投资',
            int: '家',
            num: outBoundCount || 0,
          },
          {
            id: '2',
            name: '外来投资',
            int: '家',
            num: absorbCount || 0,
          },
        ];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.dataList {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  padding: 0 0 0 3rem;
  height: 100%;
  /* border-bottom: 1px solid #eee; */
}

.itemList {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  width: 50%;
  /* border-bottom: 1px solid #eee; */
}

.iconBg {
  width: 37px;
  height: 37px;
}
.icon0 {
      background: url('https://static.idicc.cn/cdn/pangu/assets/screen/new/point3.webp') center/contain no-repeat;
  }

  .icon1 {
 
      background: url('https://static.idicc.cn/cdn/pangu/assets/screen/new/point1.webp') center/contain no-repeat;
 
  }
 
.content {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  width:calc(100% -  40px);
  height: 55px;
  padding-left: 10px;
 
  .text {
    width: 100%;
    font-size: 0.875rem;
    color: #b8cee5;
    position: relative;
 
  }

  .num {
    font-size: 1.38rem;
    font-family: DINPro;
    color: #ffffff;
    display: flex;
    flex-wrap: nowrap;
    align-items: baseline;
    width: 100%;
    justify-content: flex-start;
    .number {

      @include YouSheBiaoTi28(1.38rem,bold);
    }
    .int {
      padding-left: 10px;

      font-size: 0.75rem;
      color: #cad3de;
    }
  }
}

@media screen and (max-width: 1450px) {
  .content {
    padding-left: 5px;
  }
}
</style>
