<template>
  <div class="topFive">
    <div
      v-for="(item, index) in topFive"
      :key="item.id"
      class="topFiveItem"
    >
      <div
        :class="['indexNum', 'indexNum' + index]"
        :style="{ background: cororList[index] }"
      >
        <div class="index">
          {{ index + 1 }}
        </div>
      </div>
      <div class="mainView">
        <!-- <el-tooltip effect="dark" :content="item.name" placement="top-start"> -->
        <div class="title">
          {{ item.name }}
        </div>
        <!-- </el-tooltip> -->
        <div class="topFiveSquare">
          <div
            class="topFiveBg"
            :style="{ width: item.value + '%' }"
          /> 
          <!-- <el-popover visible-arrow placement="top-start" width="100" trigger="hover" popper-class="topFivePop"> -->
          <!-- <div>
            {{ item.name }}: {{ item.amount }}
          </div>
          <div slot="reference" class="topFiveBg" :style="{ width: item.value + '%' }">
          </div> -->
          <!-- </el-popover> -->
        </div>
      </div>
      <div class="amount">
        {{ item.amount }}
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    showData: {
      type: Array,
      default: ()=>[]
    },
    active: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      topFive: [],
      cororList: [
        'linear-gradient(0deg, rgba(255, 64, 66, 0.102) 1%, rgba(255, 64, 66, 0.4) 99%)',
        'linear-gradient(357deg, rgba(255, 108, 0, 0.102) 2%, rgba(255, 108, 0, 0.4) 97%, rgba(255, 108, 0, 0.4) 97%)',
        'linear-gradient(357deg, rgba(255, 209, 86, 0.102) 2%, rgba(255, 209, 86, 0.4) 97%, rgba(255, 209, 86, 0.4) 97%)',
        'linear-gradient(357deg, rgba(28, 96, 253, 0.0001) 2%, rgba(28, 96, 253, 0.2) 97%, rgba(28, 96, 253, 0.2) 97%)',
        'linear-gradient(357deg, rgba(28, 96, 253, 0.0001) 2%, rgba(28, 96, 253, 0.2) 97%, rgba(28, 96, 253, 0.2) 97%)',
      ],
    };
  },
  watch: {
    showData(newValue) {
      this.init(newValue);
    },
  },

  mounted() {
    this.init(this.showData);
  },
  methods: {
    init(val) {
      if (this.active === 3) {
        this.topFive = val.map((item, index) => {
        return {
          // this.active === 1 ? item.to :
          name: item.dicKey,
          value: (item.dicValue / val[0].dicValue) * 100,
          id: item.index,
          amount: item.dicValue,
        };
      });
      } else {
      this.topFive = val.map((item, index) => {
        return {
          // this.active === 1 ? item.to :
          name: this.active === 1 ? item.toName : item.fromName,
          value: (item.amount / val[0].amount) * 100,
          id: item.index,
          amount: item.amount,
        };
      });
    }
      // let chainInnovateDTO = val?.chainInnovateDTO
      // from//发起投资的地区省份
      // fromName//发起投资地区名
      // to//被投资的地区省份
      // toName//被投资地区名
      // amount//投资笔数
    },
  },
};
</script>
<style lang="scss" scoped>
@import '@/styles/public.scss';

.topFive {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  padding-top: 15px  ;
}

.topFiveItem {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 25px;
  margin-bottom: 16px;
}
.indexNum {
  width: 25px;
  height: 25px;
  margin-right: 12px;
  .index {
    width: 100%;
    height: 100%;
    text-align: center;
    line-height: 25px;
    font-size: 0.75rem;
    font-weight: normal;
    // @include YouSheBiaoTi28(12px);
    font-family: YouSheBiaoTiHei;
    color: white;
  }

  &::before {
    content: '';
    display: block;
    width: 25px;
    height: 1.65px;
  }
}
.indexNum0 {
  &::before {
    background: #ff4042;
  }
}
.indexNum1 {
  &::before {
    background: #ff6c00;
  }
}
.indexNum2 {
  &::before {
    background: #ffd156;
  }
}
.indexNum3,
.indexNum4 {
  &::before {
    background: #1c60fe;
  }
}
.amount{
  width:60px;
  margin-left: 10px;
  text-align: right;
  @include Puhuiti(12px);
}
.mainView {

  flex: 1;
}
.title {
  // 超出一行点点点
  overflow: hidden;
  text-overflow: ellipsis;
  padding-bottom: 4px;
  @include Puhuiti(12px,#C9D2DD);
}

.topFiveSquare {
  width: 100%;
  height: 6px;
  background: rgba(54, 83, 113, 0.35);
  // border-radius: 3px;

  .topFiveBg {
    // border-radius: 3px;
    height: 6px;
    background: linear-gradient(270deg, #1C5FFB 0%, rgba(28, 96, 254, 0.0001) 100%);
  }
}

@media screen and (max-width: 1450px) {
  .topFive {
    padding: 0;

    .topFiveItem {
      height: auto;
    }
  }
}
</style>
<!-- 
<style>
.topFivePop {
  background: #00112dbf !important;
  border: 1px solid #0066ff !important;
  color: #fff !important;
  font-size: 0.7rem !important;
  min-width: 90px !important;
  height: 32px;
  line-height: 32px !important;
  padding: 0 10px !important;
  width: auto !important;
}
</style> -->
