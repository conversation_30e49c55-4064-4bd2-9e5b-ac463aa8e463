<template>
  <div class="screen-main">
    <TitlePoints title="专利细分环节分布TOP5" />
    <div v-if="dataList?.length > 0">
      <div class="progress" :key="item.id" v-for="(item, index) in  dataList ">

        <el-tooltip effect="dark" :content="item.name" placement="top-start">
          <div class="left"> {{ item.name }}</div>
        </el-tooltip>

        <div :class="['right', 'right' + index]" :style="{ width: item.per + '%' }">
          <div class="num">
            {{ item.num }}
          </div>
        </div>
        <div class="num">
        </div>
      </div>
    </div>
    <div v-else class="no-data">
      <NoData />
    </div>
  </div>
</template>

<script>
import TitlePoints from '@/views/overview/components/component/titlePoint.vue'
import NoData from '@/views/overview/components/component/noData'

export default {
  props: {
    showData: {
      type: Object,
      default: null
    },
  },
  components: {
    TitlePoints,
    NoData
  },
  data () {
    return {
      dataList: [{
        name: '对外投资城市TOP5',
        num: '222'
      }]
    }
  },
  // watch: {
  //   "$store.getters.allViewData": {
  //     deep: true,
  //     immediate: true,
  //     handler (val) {
  //       val && this.init(val)
  //     }
  //   },
  // },
  watch: {
    showData (newValue) {
      this.init(newValue)
    }
  },
  mounted () {
    this.init(this.showData)
  },
  methods: {
    init (val) {
      if (val)
      {
        let topPatentCountByIndustryNode = val?.topPatentCountByIndustryNode
        let dataList = topPatentCountByIndustryNode?.map((e, index) => {
          return {
            per: (e.number / topPatentCountByIndustryNode[0].number) * 100,
            num: e.number,
            id: index,
            name: e.industryNodeName
          }
        })
        this.dataList = dataList
        //       number//统计值
        // industryNodeId
        // industryNodeName//节点名
      }
    }
  }
}

</script>
<style scoped lang="scss">
.screen-main {
  padding: 0.625rem;
  width: 100%;
  height: 100%;
}

.no-data {
  width: 100%;
  height: 100%;
  display: flex;
}

.progress {
  width: 100%;
  display: grid;
  grid-template-columns: 30% 50% 20%;
  align-items: center;
  justify-content: space-between;
  padding: 2% 0px 10px 20px;

  .left {
    font-size: 0.88rem;
    font-weight: 500;
    color: #BFC8D7;
    // 超出一行点点点
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

  }

  .right {
    position: relative;
    height: 4px;
    border-radius: 1px;

    &::after {
      content: "";
      display: inline-block;
      width: 50px;
      height: 45px;
      position: absolute;
      right: -22px;
      top: -20px;
      // margin-bottom: 2px;
      // margin-right: 5px;

    }

    .num {
      position: absolute;
      left: calc(100% + 15px);

      font-size: 1.13rem;
      font-family: DINPro;
      font-weight: bold;
      color: #FFFFFF;
      line-height: 4px;
    }
  }

  .right0 {
    background: linear-gradient(90deg, #2b3054, #C1A9F9 100%);

    &::after {
      background: url('~@/assets/screen/new/icon11.png') center/cover no-repeat;
    }
  }

  .right1 {
    background: linear-gradient(90deg, #353c46, #F1D892 100%);

    &::after {
      background: url('~@/assets/screen/new/icon12.png') center/cover no-repeat;
    }
  }

  .right2 {
    background: linear-gradient(90deg, #1b414e, #66FFD2 100%);

    &::after {
      background: url('~@/assets/screen/new/icon13.png') center/cover no-repeat;
    }
  }

  .right3,
  .right4 {
    background: linear-gradient(90deg, #1c3457, #66AAFF 100%);

    &::after {
      background: url('~@/assets/screen/new/icon13.png') center/cover no-repeat;
    }
  }
}

@media screen and (max-width: 1450px) {
  .progress {
    padding: 0 0px 10px 20px;
  }
}
</style>