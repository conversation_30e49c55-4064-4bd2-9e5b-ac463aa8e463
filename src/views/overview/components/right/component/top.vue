<template>
  <div class="dataList">
    <div
      v-for="(item, index) in dataList"
      :key="item.id"
      class="itemList"
    >
      <div :class="['icon', 'icon' + index]" />
      <div class="content">
        <div class="text">
          {{ item.name }}
        </div>
        <div class="num">
          <span class="number">{{ item.num }}</span>
          <span class="int">{{ item.int }}</span><span class="plus">{{ item.plus }}</span>
        </div>
      </div>
    </div>
    <div />
  </div>
</template>
<script>
export default {
  props: {
    showData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      dataList: [],
      // 本地 / 全部切换
      selNum: 0,
      // 企业类型
      selTypeList: 4,
      // 本地 / 全部切换
      tab: 4,
      // 全国本地企业切换
      showLocality: false,
      // 全国本地企业切换
      elements: null,
    };
  },
  // watch: {
  //   "$store.getters.allViewData": {
  //     deep: true,
  //     immediate: true,
  //     handler (val) {
  //       val && this.init(val)
  //     }
  //   },
  // },
  watch: {
    showData(newValue) {
      this.init(newValue);
    },
  },
  mounted() {
    this.init(this.showData);
  },
  methods: {
    init(val) {
      if (val) {
        let {
          patentEnterpriseCount,
          patentEnterpriseYearCount,
          patentCount,
          patentYearIncrPercent,
        } = val;
        // patentEnterpriseCount//拥有专利企业
        // patentEnterpriseYearCount//今年首次申请专利的企业
        // patentCount//产业专利总量
        // patentYearIncrPercent//今年累积新增
        this.dataList = [
          {
            id: '1',
            name: '专利企业',
            int: '家',
            num: patentEnterpriseCount || 0,
            plus:
              patentEnterpriseYearCount && patentEnterpriseYearCount >= 0
                ? `+${patentEnterpriseYearCount}`
                : patentEnterpriseYearCount,
          },
          {
            id: '2',
            name: '专利总量',
            int: '个',
            num: patentCount || 0,
            plus:
              patentYearIncrPercent && patentYearIncrPercent >= 0
                ? `+${patentYearIncrPercent}%`
                : `${patentYearIncrPercent}%`,
          },
        ];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.dataList {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  padding-left: 15px;
  height: 100%;
  /* border-bottom: 1px solid #eee; */
}

.itemList {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  width: 50%;
  /* border-bottom: 1px solid #eee; */
}

.icon {
  width: 35%;
  height: 100%;
  background-size: 89% 100%;
}

.icon0 {
  background: url('~@/assets/screen/new/icon9.webp') center/contain no-repeat;
}

.icon1 {
  background: url('~@/assets/screen/new/icon10.webp') center/contain no-repeat;
}

.content {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  width: 65%;
  // height: 70%;
  height: 55%;
  padding-left: 15px;

  .text {
    width: 100%;
    margin-left: -5px;
    font-size: 14px;
    // font-family: PingFang SC;
    // font-weight: 400;
    color: #AEB9C0;
  font-family: puhuiti;

    // line-height: 33px;
  }

  .num {
    margin-left: -5px;

    font-family: DINPro;
    font-weight: bold;
    color: #ffffff;
    display: flex;
    flex-wrap: nowrap;
    align-items: baseline;
    // line-height: 33px;
    .number {
      @include YouSheBiaoTi28(1.38rem);
    }
    .int {
      padding-left: 10px;

      font-size: 0.75rem;
      // font-family: PingFang SC;
      // font-weight: 400;
      color: #cad3de;
      // line-height: 33px;
    }

    .plus {
      padding-left: 10px;

      font-size: 1.13rem;
      font-family: DINPro;
      font-weight: 500;
      color: #46e896;
      line-height: 38px;
      text-shadow: 0px 2px 8px rgba(5, 28, 55, 0.42);

      background: linear-gradient(
        0deg,
        #31ffaf 33.8623046875%,
        #effcfe 87.9638671875%
      );
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}

@media screen and (max-width: 1450px) {
  .content {
    padding-left: 5px;
  }
}
</style>
