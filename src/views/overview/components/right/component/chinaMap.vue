<template>
  <div
    ref="chinaMapRef"
    class="chinaMap"
  >
    <dv-flyline-chart-enhanced
      v-if="config"
      :config="config"
      :dev="true"
      class="dv-flyline-chart"
    />
  </div>
</template>
<script>
// import bgImgSrc from '@/assets/idicc/china.png';
import { mapPoints } from '@/views/overview/components/component/toolTips'
export default {
  name: 'ChinaMap',
  props: {
    showData: {
      type: Array,
      default: ()=>{}
    },
    active: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      config: null,
      obj: {
        width: '',
        height: '',
        // backgroundImage: `url(${bgImgSrc})`,
        // backgroundRepeat: 'no-repeat',
        // backgroundSize: '100% 100%'
      },
      // chinaMapRef:null
    }
  },
  watch: {
    showData (newValue) {
      this.init(newValue)
    }
  },

  mounted () {
    this.init(this.showData)
  },
  methods: {
    init (val) {
      // console.log('val', val)
      // let chainInnovateDTO = val?.chainInnovateDTO
      //       from//发起投资的地区省份
      // fromName//发起投资地区名
      // to//被投资的地区省份
      // toName//被投资地区名
      // amount//投资笔数
      // this.active  1 对外 加line 2 外来 ，不用不变动 
      // TODO
      // 后端缺centerPoint
      // let points = val.map(e => {
      //   let data = mapPoints[e.from]
      //   return data;
      // })
      // console.log(mapPoints)
      let data = val
      let lines = data?.map(e => {
        let data = {
          source: e.from,
          target: e.to,
        }
        return data;
      })
      if (lines)
      {
        this.getMap(lines)
      }

    },
    getMap (lines) {
      this.obj.width = this.$refs.chinaMapRef.clientWidth + 'px'
      this.obj.height = this.$refs.chinaMapRef.clientHeight + 'px'
      let config = {
        // centerPoint: [0.48, 0.35],
        points: mapPoints,
        lines: lines,
        k: 0.5,
        lineWidth: 0.5,//	飞线宽度	Number	---	1
        flylineColo: 'rgba(255, 192, 0, 1)',//飞线颜色
        // orbitColor: '',//轨迹颜色

      };
      this.config = config;
    }
  }

}
</script>
<style scoped>
.chinaMap {
  /* width: 17.5rem;
    height: 11.94rem; */
  width: 15rem;
  height: 13rem;
  background: url('~@/assets/screen/new/map.png') no-repeat;
  background-size: 100% 100%;
  /* background: url('https://gd-hbimg.huaban.com/af1dc36196ec2cb9f6407b872a93cb00f72f496f23a04-sYig3d_fw1200'); */
  /* background-size: 100%; */
}

.dv-flyline-chart {
  width: 100%;
  height: 100%;
}
</style>