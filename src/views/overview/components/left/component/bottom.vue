<template>
  <div class="keyEnterpriseBody">
    <div
      v-if="showValues && showValues.length > 0"
      class="valuesList"
    >
      <div
        v-for="items in showValues"
        :key="items.dicKey"
        class="valuesCard"
        @click="checkCard(items)"
      >
        <div class="dicValue">
          {{ items.dicValue }}
        </div>
        <div class="dicKey">
          {{ items.dicKey }}
        </div>

        <!-- {{ items.unit }} -->
      </div>
    </div>
    <!-- v-if="showValues && showValues.length > 0"  -->
    <!-- <div
      id="keyEnterprise"
      class="keyEnterprise"
    /> -->
    <!-- <div   class="keyEnterprise">

    </div> -->
    <!-- v-if="!(showValues && showValues.length > 0)" -->
    <!--  -->
    <div
      v-else
      class="no-data"
    >
      <NoData />
    </div>

    <EnterpriseList
      v-if="isShowList"
      show-list="default"
      :api-url="apiUrl"
      area-code=""
      :nodemessage="industryType"
      :state="state"
      @closeList="closeList"
    />
  </div>
</template>

<script>
import * as echarts from 'echarts';
import EnterpriseList from '@/views/count/admin/Enterpriselist.vue';
import {
  toolTips,
  toolTipAxis,
} from '@/views/overview/components/component/toolTips';
import NoData from '@/views/overview/components/component/noData';
export default {
  components: {
    EnterpriseList,
    NoData,
  },
  props: {
    showData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      isShowList: false,
      dataList: [],
      showValues: [],
      nodemessage: '产业重点企业情况',
      apiUrl: 'overViewEnterpriseList',
      industryType: '',
      state: 1,
    };
  },
  watch: {
    showData(newValue) {
      this.init(newValue);
    },
  },
  mounted() {
    this.init(this.showData);
  },
  methods: {
    init(val) {
      if (val) {
        let industryEnterpriseDTO = val;
        let { values } = industryEnterpriseDTO;
        // console.log(values, 'values');
        this.showValues = values;
        // let data = []
        // values.reverse()
        //   .map((item) => {
        //   if (item.dicValue > 0)
        //   {
        //     data.push(item)
        //   }
        // })
        // if (data && data.length > 0)
        // {
        //   this.showValues = values

        //   // let xNames = []
        //   // let xValue = values.map((item) => {
        //   //   xNames.push(item.dicKey)
        //   //   return {
        //   //     value: item.dicValue
        //   //   }
        //   // })
        //   // this.keyEnterprise(xNames, xValue)
        // }
      }
    },
    checkCard(params) {
 
          this.industryType = params.dicKey
          this.isShowList = true
          // 展示弹窗
 
    },
    closeList() {
      this.isShowList = false;
    },
    // keyEnterprise (xNames, xValue) {
    //   let chartDom = document.getElementById('keyEnterprise');
    //   if (chartDom)
    //   {
    //     let myChart = echarts.init(chartDom);
    //     let options = {
    //       tooltip: toolTips,
    //       grid: {
    //         left: '4%',
    //         right: '4%',
    //         bottom: '5%',
    //         top: '5%',
    //         containLabel: true, // containLabel  x/y轴内容 是否在内部
    //       },
    //       yAxis: {
    //         // 倾斜
    //         axisLabel: {
    //           interval: 0,
    //           rotate: 0,
    //           textStyle: {
    //             color: "#BFC8D7",
    //             fontSize: "9",
    //             itemSize: "",
    //             marginLeft: "2px",
    //           },
    //         },
    //         data: xNames,
    //         // ['上市公司', '新三板', '高新技术', '专精特新', '小巨人', '瞪羚企业',],
    //         type: "category",
    //         boundaryGap: true,
    //         axisLine: {
    //           lineStyle: {
    //             color: "#57617B",
    //             fontSize: "20px",
    //           },
    //         },
    //         axisTick: {
    //           show: false // 隐藏 x 轴刻度线
    //         }
    //       },
    //       xAxis : [
    //         {
    //           name: "单位(家)",
    //           nameLocation: 'end',
    //           type: "value",
    //           nameTextStyle: {
    //             color: "#BFC8D7", //颜色
    //             opacity: 0.5
    //           },
    //           splitLine: {
    //             show: false,
    //             lineStyle: {
    //               color: '#ccc',
    //               width: 1,
    //               opacity: 0.2,
    //               type: 'dashed'
    //             }
    //           },
    //           axisLine: {
    //             show: true,
    //             lineStyle: {
    //               color: "#fff",
    //               opacity: 0.2,
    //             },
    //           },
    //           axisLabel: {
    //             formatter: "{value} ",
    //             color: "#EFF7FF",
    //             opacity: 0.8
    //           },
    //         },
    //       ],
    //       // markLine: {
    //       //   z: -1
    //       // },
    //       // animationEasing: 'elasticOut',

    //       series: [
    //         {
    //           type: 'bar',
    //           name: '',
    //           label: {
    //             show: true,
    //             position: 'right',
    //             formatter: '{c}',
    //             fontSize: 10,
    //             color: '#EFF7FF',
    //           },

    //           // symbol:
    //           //   "path://M59.634,146.471 C30.538,123.924 31.892,1.996 31.892,1.996 C31.892,1.996 33.804,122.724 2.468,146.471 C13.789,146.471 50.486,146.471 59.634,146.471 Z",

    //           itemStyle: {
    //             borderWidth: 1,
    //             // 背景颜色
    //             color:"#3E6AC9",
    //               // 'rgba(66,135,255,0.4)',
    //           },
    //           barMinHeight: 2,
    //           data: xValue,
    //           z: 10,
    //         },

    //       ]
    //     };

    //     myChart.off('click'); // 这里很重要！！！
    //     let that = this
    //     myChart.on("click", function (params) {
    //       that.industryType = params.name
    //       that.isShowList = true
    //       // 展示弹窗
    //     })
    //     options && myChart.setOption(options);
    //   }
    // },
  },
};
</script>
<style lang="scss" scoped>
@import '@/styles/public.scss';

.no-data {
  display: flex;
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
}

.keyEnterprise,
.keyEnterpriseBody {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 16px 16px 0px 24px;
}
.valuesList {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  border-radius: 8px;
  background-color: rgba(2, 48, 91, 0.4); /* 卡片背景色 */
  padding: 10px 16px  ;
  &::before {
    content: '';
    position: absolute;
    margin: 16px 16px 0px 24px;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 8px;
    padding: 1px; /* 控制边框宽度 */
    opacity: 0.4;
    background: linear-gradient(180deg, #00ccff 0%, #00489a 100%);
    -webkit-mask: linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none; /* 确保不会影响卡片内容的点击 */
  }
}
.valuesCard {
  width: 50%;
  position: relative;
  text-align: center;
  display: flex;
    align-items: center;
  flex-direction: column;
  justify-content: center; 
  flex-wrap: wrap;
  cursor: pointer;
  // border-bottom: 1px solid;
   &:not(:nth-last-child(1)):not(:nth-last-child(2))
 {
    &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background:center / contain no-repeat  url(https://static.idicc.cn/cdn/pangu/assets/screen/newScreen/line.webp);
  }
  }

  .dicKey {
    width: 100%;
    @include Puhuiti(14px, #F4F4F4,400);
  }
  .dicValue {
    width: 100%;

    @include YouSheBiaoTi28(24px);
  }
}
</style>
