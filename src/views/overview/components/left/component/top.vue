<template>
  <div class="dataList">
    <div
      v-for="(item, index) in dataList"
      :key="item.id"
      class="itemList"
    >
      <div :class="['icon', 'icon' + index]" />
      <div class="content">
        <div class="text">
          {{ item.name }}
        </div>
        <div class="num">
          <span class="number"> {{ item.num }}</span><span class="int">{{ item.int }}</span>
        </div>
      </div>
    </div>
    <div />
  </div>
</template>
<script>
export default {
  props: {
    showData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      dataList: [],
      // 本地 / 全部切换
      selNum: 0,
      // 企业类型
      selTypeList: 4,
      // 本地 / 全部切换
      tab: 4,
      // 全国本地企业切换
      showLocality: false,
      // 全国本地企业切换
      elements: null,
    };
  },
  watch: {
    showData(newValue) {
      this.init(newValue);
    },
  },
  mounted() {
    this.init(this.showData);
  },
  methods: {
    init(val) {
      if (val) {
        let localIndustryBaseDTO = val;
        let {
          enterpriseCount,
          scaleEnterpriseCount,
          researchInstitutionCount,
          collegeCount,
          talentCount,
          fundCount,
        } = localIndustryBaseDTO;
        this.dataList = [
          {
            id: "1",
            name: "企业总数",
            int: "家",
            num: enterpriseCount || 0,
          },
          {
            id: "2",
            name: "规上企业",
            int: "家",
            num: scaleEnterpriseCount || 0,
          },
          {
            id: "3",
            name: "研发机构",
            int: "家",
            num: researchInstitutionCount || 0,
          },
          {
            id: "4",
            name: "高等院校",
            int: "家",
            num: collegeCount || 0,
          },
          {
            id: "5",
            name: "产业人才",
            int: "人",
            num: talentCount || 0,
          },
          {
            id: "6",
            name: "产业基金",
            int: "家",
            num: fundCount || 0,
          },
        ];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/public.scss";

.dataList {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  padding: 36px 10px 0px 10px;
  height: 100%;
  /* border-bottom: 1px solid #eee; */
}

.itemList {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 30%;
  width: 50%;
  /* border-bottom: 1px solid #eee; */
}

.icon {
  width: 50%;
  height: 100%;
  background: #f00;
}

.icon0 {
  background: url("~@/assets/screen/new/icon0.png") center/cover no-repeat;
}

.icon1 {
  background: url("~@/assets/screen/new/icon1.png") center/cover no-repeat;
}

.icon2 {
  background: url("~@/assets/screen/new/icon2.png") center/cover no-repeat;
}

.icon3 {
  background: url("~@/assets/screen/new/icon3.png") center/cover no-repeat;
}

.icon4 {
  background: url("~@/assets/screen/new/icon4.png") center/cover no-repeat;
}

.icon5 {
  background: url("~@/assets/screen/new/icon5.png") center/cover no-repeat;
}

.content {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  width: 50%;
  height: 70%;

  .text {
    width: 100%;
    margin-left: -5px;
    font-size: 0.88rem;
    color: #c9d2dd;
    // font-family: PingFangSC;
  }

  .num {
    margin-left: -5px;
    font-size: 1.38rem;
    // font-family: PingFangSC;
    font-weight: bold;

    .number {
      @include YouSheBiaoTi28();
    }
    .int {
      padding-left: 10px;

      font-size: 0.75rem;
      // font-family: PingFangSC;
      // font-weight: 400;
      color: #cad3de;
      // line-height: 33px;
    }
  }
}
</style>
