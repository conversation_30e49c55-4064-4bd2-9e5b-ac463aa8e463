<template >
  <div class="dataList">
    <div v-if="dataList && dataList.length > 0" class="dataListIf">
      <div :key="item.name" v-for="(item, index) in dataList" class="itemList">

        <div :class="['icon', 'icon' + item.type]">
        </div>
        <div class="content content1">
          <div class="text text1">
            {{ item.name }}
          </div>
          <div class="num">
            {{ item.value }}<span class="int">{{ item.unit }}</span>
          </div>
        </div>
        <div class="content content2">
          <div class="text text2">
            {{ ['全国', '全省', '全市'][item?.rankLevel] }}排名
          </div>
          <div class="num">
            NO.{{ item.rank }}
          </div>
        </div>
      </div>
    </div>
    <div v-else class="no-data">
      <NoData />
    </div>

  </div>
</template>
<script>
import NoData from '@/views/overview/components/component/noData'

export default {
  components: {
    NoData
  },
  props: {
    showData: {
      type: Object,
      default: null
    },
  },
  data () {
    return {
      dataList: [],
      // 本地 / 全部切换
      selNum: 0,
      // 企业类型
      selTypeList: 4,
      // 本地 / 全部切换
      tab: 4,
      // 全国本地企业切换
      showLocality: false,
      // 全国本地企业切换
      elements: null,

    }
  },
  watch: {
    showData (newValue) {
      this.init(newValue)
    }
  },
  mounted () {
    this.init(this.showData)
  },
  methods: {
    init (val) {
      if (val)
      {
        let industryResourceDTO = val
        let { keyResources } = industryResourceDTO

        this.dataList = keyResources?.slice(0, 2)
        let imgName = ['平均日照', '从业人员', '专利数量', '资本投入', '电力资源', '民用汽车保有量']

        this.dataList?.forEach(e => {
          e.type = imgName.indexOf(e.name) > -1 ? imgName.indexOf(e.name) : 6
        })

        // [{
        //   id: '1',
        //   name: '劳动力人口',
        //   name2: '全省排名',
        //   int: '万人',
        //   num: '123',
        //   no: "NO.9"
        // }, {
        //   id: '2',
        //   name: '光照时长',
        //   name2: '全省排名',
        //   int: '天/年',
        //   num: '1223',
        //   no: "NO.91"
        // },
        // ]
      }
    }
  }
}

</script>


<style lang="scss" scoped>
.dataListIf {
  width: 100%;
  // height: 100%;
  // overflow-y: scroll;
}

.dataList {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  padding: 10px;
  height: 100%;
  overflow-y: scroll;
  /* border-bottom: 1px solid #eee; */
}

.dataList::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px;
  /* 对应横向滚动条的宽度 */
  height: 0px;
}

/* 滚动条上的滚动滑块 */
.dataList::-webkit-scrollbar-thumb {
  background-color: rgba(84, 112, 198, 0.3);
  border-radius: 32px;
}

/* 滚动条轨道 */
.dataList::-webkit-scrollbar-track {
  display: none;
}

.itemList {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50%;
  width: 100%;
  padding: 10px;
  /* border-bottom: 1px solid #eee; */

}

.icon {
  width: 20%;
  height: 4.63rem;

}

// ['平均日照','产业链从业人员数量','专利数量','资本投入']
.icon0 {
  background: url('~@/assets/screen/new/平均日照.png') center/cover no-repeat;
}

.icon1 {
  background: url('~@/assets/screen/new/产业链从业人员数量.png') center/cover no-repeat;
}

.icon2 {
  background: url('~@/assets/screen/new/专利数量.png') center/cover no-repeat;
}

.icon3 {
  background: url('~@/assets/screen/new/资本投入.png') center/cover no-repeat;
}

.icon4 {
  background: url('~@/assets/screen/new/电力资源.png') center/cover no-repeat;
}

.icon5 {
  background: url('~@/assets/screen/new/民用汽车保有量.png') center/cover no-repeat;
}

.icon6 {
  background: url('~@/assets/screen/new/默认.png') center/cover no-repeat;
}

.content1 {
  padding-left: 3%;
  width: 46%;
}

.content2 {
  padding-left: 6%;
  width: 38%;
}

.content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  align-content: center;

  height: 100%;


  .text1 {
    background: url('~@/assets/screen/new/text_bg.png') center/cover no-repeat;
  }

  .text2 {
    background: url('~@/assets/screen/new/text_bg2.png') center/cover no-repeat;
  }

  .text {
    width: 100%;
    height: 1.5625rem;
    line-height: 1.5625rem;
    font-size: 0.88rem;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;


    &::before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 5px;
      background: linear-gradient(0deg, #FFAE5E 0%, #FFFFFF 100%);
      border-radius: 50%;
      position: relative;
      bottom: 2px;

    }

  }

  .num {

    font-size: 1.38rem;
    font-family: DINPro;
    font-weight: bold;
    color: #FFFFFF;
    padding-top: 0.6rem;
    display: flex;
    justify-content: center;
    width: 100%;
    align-items: baseline;

    .int {
      padding-left: 10px;

      font-size: 0.75rem;
      // font-family: PingFang SC;
      // font-weight: 400;
      color: #CAD3DE;
      // line-height: 33px;
    }
  }
}

@media screen and (max-width: 1450px) {
  .content .text::before {
    bottom: 0px;
  }
}
</style>