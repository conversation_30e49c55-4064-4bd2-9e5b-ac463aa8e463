<template>
  <div class="content">
    <div class="containerTop">
      <Titles title="本地产业基础" />
      <div class="top">
        <TopView :show-data="topShowData" />
      </div>
      <!-- <Titles title="重点产业资源情况" />
      <div class="middle">
        <MiddleView :showData="middleShowData" />
      </div> -->
    </div>
    <Titles title="产业重点企业情况" />
    <div class="bottom">
      <BottomView :show-data="bottomShowData" />
    </div>
  </div>
</template>
<script>
import Titles from "../component/titles.vue";
import TopView from "./component/top.vue";
// import MiddleView from './component/middle.vue';
import BottomView from "./component/bottom.vue";
export default {
  components: {
    Titles,
    TopView,
    BottomView,
    // MiddleView
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      allViewData: this.$store.getters.allViewData,
      middleShowData: null,
      topShowData: null,
      bottomShowData: null,
    };
  },
  watch: {
    data(val) {
      this.init(val);
    },
  },

  mounted() {
    this.init(this.data);
  },
  methods: {
    init(val) {
      this.allViewData = val;
      let { industryResourceDTO, industryEnterpriseDTO, localIndustryBaseDTO } =
        val;
      // console.log('allViewData', val)
      this.topShowData = localIndustryBaseDTO;
      this.middleShowData = industryResourceDTO;
      this.bottomShowData = industryEnterpriseDTO;
      // console.log('topShowData', this.topShowData)
      // console.log('middleShowData', val)
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  height: 100%;
  padding: 20px 20px 40px 20px;
  background: center/cover no-repeat
    url("https://static.idicc.cn/cdn/pangu/left_bg.png?x-oss-process=image/quality,q_85/format,webp");
  background-size: 95%;
}

.top,
.middle,
.bottom {
  // background: rgba(128, 120, 121, 0.547);
  color: white;
}

.containerTop {
  // height: calc(70% - 40px);
  height: 42%;

  .top {
    height: 85%;
    // padding: 20px 30px;
  }

  // .middle {
  //   height: calc(50% - 80px);
  //   // padding: 10px 30px;
  // }
}

.bottom {
  height: calc(59% - 50px);
}

@media screen and (max-width: 1450px) {
  .content {
    padding: 10px 10px 10px 0;
  }
}
</style>