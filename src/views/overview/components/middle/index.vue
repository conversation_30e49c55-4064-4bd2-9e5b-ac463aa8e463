<template>
  <div class="content">
    <div class="top">
      <div class="middleTopDataList">
        <div
          v-for="(i, index) in middleTopDataListItem.slice(0, 2)"
          :key="i.id"
          class="middleTopDataListItem"
        >
          <div :class="['text', index === 0 ? 'autoWith' : '']">
            <span> {{ i.name }}</span>
            <el-tooltip
              v-if="index === 0"
              :content="i.toolTip"
              placement="top"
            >
              <i
                class="el-icon-warning-outline"
                style="color: rgba(255, 255, 255, 0.4)"
              />
            </el-tooltip>
          </div>
          <div class="value">
            {{ i.value }}
          </div>
        </div>
        <div class="divider" />
        <div
          v-for="(i, index) in middleTopDataListItem.slice(2, 4)"
          :key="i.id"
          class="middleTopDataListItem"
        >
          <div :class="['text', index === 0 ? 'autoWith' : '']">
            <span> {{ i.name }}</span>
            <el-tooltip
              v-if="index === 0"
              :content="i.toolTip"
              placement="top"
            >
              <i
                class="el-icon-warning-outline"
                style="color: rgba(255, 255, 255, 0.4)"
              />
            </el-tooltip>
          </div>
          <div class="value">
            {{ i.value }}
          </div>
        </div>
      </div>

      <div
        ref="screenRef"
        class="topMap"
      >
        <div
          class="right-menu-item hover-effect"
          @click="isFullscreenFn"
        >
          全屏
          <svg-icon icon-class="fullscreen" />
        </div>
        <!-- <ScreenFull id="screenfull" class="right-menu-item hover-effect" :elements="elements" :show-text="true"
          @isFullscreen="isFullscreenFn" /> -->
        <div class="middleDataList">
          <div
            v-for="i in dataList"
            :key="i.id"
            :class="['dataItem', active === i.id ? 'active' : '']"
            @click="changeVisit(i)"
          >
            <div class="text">
              {{ i.name }}
            </div>
          </div>
        </div>
        <MapMain
          ref="mapRef"
          :map-show-data="mapShowData"
        />
      </div>
    </div>

    <Titles
      title="产业发展特征"
      bg-large
    />
    <div class="developBottom">
      <div class="bottomLeft">
        <TitlePoints title="近5年本地产业企业增长趋势" />
        <!-- v-if="registerTrendyChart && registerTrendyChart.length > 0"  -->
        <div
          id="growthTrend"
          class="growthTrend"
        />
        <div
          v-if="!(registerTrendyChart && registerTrendyChart.length > 0)"
          class="no-data"
        >
          <NoData />
        </div>
      </div>
      <div class="bottomRight">
        <TitlePoints title="本地产业企业环节分布" />
        <!-- 已使用 PercentagePlot 组件代替，无需再初始化 localIndustry -->
        <PercentagePlot :data-list="percentagePlot" />
        <div
          v-if="!(industryNodeCount && industryNodeCount.length > 0)"
          class="no-data"
        >
          <NoData />
        </div>
      </div>
    </div>
    <MapDialog
      v-if="isFullscreen"
      @closeList="isFullscreenFn"
      @showMapInfo="showMapInfo"
    >
      <div
        slot="content"
        class="topMapCopy topMap"
      >
        <div class="middleDataList">
          <div
            v-for="i in dataList"
            :key="i.id"
            :class="['dataItem', active === i.id ? 'active' : '']"
            @click="changeVisit(i)"
          >
            <div class="text">
              {{ i.name }}
            </div>
          </div>
        </div>
        <MapMain
          ref="mapCopyRef"
          :map-show-data="mapShowData"
          :is-dialog="isDialog"
        />
      </div>
    </MapDialog>
  </div>
</template>
<script>
import * as echarts from "echarts";

import MapMain from "../component/map.vue";
import Titles from "../component/titles.vue";
import TitlePoints from "@/views/overview/components/component/titlePoint.vue";
import {
  toolTips,
  toolTipAxis,
  tooltipShadow,
  colors,
} from "@/views/overview/components/component/toolTips";
import NoData from "@/views/overview/components/component/noData";
// import ScreenFull from '@/components/Screenfull';
import MapDialog from "../component/dialogs.vue";
import PercentagePlot from "@/components/PercentagePlot";
export default {
  components: {
    MapMain,
    Titles,
    TitlePoints,
    NoData,
    // ScreenFull,
    MapDialog,
    PercentagePlot,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      elements: null,
      isFullscreen: false,
      isDialog: false,
      name: "middle",
      middleTopDataListItem: [],
      dataList: [
        {
          id: 5,
          name: "重点企业",
          showList: "default",
          apiUrl: "overViewEnterpriseList",
        },
        //  {
        //   id: 4,
        //   name: "产业聚集区",
        //   showList: 'industrialFund',
        //   apiUrl: ''
        // },
        {
          id: 3,
          name: "研发机构",
          showList: "industrialFund",
          apiUrl: "",
        },
        {
          id: 1,
          name: "高等院校",
          showList: "industrialFund",
          apiUrl: "",
        },
        {
          id: 2,
          name: "产业基金",
          showList: "industrialFund",
          apiUrl: "",
        },
      ],
      active: 5,
      // roundBg: "https://static.idicc.cn/cdn/pangu/assets/screen/new/round.png", //你的图片地址
      mapShowData: null,
      industryNodeCount: [],
      registerTrendyChart: [],
      percentagePlot: {
        modelName: "",
        values: [],
      },
    };
  },
  watch: {
    data(val) {
      this.init(val);
    },
  },

  mounted() {
    this.elements = this.$refs.screenRef;
    this.init(this.data);
  },

  // 添加 beforeDestroy 钩子清理图表实例，防止内存泄漏
  beforeDestroy() {
    const chartDom = document.getElementById("growthTrend");
    if (chartDom) {
      echarts.getInstanceByDom(chartDom)?.dispose();
    }

    const chartDom2 = document.getElementById("localIndustry");
    if (chartDom2) {
      echarts.getInstanceByDom(chartDom2)?.dispose();
    }
  },

  methods: {
    showMapInfo() {
      this.changeVisit(this.dataList[0]);
    },
    isFullscreenFn(isFullscreen) {
      if (!this.isFullscreen) {
        this.isDialog = true;
        //
      }
      this.isFullscreen = !this.isFullscreen;
    },
    init(newData) {
      this.changeVisit(this.dataList[0]);
      let val = this.allData || newData;
      let chainDevelopFeatureDTO = val?.chainDevelopFeatureDTO; //产业发展特征
      // 近5年新增企业,产业链企业环节分布情况
      let { registerTrendyChart, industryNodeCount } =
        chainDevelopFeatureDTO || {};
      this.setTopData(val);
      let data = [];

      // 添加空值检查
      if (registerTrendyChart && registerTrendyChart.length) {
        registerTrendyChart.map((item) => {
          if (item.enterpriseNum > 0) {
            data.push(item);
          }
        });
      }

      // 确保数据存在再初始化图表
      if (data && data.length > 0) {
        this.registerTrendyChart = registerTrendyChart;
        this.growthTrend(registerTrendyChart);
      }
      this.industryNodeCount = industryNodeCount;
      if (industryNodeCount && industryNodeCount.length > 0) {
        this.percentagePlot = {
          modelName: "",
          values: industryNodeCount.map((e) => {
            const { allNumber, industryNodeId, industryNodeName, number } = e;
            return {
              dicValue: number,
              dicValueCountry: allNumber,
              dicKey: industryNodeName,
              industryNodeId,
            };
          }),
        };

        // 由于 DOM 已注释，不再调用此方法
        // this.localIndustry(industryNodeCount);
      }
    },
    setTopData(val) {
      let keyFeatureDTO = val?.keyFeatureDTO;
      let { outputValue, scaleIndustrialAdded } = keyFeatureDTO;
      // totalOutputValue//总产值
      // outputYearOnYearGrowth//同比增速
      // scaleIndustrialAddedValue//规上工业增加值
      // scaleYearOnYearGrowth//规上工业增加值同比增速
      this.middleTopDataListItem = [
        {
          id: 1,
          name: "总产值（亿元）",
          value: outputValue?.value || 0,
          toolTip: `数据截止至${outputValue?.year || ""}年Q${
            outputValue?.season || ""
          }（年度范围内）`,
          // 总产值：
        },
        {
          id: 2,
          name: "同比增速(%)",
          value: outputValue?.yearGrowth || 0,
          toolTip: ``,
        },
        {
          // 规上工业增加值：
          id: 3,
          name: "规上工业增加值（亿元）",
          value: scaleIndustrialAdded?.value || 0,
          toolTip: `数据截止至${scaleIndustrialAdded?.year || ""}年${
            scaleIndustrialAdded?.month || ""
          }月（年度范围内）`,
        },
        {
          id: 4,
          name: "同比增速(%)",
          value: scaleIndustrialAdded?.yearGrowth || 0,
          toolTip: ``,
        },
      ];
    },
    growthTrend(data) {
      // 使用 $nextTick 确保 DOM 已渲染
      this.$nextTick(() => {
        let years = data.map((e) => e.year);
        let enterpriseNum = data.map((e, index) => e.enterpriseNum || 0);
        let chartDom = document.getElementById("growthTrend");

        // 检查 DOM 是否存在
        if (!chartDom) {
          console.error("growthTrend DOM element not found");
          return;
        }

        let myChart = echarts.init(chartDom);
        let options = {
          // color: ['rgba(205, 208, 255, 1)'],
          title: {
            text: "",
          },
          tooltip: tooltipShadow,

          grid: {
            left: "3%",
            right: "4%",
            bottom: "7%",
            top: "10%",
            containLabel: true,
          },
          xAxis: {
            // 倾斜

            axisLabel: {
              interval: 0,
              rotate: 0,
              textStyle: {
                color: "#C9D2DD",
                fontSize: "12",
                itemSize: "",
                marginLeft: "2px",
              },
            },
            data: years,
            type: "category",
            boundaryGap: true,
            axisLine: {
              show: false,
              lineStyle: {
                color: "#57617B",
                fontSize: "20px",
              },
            },
            axisTick: {
              show: false, // 隐藏 x 轴刻度线
            },
          },
          yAxis: [
            {
              name: "单位(家)",
              nameLocation: "start",
              type: "value",
              nameTextStyle: {
                fontSize: "9",
                color: "#C9D2DD", //颜色
                opacity: 0.9,
                padding: [3, 0, 0, 0], //间距分别是 上 右 下 左
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: "#ccc",
                  width: 1,
                  opacity: 0.2,
                  type: "dashed",
                },
              },
              axisLine: {
                lineStyle: {
                  color: "#fff",
                  opacity: 0.2,
                },
              },
              axisLabel: {
                formatter: "{value} ",
                color: "#C9D2DD",
                opacity: 0.8,
                textStyle: {
                  color: "#C9D2DD",
                  fontSize: "12",
                  itemSize: "",
                  marginLeft: "2px",
                },
              },
            },
          ],
          series: [
            {
              name: "",
              type: "line",
              stack: "Total",
              label: {
                show: true,
                position: "top",
                textStyle: {
                  //数值样式
                  fontWeight: 500,
                  color: "#fff",
                  fontSize: 12,
                },
              },
              // 设置折线上数据点的样式
              itemStyle: {
                color: "#00D5FF", // 点的颜色
                // borderColor: '#00D5FF', // 点的边框颜色
                // borderWidth: 1, // 点的边框宽度
                // shadowColor: 'rgba(0, 213, 255, 0.5)', // 阴影颜色
                // shadowBlur: 2 // 阴影大小
              },
              symbolSize: 3,
              showSymbol: true,
              emphasis: {
                focus: "series",
              },
              smooth: false,
              lineStyle: {
                width: 2,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0.5, color: "#00D5FF" },
                  { offset: 0.5, color: "#00D5FF" },
                  { offset: 0.5, color: "#00d5ff" },
                  // { offset: 0.5, color: '#00d5ff' },
                  // { offset: 1, color: 'rgba(255, 255, 255, 0)' }
                ]),
              },

              areaStyle: {
                opacity: 0.8,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#41ACFF",
                  },
                  {
                    offset: 0.2,
                    color: "rgba(0, 209, 255, 0.4)",
                  },
                  {
                    offset: 1,
                    color: "rgba(0, 167, 255, 0.16)",
                  },
                ]),
              },

              data: enterpriseNum,
            },
          ],
        };
        options && myChart.setOption(options);
      });
    },
    localIndustry(industryNodeCount) {
      // 由于 HTML 中注释了 localIndustry 元素，此方法不再需要执行
      // 如果确实需要，请取消注释 HTML 中的 DOM 元素

      // 检查是否需要执行此方法
      let chartDom = document.getElementById("localIndustry");
      if (!chartDom) {
        // console.log('localIndustry DOM element not found, skipping chart initialization');
        return;
      }

      // 使用 $nextTick 确保 DOM 已渲染
      this.$nextTick(() => {
        let myChart = echarts.init(chartDom);
        let option;
        let topData;
        let names = [];
        let data = industryNodeCount?.slice(1)?.map((item) => {
          names.push(item.industryNodeName);
          return {
            value: item.number,
            name: item.industryNodeName,
          };
        });
        const sum = industryNodeCount?.[0]?.number;
        const name = industryNodeCount?.[0]?.industryNodeName;
        option = {
          tooltip: toolTips,
          legend: {
            type: "scroll",
            orient: "vertical",
            pageIconColor: "#2967c1", //翻页箭头颜色
            pageIconInactiveColor: "#0a244c", //翻页（即翻页到头时箭头的颜色）
            pageIconSize: 9, //翻页按钮大小
            // itemWidth: 12, // 设置宽度
            // itemHeight: 12, // 设置高度
            pageTextStyle: {
              color: "#999", //翻页数字颜色
              fontSize: 7,
            }, //翻页数字设置
            right: 2,
            top: 1,
            bottom: 1,
            icon: "circle",
            itemWidth: 6,
            itemHeight: 6,
            textStyle: {
              color: "white",
              fontSize: 9,
              lineHeight: 8,
            },
            data: names,
          },
          title: [
            {
              text: sum,
              top: "38%",
              textAlign: "center",
              left: "28%",
              textStyle: {
                color: "#fff",
                fontSize: 16,
                fontWeight: "600",
                fontFamily: "Source Han Sans CN-Regular, Source Han Sans CN",
                // textBorderWidth: 2,
                // textBorderColor:'red',
                textShadowColor: "#1f7ed4",
                textShadowBlur: 15, // 阴影模糊大小
                textShadowOffsetX: 0, // 阴影水平偏移
                textShadowOffsetY: 0, // 阴影垂直偏移
              },
            },
            {
              text: name,
              top: "54%",
              textAlign: "center",
              left: "28%",
              textStyle: {
                color: "rgba(255, 255, 255, 0.6)",
                fontSize: 12,
                fontWeight: "400",
                fontFamily: "Source Han Sans CN-Regular, Source Han Sans CN",
              },
            },
          ],
          // graphic: {       //图形中间图片
          //   elements: [{
          //     type: 'image',
          //     style: {
          //       image: this.roundBg,
          //       // image: this.img,
          //       width: "90",
          //       height: "90"
          //     },
          //     // left: 'center',
          //     left: '16%',
          //     top: 'center'
          //   },

          //   ]
          // },
          series: [
            {
              name: "1",
              type: "pie",
              center: ["30%", "50%"],
              radius: ["71%", "86%"],
              avoidLabelOverlap: false,
              hoverAnimation: false,
              itemStyle: {
                normal: {
                  borderRadius: 50,
                  show: true, // 显示示例牵引线
                  borderWidth: 1,
                  borderColor: "rgba(255,255,255,0.65)",
                  color: (list) => {
                    // 设置描边宽度
                    var colorList = colors;
                    return new echarts.graphic.LinearGradient(1, 1, 0, 0, [
                      {
                        //左、下、右、上
                        offset: 0,
                        color: colorList[list.dataIndex].colorStart,
                      },
                      {
                        offset: 1,
                        color: colorList[list.dataIndex].colorEnd,
                      },
                    ]);
                  },
                },
              },
              label: {
                show: false,
                color: "#fff",
                formatter: (params) => {
                  return "";
                },
                shadowBlur: 0,
              },
              labelLine: {
                show: false,
              },
              data: data,
            },
            {
              name: "",
              type: "pie",
              center: ["30%", "50%"],
              radius: ["70%", "85%"],
              avoidLabelOverlap: false,
              itemStyle: {
                // borderRadius: 50,
                // borderColor: 'rgba(37, 76, 141, 1)',
                // borderWidth: 2,
                normal: {
                  borderRadius: 50,
                  show: true, // 显示示例牵引线
                  borderWidth: 0,
                  color: (list) => {
                    // 设置描边宽度
                    var colorList = colors;
                    return new echarts.graphic.LinearGradient(1, 1, 0, 0, [
                      {
                        //左、下、右、上
                        offset: 0,
                        color: colorList[list.dataIndex].colorStart,
                      },
                      {
                        offset: 1,
                        color: colorList[list.dataIndex].colorEnd,
                      },
                    ]);
                  },
                  // borderWidth: 3,
                  borderColor: "rgba(255,255,255,0)",
                  //  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  //   {
                  //     offset: 0,
                  //     color: 'white',
                  //   },
                  //   {
                  //     offset: 1,
                  //     color: '#051427',
                  //   },
                  // ]),
                },
              },
              label: {
                show: false,
                // position: 'center',
                color: "#fff",
                // fontSize: 16,
                // fontWeight: 'bold',
                formatter: (params) => {
                  //             let data = `<div
                  // style="padding: '0px';margin:0px;background:red'"
                  // > <div>${params.data.name}</div>  <div>${params.value} </div>  </div></div>`
                  return "";
                },
                // formatter: '{c}', // {b}:数据名； {c}：数据值； {d}：百分比，可以自定义显示内容，

                shadowBlur: 0,
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 40,
                  fontWeight: "bold",
                },
              },
              labelLine: {
                show: false,
              },
              data: data,
            },
          ],
        };
        option && myChart.setOption(option);
      });
    },
    changeVisit(e) {
      this.active = e.id;
      let chainAreaDistributionDTO =
        this.$store.getters.allViewData?.chainAreaDistributionDTO; //地域分布
      let {
        chainArea,
        keyEnterprise,
        researchInstitution,
        chainCollege,
        chainFund,
      } = chainAreaDistributionDTO;
      // chainArea//产业聚集区
      // keyEnterprise//重点企业
      // researchInstitution//研发机构
      // chainCollege//高等院校
      // chainFund//产业基金
      let data = [
        [],
        [chainCollege], //高校院校
        [chainFund], //产业基金
        [researchInstitution], //研发机构
        [chainArea], //产业聚集区
        [keyEnterprise], //重点企业
      ];

      // 1, "高等院校" 2, "产业基金" 3, "研究机构" 4, "产业聚集区"
      this.mapShowData = data[e.id][0];
      let areaCode = this.$store.getters.allViewData?.areaCode; //杭州
      let divisionLevel = this.$store.getters.allViewData?.divisionLevel;
      if (this.isDialog) {
        // console.log(this.$refs?.mapCopyRef)
        this.$refs?.mapCopyRef?.init(
          areaCode,
          divisionLevel,
          this.mapShowData,
          e
        );
      } else {
        this.$refs?.mapRef?.init(areaCode, divisionLevel, this.mapShowData, e);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/public.scss";

.content {
  height: 100%;
  padding: 20px 20px 40px 20px;
}

.top {
  height: calc(70% - 40px);
}

.bottom {
  height: 30%;
}

.developBottom {
  display: flex;
  justify-content: space-between;
  height: 30%;
  width: 100%;
  background: center/cover no-repeat
    url("https://static.idicc.cn/cdn/pangu/middle_bg.png?x-oss-process=image/quality,q_85/format,webp");

  .bottomLeft {
    width: 45%;
    padding: 10px 15px 0 16px;
    position: relative;
  }

  .bottomRight {
    width: 55%;
    padding: 10px 0px 0 8px;
    position: relative;
  }

  .bottomRight {
    // width: 50%;
  }
}

.no-data {
  display: flex;
  position: absolute;
  top: 35px;
}

.no-data,
.localIndustry,
.growthTrend {
  width: 100%;
  height: calc(100% - 40px);
}

.topMapCopy.topMap {
  height: 100%;
}

.topMap {
  width: 100%;
  // display: flex;
  height: calc(100% - 40px);
  position: relative;

  .middleDataList {
    position: absolute;
    z-index: 22;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 70%;
    width: 8.75rem;
    flex-wrap: wrap;
    align-content: center;
  }

  .dataItem {
    width: 100%;
    height: 34px;
    background: url("https://static.idicc.cn/cdn/pangu/tab_normal.webp")
      center/contain no-repeat;
    background-size: 100% 100%;
    margin-bottom: 20%;
    cursor: pointer;

    &.active,
    &:hover {
      background: url("https://static.idicc.cn/cdn/pangu/tab_height.webp")
        center/contain no-repeat;
      .text {
        background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .text {
      line-height: 34px;
      text-align: center;
      width: 100%;
      height: 34px;
      @include YouSheBiaoTi(1rem);
    }
  }
}

.middleTopDataList {
  width: 100%;
  height: 5.625rem;
  padding: 0px 10%;
  display: grid;

  grid-template-columns: 1fr 1fr 0.13rem 32% 1fr;
  background: url("https://static.idicc.cn/cdn/pangu/assets/screen/new/header_bg3.png") center/cover no-repeat;
  background-size: 100% 100%;
  padding-bottom: 15px;
  align-items: center;

  .middleTopDataListItem {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .text.autoWith {
      span {
        width: auto;
      }
    }

    .text {
      width: 100%;
      text-align: center;

      span {
        width: 100%;
        display: inline-block;
        font-size: 0.875rem;
        line-height: 2rem;
        font-family: "puhuiti";
        font-weight: bold;
        // line-height: 17px;
        letter-spacing: 0px;

        font-variation-settings: "opsz" auto;
        font-feature-settings: "kern" on;
        color: #ffffff;

        text-shadow: 5px 2px 5px rgba(17, 20, 22, 0.22),
          0px 0px 10px rgba(73, 223, 255, 0.32);
      }
    }

    .value {
      font-family: YouSheBiaoTiHei;
      font-size: 1.375rem;
      background: linear-gradient(180deg, #8cb0c4 0%, #ffffff 100%);
      letter-spacing: 0.36px;
      background: linear-gradient(
          180deg,
          rgba(156, 235, 255, 0.5) 0%,
          rgba(156, 235, 255, 0.5) 23%,
          rgba(255, 255, 255, 0.5) 69%
        ),
        #e4edf7;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
      margin-right: 10px;
    }
  }
}

@media screen and (max-width: 1450px) {
  .content {
    padding: 10px 0;
  }

  .topMap {
    .dataItem {
      .text {
        font-size: 0.85rem;
      }
    }
  }
}

.divider {
  width: 0.13rem;
  height: 1.88rem;
  background: rgba(255, 255, 255, 0.1);
}

.right-menu-item {
  position: absolute;
  right: 5%;
  top: 1%;
  z-index: 25;
  width: 4.5rem;
  height: 2rem;
  background: #0b254d;
  border-radius: 0.25rem;
  font-size: 0.8rem;
  font-weight: 400;
  color: rgba(174, 192, 252, 0.8);
  text-align: center;
  line-height: 2rem;

  &:hover {
    background: #112e5b;
    border-radius: 0.25rem;
    color: rgba(255, 255, 255, 0.8);
  }
}
</style>
