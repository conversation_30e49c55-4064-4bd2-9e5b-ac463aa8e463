<template>
  <div class="noData">
    <div class="noDataBg" />
    <!-- {{ title }} -->
  </div>
</template>
<script>

export default {
  props: {
    title: {
      type: String,
      default: '暂无收录企业'
    }
  }

}
</script>

<style scoped lang="scss">
.noData {
  width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  .noDataBg{
    width: 200px;
    margin-top: 140px;
    height: 200px;
    background: center / contain no-repeat url(~@/assets/screen/new/noData.webp);
  }
}
</style>