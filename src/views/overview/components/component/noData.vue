<template>
  <div class="noData">
    <div class="noDataBg" />
    <!-- {{ title }} -->
  </div>
</template>
<script>

export default {
  name:'NoData',
  props: {
    title: {
      type: String,
      default: '暂无数据'
    }
  }

}
</script>

<style scoped lang="scss">
.noData {
  width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  .noDataBg{
    width: 100px;
    height: 100px;
    background: center / contain no-repeat url(https://static.idicc.cn/cdn/pangu/assets/screen/new/noData.webp);
  }
}
</style>