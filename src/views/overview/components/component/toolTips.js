export const toolTips = {
    trigger: "item",
    backgroundColor: "#00112dbf", // 提示框浮层的背景颜色。
    borderColor: "#0066FF", // 提示框浮层的边框颜色。
    borderWidth: 1, // 提示框浮层的边框宽。

    axisPointer: {
        type: "none", // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
        axis: "auto", // 指示器的坐标轴。
        snap: true, // 坐标轴指示器是否自动吸附到点上
        label: {
            color: "#fff",
            show: false,
            backgroundColor: "#00112D",
        },
    },
    textStyle: {
        // 提示框浮层的文本样式。
        color: "#fff",
        fontStyle: "normal",
        fontWeight: "normal",
        fontFamily: "sans-serif",
        fontSize: 14,
    },
};
export const tooltipShadow = {
    trigger: "axis",
    // axisPointer: {
    //   type: 'cross',
    //   label: {
    //     backgroundColor: '#6a7985'
    //   }
    // }
    backgroundColor: "#00112dbf", // 提示框浮层的背景颜色。
    borderColor: "#0066FF", // 提示框浮层的边框颜色。
    borderWidth: 1, // 提示框浮层的边框宽。

    axisPointer: {
        type: "shadow", // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
        axis: "auto", // 指示器的坐标轴。
        snap: true, // 坐标轴指示器是否自动吸附到点上
        label: {
            color: "#fff",
            show: false,
            backgroundColor: "#00112D",
        },
        shadowStyle: {
            color:'rgba(150, 150, 150, 0.1)',
            z:-1
        },
    },
    textStyle: {
        // 提示框浮层的文本样式。
        color: "#fff",
        fontStyle: "normal",
        fontWeight: "normal",
        fontFamily: "sans-serif",
        fontSize: 14,
    },
};

export const toolTipAxis = {
    trigger: "axis",
    // axisPointer: {
    //   type: 'cross',
    //   label: {
    //     backgroundColor: '#6a7985'
    //   }
    // }
    backgroundColor: "#00112dbf", // 提示框浮层的背景颜色。
    borderColor: "#0066FF", // 提示框浮层的边框颜色。
    borderWidth: 1, // 提示框浮层的边框宽。

    axisPointer: {
        type: "line", // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
        axis: "auto", // 指示器的坐标轴。
        snap: true, // 坐标轴指示器是否自动吸附到点上
        label: {
            color: "#fff",
            show: false,
            backgroundColor: "#00112D",
        },
    },
    textStyle: {
        // 提示框浮层的文本样式。
        color: "#fff",
        fontStyle: "normal",
        fontWeight: "normal",
        fontFamily: "sans-serif",
        fontSize: 14,
    },
};

export const mapPoints = [
    {
        name: "北京市",
        coordinate: [0.66, 0.34],
    },
    {
        name: "新疆维吾尔自治区",
        coordinate: [0.23, 0.3],
    },
    {
        name: "西藏自治区",
        coordinate: [0.23, 0.5],
    },
    {
        name: "云南省",
        coordinate: [0.44, 0.64],
    },
    {
        name: "四川省",
        coordinate: [0.45, 0.52],
    },
    {
        name: "青海省",
        coordinate: [0.36, 0.43],
    },
    {
        name: "甘肃省",
        coordinate: [0.38, 0.33],
    },
    {
        name: "宁夏回族自治区",
        coordinate: [0.52, 0.4],
    },
    {
        name: "内蒙古自治区",
        coordinate: [0.59, 0.31],
    },
    {
        name: "重庆市",
        coordinate: [0.53, 0.54],
    },
    {
        name: "陕西省",
        coordinate: [0.56, 0.45],
    },
    {
        name: "山西",
        coordinate: [0.6, 0.39],
    },
    {
        name: "黑龙江省",
        coordinate: [0.8, 0.18],
    },
    {
        name: "吉林省",
        coordinate: [0.79, 0.27],
    },
    {
        name: "辽宁省",
        coordinate: [0.74, 0.32],
    },
    {
        name: "河北省",
        coordinate: [0.64, 0.38],
    },
    {
        name: "山东省",
        coordinate: [0.68, 0.41],
    },
    {
        name: "河南省",
        coordinate: [0.62, 0.46],
    },
    {
        name: "湖北省",
        coordinate: [0.59, 0.51],
    },
    {
        name: "江苏省",
        coordinate: [0.69, 0.47],
    },
    {
        name: "上海市",
        coordinate: [0.73, 0.53],
    },
    {
        name: "浙江省",
        coordinate: [0.7, 0.55],
    },
    {
        name: "安徽省",
        coordinate: [0.67, 0.49],
    },
    {
        name: "福建省",
        coordinate: [0.67, 0.61],
    },
    {
        name: "江西省",
        coordinate: [0.65, 0.57],
    },
    {
        name: "湖南省",
        coordinate: [0.59, 0.57],
    },
    {
        name: "贵州省",
        coordinate: [0.53, 0.59],
    },
    {
        name: "广西壮族自治区",
        coordinate: [0.55, 0.64],
    },
    {
        name: "广东省",
        coordinate: [0.62, 0.64],
    },
    {
        name: "海南省",
        coordinate: [0.62, 0.65],
    },
    {
        name: "台湾省",
        coordinate: [0.71, 0.65],
    },
    {
        name: "澳门特别行政区",
        coordinate: [0.62, 0.67],
    },
    {
        name: "香港特别行政区",
        coordinate: [0.63, 0.67],
    },
    {
        name: "天津市",
        coordinate: [0.67, 0.36],
    },
];
export const colors = [
    {
        colorEnd: "#75C756",
        colorStart: "#A2D591",
    },
    {
        colorEnd: "#3E6AC9",
        colorStart: "#6989CE",
    },
    {
        colorEnd: "#FFBA00",
        colorStart: "#FDD17A",
    },
    {
        colorEnd: "#FF4044",
        colorStart: "#F57C7E",
    },
    {
        colorEnd: "#47BDE2",
        colorStart: "#84CCE3",
    },
    {
        colorEnd: "#009B63",
        colorStart: "#4CB28B",
    },
    {
        colorEnd: "#FF6500",
        colorStart: "#FF976F",
    },
    {
        colorEnd: "#9852B2",
        colorStart: "#AD7ABF",
    },
    {
        colorEnd: "#F764C9",
        colorStart: "#F093D3",
    },
    {
        colorEnd: "#75C756",
        colorStart: "#A2D591",
    },
    {
        colorEnd: "#3E6AC9",
        colorStart: "#6989CE",
    },
    {
        colorEnd: "#FFBA00",
        colorStart: "#FDD17A",
    },
    {
        colorEnd: "#FF4044",
        colorStart: "#F57C7E",
    },
    {
        colorEnd: "#47BDE2",
        colorStart: "#84CCE3",
    },
    {
        colorEnd: "#009B63",
        colorStart: "#4CB28B",
    },
    {
        colorEnd: "#FF6500",
        colorStart: "#FF976F",
    },
    {
        colorEnd: "#9852B2",
        colorStart: "#AD7ABF",
    },
    {
        colorEnd: "#F764C9",
        colorStart: "#F093D3",
    },
];
// export const mockDataEmpty = {
//     areaCode: "330110",
//     divisionLevel: "3",
//     localIndustryBaseDTO: {
//         enterpriseCount: null,
//         scaleEnterpriseCount: null,
//         researchInstitutionCount: null,
//         collegeCount: null,
//         talentCount: null,
//         fundCount: null,
//     },
//     industryResourceDTO: {
//         keyResources: [],
//     },
//     industryEnterpriseDTO: {
//         values: [],
//     },
//     keyFeatureDTO: {
//         totalOutputValue: null,
//         outputYearOnYearGrowth: null,
//         scaleIndustrialAddedValue: null,
//         scaleYearOnYearGrowth: null,
//     },
//     chainAreaDistributionDTO: {
//         chainArea: [],
//         keyEnterprise: [],
//         researchInstitution: [],
//         chainCollege: [],
//         chainFund: [],
//     },
//     chainDevelopFeatureDTO: {
//         registerTrendyChart: [],
//         industryNodeCount: [],
//     },
//     chainInnovateDTO: {
//         patentEnterpriseCount: "0",
//         patentEnterpriseYearCount: "0",
//         patentCount: "0",
//         patentYearIncrPercent: "0.0",
//         avgPatentCountOfEnterprise: "0",
//         avgPatentCountOfCountryEnterprise: "0.00",
//         avgPatentRank: "0",
//         topPatentCountByIndustryNode: [],
//     },
//     chainInvestDTO: {
//         outBoundCount: "0",
//         absorbCount: "0",
//         topOutboundInvestment: null,
//         topAbsorbInvestment: [],
//     },
// };

// export const mockDataFull = {
//     areaCode: "330110",
//     divisionLevel: "3",
//     localIndustryBaseDTO: {
//         enterpriseCount: "103",
//         scaleEnterpriseCount: "103",
//         researchInstitutionCount: "103",
//         collegeCount: "103",
//         talentCount: "103",
//         fundCount: "103",
//     },
//     industryResourceDTO: {
//         keyResources: [
//             {
//                 name: "劳动力人口",
//                 value: "123",
//                 unit: "万人",
//                 rank: "34",
//                 rankLevel: "2",
//             },
//             {
//                 name: "光照时长",
//                 value: "456",
//                 unit: "天/年",
//                 rank: "21",
//                 rankLevel: "2",
//             },
//         ],
//     },
//     industryEnterpriseDTO: {
//         values: [
//             {
//                 dicKey: "上市企业",
//                 dicValue: "4",
//                 unit: "家",
//                 proportion: null,
//             },
//             {
//                 dicKey: "新三板",
//                 dicValue: "1",
//                 unit: "家",
//                 proportion: null,
//             },
//             {
//                 dicKey: "隐形冠军企业",
//                 dicValue: "1",
//                 unit: "家",
//                 proportion: null,
//             },
//             {
//                 dicKey: "专精特新企业",
//                 dicValue: "13",
//                 unit: "家",
//                 proportion: null,
//             },
//             {
//                 dicKey: "专精特新小巨人企业",
//                 dicValue: "10",
//                 unit: "家",
//                 proportion: null,
//             },
//             {
//                 dicKey: "瞪羚企业",
//                 dicValue: "0",
//                 unit: "家",
//                 proportion: null,
//             },
//         ],
//     },
//     keyFeatureDTO: {
//         totalOutputValue: "154.6",
//         outputYearOnYearGrowth: "8.7",
//         scaleIndustrialAddedValue: "135.4",
//         scaleYearOnYearGrowth: "10.5",
//     },
//     chainAreaDistributionDTO: {
//         chainArea: [
//             {
//                 areaName: "西湖区",
//                 areaCode: "330106",
//                 count: "1",
//                 level: "1",
//             },
//         ],
//         keyEnterprise: [
//             {
//                 areaName: "余杭区",
//                 areaCode: "330110",
//                 count: "25",
//                 level: "1",
//             },
//         ],
//         researchInstitution: [
//             {
//                 areaName: "拱墅区",
//                 areaCode: "330105",
//                 count: "1",
//                 level: "1",
//             },
//         ],
//         chainCollege: [
//             {
//                 areaName: "上城区",
//                 areaCode: "330102",
//                 count: "1",
//                 level: "1",
//             },
//         ],
//         chainFund: [
//             {
//                 areaName: "富阳区",
//                 areaCode: "330111",
//                 count: "1",
//                 level: "1",
//             },
//         ],
//     },
//     chainDevelopFeatureDTO: {
//         registerTrendyChart: [
//             {
//                 year: "2019",
//                 enterpriseNum: "10",
//             },
//             {
//                 year: "2020",
//                 enterpriseNum: "2",
//             },
//             {
//                 year: "2021",
//                 enterpriseNum: "5",
//             },
//             {
//                 year: "2022",
//                 enterpriseNum: "9",
//             },
//             {
//                 year: "2023",
//                 enterpriseNum: "0",
//             },
//         ],
//         industryNodeCount: [
//             {
//                 number: "103",
//                 industryNodeId: "438",
//                 industryNodeName: "产业金脑·太阳能热",
//             },
//             {
//                 number: "2",
//                 industryNodeId: "566",
//                 industryNodeName: "项目设计/总包",
//             },
//             {
//                 number: "20",
//                 industryNodeId: "468",
//                 industryNodeName: "系统集成",
//             },
//             {
//                 number: "14",
//                 industryNodeId: "463",
//                 industryNodeName: "聚光",
//             },
//             {
//                 number: "3",
//                 industryNodeId: "464",
//                 industryNodeName: "吸热",
//             },
//             {
//                 number: "41",
//                 industryNodeId: "465",
//                 industryNodeName: "储热",
//             },
//             {
//                 number: "36",
//                 industryNodeId: "467",
//                 industryNodeName: "发电",
//             },
//             {
//                 number: "0",
//                 industryNodeId: "466",
//                 industryNodeName: "服务",
//             },
//         ],
//     },
//     chainInnovateDTO: {
//         patentEnterpriseCount: "25",
//         patentEnterpriseYearCount: "0",
//         patentCount: "10",
//         patentYearIncrPercent: "2.0",
//         avgPatentCountOfEnterprise: "20",
//         avgPatentCountOfCountryEnterprise: "3.00",
//         avgPatentRank: "10",
//         topPatentCountByIndustryNode: [
//             {
//                 number: "469",
//                 industryNodeId: "469",
//                 industryNodeName: "集热系统集热系统集热系统",
//             },
//             {
//                 number: "269",
//                 industryNodeId: "200",
//                 industryNodeName: "集热系统",
//             },
//             {
//                 number: "169",
//                 industryNodeId: "269",
//                 industryNodeName: "集热系统",
//             },
//             {
//                 number: "9",
//                 industryNodeId: "49",
//                 industryNodeName: "集热系统",
//             },
//             {
//                 number: "1",
//                 industryNodeId: "49",
//                 industryNodeName: "集热系统",
//             },
//         ],
//     },
//     chainInvestDTO: {
//         outBoundCount: "10",
//         absorbCount: "30",
//         topOutboundInvestment: [
//             {
//                 from: "浙江省", //发起投资的地区省份
//                 fromName: "浙江省", //发起投资地区名
//                 to: "山东省", //被投资的地区省份
//                 toName: "山东省", //被投资地区名
//                 amount: 119, //投资笔数
//             },
//             {
//                 from: "湖南省",
//                 fromName: "湖南省",
//                 to: "江苏省",
//                 toName: "江苏省",
//                 amount: 79,
//             },
//             {
//                 from: "安徽省",
//                 fromName: "安徽省",
//                 to: "河北省",
//                 toName: "河北省",
//                 amount: 39,
//             },
//         ],
//         topAbsorbInvestment: [
//             {
//                 to: "浙江省", //发起投资的地区省份
//                 fromName: "浙江省", //发起投资地区名
//                 from: "山东省", //被投资的地区省份
//                 toName: "山东省", //被投资地区名
//                 amount: 29, //投资笔数
//             },
//             {
//                 to: "湖南省",
//                 fromName: "湖南省",
//                 from: "江苏省",
//                 toName: "江苏省",
//                 amount: 19,
//             },
//             {
//                 to: "安徽省",
//                 fromName: "安徽省",
//                 from: "河北省",
//                 toName: "河北省",
//                 amount: 9,
//             },
//         ],
//     },
// };

// export const mapPointsObj = {
//   北京市: {
//       name: "北京市",
//       coordinate: [0.62, 0.34],
//   },
//   新疆维吾尔自治区: {
//       name: "新疆维吾尔自治区",
//       coordinate: [0.35, 0.32],
//   },
//   西藏自治区: {
//       name: "西藏自治区",
//       coordinate: [0.38, 0.52],
//   },
//   云南省: {
//       name: "云南省",
//       coordinate: [0.49, 0.64],
//   },
//   四川省: {
//       name: "四川省",
//       coordinate: [0.5, 0.53],
//   },
//   青海省: {
//       name: "青海省",
//       coordinate: [0.44, 0.43],
//   },
//   甘肃省: {
//       name: "甘肃省",
//       coordinate: [0.44, 0.34],
//   },
//   宁夏回族自治区: {
//       name: "宁夏回族自治区",
//       coordinate: [0.53, 0.4],
//   },
//   内蒙古自治区: {
//       name: "内蒙古自治区",
//       coordinate: [0.58, 0.29],
//   },
//   重庆市: {
//       name: "重庆市",
//       coordinate: [0.55, 0.54],
//   },
//   陕西省: {
//       name: "陕西省",
//       coordinate: [0.55, 0.45],
//   },
//   山西: {
//       name: "山西",
//       coordinate: [0.59, 0.39],
//   },
//   黑龙江省: {
//       name: "黑龙江省",
//       coordinate: [0.72, 0.17],
//   },
//   吉林省: {
//       name: "吉林省",
//       coordinate: [0.71, 0.26],
//   },
//   辽宁省: {
//       name: "辽宁省",
//       coordinate: [0.68, 0.31],
//   },
//   河北省: {
//       name: "河北省",
//       coordinate: [0.61, 0.37],
//   },
//   山东省: {
//       name: "山东省",
//       coordinate: [0.64, 0.41],
//   },
//   河南省: {
//       name: "河南省",
//       coordinate: [0.6, 0.45],
//   },
//   湖北省: {
//       name: "湖北省",
//       coordinate: [0.59, 0.51],
//   },
//   江苏省: {
//       name: "江苏省",
//       coordinate: [0.64, 0.49],
//   },
//   上海市: {
//       name: "上海市",
//       coordinate: [0.66, 0.52],
//   },
//   浙江省: {
//       name: "浙江省",
//       coordinate: [0.66, 0.55],
//   },
//   安徽省: {
//       name: "安徽省",
//       coordinate: [0.63, 0.51],
//   },
//   福建省: {
//       name: "福建省",
//       coordinate: [0.63, 0.61],
//   },
//   江西省: {
//       name: "江西省",
//       coordinate: [0.61, 0.58],
//   },
//   湖南省: {
//       name: "湖南省",
//       coordinate: [0.58, 0.57],
//   },
//   贵州省: {
//       name: "贵州省",
//       coordinate: [0.54, 0.6],
//   },
//   广西壮族自治区: {
//       name: "广西壮族自治区",
//       coordinate: [0.54, 0.66],
//   },
//   广东省: {
//       name: "广东省",
//       coordinate: [0.64, 0.64],
//   },
//   海南省: {
//       name: "海南省",
//       coordinate: [0.55, 0.73],
//   },
//   台湾省: {
//       name: "台湾省",
//       coordinate: [0.66, 0.66],
//   },
//   澳门特别行政区: {
//       name: "澳门特别行政区",
//       coordinate: [0.6, 0.68],
//   },
//   香港特别行政区: {
//       name: "香港特别行政区",
//       coordinate: [0.61, 0.67],
//   },
//   天津市: {
//       name: "天津市",
//       coordinate: [0.63, 0.36],
//   },
// };
