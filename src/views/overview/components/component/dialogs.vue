<template>
  <div class="box">
    <div class="list">
      <!-- <div></div> -->
      <div class="header">
        <div class="close">
          <i
            class="el-icon-close"
            @click="close"
          />
        </div>
        <img
          :src="imgUrl"
          class="listtitle"
        >
        <span class="title"> 产业相关地域分布</span>
      </div>

      <div class="content">
        <slot name="content" />
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: "EnterpriseList",
  props: {

  },
  data () {
    return {
      imgUrl: 'https://static.idicc.cn/cdn/pangu/listtitle.png',
    };
  },
  created () {
  },
  mounted () {
    this.init();
  },
  methods: {
    init () {
      this.$emit("showMapInfo");
    },
    close () {
      this.$emit("closeList");
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-loading-mask {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-loading-spinner {
    /* 重置样式 */
    width: 30px !important;
    height: 30px !important;
    top: 50% !important;
    left: 50% !important;
    position: absolute !important;
  }
}


::v-deep {
  .el-icon-close {
    cursor: pointer;
    font-size: 15px;
    color: #fff;
    float: right;
    // padding: 1.5rem;
  }
}

@font-face {
  font-family: "title";
  src: url(~@/text/YouSheBiaoTiHei-2.ttf);
}



.box {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;

  .list {
    width: 72.75rem;
    height: 55.56rem;
    background: url("~@/assets/screen/new/bgc.png") no-repeat;
    background-size: 100% 100%;
    border-radius: 10px;
    position: relative;

    .header {
      // position: relative;
      position: absolute;
      z-index: 11;
      width: 100%;
      height: 48px;
    }

    .close {
      position: absolute;
      z-index: 11;
      right: 0;
      right: 1.5rem;
      top: 1.8rem;

    }

    .listtitle {
      width: 70.75rem;
      height: 43px;
      margin-left: 1rem;
      margin-right: 1rem;
      margin-top: 1rem;
      position: absolute;
    }

    .title {
      font-size: 20px;
      font-family: title;
      position: absolute;
      font-weight: 400;
      color: #ffffff;
      line-height: 43px;
      margin-left: 4rem;
      margin-top: 1rem;
    }


    .content {
      height: 100%;
      width: 100%;
      padding: 2px;

    }

  }

}
</style>