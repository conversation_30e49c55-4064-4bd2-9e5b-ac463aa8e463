<template>
  <div class="title">
    <span> {{ title }}</span>
  </div>
</template>
<script>
export default {
  name: 'PointTitle',
  props: {
    title: {
      type: String,
      default: ''
    },

  },
}
</script>
<style lang="scss" scoped>
@import '@/styles/public.scss';

.title {
  // font-size: 0.875rem;
  // font-weight: 400;
  // color: #F6FAFF;
  // padding-bottom: 10px;
  @include YouSheBiaoTi28(1rem);
  display: flex;
    justify-content: flex-start;
    align-items: center;
  &::before {
    content: "";
    display: inline-block;
    width: 30px;
    height: 30px;
    background: center /contain no-repeat url(~@/assets/screen/newScreen/pount.webp);

    // box-shadow: 0px 0px 14px 8px rgba(45, 125, 219, 0.3)
  }
}

// @media screen and (max-width: 1450px) {
//   .title {
//     font-size: 14px;
//   }
// }
</style>