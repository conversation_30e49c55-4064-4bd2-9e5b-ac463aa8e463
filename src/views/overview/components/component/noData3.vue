<template>
  <div class="noData">
    <div class="noDataBg" />
    {{ title }}
  </div>
</template>
  <script>
  
  export default {
    name:'NoData',
    props: {
      title: {
        type: String,
        default: '暂无数据'
      }
    }
  
  }
  </script>
  
  <style scoped lang="scss">
  .noData {
    width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #666;
      font-size: 16px;
      text-align: center;
      padding: 30px 0;
      box-sizing: border-box;
    .noDataBg{
      width: 216px;
      height: 177px;
      background: center / contain no-repeat url(https://static.idicc.cn/cdn/pangu/vacancy.png);
    }
  }
  </style>