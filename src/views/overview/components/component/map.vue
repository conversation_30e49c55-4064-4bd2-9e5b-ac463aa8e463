
<template>
  <div class="map-main">
    <!-- <div id="mapGeoData" class="map-geo" v-show="data.length > 0" /> -->
    <!-- v-show="data.length === 0" -->
    <!-- :style="{ height: data.length > 0 ? '100%' : '0px' }"  -->
    <div
      id="mapGeo"
      class="map-geo"
    />
    <div
      v-if="isDialog"
      id="mapGeoDialog"
      class="map-geo"
    />

    <!--  -->
    <!-- :style="{ height: data.length > 0 ? '0px' : '100%' }"  -->
    <!--    hidden-->
    <!-- <img ref="mapSvg" id="mapSvg" v-show="false" style="width:200px;height:200px;" :src="bg"> -->
    <div style="display: none">
      <div
        ref="hoverDom"
        class="titleImg"
      >
        <div :class="['titleImgContent', `contentBg_${domData.type}`]">
          <div class="titleImgContentText">
            {{ domData.num }}
          </div>
          <div
            :class="[
              'titleImgContentTextRight',
              `titleImgContentTextRight_${domData.type}`,
            ]"
          >
            {{ domData.name }}
          </div>
        </div>
      </div>
    </div>

    <EnterpriseList
      v-if="isShowList"
      :nodemessage="nodemessage"
      :state="state"
      :show-list="showList"
      :api-url="apiUrl"
      :area-code="areaCode"
      industry-type=""
      :institution-type="institutionType"
      @closeList="closeList"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import { getCityJson } from "@/views/system-manage/idicc-scan/apiUrl";
import EnterpriseList from "@/views/count/admin/Enterpriselist.vue";
export default {
  name: "MapMain",
  components: {
    EnterpriseList,
  },
  props: {
    // mapShowData: {
    //   type: Array,
    //   default: [],
    // },
    isDialog: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      data: [],
      mapName: "china",
      colorList: ["#0016DF", "#165DFF", "#4080FF", "#6AA1FF", "#AECCFF"],
      hideMap: false,
      showBar: true,
      domData: {},
      isShowList: false,
      state: null,
      showList: "",
      nodemessage: "",
      apiUrl: "",
      areaCode: "",
      institutionType: "",
      recentShow: 0,
      timer: null,
    };
  },
  mounted() {},
  beforeDestroy() {
    clearInterval(this.timer);
    this.recentShow = 0;
    this.timer = null;
  },
  methods: {
    init(areaCode, divisionLevel, mapData, data) {
      // this.bg = bgImg
      this.recentShow = 0;
      this.state = data.id;
      this.nodemessage = data.name;
      this.showList = data.showList;
      this.apiUrl = data.apiUrl;
      // console.log(mapData, this.mapShowData, areaCode, divisionLevel, 'test')
      if (areaCode) {
        this.getMapInfo(areaCode, divisionLevel, mapData, data);
      }
    },
    closeList() {
      this.isShowList = false;
    },
    getMapInfo(regionCode, divisionLevel, mapData, viewData) {
      let code = regionCode;
      // _full 是查询所以，不加full是查询单独区域
      if (divisionLevel !== "3") {
        code = regionCode + "_full";
      }
      let writeName = new Set(["120100", "110100", "310100", "500100"]);
      if (writeName.has(regionCode)) {
        code = code.replace("_full", "");
      }
      getCityJson(code).then((res) => {
        if (res) {
          let that = this;
          const cityData = JSON.parse(res);
          let newCity = {};
          cityData.features.forEach((e) => {
            // console.log(e, e?.properties?.centroid)
            // center
            newCity[e?.properties.adcode] =
              e?.properties?.centroid || e?.properties?.center;
          });
          let showData = mapData?.map((item) => {
            // areaName//地区名
            // areaCode//地区code
            // count//数据
            // level//1 前34% 2 前35%-67% 3 前68%-100%
            let data = {
              lng: newCity[item?.areaCode]?.[0],
              lat: newCity[item?.areaCode]?.[1],
              value: item.count || 0,
              name: item.areaName,
              type: item.level,
              state: viewData.id,
              areaCode: item?.areaCode,
            };
            return data;
          });
          // console.log(showData, 'that.mapShowData')
          this.data = showData;
          this.showGeoMap(cityData);
        }
      });
    },
    async showGeoMap(cityData) {
      const hangzhouData = this.data || [];
      //   let chartDom =
      // console.log(this.isDialog)
      const chartDom = this.isDialog
        ? document.getElementById("mapGeoDialog")
        : document.getElementById("mapGeo");
      let myChart = echarts.init(chartDom);
      let series = hangzhouData.map((item, index) => {
        // console.log(item)
        return {
          type: "scatter",
          coordinateSystem: "geo", //自定义图片的 位置（lng, lat）
          data: [
            {
              name: item.name,
              value: [item.lng, item.lat],
              num: item.value,
              type: item.type,
              areaCode: item?.areaCode,
            },
          ], //自定义图片的 大小
          // symbolSize: 10,
          symbolSize: [50, 70], //自定义图片的 路径
          symbolClip: true,
          symbolPosition: "center",
          symbolOffset: ["-10%", "-52%"],

          label: {
            position: "right",
            show: false,
          },
          emphasis: {
            label: {
              show: false,
            },
          },
          rippleEffect: {
            //涟漪特效
            period: 7, //动画时间，值越小速度越快
            brushType: "stroke", //波纹绘制方式 stroke, fill
            scale: 5, //波纹圆环最大限制，值越大波纹越大
          },
          symbol: `image://https://static.idicc.cn/cdn/pangu/point_${item.type}.png`,
        };
      });

      let geo = this.isDialog
        ? [
            {
              map: "余杭区",
              zoom: 1,
              silent: true, //禁用地图的hover事件
              // boundingCoords: [[53.55, 73.66], [17.66, 135.04]],
              // zlevel: 5,
              // left: "40%",
              // top: "20%",
              roam: true, //是否开启鼠标缩放和平移漫游//roam与上一个geo配置相同
              itemStyle: {
                normal: {
                  areaColor: {
                    image: "https://static.idicc.cn/cdn/pangu/mapBg.jpg",
                    size: [1100, 880], // 图片大小，数组第一个元素为宽度，第二个为高度
                    // position: [0, 0]        // 图片位置，数组第一个元素为横坐标，第二个为纵坐标
                    repeat: "repeat",
                    // position: 'cover',
                    // repeat: 'repeat' // 是否平铺，可以是 'repeat-x', 'repeat-y', 'no-repeat'
                  },
                  borderColor: "rgba(255,255,255,0.6)",
                  borderWidth: 1,
                  shadowColor: "rgba(0,120,255,0.65)",
                  // shadowOffsetX: 4, // 阴影水平方向上的偏移距离。
                  // shadowOffsetY: 4,// 阴影垂直方向上的偏移距离。
                  // shadowBlur: 15
                },
                emphasis: {
                  areaColor: "rgba(0,255,255,.1)",
                  color: "#fff",
                },
              },
              layoutSize: 370,
              label: {
                // 基本的一些样式
                normal: {
                  show: true,
                  textStyle: {
                    color: "#FFF",
                    fontSize: "10",
                  },
                },
              },
            },
          ]
        : [
            {
              map: "余杭区",
              zoom: 1,
              // zlevel: 10,
              // left: "40%",
              silent: true, //禁用地图的hover事件
              top: "40",
              roam: false, //是否开启鼠标缩放和平移漫游//roam与上一个geo配置相同
              // boundingCoords: [[53.55, 73.66], [17.66, 135.04]],
              itemStyle: {
                normal: {
                  // areaColor: "rgba(6, 141, 255, 5)",
                  areaColor: {
                    image: "https://static.idicc.cn/cdn/pangu/mapBg2.jpg",
                    repeat: "repeat", // 是否平铺，可以是 'repeat-x', 'repeat-y', 'no-repeat'
                  },
                  borderColor: "rgba(213, 236, 255, 0.3)",
                  borderWidth: 2,
                  shadowColor: "rgba(0,96,255,0.2)",
                  // shadowColor: "white",
                  // shadowOffsetX: 4, // 阴影水平方向上的偏移距离。
                  // shadowOffsetY: 4,// 阴影垂直方向上的偏移距离。
                  shadowBlur: 15,
                },
                label: {
                  show: false,
                },
                emphasis: {
                  show: false,
                  //   // areaColor: "#00ffff",
                  //   // color: "#fff"
                },
              },
            },
            {
              map: "余杭区",
              zoom: 1,
              // boundingCoords: [[53.55, 73.66], [17.66, 135.04]],
              // zlevel: 7,
              // left: "40%",
              silent: true, //禁用地图的hover事件
              top: "30",
              roam: false, //是否开启鼠标缩放和平移漫游//roam与上一个geo配置相同
              itemStyle: {
                normal: {
                  areaColor: "rgba(6, 141, 255, 0.5)",
                  borderColor: "rgba(255,255,255,0.8)",
                  borderWidth: 2.5,
                  shadowColor: "rgba(0,120,255,0.65)",
                  // shadowOffsetX: 4, // 阴影水平方向上的偏移距离。
                  // shadowOffsetY: 4,// 阴影垂直方向上的偏移距离。
                  shadowBlur: 15,
                },
                emphasis: {
                  show: false,
                  // areaColor: "#00ffff",
                  // color: "#fff"
                },
              },
            },
            {
              map: "余杭区",
              zoom: 1,
              silent: true, //禁用地图的hover事件
              // boundingCoords: [[53.55, 73.66], [17.66, 135.04]],
              // zlevel: 5,
              // left: "40%",
              top: "30",
              roam: false, //是否开启鼠标缩放和平移漫游//roam与上一个geo配置相同
              itemStyle: {
                normal: {
                  areaColor: {
                    image: "https://static.idicc.cn/cdn/pangu/mapBg2.jpg",
                    repeat: "repeat",
                    // repeat: 'repeat' // 是否平铺，可以是 'repeat-x', 'repeat-y', 'no-repeat'
                  },
                  // areaColor: "rgba(6, 141, 255, 5)",
                  borderColor: "#7296db",
                  borderWidth: 0.4,
                  shadowColor: "#068DFF",
                },
                emphasis: {
                  areaColor: "rgba(0,255,255,.1)",
                  color: "#fff",
                },
              },
              layoutSize: 370,
              label: {
                // 基本的一些样式
                normal: {
                  show: true,
                  textStyle: {
                    color: "#FFF",
                    fontSize: "10",
                  },
                },
              },
            },
          ];

      myChart.showLoading();
      myChart.clear();
      let option = {
        grid: {
          // left: '3%',
          // right: '4%',
          // bottom: '5%',
          // top: '3%',
          // containLabel: true
        },
        backgroundColor: "transparent",
        title: {
          text: "",
          left: "center",
          textStyle: {
            color: "#fff",
          },
        },
        tooltip: {
          trigger: "item",
          formatter: (params) => {
            this.domData = params.data;
            let dom1 = this.$refs.hoverDom;
            return dom1;
          }, //数据格式化
          extraCssText: `background:transparent ;border: 0px; box-shadow:0 0 0;`,
          // alwaysShowContent: true,
          transitionDuration: 1, //提示框浮层的移动动画过渡时间，单位是 s，设置为 0 的时候会紧跟着鼠标移动。
          // padding: [0, 8],
          hideDelay: 10, //浮层隐藏的延迟
          // showContent: true, //是否显示提示框浮层
          triggerOn: "mousemove", //提示框触发的条件(mousemove|click|none)
          // showDelay: 200, //浮层显示的延迟，单位为 ms，默认没有延迟，也不建议设置。在 triggerOn 为 'mousemove' 时有效。
          enterable: true, //鼠标是否可进入提示框浮层中，默认为false，
          show: true, //是否显示提示框组件，包括提示框浮层和 axisPointer。
        },
        geo: geo,

        xAxis: {
          axisLine: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          axisTick: { show: false },
        },
        yAxis: {
          axisLine: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          axisTick: { show: false },
        },
        loading: true, // 开启加载效果
      };
      if (series && series.length > 0) {
        option.series = series;
      }

      myChart.hideLoading();
      echarts.registerMap("余杭区", cityData);

      myChart.off("click"); // 这里很重要！！！
      let that = this;
      myChart.on("click", function (params) {
        // console.log(params, 'params')
        that.isShowList = true;
        that.areaCode = params.data.areaCode;
        // 1, "高等院校" 2, "产业基金" 3, "研究机构" 4, "产业聚集区"

        that.institutionType = that.state;
        // 展示弹窗
      });

      myChart.setOption(option);

      // const autoShow = (length) => {
      //   console.log('111', length, that.recentShow)

      //   let num = (Number(that.recentShow) + 1) % length;
      //   that.recentShow = num
      //   console.log('recentShow', length, num)
      //   myChart.dispatchAction({
      //     type: "showTip",
      //     seriesIndex: 0,
      //     dataIndex: num,
      //     //  that.recentShow,
      //     // 系列的 index，在 tooltip 的 trigger 为 axis 的时候可选。
      //     // seriesIndex?: number,
      //     // 数据的 index，如果不指定也可以通过 name 属性根据名称指定数据
      //     // dataIndex?: number,
      //     // 可选，数据名称，在有 dataIndex 的时候忽略
      //     // name: hangzhouData[num]?.name,
      //     // 本次显示 tooltip 的位置。只在本次 action 中生效。
      //     // 缺省则使用 option 中定义的 tooltip 位置。
      //     // position: Array.<number>|string|Function,
      //   });
      // };
      // autoShow(hangzhouData?.length);
      // clearInterval(that.timer);
      // that.timer = window.setInterval(() => {
      //   autoShow(hangzhouData?.length);
      // }, 5000);
      //捕捉georoam事件，使下层的geo随着上层的geo一起缩放拖曳
      // myChart.on('georoam', function (params) {
      //   var option = myChart.getOption();//获得option对象
      //   if (params.zoom != null && params.zoom != undefined)
      //   { //捕捉到缩放时
      //     option.geo[1].zoom = option.geo[0].zoom + 0.1;//下层geo的缩放等级跟着上层的geo一起改变
      //     option.geo[1].center = option.geo[0].center;//下层的geo的中心位置随着上层geo一起改变
      //     option.geo[2].zoom = option.geo[0].zoom + 0.1;//下层geo的缩放等级跟着上层的geo一起改变
      //     option.geo[2].center = option.geo[0].center;//下层的geo的中心位置随着上层geo一起改变
      //   } else
      //   {//捕捉到拖曳时
      //     option.geo[1].center = option.geo[0].center;//下层的geo的中心位置随着上层geo一起改变
      //     option.geo[2].center = option.geo[0].center;//下层的geo的中心位置随着上层geo一起改变
      //   }
      //   myChart.setOption(option);//设置option
      // });
    },
    //自动切换
  },
};
</script>

<style lang="scss" scoped>
.map-main {
  // display: flex;
  height: 100%;
  // box-sizing: border-box;
  // padding-top: 28px;
  width: 100%; // div {
  //   width: 48%;

  //   &.on {
  //     width: 100%;
  //   }
  // }

  .map-geo {
    // width: 38.75rem;
    // height: 26.375rem;
    width: 100%;
    height: 100%;
    top: 0;
    position: absolute;

    left: 0 div {
      width: 100% !important;
      height: 100% !important;
    }
  }
}

.titleImg {
  background-image: url(../../../../assets/screen/new/btBg.png);
  width: 160px;
  height: 40px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  display: "flex";
  align-items: center;
  padding: 5px;

  .contentBg_1 {
    background: linear-gradient(90deg, #f12382 0%, #ff5a86 100%);
  }

  .contentBg_2 {
    background: linear-gradient(90deg, #f17c22 0%, #f5b440 100%);
  }

  .contentBg_3 {
    background: linear-gradient(90deg, #33a1a6 0%, #44d498 100%);
  }

  .titleImgContent {
    width: 100%;
    height: 30px;
    line-height: 30px;
    font-size: 16px;

    display: flex;
    justify-content: space-between;

    .titleImgContentText {
      padding-left: 10px;
      font-size: 16px;
      font-weight: 600;
      font-family: DINPro;
      font-style: italic;
      color: #ffffff;
      min-width: 70px;
    }

    .titleImgContentTextRight_1 {
      background: linear-gradient(
        90deg,
        rgb(117 38 85) 0%,
        rgba(5, 16, 34, 1) 100%
      );
    }

    .titleImgContentTextRight_2 {
      background: linear-gradient(
        90deg,
        rgb(93 45 17) 0%,
        rgba(5, 16, 34, 1) 100%
      );
    }

    .titleImgContentTextRight_3 {
      background: linear-gradient(
        90deg,
        rgb(31 75 76) 0%,
        rgba(5, 16, 34, 1) 100%
      );
    }

    .titleImgContentTextRight {
      clip-path: polygon(6% 0, 100% 0, 100% 100%, 0% 100%, 0 100%);
      padding-left: 5px;
      color: #ecf1fd;
      font-size: 13px;
      width: 55%;
      min-width: 80px;
      text-align: center;
    }
  }
}
</style>