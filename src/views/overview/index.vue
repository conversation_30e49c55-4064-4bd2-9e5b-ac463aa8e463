<template>
  <div class="app-container overView">
    <!-- 概览 -->
    <Headers state="overView">
      <div
        slot="view-container"
        class="container"
      >
        <div
          v-if="dataList"
          class="left"
        >
          <Left
            ref="leftRefs"
            :data="dataList"
          />
        </div>
        <div
          v-if="dataList"
          class="middle"
        >
          <Middle
            ref="middleRefs"
            :data="dataList"
          />
        </div>
        <div
          v-if="dataList"
          class="right"
        >
          <Right
            ref="rightRefs"
            :data="dataList"
          />
        </div>
      </div>
    </Headers>
  </div>
</template>
<script>
import Headers from '@/components/header.vue';
import Left from './components/left';
import Right from './components/right';
import Middle from './components/middle';
import { getChainOverview } from './api';
import { getPathId } from '@/utils/utils';
// import { mockDataEmpty, mockDataFull } from '@/views/overview/components/component/toolTips'

export default {
  components: {
    Headers,
    Left,
    Right,
    Middle,
  },
  data() {
    return {
      dataList: null,
    };
  },
  computed: {
    num: function () {
      return this.$route.query.id || getPathId() || null;
    },
  },
  watch: {
    num(val) {
      // console.log(val, 'test')
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      getChainOverview({
        id: this.$route.query.id || getPathId() || null,
      }).then((res) => {
        this.dataList = res;
        // this.routerId = this.$route.query.id || getPathId() || null;
        this.$store.dispatch('industryOverView/setAllViewData', res);
        //     this.$nextTick(() => {
        //   if (this.$refs.middleRefs) this.$refs.middleRefs.init(res)
        //   if (this.$refs.rightRefs) this.$refs.rightRefs.init(res)
        //   if (this.$refs.leftRefs) this.$refs.leftRefs.init(res)
        // })
        // this.$refs?.middleRefs.init(res)
        // this.$refs?.rightRefs.init(res)
        // this.$refs?.leftRefs.init(res)
        // console.log(res, 'res22');

        // setTimeout(() => {
        // }, 10)
      });
    },
  },
};
</script>
<style scoped lang="scss">
.container {
  width: 100%;
  // padding: 0px 30px;
  height: calc(100vh - 60px);
  display: grid;
  grid-template-columns: 25% auto 25%;

  // .left,
  // .right {
  //   .content {
  //     // padding: 20px 20px 10px 20px;
  //   }

  // }

  // .middle {
  //   .content {
  //     // padding: 20px 20px 10px 20px;
  //   }

  // }
  .left,
  .middle,
  .right {
    height: calc(100vh - 60px);
  }

  // flex实现 左中右布局，左右固定大小，中间撑满
}
</style>
<style lang="scss">
.overView {
  .no-data {
    // position: absolute;
    color: #666;
    font-size: 14px;
    justify-content: center;
    align-items: center;
    // left: 50%;
    // top: 50%;
    // transform: translate(-50%, -50%);
  }
}
</style>
