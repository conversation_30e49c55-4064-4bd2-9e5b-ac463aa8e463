<template>
  <div class="amount-info">
    <div :class="['left', 'index' + numIndex]" />
    <div class="right">
      <div class="title">
        {{ info.dicKey }}(家)
      </div>
      <div class="info">
        {{ num.toFixed(0) }}
      </div>
    </div>
  </div>
</template>

<script>
import gsap from "gsap";
export default {
  name: "ModuleAmount",
  components: {},
  props: {
    typeInfo: {
      type: String,
      default: "",
    },
    info: {
      type: Object,
      default: function () {
        return { title: "", num: "", unit: "" };
      }, // title / info
    },
    numIndex: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      num: 0,
    };
  },
  computed: {},
  watch: {
    info: {
      deep: true,
      immediate: true,
      handler(val) {
        // console.log(val,'val');
        gsap.to(this, { duration: 1, num: Number(val.dicValue) || 0 });
      },
    },
  },

  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
@import "@/styles/public.scss";

.amount-info {
  width: 300px;
  color: #fff;
  border-radius: 4px;
  // padding: 0.6rem 0.8rem 0.2rem 0.8rem !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    width: 41px;
    height: 63px;
  }
  .index0 {
    background: center / contain no-repeat
      url("~@/assets/screen/new/middleTop2.svg");
  }
  .index1 {
    background: center / contain no-repeat
      url("~@/assets/screen/new/middleTop1.svg");
  }
  .index2 {
    background: center / contain no-repeat
      url("~@/assets/screen/new/middleTop3.svg");
  }
  .right {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    padding-top: 10px;
    padding-left: 10px;
    .title {
      width: 100%;
      display: flex;
      height: 1.2rem;
      // @include PingFangSC(12px, #c9d2dd);
      color: #c9d2dd;
      font-size: 12px;
    }
    .info {
      display: flex;
      height: 1.6rem;
      @include YouSheBiaoTi28();
    }
  }
}
</style>
