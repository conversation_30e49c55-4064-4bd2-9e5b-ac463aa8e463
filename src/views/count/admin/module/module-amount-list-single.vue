<template>
  <div class="amount-module">
    <div class="amount-module-list">
      <module-amount
        v-for="(info, idx) in dataList"
        :key="idx"
        :info="info"
        :num-index="idx"
      />
    </div>
  </div>
</template>
<script>
import ModuleAmount from './module-amount2.vue'
export default {
  name: 'ModuleAmountListSingle', 
  components: {
    ModuleAmount,
  },
  props: {
    companyList: {
      type: Array,
      default: null, // title / info
    },
  },
  data() {
    return {
      dataList:[]
    }
  },
  computed:{
   
  },
  watch: {
    companyList: {
      deep: true,
      handler(val) {
        const sourceList = []
        val.forEach((item, idx) => {
          const {dicKey, dicValue, unit} = item.values[0];
          if(idx !== 3){
            sourceList.push({dicKey, dicValue, unit})
          }
        });
        this.dataList = sourceList;
      }
    },
  },
 
  mounted(){
  },
  methods: {
    
    
  }
}
</script>

<style lang="scss" scoped>

.amount-module-list{
  width: 100%;
  display: flex;
  justify-content: space-between;
  .amount-info{ 
    box-sizing: border-box;
    width: 100%;
  }
}
</style>
