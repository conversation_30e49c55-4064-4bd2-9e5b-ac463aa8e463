<template>
  <div class="amount-wrapper">
    <div
      ref="scrollContent"
      class="amount-content"
      @mouseenter="pauseAnimation"
      @mouseleave="resumeAnimation"
    >
      <!-- 第一部分数据（用于循环结束后的衔接） -->
      <div
        v-for="(info, k) in sliceData"
        :key="`clone1-${k}`"
        class="amount-info"
        @click="firmList(info)"
      >
        <div class="text">
          <div
            class="left"
            :style="{ backgroundImage: `url(${svgIcons[info.dicKey] || ''})` }"
          />
          <div class="right">
            <div class="info">
              <span class="num">
                {{ info?.dicValue }}
              </span>
              <div class="unit">
                {{
                  info.dicKey !== '从业人员'
                    ? info.dicValue > 10000
                      ? '万家'
                      : '家'
                    : info.dicValue > 10000
                      ? '万人'
                      : '人'
                }}
              </div>
            </div>
            <div class="title">
              {{ info.dicKey.replace('企业', '') }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 主数据 -->
      <div
        v-for="(info, k) in sliceData"
        :key="`main-${k}`"
        class="amount-info"
        @click="firmList(info)"
      >
        <div class="text">
          <div
            class="left"
            :style="{ backgroundImage: `url(${svgIcons[info.dicKey] || ''})` }"
          />
          <div class="right">
            <div class="info">
              <span class="num">
                {{ info?.dicValue }}
              </span>
              <div class="unit">
                {{
                  info.dicKey !== '从业人员'
                    ? info.dicValue > 10000
                      ? '万家'
                      : '家'
                    : info.dicValue > 10000
                      ? '万人'
                      : '人'
                }}
              </div>
            </div>
            <div class="title">
              {{ info.dicKey.replace('企业', '') }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 第二部分数据（用于滚动衔接） -->
      <div
        v-for="(info, k) in sliceData"
        :key="`clone2-${k}`"
        class="amount-info"
        @click="firmList(info)"
      >
        <div class="text">
          <div
            class="left"
            :style="{ backgroundImage: `url(${svgIcons[info.dicKey] || ''})` }"
          />
          <div class="right">
            <div class="info">
              <span class="num">
                {{ info?.dicValue }}
              </span>
              <div class="unit">
                {{
                  info.dicKey !== '从业人员'
                    ? info.dicValue > 10000
                      ? '万家'
                      : '家'
                    : info.dicValue > 10000
                      ? '万人'
                      : '人'
                }}
              </div>
            </div>
            <div class="title">
              {{ info.dicKey.replace('企业', '') }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModuleAmount',
  components: {},
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      num: 0,
      sliceData: [],
      index: 0,
      svgIcons: {},
      currentTranslate: 0,
      currentIndex: 0,
      animating: false,
      isAnimationPaused: false
    };
  },
  computed: {},
  watch: {
    data(newValue) {
      this.init(newValue);
    },
  },
 
  mounted() {
    this.init(this.data);
    let svgIcons = {
      国家级独角兽企业: 'https://static.idicc.cn/cdn/pangu/国家级独角兽企业.svg',
      国家级专精特新小巨人企业:'https://static.idicc.cn/cdn/pangu/国家级专精特新小巨人企业.svg',
      省级专精特新中小企业: 'https://static.idicc.cn/cdn/pangu/国家级专精特新小巨人企业.svg',
      省级专精特新小巨人企业: 'https://static.idicc.cn/cdn/pangu/国家级专精特新小巨人企业.svg',
      省级创新型中小企业: 'https://static.idicc.cn/cdn/pangu/省级创新型中小企业.svg',
      省级技术先进型服务企业: 'https://static.idicc.cn/cdn/pangu/省级技术先进型服务企业.svg',
      省级技术创新示范企业: 'https://static.idicc.cn/cdn/pangu/省级技术创新示范企业.svg',
      省级雏鹰企业: 'https://static.idicc.cn/cdn/pangu/省级雏鹰企业.svg',
      省级瞪羚企业: 'https://static.idicc.cn/cdn/pangu/省级瞪羚企业.svg',
      省级独角兽企业: 'https://static.idicc.cn/cdn/pangu/国家级独角兽企业.svg',
      省级隐形冠军企业: 'https://static.idicc.cn/cdn/pangu/省级隐形冠军企业.svg',
      省级科技小巨人企业: 'https://static.idicc.cn/cdn/pangu/省级科技小巨人企业.svg',
    };
    this.svgIcons = svgIcons;

    // 监听动画迭代完成事件，实现平滑重置
    this.$nextTick(() => {
      this.setupAnimation();
    });
  },
  // 在beforeDestroy钩子中清理
  
  beforeDestroy() {
    // 移除事件监听器，防止内存泄漏
    const scrollContent = this.$refs.scrollContent;
    if (scrollContent) {
      scrollContent.removeEventListener('animationiteration', this.onAnimationIteration);
    }
  },
  
  methods: {
    firmList(i) {
      this.$emit('showListFn', i);
    },
    init(newValue) {
      if (newValue.length > 0) {
        let datas =newValue
        this.showData(datas);
      }
    },
    showData(datas) {
      this.sliceData = datas
    },
    setupAnimation() {
      const scrollContent = this.$refs.scrollContent;
      if (!scrollContent) return;
      
      // 计算单组数据的高度，用于动画重置
      this.$nextTick(() => {
        // 添加动画迭代事件监听
        scrollContent.addEventListener('animationiteration', this.onAnimationIteration);
      });
    },
    
    onAnimationIteration() {
      // 动画每完成一次迭代时触发
      // 可以在这里添加额外的逻辑，如需要的话
    },
    
    pauseAnimation() {
      const scrollContent = this.$refs.scrollContent;
      if (!scrollContent) return;
      
      this.isAnimationPaused = true;
      scrollContent.style.animationPlayState = 'paused';
    },
    
    resumeAnimation() {
      const scrollContent = this.$refs.scrollContent;
      if (!scrollContent || !this.isAnimationPaused) return;
      
      this.isAnimationPaused = false;
      scrollContent.style.animationPlayState = 'running';
    },
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.amount-wrapper {
  width: 100%;
  height: 630px;
  overflow: hidden;
  position: relative;
}

.amount-content {
  width: 100%;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  animation: smoothScroll 20s linear infinite;
}

@keyframes smoothScroll {
  0% {
    transform: translateY(0);
  }
  100% {
    /* 滚动距离为一组数据的高度 */
    transform: translateY(-33.33%);
  }
}
 

.text {
  width: 100%;
  padding: 0 15px 0 10px;
  display: flex;
  flex-wrap: nowrap;

  .left {
    width: 68px;
    height: 68px;
    background-position: center;
    background: center;
    background-size: 160%;
  }
  .right {
    flex: 1;
    height: 68px;
    position: relative;
    .title {
      width: 185px;
      height: 65px;
      color: #ffffff;
      font-size: 14px;
      display: flex;
      align-items: end;
      padding-bottom: 16px;
      justify-content: center;
      text-shadow: 0px 0px 4.69px rgba(30, 198, 255, 0.8);
      background: center/contain no-repeat
        url('https://static.idicc.cn/cdn/pangu/assets/screen/new/titleBg.svg');
        background-position-y: 10px;
    }
    .info {
      width: 186px;
      display: flex;
      justify-content: center;
      position: absolute;
      z-index: 1;
      .num {
        @include YouSheBiaoTi28();
      }
      .unit {
        float: left;
        font-size: 12px;
        margin-left: 0.3rem;
        margin-top: 0.37rem;
        color:rgba(201, 210, 221, 1);
      }
    }
  }
  .icon {
    width: 30px !important;
    height: 30px;
  }
}
</style>
