<template>
  <div>
    <div id="main" />
  </div>
</template>

<script>
import * as echarts from 'echarts';
export default {
  name: 'ModuleRoll',
  components: {},
  props: {
    dataList: {
      type: Array,
      default: function () {
        return [
          /* { newsSummary:'aaaa',gmtModify:'111' } */
        ];
      },
    },
  },
  data() {
    return {
      animate: true,
      list: [],
    };
  },
  computed: {},
  watch: {
    dataList: {
      deep: true,
      handler(newVal) {
        if (newVal) {
          this.industrySector(newVal);
        }
      },
    },
  },
  beforeCreate() {},
  mounted() {
    //this.industrySector();
  },
  created() {},

  methods: {
    industrySector(newVal) {
      let myChart = echarts.init(document.getElementById('main'));
      let totalQuantity = newVal[0];
      // let centreText = totalQuantity.dicKey.replace('产业金脑·', '') || '';
      let dataList = newVal.slice(1);
      /*       let arr =[1,2,3,4,5,6,7,8,9]
      let add =arr.map(it=>{
        return{
          dicKey:it,
          dicValue:it * 100,
          proportion:it + "%",
          unit:"家"
        }
      })
     let newArr=[...dataList,...add] */
      let colors = [
        ['#1637FF', '#0482FF'],
        ['#FCC413', '#F7E049'],
        ['#3FC444', '#29CC79'],
        ['#FC4907', '#F19C0B'],
        ['#771DFF', '#A946FF'],
        ['#60BFCA', '#1DFFF7'],
        ['#FFAC46', '#FFCE6C'],
        ['#1637FF', '#0482FF'],
        ['#FCC413', '#F7E049'],
        ['#3FC444', '#29CC79'],
        ['#FC4907', '#F19C0B'],
        ['#771DFF', '#A946FF'],
        ['#60BFCA', '#1DFFF7'],
        ['#FFAC46', '#FFCE6C'],
        ['#1637FF', '#0482FF'],
        ['#FCC413', '#F7E049'],
        ['#3FC444', '#29CC79'],
        ['#FC4907', '#F19C0B'],
        ['#771DFF', '#A946FF'],
        ['#60BFCA', '#1DFFF7'],
        ['#FFAC46', '#FFCE6C'],
        ['#1637FF', '#0482FF'],
        ['#FCC413', '#F7E049'],
        ['#3FC444', '#29CC79'],
        ['#FC4907', '#F19C0B'],
        ['#771DFF', '#A946FF'],
        ['#60BFCA', '#1DFFF7'],
        ['#FFAC46', '#FFCE6C'],
        ['#1637FF', '#0482FF'],
        ['#FCC413', '#F7E049'],
        ['#3FC444', '#29CC79'],
        ['#FC4907', '#F19C0B'],
        ['#771DFF', '#A946FF'],
        ['#60BFCA', '#1DFFF7'],
        ['#FFAC46', '#FFCE6C'],
        ['#1637FF', '#0482FF'],
        ['#FCC413', '#F7E049'],
        ['#3FC444', '#29CC79'],
        ['#FC4907', '#F19C0B'],
        ['#771DFF', '#A946FF'],
        ['#60BFCA', '#1DFFF7'],
        ['#FFAC46', '#FFCE6C'],
        ['#1637FF', '#0482FF'],
        ['#FCC413', '#F7E049'],
        ['#3FC444', '#29CC79'],
        ['#FC4907', '#F19C0B'],
        ['#771DFF', '#A946FF'],
        ['#60BFCA', '#1DFFF7'],
        ['#FFAC46', '#FFCE6C'],
        ['#1637FF', '#0482FF'],
        ['#FCC413', '#F7E049'],
        ['#3FC444', '#29CC79'],
        ['#FC4907', '#F19C0B'],
        ['#771DFF', '#A946FF'],
        ['#60BFCA', '#1DFFF7'],
        ['#FFAC46', '#FFCE6C'],
      ];
      let newArr = dataList.map((item, index) => ({
        name: item.dicKey,
        value: item.dicValue,
        proportion: item.proportion || '',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: colors[index][0] },
            { offset: 1, color: colors[index][1] },
          ]),
        },
        //proportion:"20%"
      }));
      if (newArr.length > 0) {
        let grid = {
          // 离容器左侧的距离
          left: '5%',
          top: '0%',
          right: '5%',
          bottom: '10%',
        };
        let tooltip = {
          trigger: 'item',
          backgroundColor: '#00112dbf', // 提示框浮层的背景颜色。
          borderColor: '#0066FF', // 提示框浮层的边框颜色。
          borderWidth: 1, // 提示框浮层的边框宽。
          textStyle: {
            // 提示框浮层的文本样式。
            color: '#fff',
            fontStyle: 'normal',
            fontWeight: 'normal',
            fontFamily: 'sans-serif',
            fontSize: 12,
          },
          //实例
          formatter: function (params) {
            let res =
              params.data.name +
              '： ' +
              '<span style="color: #00FFF0;font-size: 12px;">' +
              params.data.value +
              '</span> 家' +
              '<br/>' +
              '占比：' +
              '<span style="color: #00FFF0;font-size: 12px;">' +
              params.data.proportion +
              '</span>';
            return res;
          },
        };
        let title = [
          {
            text: `${totalQuantity.dicValue}${totalQuantity.unit}`,
            top: '45%',
            textAlign: 'center',
            left: '24%',
            textStyle: {
              color: '#C2E0FF',
              fontSize: 12,
              fontWeight: '400',
              //fontFamily: "PangMenZhengDao",
              opacity: 0.6,
            },
          },
          // {
          //   text: centreText,
          //   top: '10%',
          //   textAlign: 'center',
          //   left: '24%', //第二个21?
          //   textStyle: {
          //     color: '#fff',
          //     fontSize: 14,
          //     fontWeight: '600',
          //     //fontFamily: "PangMenZhengDao",
          //   },
          // },
        ];
        let series = [
          {
            type: 'pie',
            zlevel: 2,
            radius: ['45%', '55%'],
            animationDuration: 1500,
            animationDurationUpdate: 1500,
            itemStyle: {
              borderRadius: 0,
            },
            left: '-50%', //2top10%
            top: '0%',
            emphasis: {},
            label: {
              show: false, // 不显示名称
            },
            data: newArr,

            //minAngle: 5 // 设置最小角度为5度
          },
          {
            type: 'gauge',
            zlevel: 1,
            // z: 198,
            center: ['25%', '50%'],
            radius: '40%',
            // left: '-25%', //2top10%
            // top: '10%',
            startAngle: 90,
            endAngle: -270,
            axisLine: {
              show: false,
            },
            axisTick: {
              distance: -6,
              length: 6,
              lineStyle: {
                color: '#585e67',
                width: 2,
              },
            },
            axisLabel: {
              show: false,
            },
            splitNumber: 6,
            splitLine: {
              show: true,
              distance: -6,
              length: 6,
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.18)',
                width: 2,
              },
            },
            data: [],
            detail: {},
            pointer: {
              show: false,
            },
            // detail: {
            //   show: 0,
            // },
          },
        ];
        let lineHeight = 14;
        let fontSize = 12;
        let top = '20%';
        let left = '50%';

        let option = {
          grid,
          tooltip,
          // 圆心文字
          title,
          // 图例样式
          legend: {
            type: 'scroll',
            textStyle: {
              color: '#fff',
              fontSize,
              opacity: 0.8,
              lineHeight, // 设置文字之间的上下间距
            },
            //scrollDataIndex:2,//默认第几页
            top, //调整图例位置
            left, //整体盒子距离
            /*  pageIcons:{
            vertical:[
              "image://https://img1.baidu.com/it/u=1890390320,3399874998&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto?sec=1703091600&t=c155259c9b8ac8844e997f0fc9e182fe"
            ]
          }, */
            pageIconColor: '#fff',
            pageIconInactiveColor: '#fff',
            pageIconSize: '12', //翻页按钮大小，可以是数组[]
            pageTextStyle: {
              color: '#FFF',
            },
            orient: 'vertical', //竖着展示
            //bottom: 'middle',
            //itemGap: 16, // 设置图例项之间的间距
            itemWidth: 15, //图例大小
            itemHeight: 7, //修改icon图形大小
            icon: 'circle', //图例前面的图标形状
            formatter: function (name) {
              //const item = newArr.find(item => item.name === name); // 查找当前图例对应的数据项
              return `${name}`;
            },
          },
          series,
        };
        myChart.setOption(option);
        window.addEventListener('resize', () => {
          myChart.resize();
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
#main {
  width: 100%;
  height: 25vh;
}
</style>
