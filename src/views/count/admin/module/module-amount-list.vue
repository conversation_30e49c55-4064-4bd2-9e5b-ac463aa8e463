<template>
  <div class="amount-module">
    <div
      class="amount-module-list"
    >
      <ModuleAmount
        :data="companyList[0].values"
        @showListFn="showListFn"
      />
    </div>
  </div>
</template>

<script>
import ModuleAmount from './enterpriseCard.vue'
export default {
  name: 'ModuleAmountList', 
  components: {
    ModuleAmount,
  },
  props: {
    companyList: {
      type: Array,
      default: null, // title / info
    },
  },
  data() {
    return {
      
    }
  },
  watch: {
    'companyList'(){
      //console.log(this.companyList[0].values);
    }
  },

  methods: {
    showListFn(i){
      this.$emit('showList',i.dicKey)
    }
  }
}
</script>

<style lang="scss" scoped>
@media  screen and (min-width: 1100px){
  .amount-module {
    width: 100%;
    height: calc(100% - 5rem) !important;
   
  }
}

.amount-module{
  display: flex;
  height: calc(100% - 3rem);
  padding: 0 32px !important;
  margin-top: 2% !important;
  overflow: hidden;
}
.amount-module-list{
  // margin: auto;
  width: 100%;

  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  .amount-info{ 
    box-sizing: border-box;
    width: calc(16% + 0.143rem);
    // width: calc(31.33% + 0.143rem);
    margin: 0.2rem;
  }
}
</style>
