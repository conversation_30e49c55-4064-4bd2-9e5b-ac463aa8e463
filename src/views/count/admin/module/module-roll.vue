<template>
  <div
    id="nwwest-roll"
    class="nwwest-roll"
  >
    <div
      v-if="dataList.length > 0"
      class="roll-ul"
    >
      <div 
        v-for="(item,index) in dataList"
        ref="roll"
        :key="index" 
        :class="['info', animate==true?'anim':'']" 
        @mouseover="moveWay"  
        @mouseleave="leaveWay"
      >
        <div class="item">
          <a
            class="name"
            :href="item.newsUrl"
            target="_blank"
          >{{ item.newsSummary || '-' }}</a>
          <!-- <span class="site">{{item.site}}</span> -->
          <a
            :href="item.newsUrl"
            class="time"
            target="_blank"
          >
            {{ item.publicDate ? item.publicDate && item.publicDate.split(' ')[0] : '' }}
          </a>
        </div>
      </div>
    </div>
    <div
      v-else
      class="no-data"
    >
      暂无产业事件
    </div>
  </div>
</template>

<script>
let setIntervalTime = null;
export default {
  name: 'ModuleRoll', 
  components: {
  },
  props: {
    dataList:{
      type: Array,
      default: function () {
       return [/* { newsSummary:'aaaa',gmtModify:'111' } */]
     }
    }
  },
  data() {
    return {
      animate:true,
      list:[]
    };
  },
  computed:{
   
  },
  watch: {
    // dataList: {
    //   deep: true,
    //   handler(val) {
    //   }
    // },
  },
  beforeCreate(){
    
  },
  mounted(){
    setIntervalTime = setInterval(this.scroll,2000) 
  },
  created(){},
  
  methods: {
    scroll(){
        let con1 = this.$refs.roll;
        con1[0].style.marginTop = '30px '
        this.animate=!this.animate;
        var that = this; // 在异步函数中会出现this的偏移问题，此处一定要先保存好this的指向
        setTimeout(function(){
            that.dataList.push(that.dataList[0]);
            that.dataList.shift();
            con1[0].style.marginTop='0px';
            that.animate=!that.animate;  // 这个地方如果不把animate 取反会出现消息回滚的现象，此时把ul 元素的过渡属性取消掉就可以完美实现无缝滚动的效果了
        },0)
    },
    // 移入
    moveWay(){
      clearInterval(setIntervalTime);
      setIntervalTime = null;
    },
    // 移出
    leaveWay(){
      setIntervalTime = setInterval(this.scroll,2000) 
    }
  }
}
</script>

<style lang="scss" scoped>
.nwwest-roll{
  height: 11rem;
  margin: 1rem auto;
  transition: all 0.5s;
  overflow: hidden;
  padding: rem 1rem;
  position: relative;
  .no-data{
    position: absolute;
      font-size: 16px;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
  }
}

.info{
  height: 1.2rem;
  line-height: 1.4rem;
  margin: 0.94rem 1rem;
  font-size: 0.8rem;
  display: flex;
  transition: all 0.2s;
  .item{
    width: 100%;
    display: flex;
    position:relative;
    padding: 0rem 0.4rem;
    background: rgb(0 17 45 / 67%);
    border: 0.14rem solid #1d40756b;
    height: 2rem;
    line-height: 2rem;
    margin: 0.5rem 0;
  }
  .name{
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  .time{ min-width: 9rem; text-align: right;}

}
.anim{
transition: all 0.5s;

}
</style>
