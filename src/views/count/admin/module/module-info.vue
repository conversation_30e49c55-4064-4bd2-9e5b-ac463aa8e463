<template>
  <div class="module-info">
    <div
      v-if="title"
      class="title"
    >
      <div class="title-info">
        <div :class="centres ? 'name-info' : 'name-info2'">
          {{ title }}
          <el-tooltip
            class="item"
            effect="dark"
            content="统计总值为各个环节的去重值"
            placement="top"
          >
            <span v-if="title=='产业环节分布'">
              <i class="el-icon-question" />
            </span>
          </el-tooltip>
        </div>
      </div>
    </div>
    <slot />
    <!-- </div> -->
  </div>
</template>

<script>
//import Img from '@/views/echarts/large-screen/img.vue'

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'ModuleInfo',
  components: {
    //Img
  },
  props: {
    type:{
      type: String,
      default: '', // title / info
    },
    title: {
      type: String,
      default: '', // title
    },
    centres:{
      type:Boolean,
      default:true
    }
  },
  data() {
    return {
      
    }
  },
  computed:{
   
  },
  watch:{
  },
  mounted(){
    
  },
  methods: {
    
    
  }
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: "title";
  src: url(~@/text/YouSheBiaoTiHei-2.ttf);
}
.module-info.module-info2{
  .bg-img{
    position: absolute;
    left: 0%;
    top: 0%;
    height: 92.5%;
    width:100%;
  }
}
.module-info{
  color: #fff;
  position: relative;
  padding: 0 0.4rem;
  .bg-img{
    position: absolute;
    width: 104%;
    height: 100%;
    left: -2%;
  }
  .module-main{
    position: absolute;
    z-index: 2;
  }
  .title{
    position: relative;
    display: flex;
    width: 103%;
    height: 2.2rem;
    .bg{
      width: 100%;
      position: absolute;
      z-index: 9;
      display: flex;
      top: -0.1rem;
      img{
        margin: auto;
        width: 130%;
        height: auto;
        margin-left: -2rem;
        height: 5rem;
      }
    }
    .bg2{
      width: 100%;
      position: absolute;
      z-index: 9;
      display: flex;
      top: -0.1rem;
      img{
        margin: auto;
        width: 100%;
        height: auto;
        margin-left: -1.35rem;
        height: 5rem;
      }
    }
    .title-info{
      width: 100%;
      color: #fff;
      z-index: 9999;
      display: flex;
      .name-info{
        font-size: 1.4rem;
        margin-left: 3.4rem;
        font-family: title;
        line-height: 3rem;
      }
      .name-info2{
        font-size: 1.4rem;
        margin-left: 4.8rem;
        font-family: title;
        line-height: 3rem;
      }
    }
  }
}
</style>
