<template>
  <div class="nwwest-roll" id="nwwest-roll">
    <div class="roll-ul">
      <div v-for="(item,idx) in dataList" ref="rollul" :key="idx" :class="['info', animate==true?'anim':'']">
        <span class="name">{{item.newsSummary}}</span>
        <!-- <span class="site">{{item.site}}</span> -->
        <span class="time">{{item.gmtModify ? item.gmtModify.split(' ')[0] : ''}}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModuleRoll', 
  components: {
  },
  props: {
    dataList:{
      type: Array,
      default: null, 
    }
  },
  data() {
    return {
      animate:true,
      list:[]
    };
  },
  computed:{
   
  },
  watch:{
  },
  beforeCreate(){
    
  },
  mounted(){
  },
  created(){
    setInterval(this.scroll,2000) 
  },
  methods: {
    scroll(){
        let con1 = this.$refs.rollul;
        con1[0].style.marginTop='30px';
        this.animate=!this.animate;
        var that = this; // 在异步函数中会出现this的偏移问题，此处一定要先保存好this的指向
        setTimeout(function(){
            that.list.push(that.list[0]);
            that.list.shift();
            con1[0].style.marginTop='0px';
            that.animate=!that.animate;  // 这个地方如果不把animate 取反会出现消息回滚的现象，此时把ul 元素的过渡属性取消掉就可以完美实现无缝滚动的效果了
        },0)
    }

  }
}
</script>

<style lang="scss" scoped>
.nwwest-roll{
  height: calc(100% - 6rem);
  margin: 2rem auto;
  transition: all 0.5s;
  overflow: hidden;
  padding: rem 1rem;
}

.info{
  height: 1.2rem;
  line-height: 1.4rem;
  margin: 0.94rem 1rem;
  font-size: 0.8rem;
  display: flex;
  .name{
    flex: 1;height: 1.4rem; overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  .time{ min-width: 9rem; text-align: right;}

}
.anim{
transition: all 0.5s;

}
</style>
