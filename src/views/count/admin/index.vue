<template>
  <div class="screen-count">
    <div class="screen-info">
      <!-- 产业洞察 -->
      <Headers state="dashboard">
        <div slot="view-container">
          <div
            class="reportcs"
            @click="report"
          >
            <div class="count">
              <img
                src="https://static.idicc.cn/cdn/pangu/acc.png"
                class="message"
              ><span>产业洞察</span>
            </div>
          </div>

          <div class="screen-content">
            <div class="top-info">
              <div class="count-module left">
                <!-- 企业数量及从业人员 -->
                <Titles :title="contentTitle[0]" />
                <enterprise-num-bar-chart :chart-data="contDataList[0][3]" />
                <!-- 上市板块 -->
                <Titles :title="contDataList[2][0].modelName" />
                <city-bar-chart-3D
                  :chart-data="contDataList[2][0]"
                  @showList="showList"
                />
                <!-- </ModuleInfo> -->
              </div>
              <div class="centre-info">
                <div class="module-info-2line">
                  <div class="module-info-2line-box">
                    <div class="centerTop">
                      <module-amount-list-single
                        :company-list="contDataList[0]"
                        :type-info="enterpriseNum"
                      />
                    </div>

                    <MapEcharts
                      slot="screenMain"
                      class-name="chart-container"
                      height="100%"
                      width="100%"
                      form-name="产业洞察"
                      :sel-num="selNum"
                      :is-count="isCount"
                      :get-map-relation="getMapRelation"
                      :search-node="searchNode"
                      @setFetchParams="setFetchParams"
                      @cityDataList="cityDataList"
                      @OnSearch="OnSearch"
                    />
                    <!-- :search-city="searchCity" -->
                  </div>
                </div>
              </div>
              <div class="count-module right">
                <!-- 市场主体分布top -->
                <Titles :title="contDataList[3][0].modelName" />
                <div class="righhtTop">
                  <TopFive
                    v-if="
                      contDataList[3][0] &&
                        contDataList[3][0]?.values?.length > 0
                    "
                    ref="topFiveRef"
                    :show-data="contDataList[3][0].values.slice(0, 5)"
                    :active="3"
                  />
                </div>

                <!-- <city-bar-chart
                  :chart-data="contDataList[4][0]"
                  :city-type="cityType[0]"
                /> -->
                <!-- 创新成果分布top -->
                <Titles :title="contDataList[4][0].modelName" />
                <city-bar-chart2
                  :chart-data="contDataList[4][0]"
                  :city-type="cityType[1]"
                />
              </div>
            </div>
            <div class="bottom-info">
              <!-- 科技型企业 -->
              <div class="bottom-info-left">
                <Titles
                  :title="contDataList[1][0].modelName"
                  :bg-large="true"
                />
                <ModuleAmountList
                  :company-list="contDataList[1]"
                  @showList="showList"
                />
                <!-- </ModuleInfo> -->
              </div>
              <div class="bottom-info-right">
                <!-- 产业环节分布 -->
                <Titles :title="contentTitle[6]" />
                <!-- 省份 -->
                <div
                  v-if="province"
                  class="percentagePlot"
                >
                  <PercentagePlot :data-list="rollList[0]" />
                </div>
                <!-- 全国 -->
                <div v-else>
                  <module-roll
                    v-if="rollList"
                    style="position: relative; z-index: 999"
                    :data-list="rollList?.[0]?.values"
                  />
                </div>
                <!-- </ModuleInfo> -->
              </div>
            </div>
          </div>
        </div>
      </Headers>
    </div>
    <div
      v-if="loading"
      v-loading="loading"
      style="z-index: 999"
      class="loading-info"
      customClass="loading-info-icon"
      element-loading-text=""
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgb(0 0 0 / 14%)"
      element-loading-color="#fff"
    />
    <deriveIndustry
      v-if="toleadfail"
      :toleadfail.sync="toleadfail"
    />
    <Enterpriselist
      v-if="isshowList"
      :nodemessage="nodemessage"
      :condition="condition"
      @closeList="closeList"
    />
  </div>
</template>

<script>
import { getPathId } from "@/utils/utils";
import Titles from "@/views/overview/components/component/titles.vue";
import {
  getCont,
  // industrialEvent,
  getCity,
  // loadByParent,
  chartApi,
  getList,
  industryChainNodeAPI,
  getMinAreaByTokenAPI,
} from "./apiUrl";
import { contentTitle, contList, rollList, contSet } from "./countData";
import deriveIndustry from "./deriveIndustry.vue";
import MapEcharts from "@/views/echarts/components/charts-map.vue";
// import NationalMap from '@/views/echarts/components/map/index.vue';
import EnterpriseNumBarChart from "./EnterpriseNumBarChart"; //
// import CityBarChart from './CityBarChart';
import CityBarChart2 from "./CityBarChart2";
import CityBarChart3D from "./CityBarChart3D";
import TopFive from "@/views/overview/components/right/component/topFive.vue";

// import LineChart from './LineChart';

import ModuleAmountList from "./module/module-amount-list.vue";
import ModuleAmountListSingle from "./module/module-amount-list-single.vue";
import ModuleRoll from "./module/module-roll1.vue";
import { message } from "@/utils/MessageUtil";
import Headers from "@/components/header.vue";
import Enterpriselist from "./Enterpriselist.vue";
import PercentagePlot from "@/components/PercentagePlot";

export default {
  name: "DashboardAdmin",
  components: {
    // ModuleInfo,
    // NationalMap,
    MapEcharts,
    Headers,
    EnterpriseNumBarChart,
    // CityBarChart,
    Enterpriselist,
    // LineChart,
    CityBarChart3D,
    ModuleAmountList,
    ModuleAmountListSingle,
    ModuleRoll,
    CityBarChart2,
    deriveIndustry,
    PercentagePlot,
    Titles,
    TopFive,
  },
  data() {
    return {
      isshowList: false,
      nodemessage: {},
      enterpriseNum: "enterpriseNum",
      chainName: "企业链",
      orgAreaName: "",
      toleadfail: false,
      rollList: rollList,
      cityOptionList: [],
      isCount: 1,
      searchNode: "",
      optionsList: [], // 产业链数据
      contentTitle: contentTitle, // 默认模块名称

      // 省市区 - 级联加载
      searchCity: { 0: "-1", filesUrl: ["-1"], path: "-1" },
      optionProps: {
        lazy: true,
        checkStrictly: true, // 任意一级选中
        lazyLoad: this.lazyLoad,
      },
      id: "",
      contDataList: contList, // 初始化展示
      loading: false,
      cityData: {},
      contChartSet: contSet, // 类型处理
      isFormatName:
        "近N年新增企业,上市及挂牌企业,上市企业营收,上市企业利润,企业重点分布省,企业重点分布市,企业重点分布区,'上市板块'", // 当chart 数据进行特殊处理
      unitList: [["个"], [""], ["家"], ["元", "元"], ["家"], ["家"]], // 单位
      fetchParams: {},
      selNum: 0,

      cityType: [], // 企业重点分布 省市
      cityType1: 5,
      cityType2: 6,

      secondQueryParam: "", // 二级查询菜单字段

      mapParams: {},
      condition: {},
      province: "",
      // searchDatas:{}
    };
  },
  computed: {},
  watch: {
    cityData() {
      this.initData();
    },
  },
  created() {
    this.getMinAreaByToken();
  },
  mounted() {
    this.id = this.$route.query?.id || getPathId() || null;
    this.getOptionsList(); // 产业链节点
    this.initData();
    this.getVisList();
  },
  methods: {
    getVisList() {
      const id = this.$route.query?.id || getPathId() || null;
      if (!id) {
        return;
      }

      let data = {
        id: parseInt(id),
        enterpriseType: 4, // 企业类型
        secondQueryParam: "", // 企业名称
      };
      getList(chartApi.atlasRelation, data).then((res) => {
        let data = res;
        this.$store.dispatch("visView/changeViewData", data);
      });
    },
    showList(data) {
      this.nodemessage = data.name || data;
      this.isshowList = true;
    },
    closeList() {
      this.isshowList = false;
    },
    async getMinAreaByToken() {
      let queryId = this.$route.query.id || getPathId() || null;

      const res = await getMinAreaByTokenAPI({
        relationId: queryId,
      });
      this.chainName = res.chainName;
      this.orgAreaName = res.minArea;
    },
    report() {
      // 设置菜单栏为打开状态
      // this.$store.dispatch("app/toggleSideBar");
      this.$router.replace("/idicc/view");
    },

    initData() {
      this.condition = this.formatParams();
      this.getContData(); // 统计接口
      this.getIndustrialEventWay(); // 产业事件
      //this.formatCity();
    },
    cityDataList(val) {
      this.cityData = val;
    },

    lazyLoad(node, resolve) {
      const { level, data = {} } = node;
      const { id = "" } = data;
      let params = { type: level + 1, parentId: id };
      let nodes = level === 0 ? [{ value: "-1", label: "全国" }] : [];
      if (level <= 2) {
        getCity(params)
          .then((res) => {
            res.forEach((city) => {
              const { id, name } = city;
              nodes.push({
                value: name,
                label: name,
                id: id,
                leaf: level >= 2,
              });
            });
            resolve(nodes);
          })
          .catch(() => {});
      } else {
        resolve([]);
      }
    },

    handleChange(value) {
      if (value) {
        this.searchCity.path = value[value.length - 1];
      }
      // 解决：el-cascader当设置了checkStrictly:true属性时，可以选择任意一级的菜单。但是同时设置动态加载的时候。点击前面的radio按钮会出现一个暂无数据的面板
      const panelRefs = this.$refs.refHandle.$refs.panel;
      if (panelRefs.activePath.length !== 0) {
        panelRefs.activePath.forEach((item) => {
          if (item.children.length === 0) {
            panelRefs.lazyLoad(panelRefs.getCheckedNodes()[0]);
          }
        });
      }
    },

    // 查询条件处理
    formatParams() {
      //const cityData = this.searchCity.filesUrl;
      const cityData = this.cityData;
      const nodeId = this.searchNode;
      let province = undefined;
      let city = undefined;
      let area = undefined;
      if (cityData) {
        if (cityData.length > 1) {
          province = cityData[1].countArea;
          if (cityData.length > 2) {
            city = cityData[2].countArea;
            if (cityData.length > 3) {
              area = cityData[3].countArea;
            }
          }
        }
      }
      let data = { id: this.id * 1, nodeId, province, city, area };
      // this.searchDatas =data
      return data;
    },

    // 统计接口 - 统一请求
    getContData() {
      let searchData = this.formatParams();
      this.fetchParams = searchData;
      this.selNum = this.selNum + 1;
      // 1、中间头部
      let p1 = this.getContWay({ ...searchData, modelType: 1 });
      // 2、科技型企业
      let p2 = this.getContWay({ ...searchData, modelType: 2 });
      // 3、上市及挂牌企业
      let p3 = this.getContWay({ ...searchData, modelType: 3 });

      // let p4 = this.getContWay({ ...searchData, modelType: 4 });

      // 全国范围显示5和6，省范围显示6和7， 市和区县范围显示5和7
      // 5.企业重点分布省；
      // 6.企业重点分布市；
      // 7.企业重点分布区
      let cityType = [5, 6];
      // 6， 创新成果分布
      //5、 市场主体分布TOP5
      const governDirectly = ["重庆市", "北京市", "天津市", "上海市"];
      if (governDirectly.includes(searchData.province)) {
        searchData.city = searchData.province;
      }
      if (searchData.province) {
        cityType = [5, 6];
      }
      if (searchData.city) {
        cityType = [5, 6];
      }
      this.cityType = cityType;
      this.cityType1 = 5;
      this.cityType2 = 6;
      if (searchData.city == "东莞市") {
        searchData.area = "东莞市";
      }
      let p5 = this.getContWay({ ...searchData, modelType: cityType[0] });
      let p6 = this.getContWay({ ...searchData, modelType: cityType[1] });
      this.loading = true;
      // Promise.all([p1, p2, p3, p4, p5, p6])
      Promise.all([p1, p2, p3,  p5, p6])
        .then((res) => {
          // if(!res[5] || !res[5].modelName){
          //   res[5] = [{'modelName':'企业重点分布省份',values:[]}]
          // }
          // if(!res[6] || !res[5].modelName){
          //   res[6] = [{'modelName':'企业重点分布城市',values:[]}]
          // }
          let today = new Date();
          let year = today.getFullYear();
          let month = today.getMonth() + 1;
          if (month <= 5) {
            year -= 2;
          } else {
            year -= 1;
          }
          this.contDataList = this.chartFormatData(res);
          // consoel.log(this.contDataList, 'contDataList==>');
          // this.contDataList[3][0].modelName = `上市公司经营状况(${year})`;
          this.loading = false;
        })
        .catch((error) => {
          this.loading = false;
          message.error(error);
        });
    },

    // 统计 - 数据格式化
    chartFormatData(source) {
      const dataList = [];
      const isFormatName = this.isFormatName;
      const contChartSet = this.contChartSet;
      const unitList = this.unitList;
      source.forEach((childList, idx) => {
        const child = [];
        childList.forEach((item, childIdx) => {
          if (isFormatName.includes(item.modelName) && idx !== 0) {
            const xData = [];
            const yData = [];
            let unit = "";
            unit = unitList[idx];
            item?.values.forEach((keyData) => {
              xData.push(keyData.dicKey);
              yData.push(keyData.dicValue);
            });
            child.push({
              loading: false,
              modelName: item.modelName,
              ...contChartSet[idx],
              ["unit" + parseInt(childIdx + 1)]: unit,
              ["xData" + parseInt(childIdx + 1)]: xData,
              ["yData" + parseInt(childIdx + 1)]: yData,
            });
          } else {
            child.push({ loading: false, ...item, ...contChartSet[idx] });
          }
        });
        dataList.push(child);
      });
      return dataList;
    },

    // 统计数据 - 产业事件
    async getIndustrialEventWay() {
      const params = {
        ...this.formatParams(),
        modelType: 7,
        nodeId: this.searchNode,
      };
      const res = await getCont(params);
      this.rollList = res;
      // ocnsoel.log(this.rollList, 'rollList');
    },

    // 统计接口请求
    getCont1(modelType) {
      // modelType
      // 1.企业数量及从业人员；
      // 2.科技型企业；
      // 3.上市板块；
      // 4.上市企业营收/利润；
      // 5.企业重点分布省；
      // 6.企业重点分布市；
      // 7.企业重点分布区
      const params = {
        modelType: modelType,
        ...this.formatParams(),
      };
      return new Promise((resolve, reject) => {
        getCont(params)
          .then((res) => {
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    getContWay(params) {
      // modelType
      // 1.企业数量及从业人员；
      // 2.科技型企业；
      // 3.上市板块；
      // 4.上市企业营收/利润；
      // 5.企业重点分布省；
      // 6.企业重点分布市；
      // 7.企业重点分布区

      return new Promise((resolve, reject) => {
        getCont(params)
          .then((res) => {
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 产业链节点数据 - 请求
    async getOptionsList() {
      const params = { id: this.$route.query?.id || getPathId() || null };
      /*       loadByParent(params).then((res) => {
        this.chainName = res.nodeName;
        this.orgAreaName = res.orgAreaName;
        this.optionsList = this.formatWay(res.childNodes);
      }); */
      const res = await industryChainNodeAPI(params);
      this.optionsList = res;
      // this.orgAreaName = res.orgAreaName;
    },

    // 产业链节点数据 - 数据格式化
    formatWay(childNodes) {
      let nodeList = [];
      childNodes?.forEach((item) => {
        let children = null;
        if (item?.childNodes && item.childNodes.length >= 1) {
          children = this.formatWay(item?.childNodes);
          nodeList.push({
            value: item.id,
            label: item.nodeName,
            children: children,
          });
        } else {
          nodeList.push({ value: item.id, label: item.nodeName });
        }
      });
      return nodeList;
    },

    // 搜索
    OnSearch(val) {
      this.searchNode = val;
      // this.getMapRelation();
      this.initData();
      // this.closeDetail();
    },

    // 地图双击 - 省市区传参
    mapDBCityData(params) {
      let searchCity = [];
      params.forEach((item) => {
        item.countArea != "全国" && searchCity.push(item.countArea);
      });
      if (!params || params.length == 1) {
        searchCity = ["全国"];
      }
      this.searchCity = searchCity;
      this.initData();
    },

    // 查询条件 - 传参 - 省/市/区
    setFetchParams(params) {
      this.fetchParams = params;
      this.mapParams = params;
    },
    // 获取地图数据
    getMapRelation() {
      const noData = ["重庆市", "北京市", "上海市", "天津市"];
      const id = this.$route.query?.id || getPathId() || null;
      if (!id) {
        return;
      }
      const fetchParams = this.mapParams;
      // const fetchParams = this.fetchParams;
      // const fetchParams = this.formatParams();
      let province = null;
      let city = null;
      let area = null;
      if (fetchParams && fetchParams.length >= 2) {
        province = fetchParams[1].countArea;
        if (noData.includes(province)) {
          city = fetchParams[1].countArea;
        }
        if (noData.includes(province) && fetchParams.length == 4) {
          area = fetchParams[3].countArea;
        }
        if (fetchParams.length >= 3 && !noData.includes(province)) {
          city = fetchParams[2].countArea;
          if (fetchParams.length >= 4) {
            area = fetchParams[3].countArea;
          }
        }
      }
      let data = {
        id: parseInt(id),
        //code: this.searchValueData,
        chainNodeId: this.searchNode,
        province,
        city,
        area,
        secondQueryParam: this.secondQueryParam,
      };
      this.province = province;
      return new Promise((resolve, reject) => {
        getList(chartApi.mapRelation, data)
          .then((res) => {
            this.dataList = res;
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
<style lang="scss">
::v-deep {
  .node-info-tree {
    border-color: #0066ff;
  }

  .node-info-tree.el-popper {
    background: #00112d;
    border: blue 1px solid;

    .el-cascader-menu {
      border-right: blue 1px solid;
    }

    .el-cascader-node__label {
      color: #fff;
    }

    .el-cascader__dropdown {
      border: red 1px solid;
    }
  }
}

.node-info-tree {
  border-color: #0066ff;
}

.node-info-tree.el-popper {
  border: #0066ff 1px solid;
  background: #00112d;

  .el-cascader-node__label {
    color: #fff;
  }

  .el-cascader-node:not(.is-disabled):hover,
  .el-cascader-node:not(.is-disabled):focus,
  .el-cascader-node:not(.is-disabled):hover,
  .el-cascader-node:not(.is-disabled):focus {
    background: #184290;
  }

  .el-radio__inner {
    border: #0066ff 1px solid;
    background: transparent;
  }

  .el-radio__input.is-checked .el-radio__inner {
    background: #0066ff;
  }

  .el-cascader__dropdown {
    border-color: #0066ff;
  }
}
.bottom-info-left {
  .module-info {
    img {
      width: 100% !important;
      left: 0 !important;
    }
    .title {
      width: 100% !important;
    }
    .bg2 img {
      width: 102% !important;
    }
    .imgbgc {
      width: 100% !important;
      height: 100% !important;
    }
  }
}
</style>
