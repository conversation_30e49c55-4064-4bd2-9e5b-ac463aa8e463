<template>
  <div
    :id="chartData[0].id"
    class="echart"
    :style="myChartStyle"
  />
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "LineChart",
  props: {
    chartData: {
      type: Array,
      default: function () {
        return [{ id: "echart" }];
      },
    },
  },
  data() {
    return {
      myChartStyle: { float: "left", width: "100%", height: "400px" }, //图表样式
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        val[0].xData1 && this.initEcharts(val);
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initEcharts(this.chartData);
    });
  },
  methods: {
    // 转为亿
    unitWay(source) {
      return (
        (source &&
          source?.map((item) => {
            return (item / 100000000).toFixed(2);
          })) ||
        []
      );
    },
    initEcharts(chartData) {
      let { xData1 = null, yData1 = null, id = "" } = chartData[0];
      let { yData2 = null } = chartData[1];
      let yData3 = [];
      yData1.map((e, n) => {
        if (e && yData2[n]) {
          let val = (yData2[n] / e).toFixed(4) * 100 || 0;
          val = val.toFixed(2);
          yData3.push(val);
        } else {
          yData3.push(0);
        }
      });
      yData1 = this.unitWay(yData1);
      //yData2 = this.unitWay(yData2);
      //console.log(yData3,'yData3营收利润率');
      //console.log(yData1,'yData1企业营收');

      // yData3 = this.unitWay(yData3);
      // 基本柱状图
/*       if (chartData[0].xData1[0] == "硅原料") {
        yData3 = ["41.40", "7.77", "9.65", "9.26", "10.38", "22.58"];
        yData1 = [
          "2089.34",
          "2690.47",
          "1378.59",
          "1080.13",
          "21359.78",
          "3.72",
        ];
      } */

/*        const option = {
        // 主图形位置 
        grid: {
          left:20,  right: 20, bottom: 0, top:'30', containLabel: true // containLabel  x/y轴内容 是否在内部
        },
        xAxis: {
           // 倾斜
           axisLabel: {
              interval:0,
              rotate:0,
              textStyle: {
                color: '#ffffffc2',
                fontSize:'12',
                itemSize:'',
                marginLeft:'2px'
              },
          },
          data: xData1,
          type: 'category',
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#57617B',
              fontSize:'20px'
            }
          },
        },
        // y轴数据属性设置
        yAxis: [
          {
            name: "营收(亿元)",
            type: "value",
            nameTextStyle: {
              color: "#F2F6FF", //颜色
              padding: [0, 0, 0, 0], //间距分别是 上 右 下 左
            },
            splitLine: { show: false },
            axisLine: {
              lineStyle: {
                color: '#fff',
              }
            },
            axisLabel: {
              formatter: '{value} ',
            }
          },
          {
            name: "营收利润率（%）",
            type: "value",
            nameTextStyle: {
              color: "#F2F6FF", //颜色
              padding: [0, 0, 0, 0], //间距分别是 上 右 下 左
            },
            splitLine: { show: false },
            axisLine: {
              lineStyle: {
                color: '#B4B4B4',
              }
            },
            axisLabel: {
              formatter: '{value} ',
            }
          }
        ],
        series: [
          {
            name: '企业营收',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: {
              normal: {
                width: 3
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 3, 0, [{
                  offset: 0,
                  color: '#3370FF'
                }, {
                  offset: 0.8,
                  color: '#1860b194'
                }], false),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 3
              }
            },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                  offset: 1,
                  color: '#3370FF'
                }, {
                  offset: 0,
                  color: '#1860b194'
                }])
              },
              emphasis: {
                color: '#3370FF',
                borderColor: '#3370FF',
                extraCssText: 'box-shadow: 8px 8px 8px rgba(0, 0, 0, 1);',
                borderWidth: 2
              }
            },
            barMinHeight: 2,
            data: yData1
          }, 

          // {
          //   name: '企业利润',
          //   type: 'line',
          //   smooth: true,
          //   symbol: 'circle',
          //   symbolSize: 5,
          //   showSymbol: false,
          //   yAxisIndex: 1,
          //   lineStyle: {
          //     normal: {
          //       width: 3
          //     }
          //   },
          //   areaStyle: {
          //     normal: {
          //       color: new echarts.graphic.LinearGradient(0, 0, 3, 0, [{
          //         offset: 1,
          //         color: '#F2C347'
          //       }, {
          //         offset: 0,
          //         color: '#f2c3471f'
          //       }], false),
          //       shadowColor: '#f2c3471f',
          //       shadowBlur: 10
          //     }
          //   },
          //   itemStyle: {
          //     normal: {
          //       color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
          //         offset: 0,
          //         color: '#f2c3471f'
          //       }, {
          //         offset: 1,
          //         color: '#F2C347'
          //       }])
          //     },
          //     emphasis: {
          //       color: '#F2C347',
          //       borderColor: '#F2C347',
          //       extraCssText: 'box-shadow: 8px 8px 8px rgba(0, 0, 0, 1);',
          //       borderWidth: 2
          //     }
          //   },
          //   data: yData2
          // }, 
          {
            name: '营收利润率',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            yAxisIndex: 1,
            lineStyle: {
              normal: {
                width: 3
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 3, 0, [{
                  offset: 1,
                  color: '#F2C347'
                }, {
                  offset: 0,
                  color: '#f2c3471f'
                }], false),
                shadowColor: '#f2c3471f',
                shadowBlur: 10
              }
            },
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                  offset: 0,
                  color: '#f2c3471f'
                }, {
                  offset: 1,
                  color: '#F2C347'
                }])
              },
              emphasis: {
                color: '#F2C347',
                borderColor: '#F2C347',
                extraCssText: 'box-shadow: 8px 8px 8px rgba(0, 0, 0, 1);',
                borderWidth: 2
              }
            },
            data: yData3
          }, 
        ],
        
        title: {
          text: '',
          x: 'center',
          y: 0,
          textStyle: {
            color: '#B4B4B4',
            fontSize: 14,
            fontWeight: 'normal',
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#00112dbf', // 提示框浮层的背景颜色。
          borderColor: '#0066FF', // 提示框浮层的边框颜色。
          borderWidth: 1, // 提示框浮层的边框宽。

          axisPointer: {
            type: 'shadow', // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
            axis: 'auto', // 指示器的坐标轴。 
            snap: true, // 坐标轴指示器是否自动吸附到点上
            label: {
              color:'#fff',
              show: true,
              backgroundColor: '#00112D'
            }
          },
          textStyle: { // 提示框浮层的文本样式。
            color: '#fff',
            fontStyle: 'normal',
            fontWeight: 'normal',
            fontFamily: 'sans-serif',
            fontSize: 12,
          },
          //实例
        formatter: function (params) {
              var res = '' + params[0].name;
              //使用for可以将需要的数据全部加到res 
              //注意下边的<br/>  
              let marker0 = "<span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgba(16,97,204,1);\"></span>";
              let marker1 = "<span style=\"display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(242 195 71)\"></span>";
              for (var i = 0, l = params.length; i < l; i++) {
                let marker = marker0;
                if(i == 1){
                  marker = marker1;
                }
                if(params[i].seriesName == '企业营收'){
                  res += '<br/>' + marker +params[i].seriesName + ': ' +'<span style="color: #00FFF0;font-size: 16px;">' +params[i].value+'</span> '+'亿元';
                }else{
                  res += '<br/>' + marker +params[i].seriesName + ': ' +'<span style="color: #00FFF0;font-size: 16px;">' +params[i].value+'</span> '+'%';
                }
                // res += '<br/>' + marker +params[i].seriesName + ': ' +'<span style="color: #00FFF0;font-size: 16px;">' +params[i].value+'</span> '+'亿元';
              }          
              return res;
          }
        }
      };  */
       let option = {
        title: {
          text: "",
          x: "center",
          y: 0,
          textStyle: {
            color: "#B4B4B4",
            fontSize: 14,
            fontWeight: "normal",
          },
        },
        grid: {
          left: 20,
          right: 20,
          bottom: 16,
          top: 40,
          containLabel: true, // containLabel  x/y轴内容 是否在内部
        },
        xAxis: {
          // 倾斜
          axisLabel: {
            interval: 0,
            rotate: 0,
            textStyle: {
              color: "#ffffffc2",
              fontSize: "12",
              itemSize: "",
              marginLeft: "2px",
            },
          },
          data: xData1,
          type: "category",
          boundaryGap: true,
          axisLine: {
            lineStyle: {
              color: "#57617B",
              fontSize: "20px",
            },
          },
          axisTick: {
            show: false // 隐藏 x 轴刻度线
         }
        },
        yAxis: [
          {
            name: "营收(亿元)",
            nameLocation: 'end',
            type: "value",
            nameTextStyle: {
              color: "#fff", //颜色
              opacity:0.3
            },
            splitLine: { show: true ,lineStyle: {
             color: '#ccc',
             width: 1,
             opacity:0.2,
             type: 'dashed'
            }},
            axisLine: {
              lineStyle: {
                color: "#fff",
              },
            },
            axisLabel: {
              formatter: "{value} ",
              color: "#fff",
              opacity:0.3
            },
          },
          {
            name: "营收利润率(%)",
            type: "value",
            nameTextStyle: {
              color: "#fff", //颜色
              opacity:0.3
            },
            splitLine: { show: false },
            axisLine: {
              lineStyle: {
                color: "#fff",
              },
            },
            axisLabel: {
              formatter: "{value} ",
              color: "#fff",
              opacity:0.3
            },
          },
        ],
        dataZoom: [],
        series: [
          {
            type: "pictorialBar", //类型
            name: "企业营收",
            barCategoryGap: "0%", //间距

            // symbolSize: 5, 图形大小？？
            showSymbol: false,
            symbol:
              "path://M12.000,-0.000 C12.000,-0.000 16.074,60.121 22.731,60.121 C26.173,60.121 -3.234,60.121 0.511,60.121 C7.072,60.121 12.000,-0.000 12.000,-0.000 Z",
   /*          label: {
              show: true,
              position: "top",
              color: "#fff",
            }, */
            areaStyle: {
              normal: {
                width: 3,
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  3,
                  0,
                  [
                    {
                      offset: 0,
                      color: "#3370FF",
                    },
                    {
                      offset: 0.8,
                      color: "#1860b194",
                    },
                  ],
                  false
                ),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 3,
              },
            },
            itemStyle: { 
              borderWidth: 3,
              //borderColor: '#fff',
              // 柱状图外边框线的渐变
              borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
               {
                 offset: 0,
                 color: '#fff'
               },
               {
                 offset: 1,
                 color: '#0e3269'
               }
             ]),
             // 背景颜色
             // color: 'rgba(69, 126, 255, 0.3)',
              color :new echarts.graphic.LinearGradient(0, 0, 0, 1, [
               {
                 offset: 0,
                 color: '#145395'
               },
               {
                 offset: 1,
                 color: '#0c3571'
               }
             ]),
              /* normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 1,
                    color: "#0d366f",
                    option:0.1,
                  },
                  {
                    offset: 0,
                    color: "#0d366f",
                    option:0.1
                  },
                ]),
              }, */
              // 鼠标悬浮柱状图的样式
              emphasis: {
                color: "#3370FF",
                borderColor: "#3370FF",
                /* color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
               {
                 offset: 0,
                 color: '#4bf0d9'
               },
               {
                 offset: 1,
                 color: '#062e4f'
               }
              ]),
                borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
               {
                 offset: 0,
                 color: '#4bf0d9'
               },
               {
                 offset: 1,
                 color: '#0b3a57'
               }
             ]), */
                extraCssText: "box-shadow: 8px 8px 8px rgba(0, 0, 0, 1);",
                borderWidth: 2,
              },
            },
            barMinHeight: 2,
            data: yData1,
          },
          {
            type: "line",
            name: "营收利润率",
            symbol: "circle",
            symbolSize: 5,
            showSymbol: true,
            yAxisIndex: 1,
            //smooth: true,
            data: yData3,
             itemStyle: {
              color: "#fff",
              borderWidth: 0,
              opacity: 0.8, // 不透明
            }, 
            lineStyle: {
              color: "#417cc0",
              width: 1.5,
            },
          //    areaStyle: {
          //     normal: {
          //       color: new echarts.graphic.LinearGradient(0, 0, 3, 0, [{
          //         offset: 1,
          //         color: '#F2C347'
          //       }, {
          //         offset: 0,
          //         color: '#f2c3471f'
          //       }], false),
          //       shadowColor: '#f2c3471f',
          //       shadowBlur: 10
          //     }
          //   },  
          //    itemStyle: {
          //     normal: {
          //       color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
          //         offset: 0,
          //         color: '#f2c3471f'
          //       }, {
          //         offset: 1,
          //         color: '#F2C347'
          //       }])
          //     },
          //     emphasis: {
          //       color: '#F2C347',
          //       borderColor: '#F2C347',
          //       extraCssText: 'box-shadow: 8px 8px 8px rgba(0, 0, 0, 1);',
          //       borderWidth: 2
          //     }
          //   },  
          },
        ],
        tooltip: {
          trigger: "axis",
          backgroundColor: "#00112dbf", // 提示框浮层的背景颜色。
          borderColor: "#0066FF", // 提示框浮层的边框颜色。
          borderWidth: 1, // 提示框浮层的边框宽。

          axisPointer: {
            type: "shadow", // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
            axis: "auto", // 指示器的坐标轴。
            snap: true, // 坐标轴指示器是否自动吸附到点上
            label: {
              color: "#fff",
              show: true,
              backgroundColor: "#00112D",
            },
          },
          textStyle: {
            // 提示框浮层的文本样式。
            color: "#fff",
            fontStyle: "normal",
            fontWeight: "normal",
            fontFamily: "sans-serif",
            fontSize: 12,
          },
          //实例
          formatter: function (params) {
            var res = "" + params[0].name;
            //使用for可以将需要的数据全部加到res
            //注意下边的<br/>
            let marker0 =
              '<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgba(16,97,204,1);"></span>';
            let marker1 =
              '<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:rgb(242 195 71)"></span>';
            for (var i = 0, l = params.length; i < l; i++) {
              let marker = marker0;
              if (i == 1) {
                marker = marker1;
              }
              if (params[i].seriesName == "企业营收") {
                res +=
                  "<br/>" +
                  marker +
                  params[i].seriesName +
                  ": " +
                  '<span style="color: #00FFF0;font-size: 16px;">' +
                  params[i].value +
                  "</span> " +
                  "亿元";
              } else {
                res +=
                  "<br/>" +
                  marker +
                  params[i].seriesName +
                  ": " +
                  '<span style="color: #00FFF0;font-size: 16px;">' +
                  params[i].value +
                  "</span> " +
                  "%";
              }
              // res += '<br/>' + marker +params[i].seriesName + ': ' +'<span style="color: #00FFF0;font-size: 16px;">' +params[i].value+'</span> '+'亿元';
            }
            return res;
          },
        },
          animation:true,
          animationDuration: function () {
           return 3000;
          },
          animationDurationUpdate: function () {
              return 3000;
          },
      }; 

      const myChart = echarts.init(document.getElementById(id));
      myChart.setOption(option);
      //随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  },
};
</script>
<style scoped lang="scss">
.echart{
  z-index: 9999;
}
</style>

