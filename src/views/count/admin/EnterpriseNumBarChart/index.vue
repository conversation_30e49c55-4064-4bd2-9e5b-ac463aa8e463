<template>
  <div class="echart">
    <div class="number">
      <div class="init">
        <span class="text">近5年新增企业</span>
        <span class="num"> {{ numbers }}</span>
        <span class="int"> 家</span>
      </div>
    </div>
    <div
      :id="chartData.id"
      ref="provinceRankingBarChart"
      class="myChartStyle"
    />
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'EnterpriseNumBarChart',
  props: {
    chartData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      sourceData: {},
      
      dataUnit: [
        [10000, '万个'],
        [1000000, '百万个'],
      ],
      numbers: 0,
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        const params = this.formatWay(val);
        this.initEcharts(params);
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      const params = this.formatWay(this.chartData);
      this.initEcharts(params);
    });
  },
  methods: {
    // 判断当前的单位
    unitDecideWay() {
      let dataU = [];
      // let dataUnit = this.dataUnit;
      /*       source.forEach((item) => {
        if(item.dicValue >= 10000){
          dataU = [10000, '万家']
        }
      }); */
      dataU = [1, '家'];
      return dataU;
    },
    formatWay(val) {
      const xData = [];
      const yData = [];
      const unitD = this.unitDecideWay(val.values);
      val.values.forEach((item) => {
        // console.log('==values',item)
        if (item.dicKey) {
          xData.push(item.dicKey);
        }
        if (!(unitD && unitD.length > 0)) {
          yData.push(item.dicValue);
        } else {
          let xD = parseInt(item.dicValue / unitD[0]);
          if (item.dicValue < 10000) {
            xD = parseFloat(item.dicValue / unitD[0]);
          }
          yData.push(xD);
        }
      });
      const source = { ...val, xData, yData, unit: unitD ? unitD[1] : '家' };
      return source;
    },

    initEcharts(chartData) {
      let { xData = null, yData = null, id = null, unit = '个' } = chartData;
      let amount = yData.reduce((acc, cur) => {
        return Number(acc) + Number(cur) || 0;
      }, 0);
      this.numbers = amount;
      let option = {
        //   title: [
        //     {
        //       text: ``,
        //       textStyle: {
        //         //color: '#626d7c',
        //         color: '#fff',
        //         opacity: 0.6,
        //         fontSize: 12,
        //         fontWeight: '100',
        //       },
        //       padding: [10, 0, 0, 0],
        //     },
        //     {
        //       text: `${sum} 家`,
        //       textStyle: {
        //         color: '#FFF',
        //         fontSize: 13,
        //         fontWeight: 'normal',
        //       },
        //       left: '27%',
        //       padding: [10, 0, 0, 0],
        //     },
        //   ],
        grid: {
          // 离容器左侧的距离
          left: '10%',
          top: '10%',
          right: '5%',
          bottom: '15%',
        },
        /*         legend: {
          // 图例，可以点击每条线的名称来显示/隐藏该线
          data: ["销量1"],
        }, */
        xAxis: {
          data: xData,
          // 去除x轴的背景线条
          splitLine: {
            show: false,
          },
          axisLabel: {
            color: '#C9D2DD',
          },
          // 隐藏x轴的刻度线和线条
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          // 隐藏y轴的数字
      
          // 去除y轴的背景线条
          splitLine: {
            show: true,
            lineStyle: {
              color: '#FFFFFF',
              width: 1,
              opacity: 0.18,
              type: 'dashed',
            },
          },
          axisLine: {
            color: '#fff',
            lineStyle: {
              color: '#fff',
            },
          },
          axisLabel: {
            formatter: '{value} ',
            color: '#C9D2DD',
          },
        },
        series: [
          {
            type: 'line',
            smooth: true,
            data: yData,
            areaStyle: {
              //color: 'rgba(22, 52, 130, 0.5)', // 设置阴影颜色及透明度
              origin: 'auto', // 设置阴影起始位置为折线图的起始位置
              color: {
                type: 'linear', // 设置渐变类型为线性渐变
                x: 0, // 渐变起始位置
                y: 0, // 渐变起始位置
                x2: 0, // 渐变结束位置
                y2: 1, // 渐变结束位置
                colorStops: [
                  {
                    // 颜色渐变控制点
                    offset: 0, // 0%!处(MISSING)的颜色
                    color: 'rgba(54, 132, 211, 1)', // 颜色
                  },
                  {
                    // 颜色渐变控制点
                    offset: 1, // 0%!处(MISSING)的颜色
                    color: 'rgba(2, 34, 74, 0.1)', // 颜色
                  },
                ],
              }, // 设置渐变阴影颜色及透明度
            },
            lineStyle: {
              width: 2,
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: '#09EDDF' },
                { offset: 0.4, color: '#22B8F3' },
                { offset: 1, color: '#2083FC' },
                // { offset: 0.5, color: '#00d5ff' },
                // { offset: 1, color: 'rgba(255, 255, 255, 0)' }
              ]),
            },
            // lineStyle: {
            //   color: '#417cc0',
            //   width: 2,
            // },
            // 设置每个点的大小和颜色
            itemStyle: {
              color: '#60aaff',
              borderWidth: 0,
              opacity: 1, // 不透明
            },
            emphasis: {
              itemStyle: {
                borderWidth: 1, // 设置选中状态下的边框宽度为1
                opacity: 1, // 不透明
              },
            },
            symbol: 'circle', // 设置点的形状为圆形
            symbolSize: 5, // 设置每个点的大小为10
            // 在折线图上显示对应的数值
            label: {
              show: true,
              position: "top",
              color: '#fff', // 将标签颜色设为白色
            },  
          },
        ],
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#00112dbf', // 提示框浮层的背景颜色。
          borderColor: '#0066FF', // 提示框浮层的边框颜色。
          borderWidth: 1, // 提示框浮层的边框宽。

          axisPointer: {
            type: 'none', // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
            axis: 'auto', // 指示器的坐标轴。
            snap: true, // 坐标轴指示器是否自动吸附到点上
            label: {
              color: '#fff',
              show: false,
              backgroundColor: '#00112D',
            },
          },
          textStyle: {
            // 提示框浮层的文本样式。
            color: '#fff',
            fontStyle: 'normal',
            fontWeight: 'normal',
            fontFamily: 'sans-serif',
            fontSize: 12,
          },
          //实例
          formatter: function (params) {
            let res =
              params[0].name +
              '年： ' +
              '<span style="color: #00FFF0;font-size: 16px;">' +
              params[0].value +
              '</span> ' +
              unit;
            return res;
          },
        },
        animation: true,
        animationDuration: function () {
          return 3000;
        },
        animationDurationUpdate: function () {
          return 3000;
        },
      };
      const myChart = echarts.init(document.getElementById(id));
      myChart.setOption(option);
      //随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize();
      });
    },
  },
};
</script>
<style scoped lang="scss">
@import '@/styles/public.scss';
@import '@/styles/screen/card.scss';
 
.myChartStyle {
  width: 100%;
  height: calc(100% - 65px);
}
</style>
