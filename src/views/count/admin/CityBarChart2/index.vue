<template>
  <!-- <div>444</div> -->
  <div
    :id="chartData.id"
    class="echart"
    :style="myChartStyle"
  />
</template>
  
  <script>
  import * as echarts from "echarts";
  import { provinceList } from '../countData'
import {YAxis,XAxis} from '@/utils/XYLineConfig'
  
  export default {
    name:'CityBar2',
    props: {
      chartData: {
        type: Object,
        default:function () {
         return { id: 'echart' }
       }
      },
      cityType:{
        type:Number,
        default:null
      }
    },
    data() {
      return {
        myChartStyle: { float: "left", width: "100%", height: "400px" } //图表样式
      };
    },
    watch: {
      chartData: {
        deep: true,
        handler(val) {
         val &&  this.initEcharts(val)
        }
      },
    },
    mounted() {
      this.$nextTick(() => {
        this.initEcharts(this.chartData);
      })
    },
  
    methods: {
      xFromatData(params){
        let newCityList = []
        if(this.cityType == 5){
          params.forEach((city) => {
            const province =  provinceList.find((item )=>{
              if(item[1] === city){
                return true
              }
            })
            if(province){
              newCityList.push(province[0])
            }else{
              newCityList.push(city);
            }
          });
          return newCityList;
        }else{
          return params;
        }
      },
      initEcharts(chartData) {
        const { id='city', type='bar',} = chartData;
        let texts = ''
        let unit1 = ''
        if(chartData.id == 'sixth'){
          texts= '数量(个)'
          unit1='个'
        }else{
          texts= '数量(家)'
          unit1='家'
        }
         let dataX =[]
         let dataY =[] 
         if(chartData.values){
          this.chartData.values.forEach(item =>{
          if(item.dicKey){
            dataX.push(item.dicKey) 
          }
        })
        chartData.values.forEach(item =>{
          if(item.dicValue){
            dataY.push(item.dicValue) 
          }
        })
        }else{
          dataX =chartData.xData1
          dataY =chartData.yData1
        }
  
      //  let xData = this.xFromatData(xData1);
        
        // 基本柱状图
        const option = {
          // 主图形位置 
          grid: {
            left:0,  right: 24, bottom: 0, top:'25%', containLabel: true // containLabel  x/y轴内容 是否在内部
          },
       
          title: {
            text:texts,
            x:-5,
            y: 16,
            textStyle: {
              color: '#C9D2DD',
              fontSize: 12,
              fontWeight: 'normal',
            },
          },
              //x, y轴数据属性设置
        xAxis:XAxis(dataX,true),
        yAxis: [YAxis()],
   
          series: [
            { 
              name: '',
              type:type,  //形状为柱状图
              barWidth: 10,
              itemStyle: {
                normal: {
                  barBorderRadius: 0,
                  type: 'linear',
                   color: new echarts.graphic.LinearGradient(
                    0, 0, 0, 1,
                    [
                      { offset: 0, color: '#46FF9B' },

                      { offset: 1, color: 'rgba(29, 255, 232, 0.01)' }
                    ]
                  ) ,
                //  color:'#187575',
                opacity:0.8
                }
              },
              barMinHeight: 2,
              data: dataY,
            }
          ],
         
          tooltip: {
            trigger: 'axis',
            backgroundColor: '#00112dbf', // 提示框浮层的背景颜色。
            borderColor: '#0066FF', // 提示框浮层的边框颜色。
            borderWidth: 1, // 提示框浮层的边框宽。
  
            axisPointer: {
              type: 'shadow', // 'line' 直线指示器  'shadow' 阴影指示器  'none' 无指示器  'cross' 十字准星指示器。
              axis: 'auto', // 指示器的坐标轴。 
              snap: true, // 坐标轴指示器是否自动吸附到点上
              label: {
                color:'#fff',
                show: false,
                backgroundColor: '#00112D'
              }
            },
            textStyle: { // 提示框浮层的文本样式。
              color: '#fff',
              fontStyle: 'normal',
              fontWeight: 'normal',
              fontFamily: 'sans-serif',
              fontSize: 12,
            },
            //实例
          formatter: function (params) {
              let res = params[0].name  +params[0].seriesName + ': ' +'<span style="color: #00FFF0;font-size: 16px;">' +params[0].value+'</span> '+ unit1;
              return res;
            }
          },
          animation:true,
        animationDuration: function () {
         return 3000;
        },
        animationDurationUpdate: function () {
            return 3000;
        }
        };
        const myChart = echarts.init(document.getElementById(id));
        myChart.setOption(option);
        //随着屏幕大小调节图表
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      }
    }
  };
  </script>