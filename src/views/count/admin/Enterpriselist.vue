<template>
  <div class="box">
    <div class="list">
      <div class="listHeader">
        <span class="title"> {{ nodemessage }} 
          <span v-if="!(apiUrl==='parkView')">
            ({{ totals }})
          </span>
        </span>
        <el-tooltip
          v-if="!state"
          effect="dark"
          content="默认导出前5000条数据"
          placement="top"
        >
          <div
            class="export"
            :class="xzloading ? 'lod' : ''"
            @click="exportex"
          >
            <img
              v-if="!xzloading"
              src="https://static.idicc.cn/cdn/pangu/dashboard.png"
              class="up"
            >
            <!-- <span>{{ xzloading ? '导出中' : '导出' }}</span> -->
            <span v-if="!xzloading">导出</span>
            <i
              v-if="xzloading"
              style="color: #fff !important; margin-right: 5px"
              class="el-icon-loading"
            />
            <span v-if="xzloading">导出中</span>
          </div>
        </el-tooltip>
      </div>
      <div
        class="listClose"
        @click="close"
      />
      <div class="content">
        <div
          class="headline"
          :style="{
            gridTemplateColumns: dataList[headerList].gridTemplateColumns,
          }"
        >
          <div
            v-for="item in dataList[headerList ? headerList : 'default'].data"
            :key="item.id"
            class="listItem"
          >
            <span>{{ item.name }}</span>
          </div>
        </div>
        <div
          v-loading="listloading"
          class="enterpriseList"
          element-loading-background="rgba(27, 71, 134, 0.5)"
        >
          <div
            v-for="(item, index) in enterpriseList"
            :key="index"
            class="enterpriseListItem"
          >
            <div
              v-if="showList === 'default' || !showList"
              class="singleBox"
              :style="{
                gridTemplateColumns: dataList[headerList].gridTemplateColumns,
              }"
            >
              <!-- default 5个  -->
              <el-tooltip
                effect="dark"
                :content="item.enterpriseName"
                placement="top-start"
              >
                <div class="name listItem">
                  {{ item.enterpriseName }}
                </div>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                :content="item.place"
                placement="top-start"
              >
                <div class="area listItem">
                  <span class="areas">{{ item.place }}</span>
                </div>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                :content="item.secondChainNodeNames"
                placement="top-start"
              >
                <div class="link listItem">
                  <span class="areas">{{ item.secondChainNodeNames }}</span>
                </div>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                :content="item.registerDate"
                placement="top-start"
              >
                <div class="time listItem">
                  <span class="areas">{{ item.registerDate }}</span>
                </div>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                :content="item.registeredCapital"
                placement="top-start"
              >
                <div class="capital listItem">
                  <span class="areas">{{ item.registeredCapital }}</span>
                </div>
              </el-tooltip>
            </div>
            <div
              v-if="showList && showList === 'industrialAgglomerationArea'"
              class="singleBox"
              :style="{
                gridTemplateColumns: dataList[headerList].gridTemplateColumns,
              }"
            >
              <div class="name">
                {{ item.name }}
              </div>
              <el-tooltip
                effect="dark"
                :content="item.type"
                placement="top-start"
              >
                <div class="time listItem">
                  <span class="areas">{{ item.type }}</span>
                </div>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                :content="item.place"
                placement="top-start"
              >
                <div class="area listItem">
                  <span class="areas">{{ item.place }}</span>
                </div>
              </el-tooltip>
            </div>
            <div
              v-if="showList && showList === 'industrialFund'"
              class="singleBox"
              :style="{
                gridTemplateColumns: dataList[headerList].gridTemplateColumns,
              }"
            >
              <!-- industrialFund 2个  -->
              <div class="name">
                {{ item.name }}
              </div>
              <el-tooltip
                effect="dark"
                :content="item.type"
                placement="top-start"
              >
                <div class="time listItem">
                  <span class="areas">{{ item.place }}</span>
                </div>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
      <div class="ye">
        <el-pagination
          small
          :current-page.sync="form.pageNum"
          :page-size.sync="form.pageSize"
          :total="+total"
          layout="prev, pager, next"
          @size-change="formationList"
          @current-change="formationList"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  insightEnterpriseListAPI,
  getOverViewEnterpriseList,
  getInstitutionList,
  getParkOverView,
} from './apiUrl';
import { insightEnterprisedownloadAPI } from '@/api/export';
import { getPathId } from '@/utils/utils';

export default {
  name: 'EnterpriseList',
  props: {
    type: {
      type: String,
      default: '',
    },
    nodemessage: {
      type: String,
      default: () => {},
    },
    condition: {
      type: Object,
      default: () => {},
    },
    state: {
      type: Number,
      default: null,
    },
    showList: {
      type: String,
      default: null,
    },
    apiUrl: {
      type: String,
      default: '',
    },
    areaCode: {
      type: String,
      default: '',
    },

    institutionType: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      img: 'https://static.idicc.cn/cdn/pangu/listtitle.png',
      form: {
        pageNum: 1,
        pageSize: 11,
      },
      total: 0,
      totals: 0,
      listloading: false,
      xzloading: false,
      enterpriseList: [],
      dataList: {
        // 默认
        default: {
          data: [
            {
              name: '企业名称',
              id: 1,
            },
            {
              name: '所在地区',
              id: 2,
            },
            {
              name: '产业环节',
              id: 3,
            },
            {
              name: '成立日期',
              id: 4,
            },
            {
              name: '注册资金',
              id: 5,
            },
          ],
          gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr',
        },
        //  产业聚集区,高等院校,研发机构,:名称、类型、所在地区
        industrialAgglomerationArea: {
          data: [
            {
              name: '名称',
              id: 1,
            },
            {
              name: '类型',
              id: 2,
            },
            {
              name: '所在地区',
              id: 3,
            },
          ],
          gridTemplateColumns: '2fr 1fr 1fr',
        },

        // 产业基金:名称、所在地区
        industrialFund: {
          data: [
            {
              name: '名称',
              id: 1,
            },
            {
              name: '所在地区',
              id: 1,
            },
          ],
          gridTemplateColumns: '1fr 1fr',
        },
      },
      headerList: 'default',
    };
  },
  created() {
    this.formationList();
  },
  methods: {
    close() {
      this.$emit('closeList');
    },
    async exportex() {
      if (this.xzloading) {
        return this.$message({
          message: '正在导出中，请耐心等待',
          type: 'warning',
          customClass: 'admin-tips-message',
          duration: 3000,
          showClose: true,
          center: false, // 是否居中
        });
      }
      if (this.total == 0) {
        return this.$message({
          message: '这里还什么都没有~',
          type: 'warning',
          customClass: 'admin-tips-message',
          duration: 3000,
          showClose: true,
          center: false, // 是否居中
        });
      }
      let queryId = this.$route.query.id || getPathId() || null;
      let data = {
        secondQueryParam: this.nodemessage,
        pageNum: 1,
        pageSize: 5000,
        id: queryId,
        province: this.condition.province,
        city: this.condition.city,
        area: this.condition.area,
        chainNodeId: this.condition.nodeId,
      };
      try {
        this.xzloading = true;
        const res = await insightEnterprisedownloadAPI(data);
        let blob = new Blob([res], {
          type: 'text/csv,charset=UTF-8',
        });
        let objectUrl = URL.createObjectURL(blob);
        const fileName = `${this.nodemessage}列表.xlsx`;
        const downloadLink = document.createElement('a');
        downloadLink.href = objectUrl;
        downloadLink.download = fileName;
        downloadLink.click();
      } finally {
        this.xzloading = false;
      }
    },
    async formationList() {
      if (!this.showList) {
        this.headerList = 'default';
      } else {
        this.headerList = this.showList;
      }
      if (!this.showList) {
        let queryId = this.$route.query.id || getPathId() || null;

        try {
          this.listloading = true;
          // /boardDriver/enterpriseList
          let data = {
            secondQueryParam: this.nodemessage,
            pageNum: this.form.pageNum,
            pageSize: this.form.pageSize,
            id: queryId,
            province: this.condition.province,
            city: this.condition.city,
            area: this.condition.area,
            chainNodeId: this.condition.nodeId,
          };
          const res = await insightEnterpriseListAPI(data);
          this.setData(res);
        } finally {
          this.listloading = false;
        }
      } else {
        let queryId = this.$route.query.id || getPathId() || null;
        if (this.apiUrl === 'overViewEnterpriseList') {
          let data = {
            orgChainRelationId: queryId, // 机构产业链关联ID
            regionCode: this.areaCode, // 地区code,为空则查询机构所在地
            secondQueryParam: this.nodemessage, // 查询分类名,为空则查询全部
            pageNum: this.form.pageNum,
            pageSize: this.form.pageSize,
          };
          getOverViewEnterpriseList(data)
            .then((res) => {
              this.setData(res);
            })
            .finally(() => {
              this.loading = false;
            });
        } else if (this.apiUrl === 'parkView') {
          let data = {
            orgChainRelationId: queryId, // 机构产业链关联ID
            regionCode: this.areaCode, // 地区code,为空则查询机构所在地
            code: 'zmd20250612', // 机构产业链关联ID
            type: this.type,
            pageNum: this.form.pageNum,
            pageSize: this.form.pageSize,
          };
          getParkOverView(data)
            .then((res) => {
              this.setData(res);
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          let data = {
            orgChainRelationId: queryId, // 机构产业链关联ID

            regionCode: this.areaCode, // 地区code,为空则查询机构所在地
            pageNum: this.form.pageNum,
            pageSize: this.form.pageSize,
            institutionType: this.institutionType, // 1, "高等院校" 2, "产业基金" 3, "研究机构" 4, "产业聚集区"
          };
          getInstitutionList(data)
            .then((res) => {
              this.setData(res);
            })
            .finally(() => {
              this.loading = false;
            });
        }
      }
    },
    setData(data) {
      // console.log(this.headerList, this.showList, 'showList')
      this.enterpriseList = data.records;
      // showList industrialAgglomerationArea 3个
      // industrialFund 2个
      // default 5个
      this.enterpriseList.forEach((item) => {
        if (item?.province == item?.city) {
          item.place = item?.province + item?.area;
        } else {
          item.place = item?.province + item?.city + item?.area;
        }
        if (item.secondChainNodeNames) {
          item.secondChainNodeNames = item.secondChainNodeNames.join(';');
        }
      });
      // console.log(this.enterpriseList)
      this.totals = data.total;
      this.total = data.total > 10000 ? 10000 : data.total;
    },
  },
};
</script>

<style lang="scss" scoped>
@import './Enterpriselist.scss';
</style>
