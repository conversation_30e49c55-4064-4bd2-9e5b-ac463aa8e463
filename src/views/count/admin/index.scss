.screen-bg,
.screen-info {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
}

.screen-bg {
  background-image: linear-gradient(to bottom, #e4e4e421, #ffffff14 3%, #00800000 20%);
  z-index: 9;
}

.screen-info {
  z-index: 99;
  background: transparent;
  position: fixed;
  z-index: 999;
  position: relative;

  width: 100%;
  height: 100vh;
  position: absolute;
  top: 0px;
}

.screen-content {
  width: 100%;
  height: calc(100vh - 60px);
  padding: 0 16px 0 0;
}
.screen-count {
  background-color: #070d61;
  width: 100vw;
  height: 100vh;
  position: relative;
  min-width: 1080px;
  overflow: hidden;
  .top-info {
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    height: calc(100vh - 340px);

    .count-module {
      display: flex;
      width: 26%;
      height: 100%;
      flex-direction: column;
      padding: 16px;
    }

    .count-module.left {

    }
      .count-module.right {
        height: 90%;
      .righhtTop {
        width: 100%;
        height: calc(50% - 30px);
        padding: 16px 16px 16px 32px;
      }
    }

    .centre-info {
      display: flex;
      flex: 1;
      height: 100%;
      flex-direction: column;
      .module-info-2line {
        height: 100%;
        &-box {
          position: relative;
          box-sizing: border-box;
          height: 100%;
        }
        .centerTop {
          position: absolute;
          top: 2%;
          left: 27%;
          width: 60%;
        }
      }
    }

    .chart-container {
      margin: auto;
      position: relative;
    }
  }
  .bottom-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 270px;
    width: 100%;
    position: relative;
    .bottom-info-left {
      width: 73%;
      height: 100%;
      display: flex;
      padding: 6px 0rem;
      flex-direction: column;
    }
    .bottom-info-right {
      width: 26%;
      // height: 100%;
      height: 30vh;
      padding: 6px 1rem;
      position: absolute;
    right: 0;
    bottom: 0;
      .module-info {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.echart {
  height: calc(50% - 30px) !important;
  padding-left: 30px;
}

::v-deep {
  .el-input--suffix .el-input__inner {
    color: #fff;
  }

  .el-select > .el-input {
    width: 8.4rem;
    //border: 1px solid #3695ff;
    font-weight: 700;
    background-color: #031c3e;
    border-radius: 8px;
  }

  .el-select .el-input.is-focus .el-input__inner {
    border: 1px solid #3695ff;
  }

  .el-select .el-input__inner:focus {
    border: 1px solid #3695ff;
  }

  .el-input--suffix .el-input__inner {
    border: 1px solid #3695ff;
    font-weight: 700;
    background-color: #031c3e;
    border-radius: 8px;
  }

  .el-select-dropdown__item .selected :hover {
    border: 1px solid #3695ff;
  }

  .el-input--suffix .el-input__inner:hover {
    border: 1px solid #3695ff;
  }

  .el-cascader {
    .el-input .el-input__inner {
      font-size: 0.8rem;
      background: transparent;
      border: none;
      height: 100%;
      color: #fff;
    }
  }

  .city-info.el-cascader {
    width: 6rem;
    font-size: 14px;
    position: absolute;
    left: 1.4rem;
    height: 50%;
    line-height: 50%;
    margin-top: 2%;

    s .el-input__suffix {
      top: -30%;
    }

    .el-input {
      .el-input__inner {
        font-size: 0.8rem;
        background: #000f47;
        border: none;
        height: 100%;
        padding: 0px 0.78rem 0 0.5rem;
      }
    }

    .el-input.el-input--suffix {
      height: 100%;
      line-height: 1rem;
    }

    .el-input .el-icon-arrow-down {
      font-size: 0.8rem;
      height: 1.4rem;
      line-height: 1.5rem;
    }

    .el-input .el-icon-circle-close {
      font-size: 0.8rem;
      height: 1.4rem;
      line-height: 1.5rem;
    }

    .el-cascader-panel {
      position: absolute;
      background: #09132a;
      top: -16px;
    }

    .el-cascader-node__label {
      color: #fff;
    }
  }

  .node-info.el-cascader {
    width: calc(100% - 18rem);
    font-size: 14px;
    line-height: 2rem;
    position: absolute;
    left: 9rem;
    height: 2.4rem;
    margin-top: 0.3rem;

    .el-input .el-input__inner {
      font-size: 0.8rem;
      background: transparent;
      border: none;
      height: 100%;
    }

    .el-input .el-icon-arrow-down {
      font-size: 0.8rem;
      height: 2rem;
      line-height: 2rem;
    }

    .el-input .el-icon-circle-close {
      font-size: 0.8rem;
      height: 2.2rem;
      line-height: 2rem;
    }

    .el-cascader-panel {
      position: absolute;
      background: #09132a;
      top: -16px;
    }

    .el-cascader-node__label {
      color: #fff;
    }
  }
}

.percentagePlot {
  width: 100%;
  height: 100%;
  padding: 20px 0;
  overflow: scroll;
}

.percentagePlot::-webkit-scrollbar {
  width: 0;  /* 隐藏滚动条 */
  height: 0;
}

.loading-info {
  position: fixed;
  color: #ffffffe3;
  font-size: 1.2rem;
  z-index: 99;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep {
  .el-loading-spinner i {
    display: none;
  }

  .el-loading-spinner {
    width: 100px;
    height: 100px;
    top: calc(50% - 50px);
    left: calc(50% - 50px);
    transform: translate(-130px, -130px);
    background: url("../../../assets/screen/loading.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    animation: load 2s linear infinite;
  }

  @keyframes load {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .loading-info .el-loading-spinner .el-loading-text {
    font-size: 3rem;
    color: #dce1ea;
  }

  .el-loading-mask {
    // background-color: #ffffff26 !important;
  }
}

.reportcs {
  z-index: 99999;
  position: absolute;
  background: url("~@/assets/screen/new/chanyan.webp") no-repeat;
  background-size: 130px 42px;
  top: 25px;
  // right: calc(26% + 10px);
  left: 27.3%;
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  width: 130px;
  height: 37px;
  margin-left: 10px;
  display: flex;
  font-weight: 700;

  .count {
    width: 100%;
    justify-content: center;
    align-items: center;
    text-align: center;
    display: flex;

    .message {
      width: 20px;
      height: 20px;
    }

    span {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      margin-left: 5px;
      font-weight: 800;
      color: #fff;
    }
  }
}

.train-tree {
  background: red;
}
