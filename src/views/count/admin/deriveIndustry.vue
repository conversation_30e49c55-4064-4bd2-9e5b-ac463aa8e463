<template>
  <div>
    <el-dialog
      width="52%"
      :visible="toleadfail"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <div
        slot="title"
        class="title"
      >
        <div class="headline-left">
          <img
            src="https://static.idicc.cn/cdn/pangu/title-icon.png"
            alt=""
          >
          <span class="name">光伏产业洞察报告</span><span class="example">(示例)</span>
        </div>
        <div class="headline-right">
          <div
            class="button"
            @click="exportAreport"
          >
            <span> 一键导出 </span>
          </div>
        </div>
      </div>
      <div class="content">
        <div class="content-a">
          <div class="text">
            <p>1）苏粤鲁稳坐第一方阵，浙江领衔第二方阵</p>
            <span>
              以地域分布的视角来看产业发展，光伏企业主要集中在江苏、山东、广东等东部沿海发达地区,其中江苏、广东、山东以合计占比44%的成绩遥遥领先其他省份；其他地区中，浙江省紧随其后领衔其他地区。
            </span>
            <br>
            <img
              style="margin-top: 3.4rem; width: 300px"
              src="https://static.idicc.cn/cdn/pangu/preview-1.png"
            >
            <div style="margin-top: 1rem">
              <p>3）特色鲜明，形成差异化竞争的大产业链分工</p>
              <span>
                对比我国江苏与广东两大光伏产业集群，江苏主要以光伏组件／光伏电池为核心，占据各省首位，且遥遥领先；广东则以储能与发电站为主，占据榜单头名。区域以自身特色为基础，打造区域产业集聚竞争力，也是产业发展走向成熟的主要衡量标准之一。
              </span>
            </div>
          </div>
          <div class="img">
            <img
              style="margin-left: 2rem; width: 300px; height: 160px"
              src="https://static.idicc.cn/cdn/pangu/preview-3.png"
            >
            <p
              style="
                margin-left: 2rem;
                font-size: 14px;
                color: #fff;
                line-height: 1.6em;
              "
            >
              2）以点带面,光伏城市集群发展雏形显现
            </p>
            <div style="margin-left: 2rem; width: 29rem">
              <span style="font-size: 13px; color: #fff; line-height: 1.6em">
                以集聚发展的视角来看光伏产业，我国已形成以江苏为核心的产业集群，带动山东、浙江、上海等地形成长三角光伏产业带，未来将进一步漫延至安徽、河南、河北等地形成光伏产业圈。
                而以深圳、广州为核心的光伏产业强点，带动省内东莞、佛山等城市产业发展，未来进而漫延至全省，形成具备明显优势的光伏产业集聚地。
              </span>
            </div>
            <br>
            <span
              style="
                display: flex;
                ,font-size: 14px;
                margin-top: 2rem;
                margin-left: 2rem;
                color: #fff;
              "
            >产业环节分布TOP1</span>
            <img
              style="
                margin-left: 2rem;
                margin-top: 1.5rem;
                width: 360px;
                height: 150px;
              "
              src="https://static.idicc.cn/cdn/pangu/preview-2.png"
            >
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "DeriveIndustry",
  props: {
    toleadfail: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  methods: {
    cancel() {
      this.$emit("update:toleadfail", false);
    },
    exportAreport() {
      this.$message({
        message: "该功能开发中，敬请期待~",
        type: "warning",
        customClass: "admin-tips-message",
        duration: 3000,
        showClose: true,
        center: false, // 是否居中
      });
    },
  },
};
</script>
      
  <style scoped lang="scss">
.title {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 1rem;
  .headline-left {
    display: flex;
    img {
      width: 2.5rem;
      height: 3rem;
      margin-left: 2rem;
      margin-top: 0.2rem;
    }
    .name {
      font-size: 18px;
      font-weight: 700;
      color: #fff;
      line-height: 3.5rem;
    }
    .example {
      margin-left: 0.2rem;
      color: #fff;
      line-height: 3.8rem;
    }
  }
  .headline-right {
    margin-right: 3rem;
    .button {
      cursor: pointer;
      background: url("~@/assets/screenCount/dc-.png") no-repeat;
      background-size: cover;
      width: 11rem;
      height: 2.8rem;
      margin-top: 0.4rem;
      position: relative;
      span {
        color: #fff;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 1.2rem;
      }
    }
  }
}
.content {
  height: 49rem;
  .content-a {
    height: 12rem;
    display: flex;
    .text {
      margin-bottom: 2rem;
      margin-right: 2rem;
      margin-left: 2rem;
      p {
        font-size: 14px;
        color: #fff;
      }
      span {
        font-size: 13px;
        color: #fff;
        line-height: 1.6em;
      }
    }
    img {
      p {
        font-size: 14px;
        color: #fff;
      }
      span {
        font-size: 13px;
        color: #fff;
        line-height: 1.6em;
      }
    }
  }
}
::v-deep {
  .el-dialog__header {
    background: transparent !important;
  }
  .el-dialog {
    // background: url('~@/assets/screenCount/report-bgc.png');
    // background-size: cover;
    background: center/cover no-repeat
      url("https://static.idicc.cn/cdn/pangu/report-bgc.png?x-oss-process=image/quality,q_85/format,webp");
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: #fff;
  }
  :hover .el-dialog__headerbtn .el-dialog__close {
    color: #fff;
  }
  // 弹层内容背景
  .el-dialog__body {
  }
}
</style>