const apiDomain2 =
    "https://www.fastmock.site/mock/cd339c8f48c9e13af2c490b1481ffe1c/qingteng";
import interfaceRequest from "@/utils/interfaceRequest";
// import requestOther from '@/utils/requestOther'
import axios from "axios";
import {
    apiUrl,
    // cityJsonUrl
} from "@/api/user";
import store from "@/store";
import { getToken } from "@/utils/auth";

// import { download } from '@/utils';
const apiDomain = apiUrl;

/**
 * 大屏
 */
export const chartApi = {
    atlasRelation: apiDomain + "/admin/orgIndustryChainRelation/atlas", // 图谱列表
    mapRelation: apiDomain + "/admin/orgIndustryChainRelation/map", // 地图列表
    //enterpriseList:apiDomain+'/admin/orgIndustryChainRelation/enterpriseList', //  图谱 - 企业清单
    enterpriseList:
        apiDomain + "/admin/orgIndustryChainRelation/atlasEnterpriseList", //  图谱 - 企业清单
    mapenterpriseList:
        apiDomain + "/admin/orgIndustryChainRelation/mapEnterpriseList", //  地图 - 企业清单
    //enterpriseListExport:apiDomain+'/admin/orgIndustryChainRelation/enterpriseListExport', // 图谱 - 导出企业清单
    enterpriseListExport:
        apiDomain + "/admin/orgIndustryChainRelation/atlasEnterpriseListExport", // 图谱 - 导出企业清单
    //download:apiDomain+'/admin/orgIndustryChainRelation/download', // 图谱 - 导出地图企业清单
    download:
        apiDomain + "/admin/orgIndustryChainRelation/mapEnterpriseListExport", // 图谱 - 导出地图企业清单
    enterpriseDetail: apiDomain + "/admin/enterprise/detail", // 图谱 - 企业详情
    cityList: apiDomain + "/admin/administrativeDivision/list", //图谱 - 导出企业清单
    enterpriseParticulars:
        apiDomain + "/admin/orgIndustryChainRelation/enterpriseDetail", //获取指定企业详情
        enterpriseParticularsV2:
        apiDomain + "/admin/orgIndustryChainRelation/enterpriseDetailV2", //获取指定企业详情
    //loadByParent:apiDomain+'/admin/industryChainNode/loadByParent', // 地图 - 产业链节点数据
    loadByParent: apiDomain + "/admin/industryChainNode/loadByOrgId", // 地图 - 产业链节点数据
    // chainRelationGeo:apiDomain+'/admin/orgIndustryChainRelation/geo/data', // 地图 - 产业链节点数据
    chainRelationGeo: apiDomain + "/admin/orgIndustryChainRelation/geo/data", // 地图 - 产业链节点数据
    countEnterpriseNumByModel:
        apiDomain + "/admin/org/count/report/countEnterpriseNumByModel", // 产业统计
    queryIndustryChainPublicOpinion:
        apiDomain + "/admin/org/count/report/queryIndustryChainPublicOpinion", // 产业统计-产业事件
    overViewEnterpriseList: apiDomain + "/boardDriver/enterpriseList",
    institutionList: apiDomain + "/boardDriver/institutionList",
    parkOverView:'/boardDriver/poc/enterpriseList',
};

// 统计 公共接口
export function getCont(data) {
    return interfaceRequest({
        url: apiUrl + chartApi.countEnterpriseNumByModel,
        // url:apiDomain2+chartApi.countEnterpriseNumByModel+'1',
        method: "post",
        data,
    });
}

// 统计 产业事件
export function industrialEvent(data) {
    return interfaceRequest({
        url: apiUrl + chartApi.queryIndustryChainPublicOpinion,
        method: "POST",
        data,
    });
}

// 城市 - json 数据
export function getCityJson(code, areaType, cityData) {
    const cityUrl = apiUrl + chartApi.chainRelationGeo;
    let params = "";
    if (code == 110100) {
        params = `110000_full`;
    } else if (code == 310100) {
        params = `310000_full`;
    } else if (code == 120100) {
        params = `120000_full`;
    } else if (code == 500100) {
        params = `500000_full`;
    } else {
        // 台湾
        if (code == 710000) {
            params = `${code}`;
            // 东莞
        } else if (code == 441900) {
            params = `${code}`;
        } else if (
            areaType === "1" ||
            areaType === "2" ||
            ["110100"].includes(code)
        ) {
            params = `${code}_full`;
        } else if (areaType === "3" && cityData.length == 3) {
            params = `${code}_full`;
        } else {
            params = `${code}`;
        }
    }
    return interfaceRequest({
        url: cityUrl,
        params: { code: params },
        method: "GET",
    });
}

// https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=650000_full
// export function getCityJson(code,areaType) {
//   let params = ''
//   if(areaType === '1' || areaType === '2'){
//     params = `geojson?code=${code}_full`;
//   }else{
//     params = `geojson?code=${code}`;
//   }
//   const cityUrl = cityJsonUrl+`/areas_v3/bound/${params}`;
//   return requestOther({
//       url:cityUrl,
//       method:'GET',
//   })
// }

export function getList(url, data) {
    let dUrl = apiUrl + url;
    return interfaceRequest({
        url: dUrl,
        method: "POST",
        data,
    });
}

// 文件下载
export function enterpriseListExportApi(data) {
    let dUrl = apiUrl + chartApi.enterpriseListExport;
    return axios({
        method: "post",
        url: dUrl,
        headers: {
            "Content-Type": "application/json",
            token: getToken(),
            // store.getters.user.token
        },
        data: data, // 参数
        responseType: "blob" || "",
        // responseType: 'blob' // 表明返回服务器返回的数据类型
    });
}

// 地图 - 产业链节点数据
export function loadByParent(data) {
    return interfaceRequest({
        url: apiUrl + chartApi.loadByParent,
        method: "GET",
        params: { ...data },
    });
}

export function getListMock(url, data) {
    return interfaceRequest({
        url: apiDomain2 + url,
        method: "POST",
        data,
    });
}

export function getDetail(url, data) {
    return interfaceRequest({
        url: apiUrl + url,
        method: "GET",
        params: { ...data },
    });
}

// 城市 - 列表
// export function getCity(data) {
//   return interfaceRequest({
//       url:apiUrl+chartApi.cityList,
//       method:'GET',
//       params:{...data}
//   })
// }

export function getCity(data) {
    return interfaceRequest({
        url: apiUrl + chartApi.cityList,
        method: "GET",
        params: { ...data },
    });
}

export function exportWay(data) {
    let dUrl = apiUrl + chartApi.download;
    return axios({
        method: "post",
        url: dUrl,
        headers: {
            "Content-Type": "application/json",
            token: getToken(),
            // store.getters.user.token
        },
        data: data, // 参数
        responseType: "blob" || "",
        //responseType: 'blob' // 表明返回服务器返回的数据类型
    });
}
export function industryChainNodeAPI(params) {
    return interfaceRequest({
        url: "/admin/industryChainNode/loadSecondNodeByOrgId",
        method: "GET",
        params,
    });
}

/**
 * 获取当前登录用户所在机构的最小政区划
 * @param {*} params
 * @returns
 */
export function getMinAreaByTokenAPI(params) {
    return interfaceRequest({
        url: "/admin/orgIndustryChainRelation/getMinAreaByOrgIndustryRelationId",
        method: "GET",
        params,
    });
}
/**
 * 根据标签名称获取企业列表
 * @param {*} params
 * @returns
 */
export function insightEnterpriseListAPI(data) {
    return interfaceRequest({
        url: "/admin/orgIndustryChainRelation/insightEnterpriseList",
        method: "POST",
        data,
    });
}
/**
 * 根据标签名称获取企业列表
 * @param {*} params
 * @returns
 */
export function getOverViewEnterpriseList(data) {
    return interfaceRequest({
        url: apiUrl + chartApi.overViewEnterpriseList,
        method: "POST",
        data,
    });
}

/**
 * 产业金脑概览-机构列表

 * @param {*} params
 * @returns
 */

export function getInstitutionList(data) {
    return interfaceRequest({
        url: apiUrl + chartApi.institutionList,
        method: "POST",
        data,
    });
}

/**
 * 园区概览-企业列表接口

 * @param {*} params
 * @returns
 */

export function getParkOverView(data) {
    return interfaceRequest({
        url: apiUrl + chartApi.parkOverView,
        method: "POST",
        data,
    });
}

