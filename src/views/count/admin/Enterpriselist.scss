::v-deep {
  .el-loading-mask {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-loading-spinner {
    /* 重置样式 */
    width: 30px !important;
    height: 30px !important;
    top: 50% !important;
    left: 50% !important;
    position: absolute !important;
  }
}

@font-face {
  font-family: "title";
  src: url(~@/text/YouSheBiaoTiHei-2.ttf);
}

::v-deep {
  .el-loading-spinner .path {
    stroke: #fff;
  }

  .el-icon-close {
    cursor: pointer;
    font-size: 15px;
    color: #fff;
    float: right;
    padding: 12px;
  }

  .el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
    background-image: linear-gradient(to bottom right, #2484ef, #09217b) !important;
    color: #fff !important;
  }

  .el-tag.el-tag--info {
    border-color: #2d6fba;
    color: #fff;
    background: rgba(46, 113, 188, 0.2);
  }

  .el-pagination--small .btn-prev,
  .el-pagination--small .btn-next,
  .el-pagination--small .el-pager li,
  .el-pagination--small .el-pager li.btn-quicknext,
  .el-pagination--small .el-pager li.btn-quickprev,
  .el-pagination--small .el-pager li:last-child {
    background-color: transparent !important;
  }

  .el-pagination .btn-prev .el-icon,
  .el-pagination .btn-next .el-icon {
    font-size: 12px;
    color: #fff;
  }

  .el-pager li.active {
    color: #fff !important;
  }

  .el-pagination {
    background-color: transparent !important;
    position: unset !important;
    text-align: center !important;
    padding: 0 !important;
  }

  .el-pagination__total {
    color: #fff;
  }

  .input-info .el-input .el-input__inner {
    margin-top: 0.4rem !important;
  }
}

.lod {
  color: rgba(255, 255, 255, 0.6) !important;
}

.ye {
  position: relative;
  //position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
}

.loading-icon {
  color: #fff;
  background-color: #fff;
}

.box {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2147483660;
  background: transparent !important;
  &::before {
    content: "";
    width: 100%;
    height: 5px;
    position: absolute;
    top: 9px;
    left: 10px;
    background: left/cover url("https://static.idicc.cn/cdn/pangu/assets/screen/new/dialogBg.webp") no-repeat;
    background-size: 30% 100%;
    z-index: 1;
  }
  .list {
    width: 1150px;
    height: 644px;
    border-radius: 3px;
    background: #080e2b !important;
    border: 1px solid #2cadff !important;
    padding: 2px;
    position: relative;
    .listHeader {
      width: 100%;
      display: inline-block;
      height: 35px;
      background: linear-gradient(90deg, #2cadff 0%, rgba(0, 13, 59, 0.188) 105%);
      color: #ffffff;
      font-size: 18px;
      line-height: 35px;
      padding-left: 10px;
      padding-right: 20px;
    }
    .listClose {
      width: 30px;
      height: 30px;
      background: center/cover url("https://static.idicc.cn/cdn/pangu/assets/screen/new/close.webp") no-repeat;
      position: absolute;
      right: -13px;
      top: -13px;
    }
    .title {
      font-family: YouSheBiaoTiHei;
      color: #ffffff;
    }

    .export {
      width: 82px;
      height: 30px;
      cursor: pointer;
      //background: #064889;
      background: rgba(7, 78, 147, 0.7);
      border: 1px solid #3695ff;
      opacity: 0.9;
      border-radius: 4px;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      float: right;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 2px;
      .up {
        width: 14px;
        height: 14px;
        margin-right: 10px;
      }
    }

    .content {
      padding: 16px;
      height: 35rem;

      // background-color: #fff;
      .headline {
        height: 45px;
        background: rgba(27, 71, 134, 0.4);
        display: grid;
        align-items: center;
        color: #ffffff;
        font-size: 14px;

        .listItem {
          padding-left: 20px;
          height: 100%;
          line-height: 45px;
        }
      }

      .enterpriseList {
        width: 100%;
        height: calc(100% - 45px);
      }
      .enterpriseListItem {
        &:nth-child(2n) {
          background: rgba(153, 178, 188, 0.1);
        }
      }
      .singleBox {
        width: 100%;
        height: 45px;
        line-height: 45px;
        display: grid;
        align-items: center;
        color: #ffffff;
        font-size: 14px;

        .name {
          padding-left: 19px;
        }

        .listItem {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          padding-left: 3px;

          &:first-child {
            padding-left: 19px;
          }
        }
      }
    }
  }
}
