<template>
  <div
    :id="chartData.id"
    :style="myChartStyle"
    class="echart"
  />
</template>

<script>
import * as echarts from "echarts";
import {YAxis,XAxis} from '@/utils/XYLineConfig'
export default {
  name: "CityBarChart3D",
  props: {
    chartData: {
      type: Object,
      default: function () {
        return { id: "echart" };
      },
    },
  },
  data() {
    return {
      myChartStyle: { float: "left", width: "100%", height: "400px" }, //图表样式
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        val && this.initEcharts(val);
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initEcharts(this.chartData);
    });
  },
  methods: {
    initEcharts() {
      const { xData1 = null, yData1 = null } = this.chartData;
      // 基本柱状图
      // 如需要调整宽度需要调整三个面的数值
      // 左侧面
      const CubeLeft = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c0 = [shape.x, shape.y]; //左侧面右上角
          const c1 = [shape.x - 9, shape.y - 6]; //左侧面左上角
          const c2 = [xAxisPoint[0] - 9, xAxisPoint[1] - 6]; //左侧面左下角
          const c3 = [xAxisPoint[0], xAxisPoint[1]]; //左侧面右下角
          ctx
            .moveTo(c0[0], c0[1])
            .lineTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .closePath();
        },
      });
      // 右侧面
      const CubeRight = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c1 = [shape.x, shape.y]; //右侧面左上
          const c2 = [xAxisPoint[0], xAxisPoint[1]]; //右侧面左下
          const c3 = [xAxisPoint[0] + 9, xAxisPoint[1] - 6]; //右侧面右下
          const c4 = [shape.x + 9, shape.y - 6]; //右侧面右上
          ctx
            .moveTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .lineTo(c4[0], c4[1])
            .closePath();
        },
      });
      // 顶面
      const CubeTop = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const c1 = [shape.x, shape.y]; // 下
          const c2 = [shape.x + 9, shape.y - 6]; // 右
          const c3 = [shape.x, shape.y - 12]; // 上
          const c4 = [shape.x - 9, shape.y - 6]; // 左
          ctx
            .moveTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .lineTo(c4[0], c4[1])
            .closePath();
        },
      });
      echarts.graphic.registerShape("CubeLeft", CubeLeft);
      echarts.graphic.registerShape("CubeRight", CubeRight);
      echarts.graphic.registerShape("CubeTop", CubeTop);
      const option = {
        // 主图形位置
        grid: {
          left: 0,
          right: 12,
          bottom: 0,
          top: "25%",
          containLabel: true, // containLabel  x/y轴内容 是否在内部
        },
        //x, y轴数据属性设置
        xAxis:XAxis(xData1),
        yAxis: [YAxis(),],
        series: [
          {
            type: "custom",
            renderItem: (params, api) => {
              const location = api.coord([api.value(0), api.value(1)]);
              const value = api.value(1); // 获取数据值
              let n =0
              value<=9 ? n=4 : (value<=99 ? n=8 : n=10)
              const height = 50; // 假设立方体高度为50
              return {
                type: "group",
                children: [
                  {
                    type: "CubeLeft",
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          //左侧面
                          offset: 0,
                          color: "#3EC2FF",
                        },
                        {
                          offset: 0.4,
                          color: "rgba(62, 194, 255, 0.3973)",
                        },
                        {
                          offset: 1,
                          color: "rgba(44, 166, 166, 0)",
                        },
                      ]),
                    },
                  },
                  {
                    type: "CubeRight",
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: "#2E6D86",
                        },
                        {
                          offset: 0.5,
                          color: "rgba(46, 109, 134, 0.5205)",
                        },
                        {
                          offset: 1,
                          color: "rgba(44, 166, 166, 0)",
                        },
                      ]),
                    },
                  },
                  {
                    type: "CubeTop",
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: "#3EECFF",
                    },
                  },
                  {
                    type: "text",
                    style: {
                      text: value, // 显示数据值
                      textFill: "#fff", // 文字颜色
                      fontSize: 12, // 文字大小
                      opacity: 0.6,
                    },
                    position: [location[0]-n, location[1] - 25], // 文字位置
                    offsetY: -height / 2, // 偏移量
                  },
                ],
              };
            },
            data: yData1,
          },
        ],
        title: {
          text: "家",
          x: "left",
          y: 16,
          textStyle: {
            color: "#C9D2DD",
            fontSize: 12,
            fontWeight: "normal",
          },
        },
      };
      const myChart = echarts.init(document.getElementById(this.chartData.id));
      myChart.off('click') // 这里很重要！！！
      let self = this
      myChart.on("click", function (params) {
        self.$emit('showList',params)
      });
      myChart.setOption(option);
      //随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  },
};
</script>