import router from "./router";
import store from "./store";
import "nprogress/nprogress.css";
import { getToken } from "@/utils/auth";
import { getRouteTree } from "@/api/user";
import { asyncRoutes } from "@/router";
// import _ from 'lodash';
export { default as cloneDeep } from 'lodash/cloneDeep';
let isRoutesAdded = false
let accessedRoutes = []
// 递归过滤路由函数
function filterRoutes(localRoutes, permissionNodes) {
  if (!permissionNodes || permissionNodes.length == 0) {
    return []
  }
  return localRoutes.filter(route => {
    const permissionNode = permissionNodes.find(node => (node.resourceCode === route.name && node.hidden != true))
    if (!permissionNode) return false
    if (route.children) {
      const childPermissions = permissionNode.childList || []
      route.children = filterRoutes(
        Array.isArray(route.children) ? route.children : [route.children],
        Array.isArray(childPermissions) ? childPermissions : [childPermissions]
      )
    }
    return true
  })
}
router.beforeEach(async (to, from, next) => {
  const whiteList = ['/login', '/404', '/retrievePassword']
  if (whiteList.includes(to.path)) {
    isRoutesAdded = false
    // await store.commit('permission/SET_ROUTES', [])
    // const localRoutes = _.cloneDeep(asyncRoutes);
    // router.addRoutes(localRoutes)
    return next()
  }
  const hasToken = getToken()
  if (!hasToken) {
    return next('/login')
  }
  if (to.path === '/login') {
    next('/')
  } else {
    if (!isRoutesAdded) {
      const res = await getRouteTree()
      const localRoutes = cloneDeep(asyncRoutes);
      localStorage.setItem('permission', '')
      if (!res.result || (res.result && res.result.length === 0)) {
        isRoutesAdded = true
        return next('/noOrg')
      }
      localStorage.setItem('permission', '1')
      accessedRoutes = filterRoutes(localRoutes, res.result)
      await store.commit('permission/SET_ROUTES', accessedRoutes)
      router.addRoutes(accessedRoutes)
      isRoutesAdded = true
      return next({ ...to, replace: true })
    }
    next()
  }
})

router.afterEach((to, _from) => {
  if (to.path !== '/' && to.matched.length === 0) {
    console.warn('未匹配到任何路由:', to.path);
    return;
  }
  if (to.meta && to.meta.title) {
    if (to.meta.isJN) {
      if (to.meta.title === '产业金脑') {
        document.title = '产业金脑';
      } else {
        document.title = to.meta.title + "-" + '产业金脑';
      }
    } else {
      document.title = to.meta.title + "-" + '哒达招商';
    }
  } else {
    if (to.meta.isJN) {
      document.title = '产业金脑';
    } else {
      document.title = '哒达招商';
    }
  }
  const arr = [...accessedRoutes];
  if (to.path === '/' && (arr && arr.length)) {
    if (arr[0].path !== '/') {
      // 获取第一个元素并找到最深层的第一个children节点的第一个路由路径
      let firstRoute = arr[0];
      let deepestRoute = firstRoute;
      let fullPath = firstRoute.path;

      // 递归查找最深层的第一个children节点
      const findDeepestFirstChild = function (route) {
        if (route.children && route.children.length > 0) {
          // 优先查找第一个未隐藏的菜单项
          const visibleChild = route.children.find(child => !child.meta || child.meta.hidden !== true);
          if (visibleChild) {
            deepestRoute = visibleChild;
            fullPath = fullPath + '/' + visibleChild.path;
            findDeepestFirstChild(visibleChild);
          }
        }
      };

      findDeepestFirstChild(firstRoute);
      router.push(fullPath || '/idicc/view');
      return
    }
  }
})