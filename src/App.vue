<template>
  <div id="app">
    <keep-alive>
      <router-view v-if="$route.meta.keepAlive" />
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive" />
  </div>
</template>

<script>
export default {
  name: "App",
  mounted() {
    // console.log(1)
  },
};
</script>
<style lang="scss" scoped>
::v-deep {
  .node-info-tree.el-popper {
    background: #00112d;
    .el-cascader-node__label {
      color: #fff;
    }
  }
}
</style>

<style lang="scss">
#wujie {
  .navbar {
    display: none !important;

    box-shadow: 0 0 0 !important;
    background: transparent !important;
    div {
      display: none !important;
    }
  }
  .sidebar-container,
  .logo {
    display: none !important;
  }
  .main-container.hasTagsView {
    margin-left: 0 !important;
  }
  .app-main {
    // margin-top: 16px !important;
  }
  #app .el-table thead th {
    background: linear-gradient(180deg, #fafbff 0%, #f0f4ff 100%) !important;
  }
}
#app .el-form.demo-form-inline {
  background: white;
  padding: 15px 0 10px;
  box-shadow: 0px 4px 12px 0px #eef1f8;
  border-radius: 10px 10px 10px 10px;
}
</style>
