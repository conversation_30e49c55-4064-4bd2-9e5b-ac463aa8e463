import { getToken } from "@/utils/auth"; // get token from cookie
import parkOverView from './modules/parkOverView';

const getters = {
  sidebar: state => state.app.sidebar,
  size: state => state.app.size,
  device: state => state.app.device,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  token: () => getToken(),
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  introduction: state => state.user.introduction,
  roles: state => state.user.roles,
  permission_routes: state => state.permission.routes,
  errorLogs: state => state.errorLog.logs,
  user: state => state.user.user,
  Assignauthority: state => state.user.Assignauthority,
  Wisdomattract: state => state.user.Wisdomattract,
  figureresearch: state => state.user.figureresearch,
  visViewData: state => state.visView.visViewData,
  allViewData: state => state.industryOverView.allViewData,
  parkOverView: state => state.parkOverView.allViewData,
}
export default getters
