

const state = {
    visViewData: null
}

const mutations = {
    CHANGE_VIEW: (state, data) => {
      // console.log(data,'datadatadata')
        // eslint-disable-next-line no-prototype-builtins
        state.visViewData=data
      }
}

const actions = {
    changeViewData({ commit }, data) {
        commit('CHANGE_VIEW', data)
      }
 
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}