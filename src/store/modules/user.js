import { login, logout, getLoginUserInfoAPI } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import router, { resetRouter } from '@/router'

const state = {
  token: getToken(),
  //token: null,
  user: {},
  name: '',
  avatar: '',
  introduction: '',
  roles: [],
  Assignauthority: false,
  Wisdomattract: false,//数智招商
  figureresearch: false,//产业洞察
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
    state.user.token = token
  },
  SET_ATTRACT: (state, designate) => {
    state.Wisdomattract = designate
  },
  SET_RESEARCH: (state, designate) => {
    state.figureresearch = designate
  },
  SET_DESIGNATE: (state, designate) => {
    state.Assignauthority = designate
  },
  SET_USER: (state, user) => {
    state.user = user
  },
  SETDEL_USER: (state) => {
    state.user = {}
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  }
}

const actions = {
  logins({ commit }, token) {
    commit('SET_TOKEN', token)

    setToken(token)
    return new Promise((resolve, reject) => {
      getLoginUserInfoAPI(token).then(response => {
        const { result } = response;
        result.token = token
        commit('SET_USER', result)
        const { realName = '' } = result;
        localStorage.setItem('userInfo', realName);
        commit('SET_NAME', realName)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  // user login
  /*    async login(log,data) {
      const res = await login(data)
      log.commit('SET_TOKEN', res.result.token)
      log.commit('SET_USER', res.result)
    },  */

  // user login
  login({ commit }, userInfo) {
    commit('SET_USER', userInfo)
    commit('SET_TOKEN', userInfo.token)
    setToken(userInfo.token)
    commit('SET_NAME', userInfo.realName)
  },
  // get user info
  getInfo({ commit }) {
    return new Promise((resolve, reject) => {
      const data = {
        roles: ['admin'],
        avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
        introduction: 'I am a super administrator',
        name: 'Super Admin'
      }
      const { roles, name, avatar, introduction } = data


      // roles must be a non-empty array
      if (!roles || roles.length <= 0) {
        reject('getInfo: roles must be a non-null array!')
      }

      commit('SET_ROLES', roles)
      commit('SET_NAME', name)
      commit('SET_AVATAR', avatar)
      commit('SET_INTRODUCTION', introduction)
      resolve(data)
    })
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SETDEL_USER')
        removeToken()
        resetRouter()

        // 清除所有localStorage数据
        localStorage.clear()

        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      resolve()
    })
  },

  // dynamically modify permissions
  async changeRoles({ commit, dispatch }, role) {
    const token = role + '-token'

    commit('SET_TOKEN', token)
    setToken(token)

    const { roles } = await dispatch('getInfo')

    resetRouter()

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })
    // dynamically add accessible routes
    // router.addRoutes(accessRoutes)

    // reset visited views and cached views
    dispatch('tagsView/delAllViews', null, { root: true })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}