<template>
  <div class="logout">
    <el-dropdown
      trigger="click"
      @command="handleCommand"
    >
      <span class="el-dropdown-link">
        {{ userInfo?.orgName }}
        -
        {{ userInfo?.realName }}
        <i class="el-icon-arrow-down el-icon--right" />
      </span>
      <el-dropdown-menu class="headerMenu">
        <div
          v-for="item in newList"
          :key="item.id"
        >
          <el-dropdown-item :command="item.key">
            {{ item.label }}
          </el-dropdown-item>
        </div>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import store from "@/store";
import { getToken } from "@/utils/auth"; // get token from cookie

export default {
  // eslint-disable-next-line vue/component-definition-name-casing, vue/multi-word-component-names
  // v-if="$store.getters.user.accountType == '1'"
  name: "LoGout",
  computed: {
    userInfo() {
      return JSON.parse(localStorage.getItem("MAINUSERINFO") || "{}");
    },
    newList() {
      return [{ label: "退出登录", key: "logOut" }];
    },
  },

  methods: {
    handleCommand(command) {
      if (command === "logOut") {
        this.logout();
      } else {
        // if (command === '/jinghai/dashboard') {
        //   window.open('https://jh.idicc.cn/static/jh/index.html', '_blank');
        // } else {
        this.$router.push(`${command}`);

        // window.$wujie?.bus.$emit("newRouter", { path: command });
        // }
      }
    },
    async logout() {
      this.$confirm("确定退出登录吗?", "退出登录", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        center: true,
      }).then(async () => {
        await this.$store.dispatch("user/logout");
        this.$router.push("/login");
        // 清空token
        this.$message({
          type: "success",
          message: "退出登录成功",
        });
      });
    },
    gosso() {
      const baseURL = process.env.VUE_APP_SSO_URL;
      // let token = getToken()
      // store.getters.user.token
      // const url =`${baseURL}?token=${token}`
      //window.location.href = url;
      // window.open(`${url}`, '_blank');
      this.$router.push(`/`);
    },
    goTo(url) {
      // window.$wujie?.bus.$emit('newRouter',{path: url});
      // this.$router.push(url);
    },
    toidicc() {
      // window.$wujie?.bus.$emit('newRouter',{path: '/idicc/idicc-scan'});
      this.$router.push(`/idicc/view`);

      // window.$wujie?.bus.$emit("newRouter", { path: "/pangu/idicc/view" });
      // this.$router.push('/idicc/idicc-scan')
    },
    toattract() {
      this.$router.push(`/attract/intelligentRecommendation`);
      // window.$wujie?.bus.$emit("newRouter", {
      //   path: "/pangu/attract/intelligentRecommendation",
      // });

      // this.$router.push('/attract/intelligentRecommendation')
    },
  },
};
</script>

<style lang="scss" scoped>
.el-dropdown-link {
  color: #fff;
  opacity: 0.6;
  cursor: pointer;
}

.logout {
  display: flex;
  align-items: center;

  svg {
    color: #fff;
    margin-right: 10px;
    margin-top: 5px;
  }
}
</style>
<style  lang="scss">
.headerMenu.el-dropdown-menu {
  z-index: 9999 !important;
  background-color: #00112d !important;
  border: #468ae79e 1px solid !important;

  .el-dropdown-menu__item {
    color: #fff;
    font-weight: 600 !important;

    &:hover {
      background-image: linear-gradient(to bottom right, #2484ef, #09217b);
      color: #fff;
    }
  }
}
</style>