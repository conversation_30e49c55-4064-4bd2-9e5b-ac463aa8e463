<template>
  <div class="keyEnterpriseBody">
    <div
      id="enterpriseLink"
      class="enterpriseLink"
    />
  </div>
</template>
<script>
import * as echarts from 'echarts';
import {
  tooltipShadow
} from '@/views/overview/components/component/toolTips';

export default {
  name: 'PercentagePlot',
  props: {
    dataList: {
      type: Object,
      default: () => ({}),
    },
      text: {
      type: String,
      default: '',
    },
      text1: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      data: [],
      maxData: 0,
      showList: [],
    };
  },
  watch: {
    dataList(newVal) {
      this.data = newVal || [];
      this.showList = newVal?.values?.slice(1);
      this.maxData = newVal?.values[1]?.dicValueCountry || 0;
      this.init(this.showList);
    },
  },

  mounted() {
    this.init(this.showList);
  },
  methods: {
    isEmpty(a) {
      if (Array.isArray(a)) {
        return a.length === 0;
      } else if (a && typeof a === "object") {
        return Object.keys(a).length === 0;
      }
      return false;
    },
    init(val) {
      let isEmptyData=this.isEmpty(val)
      if (!isEmptyData) {
        let config = {
          type: 'bar',
          label: {
            show: true,
            position: 'right',
            formatter: '{c}',
            fontSize: 8,
            color: '#EFF7FF',
          },
          barMinHeight: 2,
          barWidth: 5, // 添加固定宽度属性，可以根据需要调整数值
          barGap: '30%', // 同一系列的柱间距离，默认为'30%'，可以设置为固定值
          z: 10,
        };
        let firstData = {
          name: this.text1!==''?this.text1:'本地',
          type: 'bar',
          data: [],
          ...config,
          itemStyle: {
            borderWidth: 1,
            barBorderRadius: [0, 3, 3, 0], // 右侧圆角
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: 'rgba(24, 144, 255, 0.35)' }, // 渐变开始颜色
              { offset: 1, color: '#1890FF' }, // 渐变结束颜色
            ]),
          },
          barMinHeight: 3,
        };
        let secondData = {
          name: this.text!==''?this.text:'全国',
          type: 'bar',
          data: [],
          ...config,
          itemStyle: {
            borderWidth: 1,
            barBorderRadius: [0, 5, 5, 0], // 右侧圆角
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: 'rgba(30, 231, 231, 0.35)' }, // 渐变开始颜色
              { offset: 1, color: '#1EE7E7' }, // 渐变结束颜色
            ]),
          },
        };
        let xNames = [];
        val.map((item) => {
          firstData.data.push(item?.dicValue || 0);
          secondData.data.push(item?.dicValueCountry || 0);
          xNames.push(item?.dicKey);
        });
        this.keyEnterprise(xNames, [firstData, secondData]);
      }
    },
    closeList() {
      this.isShowList = false;
    },
    keyEnterprise(xNames, xValue) {
      let chartDom = document.getElementById('enterpriseLink');
      if (chartDom) {
        let myChart = echarts.init(chartDom);
        let options = {
          tooltip: tooltipShadow,
          grid: {
            left: '4%',
            right: '10%',
            bottom: '10%',
            top: '7%',
            containLabel: true, // containLabel  x/y轴内容 是否在内部
          },
          yAxis: {
            axisLabel: {
              interval: 0,
              rotate: 0,
              textStyle: {
                color: '#C9D2DD',
                fontSize: '12',
                itemSize: '',
                marginLeft: '2px',
              },
            },
            data: xNames,
            type: 'category',
            boundaryGap: true,
            splitArea: {
              show: false,
              areaStyle: {
                // color: [' rgba(255, 255, 255, 0.08)', ''], // 交替背景色
                // opacity: 0.3,
              },
            },
            axisLine: {
              lineStyle: {
                color: '#2f3750',
                fontSize: '20px',
              },
            },
            axisTick: {
              show: false, // 隐藏 x 轴刻度线
            },
          },
          xAxis: [
            {
              name: '单位(家)',
              nameLocation: 'start',
              nameTextStyle: {
                fontSize: '9',
                color: '#C9D2DD', //颜色
                padding: [4, 0, 0, 0], //间距分别是 上 右 下 左
              },
              type: 'value',
              splitLine: {
                show: true, // 显示网格线
                lineStyle: {
                  color: 'rgba(255, 255, 255, 0.1)', // 网格线颜色
                  width: 1,
                  type: 'dashed',
                },
              },
              // splitArea: {
              //   show: true, // 显示背景
              //   areaStyle: {
              //     color: ['rgba(250,250,250,0.05)', 'rgba(200,200,200,0.02)'], // 交替背景色
              //     opacity: 0.3,
              //   },
              // },
              axisLine: {
                show: false,
                lineStyle: {
                  color: '#C9D2DD',
                  opacity: 0.2,
                },
              },
              axisLabel: {
                interval: 0,
                rotate: 30,
                textStyle: {
                  color: '#C9D2DD',
                  fontSize: '12',
                  itemSize: '',
                  marginLeft: '2px',
                },
              },
            },
          ],
          legend: {
            right: '45%',
            top: '0%',
            textStyle: {
              color: '#fff',
              fontSize: 9,
            },
            itemWidth: 8,
            itemHeight: 8, //修改icon图形大小
          },
          series: xValue,
          barCategoryGap: '20%', // 不同系列柱状图之间的间距，也可以设置为固定像素值如'10px'
        };

        options && myChart.setOption(options);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.no-data {
  display: flex;
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
}

.enterpriseLink,
.keyEnterpriseBody {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
