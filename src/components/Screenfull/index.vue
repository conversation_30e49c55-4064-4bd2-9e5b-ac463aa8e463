<template>
  <div @click="click">
    <span
      v-if="showText"
      class="showText"
    >{{ !isFullscreen?'全屏':'退出' }}</span>
    <svg-icon :icon-class="isFullscreen?'exit-fullscreen':'fullscreen'" />
  </div>
</template>

<script>
import screenfull from 'screenfull'

export default {
  name: 'SreenFull',
  props:{
    elements:{
        type:HTMLDivElement,
        default:null
      },
      showText:{
        type:Boolean,
        default:false
      }
  },
  data() {
    return {
      isFullscreen: false
    }
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    this.destroy()
  },
  methods: {
    click() {
      if (!screenfull.enabled) {
        this.$message({
          message: 'you browser can not work',
          type: 'warning'
        })
        return false
      }
      if(this.elements!==''){
        !screenfull.isFullscreen?screenfull.request(this.elements):
      screenfull.exit(this.elements);
      this.$emit('isFullscreen',!this.isFullscreen)
      }else{
        screenfull.toggle()
      }
     
      // 
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen
    },
    init() {
      if (screenfull.enabled) {
        screenfull.on('change', this.change)
      }
    },
    destroy() {
      if (screenfull.enabled) {
       
        screenfull.off('change', this.change)
      }
    }
  }
}
</script>

<style scoped>
.screenfull-svg {
  display: inline-block;
  cursor: pointer;
  fill: #5a5e66;;
  width: 20px;
  height: 20px;
  vertical-align: 10px;
}
.showText{
 padding-right: 10px;
 
}
</style>
