<template>
  <div>
    <div
      v-show="isShow"
      class="mask"
    >
      <div class="text">
        <div>
          <i
            class="el-icon-loading"
            style="color: #fff;"
          />
        </div>
        <div>Loading</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "LoadingS",
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  watch:{
    isShow:{
      deep: true,
      immediate:true,
      handler(val) {
      if(val){
      document.body.style.overflow = "hidden";
     }else{
      document.body.style.overflow = "auto";
     }
      }
   
    }
  }
};
</script>

<style lang="scss">
.mask {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100000;
  .text{
	color:  rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
  }
}
</style>
