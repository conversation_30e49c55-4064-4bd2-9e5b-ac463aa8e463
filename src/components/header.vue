<template>
  <div class="view-content">
    <!-- 地图背景 -->
    <img
      v-if="
        state === 'overView' || state === 'dashboard' || state === 'MapEcharts'
      "
      class="body-img"
      src="https://static.idicc.cn/cdn/pangu/bg2.webp"
    >

    <img
      v-else-if="state === 'homePage'"
      class="body-img"
      src="https://static.idicc.cn/cdn/pangu/bg.webp"
    >
    <img
      v-else
      class="body-img"
      src="https://static.idicc.cn/cdn/pangu/bg.webp"
    >
    <div class="header">
      <div class="header-con">
        <div class="left">
          <div class="titles">
            <span
              :class="['sp1', state !== 'homePage' ? 'smallSize' : 'bigSize']"
              style="font-size:orgName?.length>10? 23px:33px;"
              @click="goHome()"
            >
              {{ orgName }}·产业金脑
              <!-- <span
                v-if="chainName"> -->
              <!-- <span v-if="state !== 'homePage'">
                  ·</span>{{ chainName }}</span> -->
            </span>

            <span
              v-if="state !== 'homePage'"
              class="dropTitle"
            >
              <el-dropdown
                trigger="click"
                @command="handleCommand"
              >
                <span class="drop">{{ industrySelected }}
                  <i class="el-icon-arrow-down el-icon--right" /></span>
                <el-dropdown-menu class="headerMenu">
                  <div class="headerMenuItems">
                    <div
                      v-for="item in industryList"
                      :key="item.id"
                    >
                      <el-dropdown-item :command="item">
                        {{ item.chainName }}
                      </el-dropdown-item>
                    </div>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </div>
        </div>
        <div class="center">
          <div
            v-if="state !== 'homePage'"
            class="navigation"
          >
            <div
              v-for="(item, index) in pageTypeList()"
              :key="index"
              :class="[
                item.value == $route.path ? 'highlight' : '',
                'tabItems',
              ]"
              @click="skip(item.value)"
            >
              <span>
                {{ item.label }}
              </span>
            </div>
          </div>
        </div>
        <div class="right">
          <logout />
        </div>
      </div>
    </div>
    <div class="main-content">
      <slot name="view-container" />
    </div>
  </div>
</template>

<script>
import { pageTypeList } from "./../views/echarts/apiUrl";
import { getMinAreaByTokenAPI } from "@/api/CattractInvestment";
import { orgIndustryChainRelationAPI } from "@/api/user";

import logout from "./logout.vue";
import { getPathId } from "@/utils/utils";

export default {
  // eslint-disable-next-line vue/no-reserved-component-names, vue/multi-word-component-names
  name: "Header",
  components: {
    logout,
  },
  props: {
    state: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      pageTypeList,
      // minArea: null,
      chainName: null,
      orgName: null,
      industryList: [],
      industrySelected: localStorage.getItem("industrySelected"),
      routerId: "",
    };
  },
  // computed: {
  //   industrySelected: function () {
  //     return this.$route.query.id || getPathId() || null;
  //   }
  // },
  // watch: {
  //   "localStorage.getItem('industrySelected')" (val) {
  //     console.log(val)
  //   }
  // },
  mounted() {
    // console.log('val')
    // if (this.state !== 'homePage')
    // {
    //   this.getMinAreaByTokenAPI();
    // }
    let orgName = localStorage.getItem("orgName");
    this.orgName = orgName;
    if (this.state !== "homePage") {
      this.init();
    }
  },
  methods: {
    init() {
      const id = getPathId() || null;
      this.routerId = getPathId() || null;
      let data = {
        isAll: 1,
      };
      orgIndustryChainRelationAPI(data).then((res) => {
        this.industryList = res.result;
        let showName = res.result.find((e) => e.id === id);
        this.industrySelected = showName?.chainName;
        localStorage.setItem("industrySelected", showName?.chainName);
      });
    },
    goHome() {
      this.$router.push("/home-page");
    },
    skip(value) {
      if (value == "/nodata") {
        return this.$message("开发中~敬请期待");
      }
      const id = this.routerId;

      //  query: { id }
      this.$router.push(`${value}?id=${id}`);

      // window.$wujie?.bus.$emit("newRouter", { path: `/pangu${value}` });
      // this.$router.push({ path: value, });
      localStorage.setItem("routerQuery", id);
    },
    handleCommand(e) {
      if (this.routerId === e.id) return;
      localStorage.setItem("routerQuery", e.id);
      this.routerId = e.id;
      this.industrySelected = e?.chainName;
      // console.log("00", this.$route.fullPath);

      localStorage.setItem("industrySelected", e?.chainName);
      // localStorage.setItem("industrySelectedId", e?.chainName);

      // 获取当前路由并更改路由的id
      const currentRoute = this.$route;
      const newQuery = { ...currentRoute.query, id: e.id };
      this.$router.push({
        path: currentRoute.path,
        query: newQuery,
      });
      window.location.reload();
    },
  },
};
</script>

<style scoped lang="scss">
::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.view-content {
  width: 100%;
  height: 100vh;
  overflow: hidden;

  .body-img {
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden;
  }

  .main-content {
    position: relative;
    z-index: 100;
    margin-top: -90px;
  }
}

.header {
  position: relative;
  width: 100%;
  height: 150px;
  z-index: 99;
  // margin-top: -17px;

  /* &::before{
      content: '';
      width: 1273px;
      height: 128px;
      position: absolute;
      top: 8px;
      left: 14px;
      background: url('./../assets/attract/title-ar.png') no-repeat;
      background-size: cover;
    } */
  .header-con {
    width: 100%;
    height: 60px;
    display: flex;
    justify-content: space-between;
    position: relative;
    background: url("~@/assets/screen/new/header_bg2.png") center/cover
      no-repeat;
    display: grid;
    grid-template-columns: 35% 50% 15%;
    background-size: 100% 100%;

    .left {
      justify-content: start;
      display: flex;
      padding: 5px 0 0 30px;
      height: 150px;
      // align-items: center;
      cursor: pointer;
      background: url("~@/assets/screen/new/header_bg1.png") center/cover
        no-repeat;
      background-size: 100% 100%;

      .titles {
        height: 60px;
        width: 100%;
        font-size: 33px;
        line-height: 60px;
        display: flex;
        flex-wrap: nowrap;
      }

      .drop {
        font-size: 1rem;
        color: #ffffff;
        height: 32px;
      }

      .dropTitle {
        padding-left: 20px;
      }

      .dropTitle,
      .drop {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: center;
      }

      .smallSize {
        font-size: 1.75rem;
      }

      .bigSize {
        font-size: 2.07rem;
      }

      .sp1 {
        cursor: pointer;
        font-family: YouSheBiaoTiHei;
        color: #ffffff;
        // line-height: 23px;
        display: inline-block;
      }

      .sp2 {
        //font-size: 16px;
        //font-family: Source Han Sans CN;
        //font-weight: 800;
        //font-style: italic;
        font-size: 14px;
        //font-family: Source Han Sans CN;
        font-weight: 400;
        //font-style: italic;
        color: #ffffff;
        opacity: 0.8;
        margin-left: 10px;
        padding-top: 8px;
        //padding-top: 6px;
        display: inline-block;
      }
    }

    .right {
      // display: flex;
      // align-items: center;
      padding-right: 30px;
      // flex-direction: row-reverse;
      display: flex;
      height: 50px;
      margin-top: 5px;
      justify-content: end;
    }

    .center {
      // position: absolute;
      height: 60px;
      width: 100%;

      .navigation {
        width: 70%;
        margin-left: 10%;
        display: flex;
        justify-content: space-between;
        flex-wrap: nowrap;
        color: #afb9c1;
        font-size: 0.9375rem;
        height: 100%;
        align-items: center;
        font-style: italic;
        // font-family: PingFangSC;
        font-weight: 600;

        .tabItems {
          width: 20%;
          height: 50px;
          text-align: center;
          padding-top: 3;
          line-height: 50px;
          cursor: pointer;
        }

        .highlight {
          color: white;
          background: url("~@/assets/screen/new/bt_bg.png") center/cover
            no-repeat;
          background-size: 100% 100%;
          width: 20%;
          height: 50px;
        }
      }
    }

    // left: 50%;
    // top: 0;
    // transform: translateX(-50%);
  }
}

.headerMenuItems {
  width: 100%;
  max-height: 400px;
  overflow-y: scroll;
}
</style>