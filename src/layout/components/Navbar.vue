<template>
  <div class="navbar">
    <hamburger
      v-if="permission"
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />
    <!-- <breadcrumb
      id="breadcrumb-container"
      class="breadcrumb-container"
    /> -->
    <div class="right-menu">
      <template v-if="device !== 'mobile'">
        <error-log class="errLog-container right-menu-item hover-effect" />
      </template>

      <el-drawer
        title=""
        :visible.sync="drawer"
        direction="rtl"
        :before-close="handleClose"
        size="415"
      >
        <template #title>
          <div class="boldText">
            会员中心
          </div>
        </template>
        <MemberCenter />
      </el-drawer>
      <div class="rightView">
        <div
          v-if="permission"
          class="vipLogo"
          @click="drawer = true"
        >
          <img
            v-if="isVip"
            src="https://static.idicc.cn/cdn/pangu/type4.png"
            alt=""
          >
          <img
            v-else
            src="https://static.idicc.cn/cdn/pangu/type2.png"
            alt=""
          >
        </div>
        <el-dropdown
          class="avatar-container right-menu-item hover-effect"
          trigger="click"
        >
          <div class="avatar-wrapper">
            <span>
              {{ userInfo.realName }}
            </span>

            <i class="el-icon-caret-bottom" />
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              divided
              @click.native="logout"
            >
              <span style="display: block">退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
// import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import ErrorLog from "@/components/ErrorLog";
import MemberCenter from "@/views/MemberCenter/MemberCenter.vue";
import { vipListAPI } from "@/api/login";

export default {
  name: "NavBar",
  components: {
    // Breadcrumb,
    Hamburger,
    ErrorLog,
    // Search
    MemberCenter,
  },
  data() {
    return {
      drawer: false,
      // fileUrl:downloadfileUrl,
      overdueStu: false, // 密码修改
      userInfo: "",
      titleName: "哒达招商",
      isActive: 0,
      isActiveIdx: 0,
      menuList: JSON.parse(localStorage.getItem("menuData")),
      isVip: false,
      permission: "",
    };
  },
  computed: {
    ...mapGetters(["sidebar", "avatar", "device"]),
  },
  async mounted() {
    this.permission = localStorage.getItem("permission");
    this.userInfo = JSON.parse(localStorage.getItem("userInfo"));
    await this.getList();
  },
  methods: {
    async getList() {
      try {
        const res = await vipListAPI();
        const myattract = Object.entries(res.result.industryStatus).map(
          ([key, value]) => ({
            ...value,
          })
        );
        const mywisdom = Object.entries(res.result.investStatus).map(
          ([key, value]) => ({
            ...value,
          })
        );

        const originalList = {
          myattract,
          mywisdom,
        };

        const screeningResult = {
          vip: {
            myattract: originalList.myattract.filter(
              (it) => it.vipEnum === "VIP" || it.vipEnum === "SVIP_VIP"
            ),
            mywisdom: originalList.mywisdom.filter(
              (it) => it.vipEnum === "VIP" || it.vipEnum === "SVIP_VIP"
            ),
          },
          svip: {
            myattract: originalList.myattract.filter(
              (it) => it.vipEnum === "SVIP" || it.vipEnum === "SVIP_VIP"
            ),
            mywisdom: originalList.mywisdom.filter(
              (it) => it.vipEnum === "SVIP" || it.vipEnum === "SVIP_VIP"
            ),
          },
        };
        if (screeningResult.svip.mywisdom.length === 0) {
          this.isVip = false;
        } else {
          this.isVip = true;
        }
      } catch (error) {
        console.error("获取我的VIP列表失败", error);
      }
    },
    handleClose() {
      this.drawer = false;
    },
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    tohomepage() {
      this.$router.push("/home-page");
    },
    async logout() {
      this.$confirm("确定要退出登录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await this.$store.dispatch("user/logout");
        this.$router.push(`/login`);
        // 清空token
        this.$message({
          type: "success",
          message: "退出登录成功",
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 18px;
          font-size: 12px;
        }
      }
    }
  }
}
.vipLogo {
  margin-right: 10px;
  display: flex;
  align-items: center;
  cursor: pointer;
  img {
    width: 46px;
    height: 32px;
  }
}
.rightView {
  display: flex;
  align-items: center;
}
.boldText {
  font-weight: bold;
  font-size: 24px;
}
</style>
