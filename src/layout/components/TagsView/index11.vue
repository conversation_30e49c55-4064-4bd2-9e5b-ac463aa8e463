<template>
  <div
    id="tags-view-container"
    class="tags-view-container"
    :style="{ width: 'calc(100% - ' + (leftwidth - 10) + 'px)' }"
  >
    <!-- 收缩按钮 -->
    <hamburger
      v-show="$store.getters.left_route.length > 0"
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />
    <scroll-pane
      ref="scrollPane"
      class="tags-view-wrapper"
      @scroll="handleScroll"
    >
      <router-link
        v-for="(tag, index) in visitedViews"
        ref="tag"
        :key="tag.path"
        :style="{
          background: isActive(tag) ? $store.state.settings.theme : '#fff',
        }"
        :class="isActive(tag) ? 'active' : ''"
        :to="{
          name11: '111',
          path: tag.path,
          query: tag.query,
          fullPath: tag.fullPath,
        }"
        tag="span"
        class="tags-view-item"
        @click="toPush(tag.path)"
        @click.middle.native="
          index == 0 && !isAffix(tag) ? closeSelectedTag(tag) : ''
        "
        @contextmenu.prevent.native="openMenu(tag, $event)"
      >
        {{ tag.title }}
        <span
          v-if="index != 0"
          class="el-icon-close"
          @click.prevent.stop="closeSelectedTag(tag)"
        />
      </router-link>
    </scroll-pane>
    <span class="right-info">
      <Settings />
      <el-tooltip
        class="right-menu-item"
        content="字体调整"
        effect="dark"
        placement="bottom"
      >
        <size-select
          id="size-select"
          class="right-menu-item hover-effect"
        />
      </el-tooltip>
      <screenfull
        id="screenfull"
        class="right-menu-item hover-effect"
      />
    </span>
    <ul
      v-show="visible"
      :style="{ left: left + 6 + 'px', top: top - 9 + 'px' }"
      class="contextmenu"
    >
      <li @click="refreshSelectedTag(selectedTag)">
        刷新
      </li>
      <!-- <li v-if="index != 0 && !isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">关闭</li> -->
      <li @click="closeOthersTags">
        关闭其他
      </li>
      <li @click="closeAllTags(selectedTag)">
        关闭所有
      </li>
    </ul>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import ScrollPane from "./ScrollPane";
import Hamburger from "@/components/Hamburger";
import path from "path";
import Settings from "../Settings/index";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";

export default {
  components: { ScrollPane, Hamburger, Settings, Screenfull, SizeSelect },
  props: {
    leftwidth: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
    };
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews;
    },
    routes() {
      return this.$store.state.permission.routes;
    },
    ...mapGetters(["sidebar", "avatar", "device"]),
  },
  watch: {
    $route() {
      this.addTags();
      this.moveToCurrentTag();
    },
    visible(value) {
      if (value) {
        document.body.addEventListener("click", this.closeMenu);
      } else {
        document.body.removeEventListener("click", this.closeMenu);
      }
    },
  },
  mounted() {
    this.initTags();
    this.addTags();
  },
  methods: {
    // 页面跳转 {path:'', query: tag.query, fullPath: tag.fullPath}
    toPush(route) {
      this.$router.push({ path: route.path });
    },
    isActive(route) {
      return route.path === this.$route.path;
    },
    isAffix(tag) {
      return tag.meta && tag.meta.affix;
    },
    filterAffixTags(routes, basePath = "/") {
      let tags = [];

      routes.forEach((route) => {
        if (route && route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path);
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta },
          });
        }
        if (route && route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path);
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags];
          }
        }
      });
      return tags;
    },
    initTags() {
      window.localStorage.removeItem("topMenuCode");
      window.localStorage.removeItem("TopMenuList");

      const affixTags = (this.affixTags = this.filterAffixTags(this.routes));
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.name) {
          this.$store.dispatch("tagsView/addVisitedView", tag);
        }
      }
    },
    // tab切换，页面跳转
    addTags() {
      const { name, fullPath, page } = this.$route;
      if (name) {
        let getCodeList = JSON.parse(localStorage.getItem("topMenuCode")) || ""; // 获取code 列表
        let topMenuList = JSON.parse(localStorage.getItem("TopMenuList")) || []; // 头部code 列表 带其他字段
        let nowPageInfo = {};
        topMenuList &&
          ((item) => {
            if (item && this.$route.path == "/" + item.code) {
              nowPageInfo = item;
            }
          });
        this.$store.dispatch("tagsView/addView", { ...this.$route });
        this.$store.dispatch("permission/operationList", nowPageInfo || ""); // nowpage添加当前页面权限数据
      }
      return false;
    },
    moveToCurrentTag() {
      const tags = this.$refs.tag;
      this.$nextTick(() => {
        for (const tag of tags) {
          if (tag.to.path === this.$route.path) {
            this.$refs.scrollPane.moveToTarget(tag);
            // when query is different then update
            if (tag.to.fullPath !== this.$route.fullPath) {
              this.$store.dispatch("tagsView/updateVisitedView", this.$route);
            }
            break;
          }
        }
      });
    },
    refreshSelectedTag(view) {
      this.$store.dispatch("tagsView/delCachedView", view).then(() => {
        const { fullPath } = view;
        this.$nextTick(() => {
          this.$router.replace({
            path: "/redirect" + fullPath,
          });
        });
      });
    },
    closeSelectedTag(view) {
      this.$store
        .dispatch("tagsView/delView", view)
        .then(({ visitedViews }) => {
          if (this.isActive(view)) {
            this.toLastView(visitedViews, view);
          }
        });
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag);
      this.$store
        .dispatch("tagsView/delOthersViews", this.selectedTag)
        .then(() => {
          this.moveToCurrentTag();
        });
    },
    closeAllTags(view) {
      this.$store.dispatch("tagsView/delAllViews").then(({ visitedViews }) => {
        if (this.affixTags.some((tag) => tag.path === view.path)) {
          return;
        }
        this.initIndexWay(); // 关闭所有，跳转至第一个菜单，
        // this.toLastView(visitedViews, view)
      });
    },

    initIndexWay() {
      let menuList = JSON.parse(localStorage.getItem("menuData"));
      localStorage.setItem("sidebarStatus", 1); // 左侧菜单打开
      let nowPage = {};
      if (
        menuList[0] &&
        menuList[0].menuList &&
        menuList[0].menuList.length >= 1
      ) {
        nowPage = menuList[0].menuList[0].subMenuList[0];
        this.$store.dispatch("permission/setLeftRoute", menuList[0].menuList); // 左侧菜单设置
      } else {
        nowPage = menuList[0];
      }
      const codeList =
        nowPage &&
        nowPage.operationList &&
        nowPage.operationList.map((value, index, array) => {
          return value.code;
        });
      nowPage && localStorage.setItem("nowPage", JSON.stringify(nowPage));
      localStorage.setItem("topMenuCode", JSON.stringify([nowPage.code]));

      this.$router.push("/" + nowPage.code);
      // this.$store.dispatch('tagsView/addView', { name:'',page:nowPage,title:nowPage.name, meta:{title: nowPage.name, affix:true, path:'/'+nowPage.code}});
      // this.$store.dispatch('tagsView/addView', { name:'',title:nowPage.name, page:nowPage,  meta:{title: nowPage.name, keepAlive: true, list:true},
      //     path:nowPage.code});
      nowPage &&
        localStorage.setItem(
          "operationList",
          JSON.stringify(nowPage.operationList) || ""
        );
      codeList && localStorage.setItem("operationCode", codeList || "/");
      // nowPage.code =  nowPage.code.substr(1)
      // nowPage && localStorage.setItem('TopMenuList', JSON.stringify([nowPage]));
    },

    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0];
      if (latestView) {
        this.$router.push(latestView.fullPath);
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        if (view.name === "Dashboard") {
          // to reload home page
          this.$router.replace({ path: "/redirect" + view.fullPath });
        } else {
          this.$router.push("/");
        }
      }
    },
    openMenu(tag, e) {
      const menuMinWidth = 105;
      const offsetLeft = this.$el.getBoundingClientRect().left; // container margin left
      const offsetWidth = this.$el.offsetWidth; // container width
      const maxLeft = offsetWidth - menuMinWidth; // left boundary
      const left = e.clientX - offsetLeft + 15; // 15: margin right

      if (left > maxLeft) {
        this.left = maxLeft;
      } else {
        this.left = left;
      }

      this.top = e.clientY - 30;
      this.visible = true;
      this.selectedTag = tag;
    },
    closeMenu() {
      this.visible = false;
    },
    handleScroll() {
      this.closeMenu();
    },
    // 收缩
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
  },
};
</script>

<style lang="scss" scoped>
.right-info {
  float: right;
  height: 40px;
  line-height: 40px;
  font-size: 18px;
  color: #333;
  padding-left: 14px;
  margin-right: 7px;
  // border-left: #f2f1f1 1px solid;  box-shadow: 1px 0px 10px #ccc;
  .right-menu-item {
    float: right;
    width: 26px;
    font-size: 13px;
  }
  .drawer-container {
    float: right;
    width: 30px;
  }
}
.tags-view-container {
  position: fixed;
  top: 56px;
  z-index: 6;
  height: 40px;
  width: 100%;
  background: #fff;
  // overflow: hidden;
  border-bottom: 1px solid #d8dce5;
  .el-scrollbar__view {
    height: 39px;
  }
  // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);
  .tags-view-wrapper {
    .tags-view-item {
      min-width: 64px;
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 30px;
      line-height: 21px;
      background: #ffffff;
      border: 1px solid #e4e7ed;
      color: #495060;
      background: #fff;
      padding: 5px 7px;
      font-size: 12px;
      text-align: center;
      margin-right: 8px;
      // margin-left: 5px;
      margin-top: 6px;
      border-radius: 3px;
      // .tags-view-item::after{content:'111'; }
      &:first-of-type {
        // margin-left: 15px;
      }
      &:last-of-type {
        margin-right: 15px;
      }
      &.active {
        // height: 37px;
        // line-height: 37px;
        // margin-top: 0px;
        // background-color: #1A305F;
        color: #fff;
        // border-color: #1A305F;
        // border-radius: 4px;
        // &::before {
        //   content: '';
        //   background: #fff;
        //   display: inline-block;
        //   width: 8px;
        //   height: 8px;
        //   border-radius: 50%;
        //   position: relative;
        //   margin-right: 2px;
        // }
      }
    }
  }
  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 9999; //3000
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
//reset element css of el-icon-close
.hamburger-container {
  float: left;
  padding: 10px 10px !important;
  // border-right: #f2f1f1 1px solid;box-shadow: 1px 0px 10px #ccc;
}
.tags-view-wrapper {
  .tags-view-item {
    min-width: 64px;
    transition: transform 0.2s, z-index 0s;
  }
  .tags-view-item:hover {
    transform: scale(1.08, 1.08);
  }
  .tags-view-item {
    .el-icon-close {
      width: 12px;
      height: 6px;
      margin-left: 9px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform-origin: 100% 50%;
      line-height: 0px;
      &:before {
        transform: scale(0.6);
        display: inline-block;
        vertical-align: -3px;
      }
      // &:hover {
      //   background-color: #b4bccc;
      //   color: #fff;
      // }
    }
  }
}
</style>