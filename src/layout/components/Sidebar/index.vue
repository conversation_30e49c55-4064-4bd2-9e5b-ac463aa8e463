<template>
  <div :class="{ 'has-logo': showLogo }">
    <logo
      v-if="showLogo"
      :collapse="isCollapse"
    />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="true"
        :unique-opened="true"
        mode="vertical"
        @select="handleSelect"
      >
        <sidebar-item
          v-for="(route, index) in permission_routes"
          :key="`${route.path}-${index}`"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import Logo from './Logo';
import SidebarItem from './SidebarItem';
import variables from '@/styles/variables.scss';
import { set } from 'lodash';

export default {
  name: 'ScrollbarB',
  components: { SidebarItem, Logo },
  
  computed: {
    ...mapGetters(['permission_routes', 'sidebar']),
    // mounted() {
    //   console.log(permission_routes,'permission_routes')
    // },
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      // console.log(path, 'activepath');
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
  // data() {
  //   return {
  //     activeMenu:''
  //   }
  // },
  // mounted() {
  //   this.getActiveMenu()
  // },
  methods: {
    // getActiveMenu() {
    //   const route = this.$route
    //   const { meta, path } = route
    //   // if set path, the sidebar will highlight the path you set
    //   this.activeMenu = meta.activeMenu ? meta.activeMenu : path
    //   console.log(this.activeMenu,'this.activeMenu')
    // },
    handleSelect(key, keyPath) {
      if (key === '/home-page') {
        let env = process.env.VUE_APP_VIEW_URL;
        if (!env || window.location.hostname === 'localhost') {
          env = 'https://localhost:9999/';
        }
        setTimeout(() => {
          window.location.reload();
        }, 300);
        window.open(`${env}#${key}`);
        return;
      } else {
        this.$router.push(key);
      }
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep {
  .el-menu {
    font-size: 18px;
    font-weight: normal;
    .submenu-title-noDropdown {
      padding-left: 1.7rem !important;
      margin-left: 6px !important;
    }
  }

  .el-menu-item {
    padding-left: 3.3rem !important;
    box-sizing: border-box;
  }

  .el-submenu__title {
    margin: 0 10px;
    box-sizing: border-box;
  }

  .el-menu .el-menu-item.is-active {
    //width: 210px;
    border-radius: 8px;
    box-sizing: border-box;
    background: linear-gradient(153deg, #aec6ff -19%, #1f61ff 84%) !important;
  }
}
</style>
