<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
  },
  render(h, context) {
    const { icon, title } = context.props;
    const vnodes = [];
    if (icon) {
      vnodes.push(
        <i class={['titleIcon', 'iconfont']} domPropsInnerHTML={icon}></i>
      );
    }

    if (title) {
      vnodes.push(<span slot='title'>{title}</span>);
    }
    return vnodes;
  },
};
</script>

<style scoped>
.sub-el-icon {
  color: currentColor;
  width: 1em;
  height: 1em;
}
.sub-el-icon2 {
  color: currentColor;
  width: 1.6em !important;
  height: 1.6em !important;
  vertical-align: -0.45em !important;
  fill: currentColor !important;
  overflow: hidden !important;
}
.titleIcon {
  margin-right: 3px;
}
</style>
 