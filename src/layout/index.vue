<template>
  <div
    :class="classObj"
    class="app-wrapper"
  >
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    <div class="logo">
      <img src="https://static.idicc.cn/static/sso/favicon.ico">
      <div
        v-if="sidebar.opened"
        class="name"
      >
        哒达招商
      </div>
    </div>
    <sidebar class="sidebar-container" />
    <div
      :class="{ hasTagsView: needTagsView }"
      class="main-container"
    >
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar />
      </div>
      <app-main />
      <!-- <right-panel v-if="showSettings">
        <settings />
      </right-panel> -->
    </div>
  </div>
</template>

<script>
import { AppMain, Navbar, Sidebar } from "./components";
import ResizeMixin from "./mixin/ResizeHandler";
import { mapState } from "vuex";
// import rightPanel from '@/components/RightPanel'
// import settings from './components/Settings'

export default {
  name: "LayLout",
  components: {
    AppMain,
    Navbar,
    Sidebar,
    //TagsView,
    // rightPanel,
    // settings
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      showSettings: (state) => state.settings.showSettings,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.logo {
  background: #e9f0ff;
  position: fixed;
  left: 0px;
  top: 0px;
  display: flex;
  align-items: center;
  width: 256px;
  height: 50px;
  padding-left: 18px;
      padding-top: 16px;

  img {
    border-style: none;
    height: 32px;
    margin-right: 8px;
  }

  .name {
    display: inline-block;
    height: 22px;
    margin-block: 0;
    margin-inline-end: 0;
    margin-inline-start: 6px;
    color: rgba(0, 0, 0, 0.88);
    animation-name: antBadgeLoadingCircle;
    animation-duration: 0.4s;
    animation-timing-function: ease;
    font-weight: 600;
    font-size: 20px;
    line-height: 22px;
    vertical-align: middle;
  }
}

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
