import request from '@/utils/request'
import {apiUrl} from '@/api/user'
/**
 * 添加企业舆情
 * @returns 
 */
export function addFeelingsAPI(data) {
    return request({
        url:apiUrl+'/admin/enterprise/public/opinion/add',
        method: 'POST',
        data
    })
}
 /**
* 企业舆情-舆情资讯
* @returns 
*/
export function informationlistAPI(data) {
    return request({
        url:'/merchants/information/list',
        method: 'POST',
        data
    })
 }
/**
 * 获取企业舆情列表
 * @returns 
 */
 export function opinionAPI(data) {
    return request({
        url:apiUrl+'/admin/enterprise/public/opinion/page',
        method: 'POST',
        data
    })
}
/**
 * 删除企业舆情
 * @returns 
 */
 export function deleteAPI(data) {
    return request({
        url:apiUrl+'/admin/enterprise/public/opinion/delete',
        method: 'POST',
        data
    })
}
/**
 * 获取企业舆情详细信息
 * @returns 
 */
 export function detailAPI(params) {
    return request({
        url:apiUrl+'/admin/enterprise/public/opinion/detail',
        method: 'GET',
        params
    })
}
/**
 * 更新企业舆情
 * @returns 
 */
 export function updateAPI(data) {
    return request({
        url:apiUrl+'/admin/enterprise/public/opinion/update',
        method: 'POST',
        data
    })
}
