import request from '@/utils/request'
import {apiUrl} from '@/api/user'
/**
 * 产业360 查询参数
 * @param {*} data
 * @returns
 */
export function getSearchParamAPI(params) {
    return request({
        url:apiUrl+"/ai/industryChain/getSearchParam",
        method:'GET',
        params,
    })
}
/**
 * 获取招商情报列表
 * @param {*} data 
 * @returns 
 */
export function attractInvestmentInformationListAPI(data) {
    return request({
        url:apiUrl+"/admin/business/enterprise/attractInvestmentInformationList",
        method:'POST',
        data,
    })
}