import request from '@/utils/request';

// 获取周报列表
export function getWeeklyReports(params) {
  return request({
    url: '/ai/weeklyReport/list',
    method: 'get',
    params
  });
}
// 获取周报 详情
export function getWeeklyReportDetial(params) {
  return request({
    url: '/ai/weeklyReport/detail',
    method: 'get',
    params
  });
}
// 获取周报 详情
export function getWeeklyReportDelete(params) {
  return request({
    url: '/ai/weeklyReport/delete',
    method: 'get',
    params
  });
}
export function getWeeklyStats(params) {
  return request({
    url: '/ai/weeklyReport/weeklyStats',
    method: 'get',
    params
  });
}

// 添加周报
export function addWeeklyReport(data) {
  return request({
    url: '/ai/weeklyReport/add',
    method: 'post',
    data
  });
}

// 更新周报
export function updateWeeklyReport(id, data) {
  return request({
    url: `/api/weekly-reports/${id}`,
    method: 'put',
    data
  });
}

// 删除周报
export function deleteWeeklyReport(id) {
  return request({
    url: `/api/weekly-reports/${id}`,
    method: 'delete'
  });
}

export function getDataReport(data) {

  return request({
    url: `/ai/investment/enterprise/staticInfoByPeriod`,
    method: 'post',
    data
  });
}
export function getCollectList(data) {
  return request({
    url: `/ai/investment/enterprise/collection/list`,
    method: 'get',
    params:data
  });
}
export function getAuditList(data) {
  return request({
    url: `/ai/entrust/client/payment/apply/audit/list`,
    method: 'get',
    params:data
  });
}
// 我的审批
export function getAuditListDetial(data) {
  return request({
    url: `/ai/entrust/client/payment/apply/detail`,
    method: 'get',
    params:data
  });
}

// 审批
export function getApplyAudit(data) {
  return request({
    url: `/ai/entrust/client/payment/apply/audit`,
    method: 'post',
    data
  });
}
// 审批check
export function getApplyAuditCheck(data) {
  return request({
    url: `/ai/entrust/client/payment/apply/check`,
    method: 'get',
    params:data
  });
}

/**
 * 招商管理  导出
 * @param {*} data 
 * @returns 
 */
export function pageListExport(data) {
    return request({
     url:"/ai/investment/enterprise/export",
    method: 'get',
      params: data,
       timeout: 9000000,
  });
 
}
