import interfaceRequest from '@/utils/interfaceRequest'
/**
 *大屏端-科技型企业、上市及挂牌类型企业excel导出
 * @param {*} params 
 * @returns 
 */
export function insightEnterprisedownloadAPI(data) {
  return interfaceRequest({
    url: '/admin/orgIndustryChainRelation/insightEnterpriseList/download',
    method: 'POST',
    data,
    timeout: 9000000,
    responseType: 'blob' || '',
  })
}
/**
* 产业360
* @param {*} params 
* @returns 
*/
export function merchantsdownloadAPI(data) {
  return interfaceRequest({
    url: '/merchants/enterprise/search/download',
    method: 'POST',
    data,
    timeout: 9000000,
    responseType: 'blob' || '',
  })
}
/**
* 招商智推
* @param {*} params 
* @returns 
*/
/**
 * 招商情报
 * @param {*} data 
 * @returns 
 */
export function investment_enterprisedownloadAPI(data) {
  return interfaceRequest({
    // url: '/merchants/investment_enterprise/search/download',
    url: '/merchants/investment_enterprise/search/download/v2',
    method: 'POST',
    data,
    timeout: 9000000,
    responseType: 'blob' || '',
  })
}
/**
 * 产业企业
 * @param {*} params 
 * @returns 
 */
export function listIndustryEnterprisedownloadAPI(data) {
  return interfaceRequest({
    url: '/dpar/industryOverview/listIndustryEnterprise/download',
    method: 'POST',
    data,
    timeout: 9000000,
    responseType: 'blob' || '',
  })
}
/**
* 产业创新
* @param {*} params 
* @returns 
*/
export function listIndustryInnovateEnterprisedownloadAPI(data) {
  return interfaceRequest({
    url: '/dpar/industryOverview/listIndustryInnovateEnterprise/download',
    method: 'POST',
    data,
    timeout: 9000000,
    responseType: 'blob' || '',
  })
}
/**
* 对外投资
* @param {*} params 
* @returns 
*/
export function investEnterprisePagedownloadAPI(data) {
  return interfaceRequest({
    url: '/dpar/industryInvestment/investEnterprisePage/download',
    method: 'POST',
    data,
    timeout: 9000000,
    responseType: 'blob' || '',
  })
}
/**
* 投向企业
* @param {*} params 
* @returns 
*/
export function investedEnterprisePagedownloadAPI(data) {
  return interfaceRequest({
    url: '/dpar/industryInvestment/investedEnterprisePage/download',
    method: 'POST',
    data,
    timeout: 9000000,
    responseType: 'blob' || '',
  })
}
/**
* 投向企业
* @param {*} params 
* @returns 
*/
export function financingPagedownloadAPI(data) {
  return interfaceRequest({
    url: '/dpar/industryInvestment/financingPage/download',
    // url: '/merchants/investment_enterprise/search/download/v2',
    method: 'POST',
    data,
    timeout: 9000000,
    responseType: 'blob' || '',
  })
}
/**
* 招商模式列表导出
* @param {*} params 
* @returns 
*/
export function listByModelTypedownloadAPI_V2(data) {
  return interfaceRequest({
    url: '/merchants/investment_enterprise/listByModelType/download/v2',
    method: 'POST',
    data,
    timeout: 9000000,
    responseType: 'blob' || '',
  })
}


