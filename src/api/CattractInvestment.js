import request from '@/utils/request'
import {apiUrl} from '@/api/user'
/**
 * 通过机构和产业链关联关系主键id获取产业链id
 * @param {*} data 
 * @returns 
 */
export function getIndustryChainIdByIdAPI(params) {
    return request({
        url:apiUrl+"/admin/orgIndustryChainRelation/getIndustryChainIdById",
        method:'GET',
        params,
    })
}
/**
 * 获取招商雷达列表
 * @param {*} data 
 * @returns 
 */
export function attractInvestmentRadarListAPI(params) {
    return request({
        url:apiUrl+"/admin/business/enterprise/attractInvestmentRadarList/v2",
        method:'GET',
        params,
    })
}
/**
 * 获取招商情报列表
 * @param {*} data 
 * @returns 
 */
export function attractInvestmentInformationListAPI(data) {
    return request({
        url:apiUrl+"/admin/business/enterprise/attractInvestmentInformationList",
        method:'POST',
        data,
    })
}
/**
 * 招商信息状态变更
 * @param {*} data 
 * @returns 
 */
export function updateAttractInvestmentStatusAPI(data) {
      const fm = new FormData()
      fm.append('id',data.id)
      fm.append('status',data.status)
    return request({
        url:apiUrl+"/admin/business/enterprise/updateAttractInvestmentStatus",
        method:'POST',
        data:fm
    })
}
/**
 * 纳入意向
 * @param {*} data 
 * @returns 
 */
export function updatainclusionIntention(data) {
  return request({
      url:apiUrl+"/ai/investment/enterprise/addEnterprise",
      method:'POST',
      data:data
  })
}
/**
 * 暂不处理
 * @param {*} data 
 * @returns 
 */
export function leaveAside(params) {
    return request({
        url:apiUrl+"/admin/business/enterprise/leaveAside",
        method:'GET',
        params
    })
  }
/**
 * 招商情报统计
 * @param {*} data 
 * @returns 
 */
export function attractInvestmentInformationStatisticsAPI(params) {
    return request({
        url:apiUrl+"/admin/business/enterprise/attractInvestmentInformationStatistics/v2",
        method:'GET',
        params,
    })
}
/**
 * 招商管理统计
 * @param {*} data 
 * @returns 
 */
export function investmentManagementStatisticsAPI(params) {
    return request({
        url:apiUrl+"/admin/investment/attraction/clue/investmentManagementStatistics",
        method:'GET',
        params,
    })
}
/**
 * 获取指定产业链的所有2级节点集合
 * @param {*} data 
 * @returns 
 */
export function getAllChainIdAPI(params) {
    return request({
        url:apiUrl+"/admin/industryChainNode/getAllSecondLevelChainNodeByIndustryChainId",
        method:'GET',
        params,
    })
}
/**
 * 获取当前用户的所在部门及其子部门的所有用户信息集合
 * @param {*} data 
 * @returns 
 */
export function listAllChildDeptUsersAPI(params) {
    return request({
        url:apiUrl+"/admin/user/listAllChildDeptUsers",
        method:'GET',
        params,
    })
}
/**
 * 获取当前用户的所在部门及其子部门的所有用户信息集合（去除同级）
 * @param {*} data 
 * @returns 
 */
export function listAllDeptAPI(params) {
    return request({
        url:apiUrl+"/admin/user/listAllChildDeptUsersRemoveIndexDept",
        method:'GET',
        params,
    })
}
/**
 * 招商信息列表
 * @param {*} data 
 * @returns 
 */
export function pageListAPI(data) {
    return request({
        url:apiUrl+"/admin/investment/attraction/clue/pageList",
        method:'POST',
        data,
    })
 
}

/**
 * 指派线索
 * @param {*} data 
 * @returns 
 */
export function assignmentClueAPI(data) {
    return request({
        url:apiUrl+"/admin/investment/attraction/clue/assignmentClue",
        method:'POST',
        data,
    })
}
/**
 * 委托招商
 * @param {*} data 
 * @returns 
 */
export function commissionConnectionAPI(data) {
    return request({
        url:apiUrl+"/admin/investment/attraction/clue/commissionConnection",
        method:'POST',
        data,
    })
}
/**
 * 新增跟进记录
 * @param {*} data 
 * @returns 
 */
export function addfollowUpRecordAPI(data) {
    return request({
        url:apiUrl+"/admin/investmentAttractionClue/followUpRecord/add",
        method:'POST',
        data,
    })
}
/**
 * 获取指定招商线索的跟进记录列表
 * @param {*} data 
 * @returns 
 */
export function followUpRecordPageAPI(data) {
    return request({
        url:apiUrl+"/admin/investmentAttractionClue/followUpRecord/pageList",
        method:'POST',
        data,
    })
}

/**
 * 获取企业详情
 * @param {*} data 
 * @returns 
 */
export function enterpriseDetailAPI(params) {
    return request({
        url:apiUrl+"/admin/orgIndustryChainRelation/enterpriseDetail",
        method:'GET',
        params,
    })
}
/**
 * 根据机构产业链关系id查询产业链节点树(为了获取导航栏文字)
 * @param {*} data 
 * @returns 
 */
export function loadByOrgIdAPI(params) {
    return request({
        url:apiUrl+"/admin/industryChainNode/loadByOrgId",
        method:'GET',
        params,
    })
}

/**
 * 获取当前登录用户所在机构的最小政区划
 * @param {*} params 
 * @returns 
 */
export function getMinAreaByTokenAPI(params) {
    return request({
        url:'/admin/orgIndustryChainRelation/getMinAreaByOrgIndustryRelationId',
        method:'GET',
        params
    })
  }
  /**
 * 用户端招商资讯列表
 * @param {*} data 
 * @returns 
 */
export function pageListToUserAPI(data) {
    return request({
        url:apiUrl+"/admin/investment/information/pageListToUser",
        method:'POST',
        data,
    })
}
/**
 * 获取指定关键字类型的关键字词典集合
 * @param {*} params 
 * @returns 
 */
export function listByTypeAPI(params) {
    return request({
        url:'/admin/keyword/dictionary/listByType',
        method:'GET',
        params
    })
  }
  /**
 * 获取一条指定名称的企业信息
 * @param {*} params 
 * @returns 
 */
export function getByNameAPI(params) {
    return request({
        url:'/admin/enterprise/getByName',
        method:'GET',
        params
    })
  }
/**
 * 招商企业推荐列表
 * @param {*} data 
 * @returns 
 */
export function searchListAPI(data) {
    return request({
        url:apiUrl+"/merchants/investment_enterprise/search",
        method:'POST',
        data,
    })
}
  /**
 * 获取强延补链环节节点
 * @param {*} params 
 * @returns 
 */
  export function getTypeNodeLinkPlaceAPI(params) {
    return request({
        url:'/merchants/chain_node_attribute/getTypeNodeLinkPlace',
        method:'GET',
        params
    })
  }
  /**
 * 资讯分页接口
 * @param {*} data 
 * @returns 
 */
export function informationListAPI(data) {
    return request({
        url:apiUrl+"/miniapps/information/page",
        method:'POST',
        data,
    })
}
  /**
 * 投资布局新资讯跟技术突破新资讯分页接口
 * @param {*} data 
 * @returns 
 */
  export function informationListEventAPI(data) {
    return request({
        url:apiUrl+"/pg/enterprise/event/pageList",
        method:'POST',
        data,
    })
}
  /**
 * 蓝屏线索指派重新指派
 * @param {*} data 
 * @returns 
 */
  export function assignOrReassignAPI(data) {
    return request({
        url:apiUrl+"/admin/investment/attraction/clue/assignOrReassign",
        method:'POST',
        data,
    })
}

/**
 * 招商企业推荐列表
 * @param {*} data 
 * @returns 
 */
export function searchListAPI_V2(data) {
    return request({
        url:apiUrl+"/merchants/investment_enterprise/search/v2",
        method:'POST',
        data,
    })
}


/**
 * 获取当前用户的所在部门及其子部门的所有用户信息集合（去除同级）
 * @param {*} data 
 * @returns 
 */
export function newListAllDeptAPI(params) {
    return request({
        url:apiUrl+"/ai/investment/enterprise/getSearchParam",
        method:'GET',
        params,
    })
}
/**
 * 招商信息列表
 * @param {*} data 
 * @returns 
 */
export function newPageListAPI(data) {
    return request({
        url:apiUrl+"/ai/investment/enterprise/pageList",
        method:'POST',
        data,
    })
 
}
/**
 * 招商经理
 * @param {*} data 
 * @returns 
 */
export function newListOrgUserListAPI(data) {
    return request({
        url:apiUrl+"/ai/investment/enterprise/listAllChildUser",
        method:'get',
        params:data,
    })
 
}
/**
 * 线索指派
 * @param {*} data 
 * @returns 
 */
export function newAssignmentClueAPI(data) {
    return request({
        url:apiUrl+"/ai/investment/enterprise/changeAssignPerson",
        method:'POST',
        data,
    })
 
}

 
/**
 * 跟进记录
 * @param {*} data 
 * @returns 
 */
export function getFollowUpRecord(data) {
    return request({
        url:apiUrl+"/ai/investment/enterprise/getFollowUpRecords",
        method:'GET',
       params: data,
    })
}
 
/**
 * 获取最新一次指派记录
 * @param {*} data 
 * @returns 
 */
export function getLatestAssignClue(data) {
    return request({
        url:apiUrl+"/ai/investment/enterprise/getLatestAssignClue",
        method:'GET',
       params: data,
    })
}

/**
 * 指派路径
 * @param {*} data 
 * @returns 
 */
export function getAssignPath(data) {
    return request({
        url:apiUrl+"/ai/investment/enterprise/getAssignPath",
        method:'GET',
       params: data,
    })
}

/**
 * 指派路径
 * @param {*} data 
 * @returns 
 */
export function getClueDetial(data) {
    return request({
        url:apiUrl+"/ai/entrust/client/clue/detail",
        method:'GET',
       params: data,
    })
}

/**
 * 线索指派
 * @param {*} data 
 * @returns 
 */
export function addClueDealRecord(data) {
    return request({
        url:apiUrl+"/ai/investment/enterprise/addFollowUpRecord",
        method:'POST',
        data,
    })
 
}

