import request from '@/utils/request'
import request2 from '@/utils/request2'

import {apiUrl,publicData,REQ_SOURCE,HEADER_TOKEN_KEY,IS_DEVELOPMENT} from './apiConfig'
import axios from "axios";
export const fileUplodUrl = localStorage.getItem('FILE_SERVER') + apiUrl.fileUpload

export const geturl = {
  publicList: '/vue-element-admin/transaction/list', // 列表接口
  // publicList:'v1/park/cloud/mng/gw', // 列表接口

  publicDelete: '/vue-element-admin/transaction/delete', // 删除接口
  publicDetail: '/vue-element-admin/transaction/detail', // 详情接口
  publicAdd: '/vue-element-admin/transaction/detail', // 新增
  publicEdit: '/vue-element-admin/transaction/detail', // 新增

  timeFlowData:'/vue-element-admin/transaction/detail',
  parkSectionListO:'8838.all.parkcloud.product.parkPoint.search', // 停车段 - 列表
  parkSectionDetailO:'8838.all.parkcloud.product.parkPoint.view', //  停车段 - 详情
  parkSectionAddO: '8838.all.parkcloud.product.parkPoint.add', //  停车段 - 新增
  parkSectionEditO: '8838.all.parkcloud.product.parkPoint.modify', //  停车段 - 修改
  parkSectionDeleteO: '8838.all.parkcloud.product.parkPoint.remove', //  停车段 - 删除
}

export const publicUrl = {
  areaConfig:'8818.co.parkcloud.customer.areaConfig.tree', // 获取省市区数据
}

export function searchUser(name) {
  return request({
    url: '/vue-element-admin/search/user',
    method: 'get',
    params: { name }
  })
}

export function transactionList(query) {
  return request({
    url: '/vue-element-admin/transaction/list',
    method: 'POST',
    params: query
  })
}

export function getlistApi( url, data) {
  let urlApi = url || apiUrl.CT + apiUrl.gateway;
  let getData = request({ url: urlApi, method: 'post',params:data })
  return getData
}

export function deleteApi(url, data) {
  let urlApi = url || apiUrl.CT + apiUrl.gateway;
  return request({ url: urlApi, method: 'post',params:data })
}

export function detailApi(url, data) {
  let urlApi = url || apiUrl.CT + apiUrl.gateway;
  return request({ url: urlApi, method: 'post',params:data })
}

export function addApi(url, data) {
  let urlApi = url || apiUrl.CT + apiUrl.gateway;
  return request({ url: urlApi, method: 'post',params:data })
}

export function editApi(url, data) {
  let urlApi = url || apiUrl.CT + apiUrl.gateway;
  return request({ url: urlApi, method: 'post',params:data })
}

// 枚举字典
export function getEnumAndDictApi( url, data) {
  let urlApi = url || apiUrl.CT + apiUrl.gateway;
  return request({ url: urlApi, method: 'post',params:data})
}

// 参数配置
export function systemParam(url, data) {
  let urlApi = url || apiUrl.CT + apiUrl.gateway;
  return request({ url: urlApi, method: 'post',params:data })
}

// excel导出
export function excelDownloadApi( data) {
  return request({ url: localStorage.getItem('FILE_SERVER') + apiUrl.fileDownload, method: 'post', data:data})
}

/**
 * 模板下载:通过文件下载方式
 * @param data dataType
 * @returns {AxiosPromise}
 */
export function downloadTemplateByFile( dataType) {
  let reqData={dataType:dataType}
  // downloadTemplate
  return request({ url: localStorage.getItem('FILE_SERVER') + apiUrl.downloadTemplate, method: 'post', data:{...publicData, bizReqData:reqData} })
}

/**
 * 模板下载：通过数据流的方式进行下载
 * @param dataType
 */
export function downloadTemplateByStream(dataType){
  //请求参数
  let reqData={dataType:dataType}
  // 请求地址
  let url= localStorage.getItem('FILE_SERVER') + apiUrl.downloadTemplate
  axios({
    method: 'post',
    url: url,
    headers: {'Content-Type': 'application/json',[HEADER_TOKEN_KEY]:localStorage.getItem('TOKEN')},
    data: {...publicData, bizReqData:reqData}, // 参数
    responseType: 'blob' // 表明返回服务器返回的数据类型
  })
  .then((res) => { // 处理返回的文件流
    const content = res
    const blob = new Blob([content])
    const fileName = Date.now()+'_模板.xls'
    if ('download' in document.createElement('a')) { // 非IE下载
      const elink = document.createElement('a')
      elink.download = fileName
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    } else { // IE10+下载
      navigator.msSaveBlob(blob, fileName)
    }
  })
}

/**
 * excel导入
 * @param dataType
 * @param fileList
 * @returns {AxiosPromise}
 */
export function fileUpload(dataType, fileList) {
  let formData=new FormData();
      formData.append('dataType',dataType)
      formData.append('source',REQ_SOURCE)
      formData.append('file',fileList[0])
      // publicFileUploadUrl
      // apiUrl.CTUrl + apiUrl.fileImport
  return request2({ url: apiUrl.publicFileUploadUrl + apiUrl.fileImport, method: 'post',
   data:formData})
}

// 图片上传
export function getOssAccessToken (data) {
  return request({ useCore: 'oss' })('', data || { 'file_type': 'image', project: 'yiche' })
}

// 省市区数据
export function getAreaApi(  data ) {
  return request({ url: apiUrl.CT + apiUrl.gateway, method: 'post', data:{...publicData, operation:publicUrl.areaConfig, bizReqData:data} })
}

export function transactionAdd(data) {
  return request({
    url: '/vue-element-admin/transaction/add',
    method: 'POST',
    data: data
  })
}

export function transactionDetail(data) {
  return request({
    url: '/vue-element-admin/transaction/detail',
    method: 'POST',
    data: data
  })
}

/**
 * POST 请求数据
 * @param operation 请求操作
 * @param data 请求参数
 */
export function doPost(operation,data){
  return doPost2("",operation,data)
}

/**
 * POST 请求数据
 * @param url 请求地址
 * @param operation 请求操作
 * @param data 参数
 */
export function doPost2(url,operation,data){
  let urlApi = url || apiUrl.CT + apiUrl.gateway;
  return request({ url: urlApi, method: 'post',params:IS_DEVELOPMENT?{d:operation}:{}, data:{...publicData, operation:operation, bizReqData:data||{}} })
}