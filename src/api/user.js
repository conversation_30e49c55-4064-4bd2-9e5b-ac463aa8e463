import request from '@/utils/request'
// import axios from 'axios'

const mockUrl = 'https://www.fastmock.site/mock/cd339c8f48c9e13af2c490b1481ffe1c/qingteng';
// export const apiUrl = 'http://pangudev.idicc.cn';
// export const apiUrl = 'http://pangutest.idicc.cn';

// export const apiUrl =   process.env.VUE_APP_PORT_URL  
export const apiUrl = '';
// export const cityJsonUrl = `https://geo.datav.aliyun.com`
export const cityJsonUrl = ``

/**
 * 登录
 * @param {*} data 
 * @returns 
 */
// export function login({username,password}) {
export function login(data) {
  // const fm = new FormData()
  // fm.append('username',username)
  // fm.append('password',password)
  return request({
    // url: mockUrl+'/user/login',
    // async:false,
    url: apiUrl + '/admin/login',
    method: 'POST',
    data,
    //data:fm
  })
}

export function getUserSms() {
  return request({
    url: apiUrl + '/admin/user/smsMessage',
    method: 'GET',
    params: { username: '13381801205' }
  })
}

export function getInfo(token) {
  return request({
    url: mockUrl + '/user/info',
    method: 'POST',
    params: { token }
  })
}

export function logout() {
  return request({
    url: apiUrl + '/admin/logout',
    method: 'post'
  })
}
/**
 * 获取登陆密码rsa加密的公钥
 * @returns 
 */
export function encryptionAPI() {
  return request({
    url: '/sso' + '/admin/user/login/publicKey',
    method: 'GET'
  })
}
/**
 * 用户侧-产业链图谱/地图列表
 * @returns 
 */
export function orgIndustryChainRelationAPI(data) {
  return request({
    url: apiUrl + '/admin/orgIndustryChainRelation/list',
    method: 'POST',
    data
  })
}
/**
 * 获取当前登录用户所在机构的最小政区划
 * @param {*} params 
 * @returns 
 */
export function getMinAreaByTokenAPI() {
  return request({
    url: '/admin/institution/getMinAreaByToken',
    method: 'GET',
  })
}
/**
 * 获取用户已绑定的权限
 * @param {*} params 
 * @returns 
 */
export function listUserBindAcl2() {
  return request({
    //url:mockUrl+'/user/logout',
    url: apiUrl + '/admin/role/acl/listUserBindAcl2',
    method: 'post'
  })
}
/**
 * 获取验证码
 * @param {*} params 
 * @returns 
 */
export function sendVerificationCodeAPI(data) {
  return request({
    //url:mockUrl+'/user/logout',
    url: apiUrl + '/sso/common/sms/log/sendVerificationCode',
    method: 'post',
    data
  })
}
/**
 * 忘记密码
 * @param {*} params 
 * @returns 
 */
export function forgetPasswordAPI(data) {
  return request({
    //url:mockUrl+'/user/logout',
    url: apiUrl + '/sso/common/user/forgetPassword',
    method: 'post',
    data
  })
}
//根据token查用户信息
export function getLoginUserInfoAPI(token) {
  return request({
    //url:mockUrl+'/user/logout',
    url: apiUrl + '/common/user/getLoginUserInfo',
    method: 'GET',
    headers: {
      token: token
    }
  })
}
/**
 * 产业链校验
 * @param {*} params 
 * @returns 
 */
export function verificationAPI(data) {
  return request({
    url: '/admin/industryChain/verification',
    method: 'post',
    data
  })
}
/**
 * 首页-获取登陆用户所属机构 
 * @param {*} params 
 * @returns 
 */
export function getOwnList() {
  return request({
    url: '/sso/admin/institution/ownList',
    method: 'GET',
  })
}
/**
 * 首页-获取树状结构 
 * @param {*} params 
 * @returns 
 */
export function getRouteTree() {
  return request({
    url: '/sso/admin/roleResource/queryUserMenuResourceTree',
    method: 'GET',
  })
}
/**
 * 获取省市
 * @param {*} data
 * @returns
 */
export function getAllAddress(params) {
  return request({
    url: "/admin/administrativeDivision/getAll",
    method: "GET",
    params,
  });
}

export function checkVerificationCodeAPI(data) {
  return request({
    url: '/sso/common/sms/log/checkVerificationCode',
    method: 'post',
    data,
  })
}

export function queryResourceByTypeBtn() {
  return request({
    url: '/sso/admin/roleResource/queryResourceByType',
    method: 'GET',
    params: {
      type: 3
    },
  })
}
