import request from '@/utils/request'
/**
 * 获取登陆人的角色列表
 * @param {*} data 
 * @returns 
 */
export function rolesAPI(data) {
  return request({
      url: '/admin/role/roles',
      method: 'POST',
      data
  })
} 
/**
 * 角色管理列表
 * @param {*} data 
 * @returns 
 */
export function rolepageAPI(data) {
  return request({
      url: '/admin/role/page',
      method: 'POST',
      data
  })
} 

/**
 * 添加角色
 * @param {*} data 
 * @returns 
 */
export function rolesAddAPI(data) {
  return request({
      url: '/admin/role/add',
      method: 'POST',
      data
  })
} 
/**
 * 编辑角色
 * @param {*} data 
 * @returns 
 */
export function rolesEditAPI(data) {
  return request({
      url: '/admin/role/edit',
      method: 'POST',
      data
  })
} 
/**
 * 删除角色
 * @param {*} data 
 * @returns 
 */
export function rolesDeleteAPI(params) {
  return request({
      url: '/admin/role/delete',
      method: 'GET',
      params
  })
} 
/**
 * 获取用户管理的权限
 * @param {*} data 
 * @returns 
 */
export function listAclByUserIdAPI(params) {
  return request({
      //url: '/admin/role/acl/roleAclTree',
      url: '/sso/admin/roleResource/queryLoginUserResourceTree',
      method: 'GET',
      params
  })
}
/**
 * 新增/编辑角色获取权限树
 * @param {*} data 
 * @returns 
 */
export function getAclTreeAPI(params) {
  return request({
      url: '/admin/acl/getAclTreeByRoleType',
      method: 'GET',
      params
  })
}
/**
 * 权限管理获取权限树
 * @param {*} data 
 * @returns 
 */
export function getAclTreeByRoleTypeAPI(data) {
  return request({
      url: '/admin/acl/getAclTree',
      method: 'POST',
      data
  })
}

/**
 * 根据角色获取已有权限
 * @param {*} data 
 * @returns 
 */
export function roleAclTreeAPI(params) {
  return request({
      url: '/admin/role/acl/roleAclList',
      method: 'GET',
      params
  })
}
/**
 * 添加成员
 * @param {*} data 
 * @returns 
 */
export function rolesaveAPI(data) {
  return request({
      url: '/admin/user/role/save',
      method: 'POST',
      data
  })
}
/**
 * 查看成员
 * @param {*} data 
 * @returns 
 */
export function listByRoleIdAPI(data) {
  return request({
      url: '/admin/user/role/pageByRoleIds',
      method: 'POST',
      data
  })
}
/**
 * 移除成员
 * @param {*} data 
 * @returns 
 */
export function deleteRoleUserAPI(params) {
  return request({
      url: '/admin/user/role/deleteRoleUser',
      method: 'GET',
      params
  })
}
/**
 * 已绑定用户
 * @param {*} data 
 * @returns 
 */
export function listRoleUserAPI(params) {
  return request({
      url: '/admin/user/role/listRoleUser',
      method: 'GET',
      params
  })
}
/**
 * 用户部门树
 * @param {*} data 
 * @returns 
 */
export function deptUserTreeAPI(params) {
  return request({
      url: '/admin/user/dept/deptUserTree',
      method: 'GET',
      params
  })
}
/**
 * 设置角色权限类型
 * @param {*} data 
 * @returns 
 */
export function setRoleTypeAclAPI(data) {
  return request({
      url: '/admin/role/acl/setRoleTypeAcl',
      method: 'POST',
      data
  })
}
/**
 * 角色权限类型回显
 * @param {*} data 
 * @returns 
 */
export function listRoleTypeAclAPI(params) {
  return request({
      url: '/admin/role/acl/listRoleTypeAcl',
      method: 'GET',
      params
  })
}
/**
 * 拖拽
 * @param {*} data 
 * @returns 
 */
export function moveAclAPI(data) {
  return request({
      url: '/admin/acl/moveAcl',
      method: 'POST',
      data
  })
}