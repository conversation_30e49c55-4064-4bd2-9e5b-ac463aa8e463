import request from '@/utils/request'
import {apiUrl} from '@/api/user'
/**
 * 下载招商模板
 * @returns 
 */
export function downloadTemplateAPI(data) {
    return request({
        url:apiUrl+'/admin/org/business/enterprise/downloadTemplate',
        method: 'POST',
        data,
        responseType:'blob' || ''
    })
}
/**
 * 招商企业配置产业链节点树
 * @returns 
 */
export function attractTreeAPI(params) {
    return request({
        url:apiUrl+'/admin/org/business/enterprise/queryChainNodeTree',
        method: 'GET',
        params
    })
}
/**
 * 招商企业导入
 * @returns 
 */
export function enterpriseimportAPI(data) {
    let formData=new FormData();
    formData.append('file',data.file)
    formData.append('orgChainId',data.orgChainId)
    formData.append('chainNodeId',data.chainNodeId)
    return request({
        url:apiUrl+'/admin/org/business/enterprise/import',
        method: 'POST',
        data:formData,
        timeout: 9000000
    })
}
/**
 * 招商企业列表
 * @returns 
 */
export function enterpriseListAPI(data) {
    return request({
        url:apiUrl+'/admin/org/business/enterprise/page',
        method: 'POST',
        data
    })
}
/**
 * 招商企业保存
 * @returns 
 */
export function enterpriseSaveAPI(data) {
    return request({
        url:apiUrl+'/admin/org/business/enterprise/batchSave',
        method: 'POST',
        data
    })
}
/**
 * 批量移除
 * @returns 
 */
export function enterpriseBatchRemoveAPI(data) {
    return request({
        url:apiUrl+'/admin/org/business/enterprise/batchRemove',
        method: 'POST',
        data
    })
}
/**
 * 企业列表分页查询
 * @returns 
 */
export function addenterpriseListAPI(data) {
    return request({
        url:apiUrl+'/admin/org/business/enterprise/pageEnterprise',
        method: 'POST',
        data
    })
}
// PC大屏 - 产业招商-招商雷达-产业链节点搜索条件

export function getCountByNodes(data) {
    return request({
        url:apiUrl+'/admin/business/enterprise/attractInvestmentRadarList/v2',
        method: 'POST',
        data
    })
}

/* 招商管理 */
// 新版搜索企业
export function newEnterpriseSearchAPI(data) {
    return request({
        url:apiUrl+'/ai/enterprise/search',
        method: 'POST',
        data
    })
}

// 新增纳入意向企业
export function addEnterpriseAPI_investment(data) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/addEnterprise',
        method: 'POST',
        data
    });
}

// 意向企业列表
export function enterpriseListAPI_investment(data) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/pageList',
        method: 'POST',
        data
    });
}

// 删除意向企业
export function removeEnterpriseAPI_investment(params) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/remove',
        method: 'GET',
        params
    });
}

// 导出意向企业列表
export function exportEnterpriseListAPI_investment(params) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/export',
        method: 'GET',
        params,
        // responseType: 'blob'
    });
}

// 审批校验
export function checkApplyAPI_entrust(params) {
    return request({
        url: apiUrl + '/ai/entrust/client/payment/apply/check',
        method: 'GET',
        params
    });
}

// 审批
export function auditApplyAPI_entrust(data) {
    return request({
        url: apiUrl + '/ai/entrust/client/payment/apply/audit',
        method: 'POST',
        data
    });
}

// 审批列表
export function listApplyAPI_entrust(params) {
    return request({
        url: apiUrl + '/ai/entrust/client/payment/apply/audit/list',
        method: 'GET',
        params
    });
}

// 委托信息
export function clientDetailAPI_entrust(params) {
    return request({
        url: apiUrl + '/ai/entrust/client/clue/detail',
        method: 'GET',
        params
    });
}

// 下属人员列表(去除出海相关)
export function listAllChildUserAPI_investment(params) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/listAllChildUser',
        method: 'GET',
        params
    });
}

// 线索指派
export function changeAssignPersonAPI_investment(data) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/changeAssignPerson',
        method: 'POST',
        data
    });
}

// 获取最新一次指派记录
export function getLatestAssignClueAPI_investment(params) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/getLatestAssignClue',
        method: 'GET',
        params
    });
}

// 指派路径
export function getAssignPathAPI_investment(params) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/getAssignPath',
        method: 'GET',
        params
    });
}

// 跟进
export function addFollowUpRecordAPI_investment(data) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/addFollowUpRecord',
        method: 'POST',
        data
    });
}

// 跟进记录
export function getFollowUpRecordsAPI_investment(params) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/getFollowUpRecords',
        method: 'GET',
        params
    });
}

// 统计分析
export function staticInfoByPeriodAPI_investment(data) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/staticInfoByPeriod',
        method: 'POST',
        data
    });
}

// 招商管理企业详情
export function getInvestEnterpriseDetailAPI_investment(params) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/getInvestEnterpriseDetail',
        method: 'GET',
        params
    });
}

// 收藏
export function collectionAPI_investment(data) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/collection',
        method: 'POST',
        data
    });
}

// 收藏列表
export function listCollectionAPI_investment(params) {
    return request({
        url: apiUrl + '/ai/investment/enterprise/collection/list',
        method: 'GET',
        params
    });
}