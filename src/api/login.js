import request from '@/utils/request'
// 完善信息
export function completeUserInfoAPI(data) {
    return request({
        url: '/ai/user/center/updateCompletion',
        method: 'POST',
        data,
    })
}
// 用户信息
export function getUserInfoAPI(params) {
    return request({
        url: '/ai/user/center/userMessage',
        method: 'GET',
        params
    })
}
// 获取二维码
export function wechatGenerateAPI(params) {
    return request({
        url: '/sso/third/qrcode/wechat/generate',
        method: 'GET',
        params
    })
}
// 获取二维码状态
export function wechatgetstatusAPI(params) {
    return request({
        url: '/sso/third/qrcode/wechat/getStatus',
        method: 'GET',
        params
    })
}
// 获取状态
export function isPopUserInfoCompletionAPI(params) {
    return request({
        url: '/ai/userKnowledgeRelation/isPopUserInfoCompletion',
        method: 'GET',
        params
    })
}

// vip用户信息
export function vipListAPI(params) {
    return request({
        url: '/ai/user/center/vipListOwn',
        method: 'GET',
        params
    })
}

export function listAllPurchaseChainAPI(params) {
    return request({
        url: '/ai/knowledgeLibrary/listAllPurchaseChain',
        method: 'GET',
        params
    })
}

export function codeBeforeCheckNumberAPI(data) {
    return request({
        url: '/admin/user/codeBeforeCheckNumber',
        method: 'post',
        data,
        baseURL: '/sso',
    })
}