
//MNG： 平台端
export const PLATFORM_MNG="MNG"
//PORTAL：运营端
export const PLATFORM_PORTAL="PORTAL"

/**
 * 是否为开发版本
 * @type {boolean} true 开发版本，false 发行版本
 */
export const IS_DEVELOPMENT= process.env.NODE_ENV === 'development'

//development 时 vue_app_platform 为空
export const sysStu = process.env.VUE_APP_PLATFORM;

/**
 * 是否为Poratl
 * @type {""|boolean} true 是，false mng
 */
export const isPortal= sysStu && sysStu===PLATFORM_PORTAL

/**
 * 请求数据源类型
 * @type {string} OPERATOR_PORTAL_PC 或是 MNG_PC
 */
export const REQ_SOURCE=isPortal?"OPERATOR_PORTAL_PC":"MNG_PC"
/**
 * 请求Header token Key
 * @type {string}
 */
export const HEADER_TOKEN_KEY=isPortal?"POAuthorization":"MAuthorization"

export const version = '';

export const apiUrl = {
  gateway:isPortal?'v1/park/cloud/portal/gw':'v1/park/cloud/mng/gw',
  QTUrl:`${process.env.VUE_APP_BASE_API}`,
  QT:process.env.VUE_APP_AGENCY==='FALSE' ? `${process.env.VUE_APP_BASE_API}${process.env.VUE_APP_BASE_API_PORT}/`:process.env.VUE_APP_BASE_API,
  fileImport:`/v1/park/cloud/file/import`, // excel 导入 :${process.env.VUE_APP_BASE_API_FILE_PORT}/v1/park/cloud/file/import
  downloadTemplate:`v1/park/cloud/file/downloadTemplateUrl`, // excel 导入下载模板地址,返回文件
  downloadTemplateSteam:`:${process.env.VUE_APP_BASE_API_FILE_PORT}/v1/park/cloud/file/downloadTemplate`,// 下载模板通过流的方式
  fileDownload:`v1/park/cloud/file/export`, // excel导出
  fileUploadUrl:`${process.env.VUE_APP_BASE_API_FILE_URL}`, // 文件上传地址
  fileUploadPort:`:${process.env.VUE_APP_BASE_API_FILE_PORT}/v1/park/cloud/file/upload`, // 文件上传
  fileUpload:'/v1/park/cloud/file/upload',
  downloadUrl:`${process.env.VUE_APP_BASE_API_DOWNLOADFILE_URL}`, // 文件服务下载地址
  publicFileUploadUrl:window.localStorage.getItem('FILE_SERVER'), // 文件上传地址 +'v1/park/cloud/file/upload'
  // 支付设置证书上传地址
  //CT_UPLOAD_CERTIFICATE:process.env.VUE_APP_BASE_API_PORT ? `${process.env.VUE_APP_BASE_API}:${process.env.VUE_APP_BASE_API_FILE_PORT}/`:`${process.env.VUE_APP_BASE_API}`
}

export const fileUrl =localStorage.getItem('FILE_SERVER')

export const downloadfileUrl = localStorage.getItem('ALIOSS_CONFIG')+'/'

/**
 * 拼接图片 Url 地址
 * @param src url
 * @returns {string|*}
 */
export function splicingImageUrl(src) {
  if(src && (src.startsWith("http://")||src.startsWith("https://")||src.startsWith("file://"))){
    return src
  }else{
    return localStorage.getItem('ALIOSS_CONFIG')+'/'+src
  }
}


export const publicData = {
  // 类型暂时统一使用 MNG_PC
  // partnerNo partnerId
  "partnerNo":isPortal?"8888888118":"8888888118", appCode:isPortal?"2021018888888252":"2021018888888151", "source":isPortal?"OPERATOR_PORTAL_PC":"MNG_PC"/*"MNG_PC"*/,"timestamp":Date.parse(new Date())
}

/**
 * 头像上传头
 * @type {{}}
 */
export const UPLOAD_HEADER={
  [HEADER_TOKEN_KEY]:localStorage.getItem('TOKEN') || ''
  /*'Content-Type':"multipart/form-data"*/
}

/**
 * 创建上传参数
 * @param purpose
 * @returns {{purpose: *, source: string}}
 */
export function CREATE_UPLOAD_PARAM(purpose){
  return {source:REQ_SOURCE,purpose}
}


// FIXME 正式发布是去掉
// 是否开启权限设置，默认false:不开启，使用本地权限 menuDataCN, true:使用服务端返回权限
export const IS_OPEN_PERMISSION = process.env.VUE_APP_IS_OPEN_PERMISSION==='true' 

