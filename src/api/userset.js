import request from '@/utils/request'
/**
 * 用户管理-新增用户
 * @param {*} data 
 * @returns 
 */
export function getAccountInfoAPI(data) {
  return request({
    url: '/admin/user/save',
    method: 'POST',
    data
  })
}
/**
 * 用户管理-查询
 * @param {*} data 
 * @returns 
 */
export function userPageAPI(data) {
  return request({
    url: '/admin/user/userPage',
    method: 'POST',
    data
  })
}
/**
* 用户管理-删除用户
* @param {*} data 
* @returns 
*/
export function userdeletedAPI(params) {
  return request({
    url: '/admin/user/deleted',
    method: 'GET',
    params
  })
}
/**
* 用户管理-编辑用户
* @param {*} data 
* @returns 
*/
export function usereditAPI(data) {
  return request({
    url: '/admin/user/edit',
    method: 'POST',
    data
  })
}
/**
* 用户管理-更改用户状态
* @param {*} data 
* @returns 
*/
export function updateStatusAPI(data) {
  return request({
    url: '/admin/user/updateStatus',
    method: 'POST',
    data
  })
} 