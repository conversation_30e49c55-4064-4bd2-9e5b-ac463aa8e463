import request from '@/utils/request'
import {apiUrl} from '@/api/user'
/**
 * 获取产业链列表
 * @returns 
 */
export function industryAPI(data) {
    return request({
        url:apiUrl+'/admin/industryChain/page',
        method: 'POST',
        data
    })
}
/**
 * 保存产业链列表
 * @returns 
 */
 export function saveAPI(data) {
    return request({
        url:apiUrl+'/admin/industryChain/save',
        method: 'POST',
        data
    })
}

/**
 * 获取树状图列表
 * @param {*} params
 * @returns 
 */
export function treeListAPI(params) {
    return request({
        url:apiUrl+"/admin/industryChainNode/tree",
        method: "GET",
        params
    })
}

/**
 * 产业链节点详情
 * @param {*} params 
 * @returns 
 */
export function nodeDetailsAPI(params) {
    return request({
        url: apiUrl+"/admin/industryChainNode/detail",
        method: "GET",
        params
    })
}

/**
 * 新增产业链节点
 * @param {*} data 
 * @returns 
 */
 export function addNode(data) {
    return request({
        url: apiUrl+'/admin/industryChainNode/save',
        method: 'POST',
        data
    })
} 

/**
 * 产业链名称
 * @param {*} params 
 * @returns 
 */
 export function industryChainTagAPI(data){
    return request({
        url: apiUrl+"/admin/industryChain/listByName",
        method: "POST",
        data
    })
}
/**
 * 产业链标签
 * @param {*} params 
 * @returns 
 */
 export function industrylabelAPIqiyong(params){
    return request({
        url: apiUrl+"/admin/industryLabel/queryByName",
        method: "GET",
        params
    })
}

/**
 * 产业链标签列表
 * @param {*} params 
 * @returns 
 */
 export function industrylabelAPI(params){
    return request({
        url: apiUrl+"/admin/industryLabel/list",
        method: "GET",
        params
    })
}
/**
 * 删除产业链节点
 * @param {*} params 
 * @returns 
 */
export function deleteAPI(params){
    return request({
        url: apiUrl+"/admin/industryChainNode/delete",
        method: "GET",
        params
    })
}

/**
 * 添加产业链标签
 * @param {*} params 
 * @returns 
 */
 export function addTagAPI(data){
    return request({
        url: apiUrl+"/admin/industryLabel/add",
        method: "POST",
        data
    })
}
/**
 * 绑定产业链标签
 * @param {*} params 
 * @returns 
 */
 export function bindTagAPI(data){
    return request({
        url: apiUrl+"/admin/industryLabel/enterprise/bind",
        method: "POST",
        data
    })
}
/**
 * 产业链标签解绑
 * @param {*} params 
 * @returns 
 */
 export function estateunBindTagAPI(data){
    return request({
        url: apiUrl+"/admin/industryLabel/enterprise/unBind",
        method: "POST",
        data
    })
}
/**
 * 更新产业链icon和产业链名称
 * @param {*} params 
 * @returns 
 */
export function updateindustryChainAPI(data){
    return request({
        url: apiUrl+"/admin/industryChain/update",
        method: "POST",
        data
    })
}
/**
 * 产业链详情
 * @param {*} params 
 * @returns 
 */
export function indetailAPI(params){
    return request({
        url: apiUrl+"/admin/industryChain/detail",
        method: "GET",
        params
    })
}
/**
 * 初始化产业链节点xy坐标值
 * @param {*} params 
 * @returns 
 */
export function initNodeXYValueAPI(params){
    return request({
        url: apiUrl+"/admin/industryChain/initNodeXYValue",
        method: "GET",
        params
    })
}

//产业链类型列表
export function industryTypeListAPI(){
    return request({
        url: apiUrl+"/admin/industryChain/categoryList",
        method:"GET"
    })
}