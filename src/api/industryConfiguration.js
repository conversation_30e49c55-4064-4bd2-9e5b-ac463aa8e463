import request from '@/utils/request'
import {apiUrl} from '@/api/user'
/**
 * 新增机构产业链关系
 * @param {*} data 
 * @returns 
 */
export function addAPI(data) {
    return request({
        url:apiUrl+"/admin/orgIndustryChainRelation/save",
        method:'POST',
        data   
    })
}
/**
 * 机构产业链配置列表
 * @param {*} data 
 * @returns 
 */
export function configurationListAPI(data) {
    return request({
        url:apiUrl+"/admin/orgIndustryChainRelation/page",
        method:'POST',
        data
    })
}
/**
 * 获取所有入驻机构
 * @param {*} data 
 * @returns 
 */
export function getAllinstitutionAPI(params) {
    return request({
        url:apiUrl+"/admin/institution/getAll",
        method:'GET',
        params
    })
}
/**
 * 获取所有产业链
 * @param {*} data 
 * @returns 
 */
export function getindustryAllAPI(params) {
    return request({
        url:apiUrl+"/admin/industryChain/getAll",
        method:'GET',
        params
    })
}

/**
 * 删除机构产业链关联关系
 * @param {*} data 
 * @returns 
 */
export function deleteInAPI(params) {
    return request({
        url:apiUrl+"/admin/orgIndustryChainRelation/delete",
        method:'GET',
        params
    })
}
/**
 * 更新产业链关联状态
 * @param {*} data 
 * @returns 
 */
export function updateStatusAPI(params) {
    return request({
        url:apiUrl+"/admin/orgIndustryChainRelation/updateStatus",
        method:'GET',
        params
    })
}