import request from '@/utils/request'
import {apiUrl} from '@/api/user'
/**
 * 获取企业数据列表
 * @returns 
 */
export function enterpriseListAPI(data) {
    return request({
        url:apiUrl+'/admin/enterprise/page',
        method: 'POST',
        data
    })
}
/**
 * 获取企业详情
 * @returns 
 */
 export function ParticularsAPI(data) {
    return request({
        url:apiUrl+'/admin/enterprise/detail',
        method: 'POST',
        data
    })
}
/**
 * 获取企业标签列表
 * @returns 
 */
 export function enterpriseTagListAPI(params) {
    return request({
        url:apiUrl+'/admin/enterprise/label/list',
        method: 'GET',
        params
    })
}
/**
 * 获取所有标签分类
 * @returns 
 */
export function tagClassifyAPI(params) {
    return request({
        url:apiUrl+'/admin/labelType/management/list',
        method: 'GET',
        params
    })
}
/**
 * 企业excel导入
 * @returns 
 */
export function enterpriseExeclAPI(file) {
    let formData=new FormData();
    formData.append('file',file)
    return request({
        url:apiUrl+'/admin/enterprise/upload',
        method: 'POST',
        data: formData,
        timeout: 9000000
    })
}
/**
 * 产业链标签excel导入
 * @returns 
 */
export function enterpriseTagExeclAPI(file) {
    let formData=new FormData();
    formData.append('file',file)
    return request({
        url:apiUrl+'/admin/industryLabel/upload',
        method: 'POST',
        data: formData,
        timeout: 9000000
    })
}
/**
 * 导入excel绑定企业和产业链标签的绑定关系
 * @returns 
 */
export function BindingRelationAPI(data) {
    let formData=new FormData();
    formData.append('file',data.file)
    formData.append('batchNumber',data.batchNumber)
    return request({
        url:apiUrl+'/admin/industryLabel/bind/enterprise/excel',
        method: 'POST',
        data: formData,
        timeout: 9000000
    })
}
/**
 * 批量导入绑定企业标签和企业关联关系
 * @returns 
 */
export function BindingAPI(data) {
    let formData=new FormData();
    formData.append('file',data.file)
    formData.append('batchNumber',data.batchNumber)
    formData.append('labelTypeId',data.labelTypeId)
    formData.append('labelId',data.labelId)
    return request({
        url:apiUrl+'/admin/enterprise/label/upload/bind',
        method: 'POST',
        data: formData,
        timeout: 9000000
    })
}
/**
 * excel导入批量更新三方企业信息表的确认状态
 * @returns 
 */
export function affirmstateAPI(file) {
    let formData=new FormData();
    formData.append('file',file)
    return request({
        url:apiUrl+'/admin/third/api/enterprise/upload',
        method: 'POST',
        data: formData,
        timeout: 9000000
    })
}
/**
 * excel批量导入上市企业
 * @returns 
 */
export function mainCompanyAPI(file) {
    let formData=new FormData();
    formData.append('file',file)
    return request({
        url:apiUrl+'/admin/listed/company/upload',
        method: 'POST',
        data: formData,
        timeout: 9000000
    })
}
/**
 * 新增企业标签
 * @returns 
 */
export function addTagAPI(data) {
    return request({
        url:apiUrl+'/admin/enterprise/label/add',
        method: 'POST',
        data
    })
}
/**
 * 企业标签绑定
 * @returns 
 */
 export function bindingTagAPI(data) {
    return request({
        url:apiUrl+'/admin/enterprise/label/bind',
        method: 'POST',
        data
    })
}
/**
 * 企业标签解绑
 * @returns 
 */
 export function unbindTagAPI(data) {
    return request({
        url:apiUrl+'/admin/enterprise/label/unBind',
        method: 'POST',
        data
    })
}

/**
 * 更改企业评分
 * @returns 
 */
 export function updategradeAPI(data) {
    return request({
        url:apiUrl+'/admin/enterprise/update',
        method: 'POST',
        data
    })
}
/**
 * 企业名称搜索
 * @returns 
 */
export function tagAutoSearchAPI(data) {
    return request({
        url:apiUrl+'/admin/enterprise/label/autoSearch',
        method: 'POST',
        data
    })
}
/**
 * 获取国民经济分类树结构
 * @returns 
 */
export function classificationTreeAPI(params) {
    return request({
        url:apiUrl+'/admin/industry/classification/tree',
        method: 'GET',
        params
    })
}



