import request from '@/utils/request'
import {apiUrl} from '@/api/user'
/**
 * 新增入驻机构
 * @param {*} data 
 * @returns 
 */
export function addSettledAPI(data) {
    return request({
        url:apiUrl+"/admin/institution/insert",
        method:'POST',
        data   
    })
}
/**
 * 入驻机构列表
 * @param {*} data 
 * @returns 
 */
export function SettledListAPI(data) {
    return request({
        url:apiUrl+"/admin/institution/page",
        method:'POST',
        data
    })
}
/**
 * 联想词
 * @param {*} data 
 * @returns 
 */
export function autoSearchAPI(data) {
    return request({
        url:apiUrl+"/admin/institution/autoSearch",
        method:'POST',
        data
    })
}
/**
 * 主账号角色获取
 * @returns 
 */
export function roleAPI() {
    return request({
        //url:apiUrl+"/admin/role/user/roles",
        url:apiUrl+"/admin/role/roles",
        method:'POST',
    })
}
/**
 * 删除入驻机构
 * @returns 
 */
export function removeAPI(data) {
    return request({
        url:"/admin/institution/delete",
        method:'POST',
        data
    })
}
/**
 * 编辑机构
 * @returns 
 */
export function institutionupdateAPI(data) {
    return request({
        url:"/admin/institution/update",
        method:'POST',
        data
    })
}
/**
 * 获取入驻机构详细信息
 * @returns 
 */
export function detailAPI(params) {
    return request({
        url:"/admin/institution/detail",
        method:'GET',
        params
    })
}