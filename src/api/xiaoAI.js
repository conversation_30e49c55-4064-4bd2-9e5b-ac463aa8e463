import request from '@/utils/request'
import {apiUrl} from '@/api/user'
/**
 * 委托列表
 * @param {*} data
 * @returns
 */
export function billingListAPI(params) {
    return request({
        url:apiUrl+"/ai/entrust/client/mine/billing",
        method:'GET',
        params,
    })
}
/**
 * 需求列表
 * @param {*} params 
 * @returns 
 */
export function demandListAPI(params) {
    return request({
        url:apiUrl+"/ai/entrust/client/demand/list",
        method:'GET',
        params,
    })
}
/**
 * 撤销需求
 * @param {*} data 
 * @returns 
 */
export function demandCancelAPI(data) {
    return request({
        url:apiUrl+"/ai/entrust/client/demand/cancel",
        method:'post',
        data,
    })
}
/**
 * 枚举类型 企业类型 融资情况
 * @param {*} params 
 * @returns 
 */
export function enumListAPI(params) {
    return request({
        url:apiUrl+"/ai/entrust/client/enumList",
        method:'GET',
        params,
    })
}
/**
 * 产业链
 * @param {*} params 
 * @returns 
 */
export function industrychainListAPI(params) {
    return request({
        url:apiUrl+"/ai/industryChain/chainTreeList",
        method:'GET',
        params,
    })
}
/**
 * 委托人新建需求
 * @param {*} data 
 * @returns 
 */
export function demandSubmitAPI(data) {
    return request({
        url:apiUrl+"/ai/entrust/client/demand/submit",
        method:'post',
        data,
    })
}
/**
 * 委托人查看需求的反馈列表
 * @param {*} data 
 * @returns 
 */
export function recommendlistAPI(params) {
    return request({
        url:apiUrl+"/ai/entrust/client/recommend/list",
        method:'get',
        params,
    })
}
/**
 * 删除反馈
 * @param {*} data 
 * @returns 
 */
export function recommenddeleteAPI(data) {
    return request({
        url:apiUrl+"/ai/entrust/client/recommend/delete",
        method:'post',
        data,
    })
}
/**
 * 查询企业类型价格
 * @param {*} data 
 * @returns 
 */
export function getEnterpriseLabelAPI(params) {
    return request({
        url:apiUrl+"/ai/entrust/client/getEnterpriseLabel",
        method:'get',
        params,
    })
}
/**
 * 新建委托单
 * @param {*} data 
 * @returns 
 */
export function addSubmit(data) {
    return request({
        url:apiUrl+"/ai/entrust/client/action/submit",
        method:'post',
        data,
    })
}
/**
 * 购买
 * @param {*} data 
 * @returns 
 */
export function billingSubmit(data) {
    return request({
        url:apiUrl+"/ai/payment/billing/submit",
        method:'post',
        data,
    })
}
/**
 * 轮询
 * @param {*} data 
 * @returns 
 */
export function paymentgetBillingStatusAPI(params) {
    return request({
        url:apiUrl+"/ai/payment/getBillingStatus",
        method:'get',
        params,
    })
}
/**
 * 退单
 * @param {*} data 
 * @returns 
 */
export function closeAPI(data) {
    return request({
        url:apiUrl+"/ai/entrust/client/close",
        method:'post',
        data,
    })
}
/**
 * 催办
 * @param {*} data 
 * @returns 
 */
export function reminderAPI(data) {
    return request({
        url:apiUrl+"/ai/entrust/client/reminder",
        method:'post',
        data,
    })
}
/**
 * 评论
 * @param {*} data 
 * @returns 
 */
export function commentAPI(data) {
    return request({
        url:apiUrl+"/ai/entrust/client/comment",
        method:'post',
        data,
    })
}
/**
 * 评论
 * @param {*} data 
 * @returns 
 */
export function clientReviewtAPI(data) {
    return request({
        url:apiUrl+"/ai/entrust/client/clientReview",
        method:'post',
        data,
    })
}
/**
 * 委托人委托单详情
 * @param {*} data
 * @returns
 */
export function closeDelAPI(params) {
    return request({
        url:apiUrl+"/ai/entrust/client/detail",
        method:'get',
        params,
    })
}
/**
 * 申请机构支付
 * @param {*} data 
 * @returns 
 */
export function paymentApply(data) {
    return request({
        url:apiUrl+"/ai/entrust/client/payment/apply",
        method:'post',
        data,
    })
}
/**
 * 委托人委托单详情
 * @param {*} data
 * @returns
 */
export function paymentApplyListAPI(params) {
    return request({
        url:apiUrl+"/ai/entrust/client/payment/apply/list",
        method:'get',
        params,
    })
}
/**
 * 申请详情
 * @param {*} data
 * @returns
 */
export function paymentdetailAPI(params) {
    return request({
        url:apiUrl+"/ai/entrust/client/payment/apply/detail",
        method:'get',
        params,
    })
}
/**
 * 获取机构列表
 * @param {*} data
 * @returns
 */
export function ownList(params) {
    return request({
        url:apiUrl+"/sso/admin/institution/ownList",
        method:'get',
        params,
    })
}
/**
 * 获取按钮权限
 * @param {*} data
 * @returns
 */
export function queryResourceByType(params) {
    return request({
        url:apiUrl+"/sso/admin/roleResource/queryResourceByType",
        method:'get',
        params,
    })
}