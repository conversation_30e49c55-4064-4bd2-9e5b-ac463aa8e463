import request from '@/utils/request'
/**
 * 获取账号基本信息
 * @param {*} data 
 * @returns 
 */
export function getAccountInfoAPI() {
  return request({
    url: '/sso/admin/user/getAccountInfo',
    method: 'POST',
  })
}
/**
 * 账号设置-查询
 * @param {*} data 
 * @returns 
 */
export function accountPageAPI(data) {
  return request({
    url: '/sso/admin/user/pageAccount',
    method: 'POST',
    data
  })
}
/**
 * 账号设置-设置密码
 * @param {*} data 
 * @returns 
 */
export function setPasswordAPI(data) {
  return request({
    url: '/sso/admin/user/setPassword',
    method: 'POST',
    data
  })
}
/**
 * 账号设置-编辑账号基本信息
 * @param {*} data 
 * @returns 
 */
export function editAccountInfoAPI(data) {
  return request({
    url: '/sso/admin/user/editAccountInfo',
    method: 'POST',
    data
  })
}
/**
 * 账号设置-获取用户已绑定权限
 * @param {*} data 
 * @returns 
 */
export function listUserBindAclAPI(data) {
  return request({
    url: '/sso/admin/role/acl/listUserBindAcl',
    method: 'POST',
    data
  })
}
/**
 * 账号设置-获取用户已绑定权限
 * @param {*} data 
 * @returns 
 */
export function querySubAccountAPI(params) {
  return request({
    url: '/sso/admin/user/querySubAccount',
    method: 'GET',
    params
  })
}
/**
 * 账号设置-获取用户已绑定权限2配置
 * @param {*} data 
 * @returns 
 */
export function listAccountSetAPI(params) {
  return request({
    url: '/sso/admin/user/listAccountSet',
    method: 'GET',
    params
  })
}
/**
 * 账号设置-子账号设置
 * @param {*} data 
 * @returns 
 */
export function accountSetAPI(data) {
  return request({
    url: '/sso/admin/user/accountSet',
    method: 'POST',
    data
  })
}
/**
 * 账号设置-设置账号产业链
 * @param {*} data 
 * @returns 
 */
export function setAccountIndustryChainAPI(data) {
  return request({
    url: '/sso/admin/user/setAccountIndustryChain',
    method: 'POST',
    data
  })
}
/**
 * 账号设置-删除账号产业链
 * @param {*} data 
 * @returns 
 */
export function deleteAccountIndustryChainAPI(params) {
  return request({
    url: '/sso/admin/user/deleteAccountIndustryChain',
    method: 'GET',
    params
  })
}
/**
 * 账号设置-使用设置回显
 * @param {*} data 
 * @returns 
 */
export function queryAccountSetAPI(params) {
  return request({
    url: '/sso/admin/user/queryAccountSet',
    method: 'GET',
    params
  })
}
/**
 * 账号设置-重置密码
 * @param {*} data 
 * @returns 
 */
export function resetPasswordAPI(params) {
  return request({
    url: '/sso/admin/user/resetPassword',
    method: 'GET',
    params
  })
}
/**
 * 账号设置-查询用户已绑定的角色列表
 * @param {*} data 
 * @returns 
 */
export function listRoleByUserIdAPI(params) {
  return request({
    url: '/sso/admin/user/role/listRoleByUserId',
    method: 'GET',
    params
  })
}
/**
 * 角色配置-角色设置
 * @param {*} data 
 * @returns 
 */
export function listRoleByUserIdForRoleSetAPI(params) {
  return request({
    url: '/sso/admin/user/role/listRoleByUserIdForRoleSet',
    method: 'GET',
    params
  })
}
/**
 * 角色配置-查询树
 * @param {*} data 
 * @returns 
 */
export function roleAclTreeForRoleSetAPI(data) {
  return request({
    url: '/sso/admin/role/acl/roleAclTreeForRoleSet',
    method: 'POST',
    data
  })
}
/**
 * 角色配置-获取机构产业链
 * @param {*} data 
 * @returns 
 */
export function listIndustryChainAPI(params) {
  return request({
    url: '/sso/admin/user/listIndustryChain',
    method: 'GET',
    params
  })
}
/**
 * 账号设置-获取用户配置的产业链
 * @param {*} data 
 * @returns 
 */
export function getAccountIndustryChainAPI(params) {
  return request({
    url: '/sso/admin/user/getAccountIndustryChain',
    method: 'GET',
    params
  })
}
/**
 * 号配置-角色设置-编辑用户角色
 * @param {*} data 
 * @returns 
 */
export function editUserRoleAPI(data) {
  return request({
    url: '/sso/admin/user/editUserRole',
    method: 'POST',
    data
  })
}

export function workerParentAPI(params) {
  return request({
    url: '/ai/invitationRecord/worker/parent',
    method: 'GET',
    params
  })
}