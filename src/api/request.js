import axios from 'axios'
import { MessageBox } from 'element-ui'
import { message } from "@/utils/MessageUtil";
import { getToken, getOrg } from "@/utils/auth"; // get token from cookie
import { getPathId } from '@/utils/utils'
import router from "@/router/index";

import store from '@/store'
// import {HEADER_TOKEN_KEY} from "@/api/apiConfig";
let JSONbigString = require('json-bigint')({ storeAsString: true });
const baseURL = process.env.VUE_APP_PORT_URL

// create an axios instance
const service = axios.create({
  baseURL: baseURL, // url = base url + request url process.env.VUE_APP_BASE_AP
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 10000, // request timeout
  // 自定义后端返回的原始数据
  // data: 后端返回的原始数据，说白了就是 JSON 格式的字符串
  transformResponse: [function (data) {
    try {
      // axios 默认会在内部这样来处理后端返回的数据
      // return JSON.parse(data)
      return JSONbigString.parse(data)
    } catch (err) {
      return data
    }
  }]
})

// request interceptor
service.interceptors.request.use(
  config => {
    config.headers['appId'] = 12;
    let orgCode = localStorage.getItem('orgCode')
    let selectedOrgCode = sessionStorage.getItem('selectedOrg')
    if (!!orgCode && selectedOrgCode && orgCode !== selectedOrgCode) {
      sessionStorage.setItem('selectedOrg', orgCode);
      window.location.reload()
      // window.$wujie?.bus.$emit('reload')
    }
    // else{
    config.headers['Content-Type'] = 'application/json'
    config.headers['token'] = getToken()
    config.headers['OrgCode'] = getOrg()
    // }

    // JSON.parse(localStorage.getItem('TOKEN'))?.value
    // ||store.getters.user.token
    // config.headers[HEADER_TOKEN_KEY] = localStorage.getItem('TOKEN')
    return config
  },
  error => {
    return Promise.reject(error)
  }
)


//在main.js设置全局的请求次数，请求的间隙
//如果0.19.0 config.retry = config.headers.retry; config.retryDelay = 1000;
axios.defaults.retry = 3;
axios.defaults.retryDelay = 1000;

//请求超时重试拦截方法
axios.interceptors.response.use(undefined, function axiosRetryInterceptor(err) {
  let config = err.config;
  // If config does not exist or the retry option is not set, reject
  if (!config || !config.retry) return Promise.reject(err);

  // Set the variable for keeping track of the retry count
  config.__retryCount = config.__retryCount || 0;

  // Check if we've maxed out the total number of retries
  if (config.__retryCount >= config.retry) {
    // Reject with the error
    return Promise.reject(err);
  }

  // Increase the retry count
  config.__retryCount += 1;

  // Create new promise to handle exponential backoff
  var backoff = new Promise(function (resolve) {
    setTimeout(function () {
      resolve();
    }, config.retryDelay || 1);
  });

  // Return the promise in which recalls axios to retry the request
  return backoff.then(function () {
    return axios(config);
  });
});


service.interceptors.response.use(
  response => {
    const res = response.data;
    if (res.code == '0') {
      return res
      // 其他非 0 状态如何处理？
    } else {
      message({
        showClose: true, // 可关闭
        message: res.msg || '请稍后重试……',
        type: 'error',
        duration: 5 * 1000,
        customClass: 'mzindex'
      })
      if (res.code == "401" || res.code == '403') {
        // let route=  ['/IndustryGraph','/MapEcharts','/dashboard','/dashboard']
        let redirectUrl = window.location.href.split("#")?.[1]?.split("?")?.[0]
        let redirectId = getPathId()
        localStorage.setItem('redirectId', redirectId)
        store.dispatch('user/logout')
        router.push("/login");
      } else if (res.code == "81011003") {
        this.$router.push('/403')
        // window.$wujie?.bus.$emit('newRouter', { path: '/403' });
        // 无操作权限
      }
      return Promise.reject(res)
    }
  },
  error => {
    // console.log('err' + error) // for debug
    return Promise.reject(error)
  }
)

export default service
