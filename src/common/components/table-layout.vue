<!--列表-->
<template>
  <!-- <div @click="getHeight">aaa</div> -->
  <div class="ms-doc">
    <!-- <div
      style="height: 20px
    ;"
    /> -->
    <!--     <div class="ms-tab">
      <span
        v-for="(title, index) in tabNameList"
        :key="index"
        :class="['title', index+1 == selTabIndex ? 'active' :'' ]"
        @click="seltabway(index+1)"
      >
        {{ title }} 
        <breadcrumb
          id="breadcrumb-container"
          class="breadcrumb-container"
        />
        <em :style="{borderLeft:(index+1 == selTabIndex ? `${$store.state.settings.theme} 4px solid`:'none')}" />
        <i
          v-if="isShowPageHelp"
          class="el-icon-question"
          @click="onClickHelp(index)"
        />
      </span>
      <div
        ref="msSelect"
        class="ms-tab-btn"
      >
        <slot name="selBtn" />
        <div
          v-show="showArrow"
          class="params-show-btn"
        >
          <i
            class="el-icon-caret-top"
            :style="{transform:isShowParams?'rotate(180deg)':'rotate(0deg)'}"
            @click="isShowParams = !isShowParams; $emit('showParamStu',isShowParams)"
          />
        </div>
      </div>
    </div> -->
      
    <!--  查询条件 -->
    <div
      class="vue_search ms-title"
      :style="{height: isShowParams?'auto':'0px'}"
      @click="$emit('query')"
    >
      <slot name="elForm" />
    </div>
    <slot name="selected" />
    <slot name="selTable" /> 
    <!-- <table-pagination
      v-if="pageNo"
      :page-no="pageNo"
      :page-size="pageSize"
      :total-count="totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    /> -->
  </div>
</template>
 
<script>
/* import Breadcrumb from '@/components/Breadcrumb'
import TablePagination from './table-pagination' */
/**
 * @property pageNo 当前tab索引，默认为 1
 * @property pageSize 当前tab名称数组
 * @property totalCount 是否显示查询右边向下箭头，默认 true 显示
 */
export default {
  name: 'TableLayout',
  components: {
/*     TablePagination,
    Breadcrumb */
  },
  props:{
    pageNo:{
      type:Number,
      default:0
    },
    pageSize:{
      type:Number,
      default:10
    },
    totalCount:{
      type:Number,
      default:0
    },

    selTabIndex:{
      type:Number,
      default:1
    },
    tabNameList:{
      type:Array,
      default:()=>[]
    },
    //是否显示查询向下箭头
    showArrow:{
      type:Boolean,
      default:true
    },
    //页面帮助显示
    isShowPageHelp:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      

      isShowParams:true,
      border:{}
    }
  },
  computed: {
 
  },
  mounted() {
 
  },
  methods: {
    seltabway(index){
      this.$emit('seltabway',index)
    },
    getHeight(){
      this.$emit('showParamStu')
      localStorage.setItem('selectHeight',document.getElementById('app').clientHeight - this.$refs.msSelect.offsetHeight - 600 );
    },
    // 分页大小
    handleSizeChange(val) {
      this.$emit('size-change',val)
    },
    // 分页行数
    handleCurrentChange(val) {
      this.$emit('current-change',val)
    },
    //页面帮助说明事件
    onClickHelp(index){
      this.$emit("onPageHelpClick",index)
    }
  }
}
</script>
<style lang="scss" scoped>
// .ms-doc{padding-bottom: 100px;}
  .vue_search{   padding-top: 0px !important; }
// .vue_search .demo-form-inline{ margin: 20px 0px 10px 0px !important; overflow: hidden; }
</style>