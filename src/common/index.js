import { dateFormat, commaNum, wordMoney } from '@/utils'

import  cloneDeep from 'lodash/cloneDeep';

import store from '@/store'
import { getToken } from "@/utils/auth"; // get token from cookie

export const switchFilter = store.getters.switchFilter

// 下拉默认项
export const defOptionFn = (defaultVal, label = '全部') => ([
  {
    value: typeof defaultVal !== 'undefined' ? defaultVal : '',
    label
  }
])
// 创建日期转换表头项
export const dateFormatter = (format = '') => ({
  formatter: (row, column, cellValue, index) => dateFormat(cellValue, format)
})
// 验证必填项
export function createFiRulesProps (label, required, rules = [], others, trigger = 'blur') {
  return {
    label,
    rules: (required ? [{ required: true, message: typeof required === 'string' ? required : '请输入' + label, trigger }] : []).concat(rules),
    ...others
  }
}


export function createOriginCascaderFormItem (label, prop, attributes, props, events, extraProps = {}) {
  return {
    props: typeof label === 'object' ? label : {
      label
    },
    fields: {
      component: 'ElCascader',
      props: { ...attributes, props },
      events
    },
    prop,
    ...extraProps
  }
}

export function createInputFormItem (label, placeholder, prop, extraProps = {}, slotText, events) {
  const placeholderLabel = typeof label === 'object' ? label.label : label
  const slotComp = typeof slotText === 'string' ? { slotComp: { component: 'div', text: slotText } } : slotText
  return {
    props: typeof label === 'object' ? label : {
      label
    },
    fields: {
      component: 'ElInput',
      props: typeof placeholder === 'object' ? placeholder : {
        placeholder: placeholder || ('请输入' + placeholderLabel)
      },
      ...slotComp,
      events
    },
    prop,
    ...extraProps
  }
}

export function createRadiosFormItem (label, prop, data, fieldExtra = {}, extraProps = {}) {
  return {
    props: typeof label === 'object' ? label : {
      label
    },
    fields: {
      component: 'ElRadioGroup',
      data,
      ...fieldExtra
    },
    prop,
    ...extraProps
  }
}

export function createCkeckBoxFormItem (label, required, prop, data, fieldExtra = {}, extraProps = {}) {
  return {
    props: {
      label,
      ...(required ? { rules: [{ type: 'array', required: true, message: `请选择${label}`, trigger: 'blur' }] } : {})
    },
    prop,
    fields: {
      component: 'ElCheckboxGroup',
      data,
      ...fieldExtra
    },
    ...extraProps
  }
}

export function createSelectFormItem (opts = {}) {
  const { label, prop, modelDef, data, allDef, others, placeholder, events } = Object.assign({
    others: {},
    events: {}
  }, opts)
  return {
    props: typeof label === 'object' ? label : { label },
    fields: {
      component: 'ElSelect',
      data: (allDef !== false ? defOptionFn(allDef) : []).concat(Array.isArray(data) ? data : []),
      props: typeof placeholder === 'object' ? placeholder : {
        placeholder: placeholder
      },
      events,
      ...others,
      modelDef
    },
    modelDef,
    prop,
    ...others
  }
}
const dateTypeProps = () => ({
  date: {
    type: 'date',
    valueFormat: 'timestamp'
  },
  daterange: {
    type: 'daterange',
    valueFormat: 'timestamp',
    defaultTime: ['00:00:00', '23:59:59'],
    startPlaceholder: '开始日期',
    endPlaceholder: '结束日期'
  }
})
export function createDateFormItem (label, type, prop, others = {}, events) {
  const typeProps = dateTypeProps()
  return {
    props: typeof label === 'object' ? label : {
      label
    },
    fields: {
      component: 'ElDatePicker',
      props: typeof type === 'object' ? type : typeProps[type] || {
        type
      },
      events
    },
    prop,
    ...others
  }
}


export function createTbItem (label, prop, others) {
  return {
    label,
    prop,
    ...others
  }
}


export function checkoutTableColumns (keys, originData) {
  const tbColumns = originData || tbColumnsFn()
  if (Array.isArray(keys)) {
    return keys.map(item => (tbColumns[item]))
  } else if (keys && typeof keys === 'object') {
    return Object.keys(keys).map(item => (Object.assign({}, tbColumns[item] || {}, keys[item] || {})))
  }
  return Object.values(tbColumns)
}

export function checkoutFormItems (keys, originData) {
  const formItems = originData || formItemsFn()
  if (Array.isArray(keys)) {
    return keys.map(item => (formItems[item]))
  } else if (keys && typeof keys === 'object') {
    return Object.keys(keys).map(item => (Object.assign({}, formItems[item] || {}, keys[item] || {})))
  }
  return Object.values(formItems)
}

// 数据导出参数
export function filterExportParams (params) {
  if (!params) return params
  let filterParams = cloneDeep(params)
  filterParams.size && (delete filterParams.size)
  filterParams.page && (delete filterParams.page)
  filterParams.Authorization =getToken()
  //  store.getters.token||JSON.parse(localStorage.getItem('TOKEN'))?.value
  return filterParams
}

export function formTimeSwtich (time, keys = ['staTime', 'endTime']) {
  let result = {}
  if (Array.isArray(keys) && keys.length === 2) {
    time && time[0] && (result[keys[0]] = Math.round((+time[0]) / 1000))
    time && time[1] && (result[keys[1]] = Math.round((+time[1]) / 1000))
  }
  return result
}
// 将1个字段分割为2个字段
export function formDataTwo (param, keys = ['minData', 'maxData']) {
  if (!param) {return}
  let result = {}
  let datas = param.split('-');
  if (Array.isArray(keys) && keys.length === 2) {
    datas[0] && (result[keys[0]] = datas[0])
    datas[1] && (result[keys[1]] = datas[1])
  }
  return result
}


// 导出表单页面通用配置数据
export function commonFormProps (isInline, props = {}) {
  return Object.assign({ labelPosition: 'left' }, isInline ? {
    class: 'common-form-inline',
    inline: true,
    labelWidth: '100px'
  } : {
    class: 'common-form',
    labelWidth: '120px'
  }, props || {})
}
// 创建ModulePane组件用模块信息
export const createModuleInfo = (title, key, component, props, others) => ({
  title,
  key,
  component,
  props,
  ...others
})
