/**
 * edit界面
 * @property {editId String|Number 弹框信息id新增=-1 关闭=0，编辑=其他}
 */

import commonMixin from './commonMixin'
  import {detailApi,addApi} from '@/api/remote-search';
  import { getPathId } from '@/utils/utils'

  export default {
    mixins: [commonMixin],
    data() {
      return {
        editInfoLoading:false,
        isBlg:true,
        actionStu:true,
        isEdit:false,
        loading:false, // 信息提交状态
        infoHeight:(document.getElementById("app").clientHeight-document.getElementById("app").clientHeight*0.1-200),
        //是否继续执行获取详情内容：当前界面包含详情编辑及嵌套有新增/编辑Dialog时使用
        isContinueStep:true
      };
    },
    props:{
      /* 弹框信息id新增=-1 关闭=0，编辑=其他 */
      editId:{
        type:String|Number,
        default:'0'
      },
      titleShow: {
        type: Boolean,
        default: true
      },
      showRow:{
        type:Number,
        default:1
      },
    },
    created(){
      //  ( this.editId == '-1' || this.editId == '0' )  && this.resetForm('ruleForm');
      this.initWay();
    },
    mounted(){ 
      if(this.editId != '-1' && this.editId != '0'){
        this.getDetailWay(this.editId) // 弹框页面 - 数据id
        this.isEdit = true
      }else{
        this.isEdit = false;
      }
      //FIXME：是否继续执行获取详情内容：当前界面包含详情编辑及嵌套有新增/编辑Dialog时使用
    let queryId= this.$route.query.id|| getPathId()|| null;

      if(this.$route.query.type != 'add' &&queryId&& this.isContinueStep){
        this.getDetailWay(queryId) // 页面跳转 - 数据id
        this.isEdit = true;
      }
    }, 
    watch:{
      // 弹框时监测数据id 进行获取信息
      'editId'(val){
        this.beforeEditId(val);
        if(this.editId != '-1' && this.editId != '0'){
          this.getDetailWay(val)
          this.isEdit = true
        }else{
          this.isEdit = false
        }
        val == '0' || val == '-1' && this.firstReset('ruleForm')
      }
    },
    methods: {
      initWay(){},
      beforeEditId(val){},

      /**
       *  提交表单
       * @param formName 当前form表单名称
       * @param isColse 是否关闭当前界面，默认true 关闭
       * @returns {Promise<unknown>}
       */
      submitForm(formName,isColse=true) {
        return new Promise((resolve, reject) => {
          this.$refs[formName].validate((valid,obj) => {
            if (valid) {
              this.loading = true;
              let url = this.apiList.dataAdd;
              this.isEdit && (url = this.apiList.dataEdit);
              let editOperation = this.apiList.dataAddOperation;
              let params = this.setParams(this.ruleForm);
              this.isEdit && (editOperation = this.apiList.dataEditOperation);
              this.editWay(url,editOperation,params,formName,isColse).then((res)=> {
                resolve(res)
              }).catch((error) =>{
                reject(error);
              })
            } else {
              this.altopen('请把信息填写完整！','warning');
              reject("请把信息填写完整");
              return false;
            }
          });
        })
      },

      setParams(ruleForm){
        return ruleForm;
      },
      // 编辑向后台请求方法
      editWay(url,editOperation,params,formName = 'formName', isClose = true){
        return new Promise((resolve, reject) => {
          addApi( url, params, editOperation ).then((res) => {
            this.loading = false;
            if(res.code == "0"){
              this.$message({ showClose: true, message: '操作成功', type: 'success'});
              if(this.editId == '0'){
                // 关闭当前页面， 进行跳转列表页面
                if(isClose){
                 this.closePageWay();
                }
              }else{
                this.formClose(formName)
              }
            }
            resolve(res)
          }).catch((error) => {
            reject(error);
            this.loading = false;
          })
        })
      },
      // 关闭当前页面方法
      closePageWay(){
        let nowPage = localStorage.getItem('nowPage');
        let view = { fullPath:nowPage.code, title:nowPage.name}
        this.$store.dispatch('tagsView/delView',view).then(({ visitedViews }) => {
          this.$router.push({path:this.pathList.listPath,query:{isGet:true}})
        })
      },

      /**
       * 处理明细方法钩子
       * @param data
       */
      handleDetail(data){
        //empty method
      },

      // 获取数据
      getDetailWay(id){
        //如果getDetailOperation不为空时，则请求详情数据
        return new Promise((resolve, reject) => {
          if(this.apiList  && this.apiList.getDetailOperation){
            var data = {id:id};
            this.editInfoLoading = true;
            detailApi(this.apiList.getDetail,data,this.apiList.getDetailOperation).then((res) => {
              this.ruleForm = res.data;
              this.handleDetail(this.ruleForm)
              this.editInfoLoading = false;
              resolve(res);
            }).catch((error) => {
              reject(error)
              this.editInfoLoading = false;
              this.$message({ showClose: true, message: '获取失败', type: 'error'});
            })
          }else{
            //再不传getDetailOperation时，视为不需要请求详情数据
            //this.$message({ type: 'info', message: '请完善请求参数' })
          }
        })
      },
      firstReset(formName){
        this.resetForm(formName)
      },
      // 重置
      resetForm(formName) {
        this.loading = false;
        if(this.editId != '-1' && this.editId != '0'){
          this.getDetailWay(this.editId) // 弹框页面 - 数据id 
          return
        }
    let queryId= this.$route.query.id|| getPathId()|| null;

        if(queryId){
          this.getDetailWay(queryId) // 页面跳转 - 数据id
          return
        }
        this.$refs[formName].resetFields();
      },
      // 弹框页面关闭按钮
      formClose(formName){
        //this.resetForm(formName)
        this.$refs[formName].resetFields();
        this.$emit('closeDialog');
      },
      // 关闭当前页面 - 跳转到列表
      returnList(){
        let nowPage = JSON.parse(localStorage.getItem('nowPage')) || {code:'',name:''};
        let view = { path:this.$route.path, title:nowPage.name}
        this.pathList.listPath && this.$store.dispatch('tagsView/delView',view).then(({ visitedViews }) => {
          this.$router.push({path:this.pathList.listPath,query:{isGet:true}})
        })
      },
      // 公共弹框
      altopen(msg,type) {
        this.$notify({ type:type,  message: msg,offset: 100,});
      },
    },
  }
