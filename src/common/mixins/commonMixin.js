
import {getEnumAndDictApi, excelDownloadApi} from '@/api/remote-search' //   接口js引用
export default {
  components: {},
  data() {
    return {
      // 导出参数
      excelDownloadParams:{
        dataType:'', // 后台提供
        exportColumn:'', // 导出列   id
        filterCondition:{} // 过滤数据
      },
      // 枚举值
      enumAndDictList:{
        dataDict:[],
        enum:[]
      },      
      excelLoading:false, //  导出excel
      nowPage: JSON.parse(localStorage.getItem('nowPage')) || undefined, // 当前页面数据
      operationList: JSON.parse(localStorage.getItem('operationList')) || [], // 当前页面按钮权限数据
      operationCode: localStorage.getItem('operationCode') || '' // 当前页面的按钮权限
    }
  },
  created(){
  },
  computed: {},
  methods: {
      // 弹框内容 msg - 提示内容   params - 数据参数   explain - 提示副内容  url - 操作数据后台地址  isGetTableList - 是否进行获取列表数据
      confirmWay( msg, params,explain,url, isGetTableList = true) {
        explain = explain || ''
        this.$confirm('<span style=" color:#333; margin-top:0px;font-size:16px">'+msg+'</span><p style="margin:0px;font-size:13px;color:#999">'+explain+'</p>', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
        }).then(() => {
          this.actionWay(params,url,isGetTableList);
        }).catch(() => {
          this.$message({ type: 'info', message: '已取消操作' })
        })
      },

      // 操作方法
      actionWay(params,url,isGetTableList) {
        deleteApi(url, params).then((res) => {
          this.$message({ type: 'success', message: '操作成功!' })
          isGetTableList && this.getListWay()
        })
      },

      // 同时获取枚举和字典
      getEnumAndDictWay( enumAndDictParams){
        // 枚举   字典 混合
        let operationList = ['8898.all.parkcloud.common.enum.name2DescMMap.get','8898.all.parkcloud.common.datadict.code2SimpleMMap.get','8898.all.parkcloud.common.mix.enumAndDict.get'];
        let idx = 2;
        if(enumAndDictParams.enumNameList.length <= 0 && enumAndDictParams.dataDictNameList.length > 0){ // 字典
          idx = 1
        }else if(enumAndDictParams.enumNameList.length > 0 && enumAndDictParams.dataDictNameList.length <= 0){ // 枚举
          idx = 0
        }else if(enumAndDictParams.enumNameList.length <= 0 && enumAndDictParams.dataDictNameList.length <= 0  ){
          operation = [];
          this.$message({ showClose: true, message: '字典数据参数请完善！', type: 'error'});
          return;
        }
        let operation = operationList[idx];
        getEnumAndDictApi('', enumAndDictParams, operation).then((res) => {
          let dataDict = (idx !== 0 ? { dataDict: (idx == 2 ? res.data.dataDict : res.data) } :[]) 
          let enumData = {}; // 枚举数据
          if(idx !== 1){
            enumData =  {enum:this.getEnumData( idx == 2 ? res.data.enum : res.data)}
          }
          this.enumAndDictList = { ...dataDict, ...enumData };
        }).catch((error) => {
          
        })
      },
      // 枚举值转换方法
      getEnumData(enums){
        let enumData = enums
        for(var i in enums){
          let value = []
          for(var k in enums[i]){
            value.push({code:k, value:enums[i][k]})
          }
          enumData[i] = value;
        }
        return enumData
      },

      // Excel导出  exportColumn 导出行列字段   filterCondition 过滤数据
      excelDownload(){
        this.excelLoading = true;
        //判断导出类型是否已经赋值,未赋值时给进行提示：
        if(!this.excelDownloadParams.dataType){
          this.$message({ showClose: true, message: '导出的数据类型不能空!', type: 'error'});
          this.excelLoading = false;
          return ;
        }
        this.excelDownloadParams.filterCondition = this.params;
        let idList = this.tableSelList.map((val) => {
          return val.id;
        })
        //导出数据Id统一使用excelIds
        idList.length > 0 ? (this.excelDownloadParams.filterCondition = {excelIds:idList}) : '' // delete this.excelDownloadParams.filterCondition
        !this.excelDownloadParams.exportColumn && delete this.excelDownloadParams.exportColumn; // 导出列数据
        excelDownloadApi( this.excelDownloadParams).then((res) => {
          window.open(res.data.url)
          this.excelLoading = false;
        }).catch((error) => {
          this.excelLoading = false;
        })
      },
  }

}
