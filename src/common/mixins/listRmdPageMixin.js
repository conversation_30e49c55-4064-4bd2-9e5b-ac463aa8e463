
import commonMixin from './commonMixin'
import {getlistApi, deleteApi } from '@/api/remote-search' //   接口js引用

export default {
  components: {},
  mixins: [commonMixin],
  data() {
    return {
      isFirstSel:true, // 进入页面是否进行获取列表数据
      selTabIndex:1, // 头部标题选中下标
      overdueStu:false, // 导入
      tableSelList:[], // 列表所选择的数据
      getMobelUrl:'', //
      importantExcelUrl:'',
      // 暂时修改列表高度为400, 一些界面列表为500以上不能计算问题
      heightData:400, // localStorage.getItem('selectHeight')
      msSelectHeight:0,
      dataSum: {},
      loading: false,
      listLoading: false,
      params: {},
      hasSearch:true, // 无查询条件 默认true有 
      hasParams:false, // 列表查询条件 - 是否有值  false无/true有
      pageNum:[10, 20, 50, 100],
      pageNo: 1, //		当前第几页	int	当前第几页，从0开始，0是第一页	Y	0
      pageSize: 10, // 每页数量	int	每页数量 默认10	Y	10
      totalCount: 0, // 列表总数
      dataList: [],
      getListParams: '',
      hasActionInfo:true,
      actionHeight:270, // 操作高度
    }
  },
  watch:{
    '$route.query'(val){
      if(val.isGet){
        this.isFirstSel && this.apiList && this.apiList.tableListOperation && this.getListWay() // 获取列表数据
        this.apiList && this.apiList.sumOperation && this.getSumWay(); // 获取汇总数据
      }
    },
  },
  created() {
    // window.addEventListener('hashchange',(e)=>{
    //   console.log('====',e)
    // },false)
  },
  mounted(){
    const that = this;
    window.onresize = function windowResize () {
      that.getHeight()
    }
    this.$nextTick(() => {
      this.getHeight()
    })
    this.isFirstSel && this.apiList && this.apiList.tableListOperation && this.getListWay() // 获取列表数据
    this.apiList && this.apiList.sumOperation && this.getSumWay(); // 获取汇总数据
  },
  computed: {
    listParams(){
      return this.params
    },
  },
  methods: {

    // 列表高度计算
    getHeight(params = true){
      let otherHeight = this.hasActionInfo ? this.actionHeight:240;
      let msSelect = 0
      if(!this.hasSearch){
         msSelect =  this.hasSelect || ( this.$refs.params && this.$refs.params.$el ?  this.$refs.params.$el.offsetHeight+20 :100);
      }else{
        msSelect = 0;
      }
      this.heightData = document.getElementById('app').clientHeight+5 - (params? msSelect:-10) - otherHeight;
    },

    // 获取列表数据
    getListWay() {
      this.listLoading = true;
      this.getParamsIsNo(this.listParams); // 检验是否有值
      var params = { ...this.listParams, pageNo: this.pageNo, pageSize: this.pageSize }
      this.getlistApiWay(params).then((res)=>{
        // console.log('res-',res);
        this.listLoading = false;
        this.totalCount = res.total || 0;
        this.dataList = this.resolveDatalist(res);
      }).catch((error)=>{
        this.dataList = []
        this.listLoading = false;
      })
    },
    
    // 列表数据拆分
    resolveDatalist(res){
      return  res.records || []
    },

    // 列表接口方法
    getlistApiWay(params){
      return new Promise((resolve,reject) => {
        getlistApi(this.apiList && this.apiList.tableList || '', params, this.apiList.tableListOperation).then((res) => {
          resolve(res);
        }).catch((error) => {
          reject(error)
        })
      })
    },


    // 列表查询条件必填值
    paramsRequired(){
      return [];
    },
    //   列表查询条件是否有值
    getParamsIsNo(listParams){
      let isEmpty = this.isEmptyObject(this.paramsRequired(),listParams);
      if(isEmpty){
        this.hasParams = false;
      }else{
        this.hasParams = true;
      }
    },
    // 查询对象 - 是否为空
    isEmptyObject(required,params){
      let blg = true;
      if(required){
        required.forEach(item => {
          delete params[item];
        });
      }
      for(const item in params){
        if(params[item] ){
          blg = false;
          break;
        }
      }
      return blg
    },
    // 查询按钮 - 方法
    searchWay() {
      this.pageNo = 1;
      this.getListWay();
      this.apiList.sumOperation && this.getSumWay(); // 获取汇总数据
    },

    // 类别 - 数据汇总，列表头部的数据数量
    getSumWay() {
      var params = { ...this.listParams}
      getlistApi('', params, this.apiList.sumOperation).then((res) => {
        this.dataSum = res.data||{};
      }).catch((error) => {
      })
    },
    /**
     * 删除
     * @param index row 索引
     * @param row row 数据
     * @param msg 提示内容
     * @param params 参数
     * @param explain 第二行描述内容
     * @param operation 操作接口
     */
    handleDel(index, row, msg, params,explain,operation='') {
      explain = explain || ''
      this.$confirm('<span style=" color:#333; margin-top:0px;font-size:16px">'+msg+'</span><p style="margin:0px;font-size:13px;color:#999">'+explain+'</p>', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      }).then(() => {
        this.delDataWay(params,row,operation);
        // this.dataList.splice(index, 1)
      }).catch((error) => {
        this.$message({ type: 'info', message: '已取消操作' })
      })
    },
    // 删除api
    delDataWay(params,row, operation) {
      row && this.$set(row, 'rowloading', true)
      let operationWay = !operation ? this.apiList.tableDeleteOperation : operation;
      deleteApi(this.apiList.tableDelete, params, operationWay).then((res) => {
        row && this.$set(row, 'rowloading', false)
        this.$message({ type: 'success', message: '操作成功!' })
        this.getListWay()
      }).catch((error)=>{
        row && this.$set(row, 'rowloading', false)
        //FIXME 这里在request上有统一提示，这里暂时先去掉：
        //this.$message({ showClose: true, message: '操作失败', type: 'error'});
      })
    },
    // 列表数据选择
    handleSelectionChange(val) {
      this.tableSelList = val;
    },

    // 重置
    resetParamWay(formName) {
      this.params = {};
      // this.$refs[formName].resetFields()
    },
    // 新增
    addWay(path, params) {
      this.$router.push({ path: path, query: params })
    },
    // 详情
    handleDetail(index, row, route) {
      this.$router.push(route)
    },
    // 编辑
    handleEdit(index, row, params) {
      this.$router.push(params)
    },
    // 分页大小
    handleSizeChange(val) {
      this.pageNo = 1;
      this.pageSize = val // 每页显示行数
      this.getListWay()
    },
    // 分页行数
    handleCurrentChange(val) {
      this.pageNo = val;
      this.getListWay()
    },
    // 是否显示列表查询条件
    showParamStu(params){
      // console.log('====是否显示列表查询条件')
      this.getHeight(params);
    },
    
    // 导入
    toLead(){
      this.overdueStu = true
    },
    // 弹框关闭
    handleClose(done) {
      this.editId = '0';
      done();
    },

    serviceTypeChange(val){
      // console.log(val);
    },
    // 头部标题选择下标
    selTabway(index){
      this.selTabIndex = index;
    },

    handleImport(){},
    handleExport(){}
  }

}
