
import commonMixin from './commonMixin'
import {detailApi} from '@/api/remote-search';   //   接口js引用
import { mapState } from 'vuex'
import { getPathId } from '@/utils/utils'
export default {
  components: {},
  mixins: [commonMixin],
  props:{
    // 弹框信息id
    editId:{
      type:String|Number,
      default:'0'
    },
  },
  watch:{
    // 弹框时监测数据id 进行获取信息
    'editId'(val){
      val != '0' && this.getDetailWay(val)
    }
  },
  data () {
    return {
      editInfoLoading:false,
      detail:'detail',
      nowPage:JSON.parse(localStorage.getItem('nowPage')) || {},
      operationList:JSON.parse(localStorage.getItem('operationList')) || [],
      loading:false, // 信息提交状态
      infoHeight:(document.getElementById("app").clientHeight-document.getElementById("app").clientHeight-400),
      detailItem:[],
      dataInfo: {},
    }
  },
  mounted(){
    this.editId != '0'  && this.getDetailWay(this.editId)
    let queryId= this.$route.query.id||getPathId()|| null
    queryId&& this.getDetailWay(queryId)
  },
  computed: {

  },
  methods: {
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    // 获取详情数据
    getDetailWay(id){
      var data = {id:id};
      this.editInfoLoading = true;
      if(this.apiList){
        detailApi( this.apiList.getDetail, data, this.apiList.getDetailOperation ).then((res) => {
          this.dataInfo = res.data || {};
          this.editInfoLoading = false;
        }).catch((error) => {
          this.$message({ showClose: true, message: '获取失败', type: 'error'});
          this.editInfoLoading = false;
        })
      }else{
        this.$message({ type: 'info', message: '请完善请求参数' })
      }
     
    },
  }

}
