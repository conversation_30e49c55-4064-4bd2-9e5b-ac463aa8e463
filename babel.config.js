module.exports = {
  presets: [
    '@vue/cli-plugin-babel/preset'
  ],
  plugins: [
    // Element UI 按需引入
    // [
    //   "babel-plugin-import",
    //   {
    //     "libraryName": "element-ui",
    //     "libraryDirectory": "lib",
    //     "style": "css"
    //   },
    //   "element-ui"
    // ],
    // Lodash 按需引入优化
    [
      "babel-plugin-import",
      {
        "libraryName": "lodash",
        "libraryDirectory": "",
        "camel2DashComponentName": false
      },
      "lodash"
    ],
    // 移除生产环境的 console
    process.env.NODE_ENV === 'production' ? [
      "babel-plugin-transform-remove-console",
      { "exclude": ["error", "warn"] }
    ] : undefined
  ].filter(Boolean)
};
