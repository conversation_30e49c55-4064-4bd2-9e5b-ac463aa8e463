module.exports = {
  presets: [
    '@vue/cli-plugin-babel/preset'
  ]
  // 暂时移除按需引入配置，避免编译错误
}
  plugins: [
    // Element UI 按需引入 - 使用 babel-plugin-import 替代 babel-plugin-component
    [
      "import",
      {
        "libraryName": "element-ui",
        "libraryDirectory": "lib",
        "style": "css"
      },
      "element-ui"
    ],
    // 移除生产环境的 console
    process.env.NODE_ENV === 'production' ? [
      "transform-remove-console",
      { "exclude": ["error", "warn"] }
    ] : undefined
  ].filter(Boolean)
};
