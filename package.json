{"name": "dadazhaoshang", "version": "4.4.0", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "Pan <<EMAIL>>", "scripts": {"dev": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve ", "dev2": "vue-cli-service serve ", "lint": "eslint --ext .js,.vue src", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode dev", "build:test": "vue-cli-service build --mode test", "build:local": "vue-cli-service build --mode local", "build:prod": "vue-cli-service build --mode prod", "build:preview": "vue-cli-service build --mode preview", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "new": "plop", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "check-keyword": "bash ./hooks/check-keyword.sh", "precommit": "npm run check-keyword"}, "pre-commit": ["precommit"], "dependencies": {"@jiaminghi/data-view": "^2.10.0", "@toast-ui/editor": "^3.1.3", "axios": "0.18.1", "clipboard": "2.0.4", "codemirror": "5.45.0", "compression-webpack-plugin": "^6.0.5", "core-js": "^3.6.5", "dayjs": "^1.11.7", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "5.4.3", "echarts-wordcloud": "^1.1.3", "element-ui": "^2.15.14", "file-saver": "2.0.1", "fuse.js": "3.4.4", "gsap": "^3.12.2", "js-cookie": "^2.2.0", "jsencrypt": "^3.3.1", "jsonlint": "1.6.3", "jszip": "3.2.1", "lodash": "^4.17.21", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "postcss-px2rem": "^0.3.0", "px2rem-loader": "^0.1.9", "qrcode2": "^1.2.3", "relation-graph": "^2.0.27", "screenfull": "4.2.0", "script-loader": "0.7.2", "sortablejs": "1.8.4", "vis-network": "9.1.9", "vue": "^2.6.14", "vue-count-to": "1.0.13", "vue-mini-player": "^0.2.1", "vue-monoplasty-slide-verify": "^1.3.1", "vue-router": "3.0.2", "vue-seamless-scroll": "^1.1.23", "vue-splitpane": "1.0.4", "vuedraggable": "2.20.0", "vuex": "3.1.0", "vuex-persistedstate": "^3.2.1", "webpack-bundle-analyzer": "^4.10.2", "xlsx": "0.14.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.46.1", "@typescript-eslint/parser": "^5.46.1", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-component": "^1.1.1", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-import": "^1.13.8", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "^6.7.2", "eslint-plugin-react": "^7.31.11", "eslint-plugin-vue": "^9.8.0", "html-webpack-plugin": "3.2.0", "husky": "^1.3.1", "lint-staged": "8.1.5", "mini-css-extract-plugin": "^0.9.0", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "pre-commit": "^1.2.2", "runjs": "4.3.2", "sass": "1.26.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "license": "MIT", "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}}