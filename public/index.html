<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">

  <script>
    !(function (c, i, e, b) { var h = i.createElement("script"); var f = i.getElementsByTagName("script")[0]; h.type = "text/javascript"; h.crossorigin = true; h.onload = function () { try { c[b] || (c[b] = new c.wpkReporter({ bid: 'uwrd0tf4-zr87a04w', rel: '2.8.2', plugins: [] })); c[b].installAll(); } catch (e) { console.error('init wpkReporter fail', e); } }; f.parentNode.insertBefore(h, f); h.src = e })(window, document, "https://g.alicdn.com/woodpeckerx/jssdk??wpkReporter.js", "__wpk");
  </script>
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <title>
    <%= webpackConfig.name %>
  </title>
</head>

<body style="position: relative;">

  <!-- <div id="app2"></div> -->

  <!-- <div id="wujie"> -->
  <div id="app"></div>
  <!-- </div> -->
  <!-- built files will be auto injected -->
</body>
<script>

</script>

</html>