// module.exports = {
//     // "env": {
//     //     "browser": true,
//     //     "es2021": true
//     // },
//     "extends": [
//         "eslint:recommended",
//         "plugin:vue/vue3-essential",
//         "plugin:@typescript-eslint/recommended"
//     ],
//     "overrides": [
//     ],
//     "parser": "@typescript-eslint/parser",
//     "parserOptions": {
//         "ecmaVersion": "latest",
//         "sourceType": "module"
//     },
//     "plugins": [
//         "vue",
//         "@typescript-eslint"
//     ],
//     "rules": {
//     }
// }
module.exports = {
    root: true,
    parserOptions: {
      parser: 'babel-eslint',
      // sourceType: 'module'
    },
    env: {
      browser: true,
      node: true,
      es6: true,
    },
    extends: ['plugin:vue/recommended', 'eslint:recommended'],
    // extends: ['babel-eslint'],
  
    // add your custom rules here
    //it is base on https://github.com/vuejs/eslint-config-vue
    rules: {
      // indent: [2, 4], // 强制使用一致的缩进
      // eqeqeq: [2, 'always'], // 要求使用 === 和 !==
      // semi: [2, 'never'], // 要求或禁止使用分号代替 ASI
      // quotes: [2, 'single'], // 强制使用一致的反勾号、双引号或单引号
      "vue/no-v-html":"off" ,
      "no-unused-vars":"off",
      "vue/multi-word-component-names":"off"
    }
  }
  